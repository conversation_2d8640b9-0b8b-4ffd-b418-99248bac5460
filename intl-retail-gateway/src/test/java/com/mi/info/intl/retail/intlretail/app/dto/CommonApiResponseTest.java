package com.mi.info.intl.retail.intlretail.app.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class CommonApiResponseTest {

    @Test
    void testConstructorWithBody() {
        // Given
        String testData = "test data";

        // When
        CommonApiResponse<String> response = new CommonApiResponse<>(testData);

        // Then
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(testData, response.getData());
    }

    @Test
    void testConstructorWithAllParameters() {
        // Given
        int code = 200;
        String message = "success";
        String data = "test data";

        // When
        CommonApiResponse<String> response = new CommonApiResponse<>(code, message, data);

        // Then
        assertEquals(code, response.getCode());
        assertEquals(message, response.getMessage());
        assertEquals(data, response.getData());
    }

    @Test
    void testFailureWithCodeAndMessage() {
        // Given
        int code = 400;
        String message = "Bad Request";

        // When
        CommonApiResponse<String> response = CommonApiResponse.failure(code, message);

        // Then
        assertEquals(code, response.getCode());
        assertEquals(message, response.getMessage());
        assertEquals("", response.getData());
    }

    @Test
    void testFailureWithCodeMessageAndData() {
        // Given
        int code = 500;
        String message = "Internal Server Error";
        String data = "Error details";

        // When
        CommonApiResponse<String> response = CommonApiResponse.failure(code, message, data);

        // Then
        assertEquals(code, response.getCode());
        assertEquals(message, response.getMessage());
        assertEquals(data, response.getData());
    }

    @Test
    void testWithNullData() {
        // Given
        String nullData = null;

        // When
        CommonApiResponse<String> response = new CommonApiResponse<>(nullData);

        // Then
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    void testWithComplexData() {
        // Given
        TestData testData = new TestData("test", 123);

        // When
        CommonApiResponse<TestData> response = new CommonApiResponse<>(testData);

        // Then
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(testData, response.getData());
    }

    // 用于测试的辅助类
    private static class TestData {
        private String name;
        private int value;

        public TestData(String name, int value) {
            this.name = name;
            this.value = value;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TestData testData = (TestData) o;
            return value == testData.value && name.equals(testData.name);
        }
    }
} 