package com.mi.info.intl.retail.api.front.position.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 阵地门店信息DTO
 *
 * <AUTHOR>
 * @date 2025/7/29
 */
@Data
public class PositionStoreInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    // 阵地信息
    /**
     * 阵地ID
     */
    private Integer positionId;

    /**
     * 阵地编码
     */
    private String positionCode;

    /**
     * 零售商ID
     */
    private Integer retailerId;

    /**
     * RMS零售商code
     */
    private String retailerCode;

    /**
     * 阵地类型
     */
    private Integer positionType;

    /**
     * 阵地类型名称
     */
    private String positionTypeName;

    // 门店信息
    /**
     * 门店ID
     */
    private Integer storeId;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private Integer storeType;

    /**
     * 门店等级
     */
    private Integer storeGrade;

    /**
     * 门店是否含PC
     */
    private Integer storeHasPC;

    /**
     * 门店是否含SR
     */
    private Integer storeHasSR;

    /**
     * 门店渠道类型
     */
    private Integer storeChannelType;
}
