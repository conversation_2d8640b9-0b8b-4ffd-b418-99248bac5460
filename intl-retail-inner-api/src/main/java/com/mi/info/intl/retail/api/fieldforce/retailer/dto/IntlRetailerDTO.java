package com.mi.info.intl.retail.api.fieldforce.retailer.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * INTL零售商DTO
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Getter
@Setter
public class IntlRetailerDTO {

    private Integer id;

    /**
     * 唯一标识
     */
    private String retailerId;

    /**
     * RMS零售商Code
     */
    private String retailerCode;

    /**
     * 零售商名称
     */
    private String retailerName;

    /**
     * 地区code
     */
    private String regionCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 国家code
     */
    private String countryCode;

    /**
     * 零售商渠道类型,整形值，
     */
    private Integer retailerChannelType;

    /**
     * 渠道类型名称
     */
    private String channelType;

    /**
     * 创建日期
     */
    private Long createdAt;

    /**
     * 更新时间
     */
    private Long updatedAt;

}
