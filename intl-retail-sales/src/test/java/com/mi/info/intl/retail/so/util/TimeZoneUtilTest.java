package com.mi.info.intl.retail.so.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TimeZoneUtil 单元测试
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
class TimeZoneUtilTest {

    @Test
    void testConvertToTimestamp_ValidTimeAndTimezone() {
        // 测试有效的时间和时区
        String timeStr = "2025-07-28 10:30:00";
        String timezoneCode = "+08:00";
        
        Long result = TimeZoneUtil.convertToTimestamp(timeStr, timezoneCode);
        
        assertNotNull(result);
        assertTrue(result > 0);
    }

    @Test
    void testConvertToTimestamp_ValidTimeWithoutTimezone() {
        // 测试有效时间但没有时区（应使用UTC）
        String timeStr = "2025-07-28 10:30:00";
        String timezoneCode = null;
        
        Long result = TimeZoneUtil.convertToTimestamp(timeStr, timezoneCode);
        
        assertNotNull(result);
        assertTrue(result > 0);
    }

    @Test
    void testConvertToTimestamp_ValidTimeWithBlankTimezone() {
        // 测试有效时间但时区为空字符串
        String timeStr = "2025-07-28 10:30:00";
        String timezoneCode = "";
        
        Long result = TimeZoneUtil.convertToTimestamp(timeStr, timezoneCode);
        
        assertNotNull(result);
        assertTrue(result > 0);
    }

    @Test
    void testConvertToTimestamp_EmptyTimeString() {
        // 测试空时间字符串
        String timeStr = "";
        String timezoneCode = "+08:00";
        
        Long result = TimeZoneUtil.convertToTimestamp(timeStr, timezoneCode);
        
        assertNull(result);
    }

    @Test
    void testConvertToTimestamp_NullTimeString() {
        // 测试null时间字符串
        String timeStr = null;
        String timezoneCode = "+08:00";
        
        Long result = TimeZoneUtil.convertToTimestamp(timeStr, timezoneCode);
        
        assertNull(result);
    }

    @Test
    void testConvertToTimestamp_InvalidTimeFormat() {
        // 测试无效的时间格式
        String timeStr = "2025/07/28 10:30:00";
        String timezoneCode = "+08:00";
        
        Long result = TimeZoneUtil.convertToTimestamp(timeStr, timezoneCode);
        
        assertNull(result);
    }

    @Test
    void testConvertToTimestamp_InvalidTimezone() {
        // 测试无效的时区格式
        String timeStr = "2025-07-28 10:30:00";
        String timezoneCode = "invalid";
        
        Long result = TimeZoneUtil.convertToTimestamp(timeStr, timezoneCode);
        
        assertNull(result);
    }

    @Test
    void testConvertToTimestamp_DifferentTimezones() {
        // 测试不同时区的转换
        String timeStr = "2025-07-28 10:30:00";
        
        Long resultUTC = TimeZoneUtil.convertToTimestamp(timeStr, "+00:00");
        Long resultBeijing = TimeZoneUtil.convertToTimestamp(timeStr, "+08:00");
        Long resultNewYork = TimeZoneUtil.convertToTimestamp(timeStr, "-05:00");
        
        assertNotNull(resultUTC);
        assertNotNull(resultBeijing);
        assertNotNull(resultNewYork);
        
        // 北京时间应该比UTC时间小8小时（8 * 60 * 60 * 1000毫秒）
        assertEquals(8 * 60 * 60 * 1000, resultUTC - resultBeijing);
        
        // 纽约时间应该比UTC时间大5小时
        assertEquals(5 * 60 * 60 * 1000, resultNewYork - resultUTC);
    }

    @Test
    void testGetCurrentTimestamp() {
        // 测试获取当前时间戳
        Long timestamp1 = TimeZoneUtil.getCurrentTimestamp();
        
        // 等待一小段时间
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Long timestamp2 = TimeZoneUtil.getCurrentTimestamp();
        
        assertNotNull(timestamp1);
        assertNotNull(timestamp2);
        assertTrue(timestamp2 >= timestamp1);
    }

    @Test
    void testConvertToTimeString_ValidTimestamp() {
        // 测试有效时间戳转换为时间字符串
        Long timestamp = 1722142200000L; // 2025-07-28 10:30:00 UTC
        String timezoneCode = "+08:00";
        
        String result = TimeZoneUtil.convertToTimeString(timestamp, timezoneCode);
        
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    @Test
    void testConvertToTimeString_NullTimestamp() {
        // 测试null时间戳
        Long timestamp = null;
        String timezoneCode = "+08:00";
        
        String result = TimeZoneUtil.convertToTimeString(timestamp, timezoneCode);
        
        assertNull(result);
    }

    @Test
    void testConvertToTimeString_InvalidTimezone() {
        // 测试无效时区
        Long timestamp = 1722142200000L;
        String timezoneCode = "invalid";
        
        String result = TimeZoneUtil.convertToTimeString(timestamp, timezoneCode);
        
        assertNull(result);
    }

    @Test
    void testConvertToTimeString_WithoutTimezone() {
        // 测试没有时区（使用系统默认时区）
        Long timestamp = 1722142200000L;
        String timezoneCode = null;
        
        String result = TimeZoneUtil.convertToTimeString(timestamp, timezoneCode);
        
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    @Test
    void testConvertToTimeString_BlankTimezone() {
        // 测试空白时区
        Long timestamp = 1722142200000L;
        String timezoneCode = "";
        
        String result = TimeZoneUtil.convertToTimeString(timestamp, timezoneCode);
        
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    @Test
    void testRoundTripConversion() {
        // 测试往返转换（时间字符串 -> 时间戳 -> 时间字符串）
        String originalTimeStr = "2025-07-28 10:30:00";
        String timezoneCode = "+08:00";
        
        Long timestamp = TimeZoneUtil.convertToTimestamp(originalTimeStr, timezoneCode);
        assertNotNull(timestamp);
        
        String convertedTimeStr = TimeZoneUtil.convertToTimeString(timestamp, timezoneCode);
        assertNotNull(convertedTimeStr);
        
        // 由于时区转换的复杂性，这里只验证格式正确
        assertTrue(convertedTimeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    @Test
    void testEdgeCases() {
        // 测试边界情况
        
        // 测试年初
        String newYear = "2025-01-01 00:00:00";
        Long newYearTimestamp = TimeZoneUtil.convertToTimestamp(newYear, "+00:00");
        assertNotNull(newYearTimestamp);
        
        // 测试年末
        String yearEnd = "2025-12-31 23:59:59";
        Long yearEndTimestamp = TimeZoneUtil.convertToTimestamp(yearEnd, "+00:00");
        assertNotNull(yearEndTimestamp);
        
        // 年末应该大于年初
        assertTrue(yearEndTimestamp > newYearTimestamp);
    }

    @Test
    void testSpecialTimezones() {
        // 测试特殊时区
        String timeStr = "2025-07-28 12:00:00";
        
        // 测试正时区
        Long positiveTimezone = TimeZoneUtil.convertToTimestamp(timeStr, "+12:00");
        assertNotNull(positiveTimezone);
        
        // 测试负时区
        Long negativeTimezone = TimeZoneUtil.convertToTimestamp(timeStr, "-12:00");
        assertNotNull(negativeTimezone);
        
        // 测试UTC
        Long utcTimezone = TimeZoneUtil.convertToTimestamp(timeStr, "+00:00");
        assertNotNull(utcTimezone);
        
        // 正时区应该小于UTC，负时区应该大于UTC
        assertTrue(positiveTimezone < utcTimezone);
        assertTrue(negativeTimezone > utcTimezone);
    }
}
