package com.mi.info.intl.retail.so.app.provider.sales;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingTypeRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesQtyRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
import com.mi.info.intl.retail.so.domain.sales.converter.SalesQtyConverter;
import com.mi.info.intl.retail.so.domain.sales.model.SoQtyIndex;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoQtyDomainService;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoQtyEsService;
import com.mi.info.intl.retail.utils.JsonUtil;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * SoSalesQtyDubboProviderImpl ES集成测试 参考 IntlSoImeiEsServiceTest 实现真实的ES连接和测试
 */
@DisplayName("SoSalesQtyDubboProviderImpl ES集成测试")
class SoSalesQtyDubboProviderImplTest {

    private static final String INDEX_NAME = "intl_retail_so_qty_dev";

    private ElasticsearchRestTemplate template;

    private IntlSoQtyEsService intlSoQtyEsService;

    private SoSalesQtyDubboProviderImpl soSalesQtyDubboProvider;

    private IntlSoQtyDomainService intlSoQtyDomainService;

    private SalesQtyConverter salesQtyConverter;

    @BeforeEach
    void setUp() {
        // 创建真实的ES连接
        template = new ElasticsearchRestTemplate(new RestHighLevelClient(
                RestClient.builder(new HttpHost("c3test.api.es.srv", 80, "http"))
                        .setRequestConfigCallback(requestConfigBuilder ->
                                requestConfigBuilder.setSocketTimeout(10000)))
        );

        // 创建ES服务实例并注入template
        intlSoQtyEsService = new IntlSoQtyEsService();
        ReflectionTestUtils.setField(intlSoQtyEsService, "template", template);
        ReflectionTestUtils.setField(intlSoQtyEsService, "indexName", INDEX_NAME);

        // 创建简化的SalesQtyConverter实例用于测试
        salesQtyConverter = createTestSalesQtyConverter();

        // 创建Domain服务实例
        intlSoQtyDomainService = new IntlSoQtyDomainService();
        ReflectionTestUtils.setField(intlSoQtyDomainService, "intlSoQtyEsService", intlSoQtyEsService);
        ReflectionTestUtils.setField(intlSoQtyDomainService, "salesQtyConverter", salesQtyConverter);

        // 创建Provider实例
        soSalesQtyDubboProvider = new SoSalesQtyDubboProviderImpl();
        ReflectionTestUtils.setField(soSalesQtyDubboProvider, "intlSoQtyDomainService", intlSoQtyDomainService);
    }

    /**
     * 创建用于测试的简化SalesQtyConverter
     */
    private SalesQtyConverter createTestSalesQtyConverter() {
        return new SalesQtyConverter() {
            @Override
            public List<SalesQtyRespDto> toTarget(List<SoQtyIndex> indexList) {
                if (indexList == null || indexList.isEmpty()) {
                    return new ArrayList<>();
                }

                List<SalesQtyRespDto> result = new ArrayList<>();
                for (SoQtyIndex index : indexList) {
                    SalesQtyRespDto dto = new SalesQtyRespDto();
                    dto.setId(index.getId());
                    dto.setSalesmanMid(index.getSalesmanMid());
                    dto.setProductCode(index.getProductCode());
                    dto.setProductLineEn(index.getProductLineEn());
                    dto.setSpuEn(index.getSpuName());
                    dto.setRrp(index.getRrp());
                    dto.setCurrency(index.getCurrency());
                    dto.setCode69(index.getCode69());
                    dto.setSalesTime(index.getSalesTime());
                    dto.setSalesTimeStr(formatTimeForTest(index.getSalesTime())); // 使用简化的时间格式化
                    dto.setReportingType(index.getReportingType());
                    dto.setIsPhotoExist(index.getIsPhotoExist());
                    dto.setCreatedBy(index.getCreatedBy());
                    dto.setCreatedOn(index.getCreatedOn());
                    dto.setCreatedOnDesc(formatTimeForTest(index.getCreatedOn())); // 使用简化的时间格式化
                    dto.setStoreCode(index.getStoreRmsCode());
                    dto.setStoreGrade(index.getStoreGrade());
                    dto.setStoreType(index.getStoreType());
                    dto.setChannelType(index.getStoreChannelType());
                    dto.setHasSr(index.getStoreHasSr());
                    dto.setHasPc(index.getStoreHasPc());
                    dto.setPositionCode(index.getPositionRmsCode());
                    dto.setPositionType(index.getPositionType());
                    dto.setRetailerCode(index.getRetailerCode());
                    dto.setCountry(index.getCountryShortcode());
                    dto.setCity(index.getCityCode());
                    result.add(dto);
                }
                return result;
            }
        };
    }

    /**
     * 简化的时间格式化方法，用于测试环境
     */
    private String formatTimeForTest(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        // 简单的格式化，避免依赖HTTP上下文
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(timestamp));
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        try {
            IndexOperations indexOps = template.indexOps(IndexCoordinates.of(INDEX_NAME));
            if (indexOps.exists()) {
                // 删除测试数据，保留索引结构
                template.delete(createTestIds(), IndexCoordinates.of(INDEX_NAME));
            }
        } catch (Exception e) {
            // 忽略清理异常
            System.out.println("清理测试数据时出现异常: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("初始化测试数据")
    void initTestData() {
        List<SoQtyIndex> testData = createTestData();
        template.save(testData, IndexCoordinates.of(INDEX_NAME));

        // 验证数据是否插入成功
        NativeSearchQuery query2x = new NativeSearchQueryBuilder()
                // .withQuery(EsQueryConverter.convert(reqDto).getQuery())
                .build();
        long count = template.count(query2x, SoQtyIndex.class, IndexCoordinates.of(INDEX_NAME));
        assertThat(count).isGreaterThan(0);
        System.out.println("成功插入 " + testData.size() + " 条测试数据，当前索引总数: " + count);
    }

    @Test
    @DisplayName("测试ES查询销量数据")
    void testEsQuerySalesQty() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(1);
        salesQtyReqDto.setPageSize(1000);
        // salesQtyReqDto.setSalesmanMid(123456L);

        salesQtyReqDto.setGroupBy("salesmanJobTitle");
        // salesQtyReqDto.setGroupBy("reportingType");
        // salesQtyReqDto.setGroupBy("salesmanMid");
        Map<String, Long> stringLongMap = intlSoQtyEsService.groupByAgg(salesQtyReqDto);
        System.out.println("stringLongMap=" + stringLongMap);

        // 执行ES查询
        PageResponse<SoQtyIndex> result = intlSoQtyEsService.search(salesQtyReqDto);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getTotalCount()).isGreaterThan(0);
        System.out.println("ES查询结果总数: " + result.getTotalCount());
        System.out.println("当前页数据: " + result.getData().size());

        // 打印前几条数据
        for (int i = 0; i < Math.min(3, result.getData().size()); i++) {
            System.out.println("数据" + (i + 1) + ": " + JsonUtil.bean2json(result.getData().get(i)));
        }
    }

    @Test
    @DisplayName("测试按产品代码查询")
    void testEsQueryByProductCode() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(0);
        salesQtyReqDto.setPageSize(5);
        salesQtyReqDto.setProductCode(12345L);

        // 执行ES查询
        PageResponse<SoQtyIndex> result = intlSoQtyEsService.search(salesQtyReqDto);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getTotalCount()).isGreaterThan(0);

        // 验证所有返回的数据都包含指定的产品代码
        for (SoQtyIndex index : result.getData()) {
            assertThat(index.getProductCode()).isEqualTo(12345L);
        }

        System.out.println("按产品代码查询结果: " + result.getTotalCount() + " 条记录");
        System.out.println("返回数据: " + result.getData().size() + " 条");
    }

    @Test
    @DisplayName("测试按门店代码查询")
    void testEsQueryByStoreCode() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(0);
        salesQtyReqDto.setPageSize(10);
        salesQtyReqDto.setStoreRmsCode("STORE001");

        // 执行ES查询
        PageResponse<SoQtyIndex> result = intlSoQtyEsService.search(salesQtyReqDto);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getTotalCount()).isGreaterThan(0);

        // 验证所有返回的数据都包含指定的门店代码
        for (SoQtyIndex index : result.getData()) {
            assertThat(index.getStoreRmsCode()).isEqualTo("STORE001");
        }

        System.out.println("按门店代码查询结果: " + result.getTotalCount() + " 条记录");
        System.out.println("返回数据: " + result.getData().size() + " 条");
    }

    @Test
    @DisplayName("测试数据转换")
    void testDataConversion() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(0);
        salesQtyReqDto.setPageSize(5);

        // 执行ES查询
        PageResponse<SoQtyIndex> indexList = intlSoQtyEsService.search(salesQtyReqDto);

        // 转换为DTO
        List<SalesQtyRespDto> dtoList = convertSoQtyIndexToSalesQtyRespDto(indexList.getData());

        // 验证转换结果
        assertThat(dtoList).isNotNull();
        assertThat(dtoList.size()).isEqualTo(indexList.getData().size());

        // 验证转换后的数据
        for (int i = 0; i < dtoList.size(); i++) {
            SalesQtyRespDto dto = dtoList.get(i);
            SoQtyIndex index = indexList.getData().get(i);

            assertThat(dto.getId()).isEqualTo(index.getId());
            assertThat(dto.getSalesmanMid()).isEqualTo(index.getSalesmanMid());
            assertThat(dto.getProductCode()).isEqualTo(index.getProductCode());
        }

        System.out.println("数据转换成功，转换了 " + dtoList.size() + " 条记录");
        if (!dtoList.isEmpty()) {
            System.out.println("转换后的第一条数据: " + JsonUtil.bean2json(dtoList.get(0)));
        }
    }

    @Test
    @DisplayName("测试获取销量报表角色数据")
    void testGetQtyReportingRoleData() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(1);
        salesQtyReqDto.setPageSize(1000);

        // 执行Provider方法
        SingleResponse<ReportingRoleRespDto> response = soSalesQtyDubboProvider.getQtyReportingRoleData(salesQtyReqDto);

        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.isSuccess()).isTrue();
        assertThat(response.getData()).isNotNull();

        ReportingRoleRespDto result = response.getData();

        // 验证各个角色数量
        assertThat(result.getPromoterCount()).isNotNull();
        assertThat(result.getTemporaryPromoterCount()).isNotNull();
        assertThat(result.getSupervisorCount()).isNotNull();
        assertThat(result.getSupervisorWithoutPromoterCount()).isNotNull();
        assertThat(result.getMerchandiserCount()).isNotNull();
        assertThat(result.getOthersCount()).isNotNull();

        // 验证总数计算正确
        Long totalCount = result.getPromoterCount() + result.getTemporaryPromoterCount() +
                result.getSupervisorCount() + result.getSupervisorWithoutPromoterCount() +
                result.getMerchandiserCount() + result.getOthersCount();

        System.out.println("报表角色数据统计结果:");
        System.out.println("Promoter Count: " + result.getPromoterCount());
        System.out.println("Temporary Promoter Count: " + result.getTemporaryPromoterCount());
        System.out.println("Supervisor Count: " + result.getSupervisorCount());
        System.out.println("Supervisor Without Promoter Count: " + result.getSupervisorWithoutPromoterCount());
        System.out.println("Merchandiser Count: " + result.getMerchandiserCount());
        System.out.println("Others Count: " + result.getOthersCount());
        System.out.println("Total Count: " + totalCount);
    }

    @Test
    @DisplayName("测试获取销量报表类型数据")
    void testGetQtyReportingTypeData() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(1);
        salesQtyReqDto.setPageSize(1000);

        // 执行Provider方法
        SingleResponse<ReportingTypeRespDto> response = soSalesQtyDubboProvider.getQtyReportingTypeData(salesQtyReqDto);

        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.isSuccess()).isTrue();
        assertThat(response.getData()).isNotNull();

        ReportingTypeRespDto result = response.getData();

        // 验证各个类型数量
        assertThat(result.getAppCount()).isNotNull();
        assertThat(result.getPcCount()).isNotNull();

        // 验证总数计算正确
        Long totalCount = result.getAppCount() + result.getPcCount();

        System.out.println("报表类型数据统计结果:");
        System.out.println("APP Count: " + result.getAppCount());
        System.out.println("PC Count: " + result.getPcCount());
        System.out.println("Total Count: " + totalCount);
    }

    @Test
    @DisplayName("测试分页获取销量数据")
    void testGetSalesQtyForPage() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(1);
        salesQtyReqDto.setPageSize(10);

        // 执行Provider方法
        PageResponse<SalesQtyRespDto> response = soSalesQtyDubboProvider.getSalesQtyForPage(salesQtyReqDto);

        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.getTotalCount()).isGreaterThan(0);
        assertThat(response.getData()).isNotNull();
        assertThat(response.getData().size()).isLessThanOrEqualTo(10);

        System.out.println("分页查询结果:");
        System.out.println("总记录数: " + response.getTotalCount());
        System.out.println("当前页数据量: " + response.getData().size());

        // 验证返回的数据结构
        if (!response.getData().isEmpty()) {
            SalesQtyRespDto firstRecord = response.getData().get(0);
            assertThat(firstRecord.getId()).isNotNull();
            assertThat(firstRecord.getSalesmanMid()).isNotNull();
            assertThat(firstRecord.getProductCode()).isNotNull();

            System.out.println("第一条数据示例: " + JsonUtil.bean2json(firstRecord));
        }
    }

    @Test
    @DisplayName("测试分页获取销量数据 - 带条件查询")
    void testGetSalesQtyForPageWithConditions() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数 - 带条件查询
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(1);
        salesQtyReqDto.setPageSize(5);
        salesQtyReqDto.setProductCode(12345L); // 按产品代码过滤
        salesQtyReqDto.setStoreRmsCode("STORE001"); // 按门店代码过滤

        // 执行Provider方法
        PageResponse<SalesQtyRespDto> response = soSalesQtyDubboProvider.getSalesQtyForPage(salesQtyReqDto);

        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.getData()).isNotNull();
        assertThat(response.getData().size()).isLessThanOrEqualTo(5);

        // 验证过滤条件生效
        for (SalesQtyRespDto record : response.getData()) {
            assertThat(record.getProductCode()).isEqualTo(12345L);
            assertThat(record.getStoreCode()).isEqualTo("STORE001");
        }

        System.out.println("带条件分页查询结果:");
        System.out.println("总记录数: " + response.getTotalCount());
        System.out.println("当前页数据量: " + response.getData().size());
        System.out.println("过滤条件: productCode=12345, storeCode=STORE001");
    }

    @Test
    @DisplayName("测试分页获取销量数据 - 空结果")
    void testGetSalesQtyForPageEmptyResult() {
        // 先初始化测试数据
        initTestData();

        // 准备查询参数 - 使用不存在的条件
        SalesQtyReqDto salesQtyReqDto = new SalesQtyReqDto();
        salesQtyReqDto.setPageIndex(1);
        salesQtyReqDto.setPageSize(10);
        salesQtyReqDto.setProductCode(99999L); // 不存在的产品代码

        // 执行Provider方法
        PageResponse<SalesQtyRespDto> response = soSalesQtyDubboProvider.getSalesQtyForPage(salesQtyReqDto);

        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.getTotalCount()).isEqualTo(0);
        assertThat(response.getData()).isEmpty();

        System.out.println("空结果查询测试:");
        System.out.println("总记录数: " + response.getTotalCount());
        System.out.println("当前页数据量: " + response.getData().size());
    }

    /**
     * 创建测试数据
     */
    private List<SoQtyIndex> createTestData() {
        List<SoQtyIndex> list = new ArrayList<>();
        long currentTime = System.currentTimeMillis();

        // 定义角色代码数组，对应ReportingRoleEnum的值
        String[] roleCodes = {
                "500900001", // PROMOTER
                "100000027", // TEMPORARY_PROMOTER
                "500900002", // SUPERVISOR
                "100000051", // SUPERVISOR_WITHOUT_PROMOTERS
                "100000024"  // MERCHANDISER
        };

        // 定义报表类型代码数组，对应ReportingTypeEnum的值
        String[] typeCodes = {
                "100000000", // PC
                "100000001"  // APP
        };

        for (int i = 0; i < 50; i++) {
            SoQtyIndex index = SoQtyIndex.builder()
                    .id(1000000L + i)
                    .rmsId("RMS" + String.format("%06d", i))
                    .salesmanMid(123456L + (i % 5)) // 5个不同的销售员
                    .salesmanJobtitle(1 + (i % 3)) // 3种不同的职位
                    .productCode(12345 + (i % 3)) // 3种不同的产品
                    .productLineEn("ProductLine" + (i % 2))
                    .spuName("SPU" + (i % 3))
                    .quantity(1 + (i % 5)) // 1-5的数量
                    .rrpCode("RRP" + String.format("%03d", i))
                    .rrp(new BigDecimal(100 + i * 10))
                    .currency("USD")
                    .code69("69" + String.format("%012d", i))
                    //                    .batchId(1)
                    .reportingType(Integer.parseInt(typeCodes[i % typeCodes.length])) // 使用类型代码
                    //                    .dataFrom(1)
                    .isPhotoExist(1)
                    .status(1)
                    //                    .orgInfoId(1000L + i)
                    //                    .userInfoId(2000L + i)
                    .storeRmsCode("STORE" + String.format("%03d", i % 10)) // 10个不同的门店
                    .storeGrade(1 + (i % 3))
                    .storeType(1 + (i % 2))
                    .storeChannelType(1 + (i % 2))
                    .storeHasSr(1)
                    .storeHasPc(1)
                    .positionRmsCode("POS" + String.format("%03d", i % 5))
                    .positionType(1 + (i % 2))
                    .retailerCode("RET" + String.format("%03d", i % 8))
                    .countryShortcode("CN")
                    .cityCode("CITY" + String.format("%06d", i))
                    .salesTime(currentTime - (i * 86400000L)) // 每天递减
                    .createdBy(123456L)
                    .createdOn(currentTime - (i * 3600000L)) // 每小时递减
                    .createdByJobtitle(Integer.parseInt(roleCodes[i % roleCodes.length])) // 使用角色代码
                    .modifiedBy(123456L)
                    .modifiedOn(currentTime)
                    .build();
            list.add(index);
        }
        return list;
    }

    /**
     * 创建测试数据的ID列表用于清理
     */
    private List<Long> createTestIds() {
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < 50; i++) {
            ids.add(1000000L + i);
        }
        return ids;
    }

    /**
     * 将SoQtyIndex转换为SalesQtyRespDto
     */
    private List<SalesQtyRespDto> convertSoQtyIndexToSalesQtyRespDto(List<SoQtyIndex> indexList) {
        if (indexList == null || indexList.isEmpty()) {
            return new ArrayList<>();
        }

        List<SalesQtyRespDto> result = new ArrayList<>();
        for (SoQtyIndex index : indexList) {
            SalesQtyRespDto dto = new SalesQtyRespDto();
            dto.setId(index.getId());
            dto.setSalesmanMid(index.getSalesmanMid());
            dto.setProductCode(index.getProductCode());
            dto.setProductLineEn(index.getProductLineEn());
            dto.setSpuEn(index.getSpuName());
            dto.setRrp(index.getRrp());
            dto.setCurrency(index.getCurrency());
            dto.setCode69(index.getCode69());
            dto.setSalesTime(index.getSalesTime());
            dto.setSalesTimeStr(formatTimeForTest(index.getSalesTime())); // 使用简化的时间格式化
            dto.setReportingType(index.getReportingType());
            dto.setIsPhotoExist(index.getIsPhotoExist());
            dto.setCreatedBy(index.getCreatedBy());
            dto.setCreatedOn(index.getCreatedOn());
            dto.setCreatedOnDesc(formatTimeForTest(index.getCreatedOn())); // 使用简化的时间格式化
            dto.setStoreCode(index.getStoreRmsCode());
            dto.setStoreGrade(index.getStoreGrade());
            dto.setStoreType(index.getStoreType());
            dto.setChannelType(index.getStoreChannelType());
            dto.setHasSr(index.getStoreHasSr());
            dto.setHasPc(index.getStoreHasPc());
            dto.setPositionCode(index.getPositionRmsCode());
            dto.setPositionType(index.getPositionType());
            dto.setRetailerCode(index.getRetailerCode());
            dto.setCountry(index.getCountryShortcode());
            dto.setCity(index.getCityCode());
            result.add(dto);
        }
        return result;
    }
} 