package com.mi.info.intl.retail.so.app.provider.upload;

import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.intlretail.service.api.ldu.SnImeiService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.upload.enums.VerifyResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

/**
 * ImeiReportVerifyServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
class ImeiReportVerifyServiceImplTest {

    @Mock
    private SnImeiService snImeiService;

    @Mock
    private ProductApiService productApiService;

    @InjectMocks
    private ImeiReportVerifyServiceImpl imeiReportVerifyService;

    private ImeiReportVerifyRequest validRequest;

    @BeforeEach
    void setUp() {
        setupValidRequest();
    }

    private void setupValidRequest() {
        validRequest = new ImeiReportVerifyRequest();
        validRequest.setSns(Arrays.asList("60983/65QB01360", "60983/65QB01360"));
        validRequest.setUserTitle(500900001);
        validRequest.setMiId("2105098538");
        validRequest.setCountryCode("ID");
    }

    @Test
    void testImeiReportVerify_Success() {
        // 准备Mock数据
        setupMockData();

        // 执行测试
        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        
        @SuppressWarnings("unchecked")
        List<ImeiReportVerifyResponse> responseList = (List<ImeiReportVerifyResponse>) response.getData();
        assertEquals(2, responseList.size());
    }

    @Test
    void testImeiReportVerify_EmptySns() {
        validRequest.setSns(Collections.emptyList());

        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        assertEquals(400, response.getCode());
        assertEquals("sns cannot be empty", response.getMessage());
    }

    @Test
    void testImeiReportVerify_TooManySns() {
        List<String> largeSns = new ArrayList<>();
        for (int i = 0; i < 101; i++) {
            largeSns.add("60983/65QB0136" + i);
        }
        validRequest.setSns(largeSns);

        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        assertEquals(400, response.getCode());
        assertEquals("sns size cannot exceed 10", response.getMessage());
    }

    @Test
    void testImeiReportVerify_EmptyMiId() {
        validRequest.setMiId("");

        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        assertEquals(400, response.getCode());
        assertEquals("userId cannot be empty", response.getMessage());
    }

    @Test
    void testImeiReportVerify_ZeroUserTitle() {
        validRequest.setUserTitle(0);

        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        assertEquals(400, response.getCode());
        assertEquals("userTitle cannot be empty", response.getMessage());
    }

    @Test
    void testImeiReportVerify_EmptyCountryCode() {
        validRequest.setCountryCode("");

        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        assertEquals(400, response.getCode());
        assertEquals("countryCode cannot be empty", response.getMessage());
    }

    @Test
    void testImeiReportVerify_LegalityValidationFailed() {
        validRequest.setSns(Arrays.asList("123")); // 长度不足

        setupMockData();

        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        assertNotNull(response);
        @SuppressWarnings("unchecked")
        List<ImeiReportVerifyResponse> responseList = (List<ImeiReportVerifyResponse>) response.getData();
        assertEquals(1, responseList.size());
        assertEquals(VerifyResult.LEGALITY_VALIDATION_FAILED.getValue(), responseList.get(0).getVerifyResult());
    }

    @Test
    void testImeiReportVerify_ProductValidationFailed() {
        validRequest.setSns(Arrays.asList("99999/65QB01360")); // 不存在的产品代码

        setupMockData();

        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        assertNotNull(response);
        @SuppressWarnings("unchecked")
        List<ImeiReportVerifyResponse> responseList = (List<ImeiReportVerifyResponse>) response.getData();
        assertEquals(1, responseList.size());
        assertEquals(VerifyResult.PRODUCT_VALIDATION_FAILED.getValue(), responseList.get(0).getVerifyResult());
    }

    @Test
    void testImeiReportVerify_ServiceException() {
        when(snImeiService.querySnImeiInfo(any())).thenThrow(new RuntimeException("Service error"));

        CommonApiResponse<Object> response = imeiReportVerifyService.imeiReportVerify(validRequest);

        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("ImeiReportVerifyServiceImpl Error"));
    }

    private void setupMockData() {
        // Mock ProductApiService
        Map<Integer, ProductInfoDTO> productInfoMap = new HashMap<>();
        ProductInfoDTO productInfo = new ProductInfoDTO();
        productInfo.setGoodsId("60983");
        productInfo.setId(1001);
        productInfo.setName("Test Product");
        productInfo.setSkuName("Test SKU");
        productInfo.setIsSn(2); // 串码管理商品
        productInfoMap.put(60983, productInfo);
        
        //when(productApiService.queryProductsByGoodIds(anyList())).thenReturn(productInfoMap);

        // Mock SnImeiService
        List<SnImeiInfoDto> snImeiInfoList = new ArrayList<>();
        SnImeiInfoDto snImeiInfo = new SnImeiInfoDto();
        snImeiInfo.setSn("60983/65QB01360");
        snImeiInfo.setImei("861234567890123");
        snImeiInfo.setImei2("861234567890124");
        snImeiInfo.setGoodsId("60983");
        snImeiInfo.setB2bCustomerCode(43256);
        snImeiInfoList.add(snImeiInfo);

        //CommonResponse<List<SnImeiInfoDto>> snImeiResponse = new CommonResponse<>();
        //snImeiResponse.setData(snImeiInfoList);
        //when(snImeiService.querySnImeiInfo(any())).thenReturn(snImeiResponse);
    }
}