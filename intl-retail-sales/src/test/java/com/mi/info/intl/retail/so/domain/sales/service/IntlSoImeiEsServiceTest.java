package com.mi.info.intl.retail.so.domain.sales.service;

import com.alibaba.cola.dto.PageResponse;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.utils.JsonUtil;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;


@ExtendWith(MockitoExtension.class)
public class IntlSoImeiEsServiceTest {

    @InjectMocks
    private IntlSoImeiEsService service;

    @BeforeEach
    void setup() {
        ElasticsearchRestTemplate template = new ElasticsearchRestTemplate(new RestHighLevelClient(
                RestClient.builder(new HttpHost("c3test.api.es.srv", 80, "http"))
                        .setRequestConfigCallback(requestConfigBuilder ->
                                requestConfigBuilder.setSocketTimeout(10000)))
        );
        ReflectionTestUtils.setField(service, "template", template);
        ReflectionTestUtils.setField(service, "indexName", "intl_retail_so_imei_dev");
    }

    @Test
    public void testDelete(){
        SalesImeiReqDto req = SalesImeiReqDto.builder()
                .build();
        req.setPageIndex(0);
        req.setPageSize(500);
        PageResponse<SoImeiIndex> pageResponse = service.queryByPage(req);
        service.delete(pageResponse.getData());
        Assertions.assertTrue(true);
    }
    
    @Test
    public void InitData(){
        List<SoImeiIndex> list = new ArrayList<SoImeiIndex>(100);
        for (int i = 0; i < 100; i++) {

            SoImeiIndex index =  SoImeiIndex.builder()
                        .id(52337944l+i)
                        .imei1Mask("8612****890123"+i)
                        .imei1Hash("861234567890123"+i)
                        .imei2Hash("861221433890123"+i)
                        .imei2Mask("86123****90123"+i)
                        .rrp(new BigDecimal(100))
                        .rrpCode("100")
                        .currency("USD")
                        .salesTime(1690000000L)
                        .activationTime(1690000000L)
                        .activationResult(1)
                        .salesmanMid(1l+i)
                        .salesmanJobtitle(1)
                        .storeCode("1000000000000000000000000000000")
                        .createdBy(1L)
                        .retailerCode("1000000000000000000000")
                        .positionCode("1000000000000000000000000000000")
                        .productCode(22222)
                        .storeRmsCode("1000000000000000000000000000000")
                        .positionRmsCode("10000000000000000")
                        .storeGrade(1)
                        .storeType(1)
                        .storeChannelType(1)
                        .storeHasPc(1)
                        .storeHasSr(1)
                        .positionType(1)
                        .countryShortcode("CN")
                        .cityCode("1000000")
                        .createdOn(1690000000L+i)
                        .modifiedBy(1L)
                        .modifiedOn(1690000000L)
                        .reportingType(1)
                        .build();
                list.add(index);
        }
        service.batchInsert(list);
        Assertions.assertTrue(true);
    }



    @Test
    public void TestQuery(){
        SalesImeiReqDto req = SalesImeiReqDto.builder()
                //.id(12334464l)
                //.salesmanMid(80l)
                .storeGradeList(Arrays.asList(1,2,3))
                .salesmanJobtitleList(Arrays.asList(1))
                //.productCode(21421421l)
                //.storeTypeList(Arrays.asList(2,3))
                .build();
        req.setPageSize(30);
        req.setPageIndex(0);
        req.setSearchAfter(Lists.newArrayList(52338041));
        PageResponse<SoImeiIndex> page = service.queryByPage(req);
        for (SoImeiIndex index : page.getData()) {
            System.out.println(JsonUtil.bean2json(index));
        }
        Assertions.assertTrue(true);
    }

    @Test
    public void TestGroupByAgg(){
        SalesImeiReqDto req = SalesImeiReqDto.builder()
                //.id(12334464l)
                //.salesmanMid(80l)
                //.salesmanJobtitleList(Arrays.asList(1))
                .storeGradeList(Arrays.asList(1,2,3))
                //.productCode(21421421l)
                //.storeTypeList(Arrays.asList(2,3))
                .build();
        req.setGroupBy("salesmanMid");
        Map<String,Long> map = service.groupByAgg(req);
        System.out.println(map);
        Assertions.assertTrue(true);
    }

    @Test
    public void TestUpdate(){
        for (int i = 0; i < 100; i++) {
            SoImeiIndex index =  SoImeiIndex.builder()
                    .id(52337945l)
                    .imei1Mask("8612****890123"+i)
                    .imei1Hash("86123456hash"+i)
                    .imei2Hash("86122143hash"+i)
                    .imei2Mask("86123****90123"+i)
                    .rrp(new BigDecimal(100).add(new BigDecimal(i)))
                    .rrpCode("100")
                    .currency("USD")
                    .salesTime(1690000000L)
                    .activationTime(new Date().getTime())
                    .activationResult(2)
                    .salesmanMid(1l+i)
                    .storeCode("1000000000000000000000000000000")
                    .createdBy(1L)
                    .retailerCode("1000000000000000000000")
                    .positionCode("1000000000000000000000000000000")
                    .productCode(8888)
                    .storeRmsCode("1000000000000000000000000000000")
                    .positionRmsCode("10000000000000000")
                    .storeGrade(1)
                    .storeType(1)
                    .storeChannelType(1)
                    .storeHasPc(1)
                    .storeHasSr(1)
                    .positionType(1)
                    .countryShortcode("CN")
                    .cityCode("1000000")
                    .createdOn(1690000000L+i)
                    .modifiedBy(1L)
                    .modifiedOn(new Date().getTime())
                    .reportingType(1)
                    .build();
           // service.update(index);
        }
        Assertions.assertTrue(true);

    }

}
