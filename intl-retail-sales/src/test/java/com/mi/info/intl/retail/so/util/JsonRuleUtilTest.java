package com.mi.info.intl.retail.so.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JsonRuleUtil 单元测试
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
class JsonRuleUtilTest {

    @Test
    void testParseImeiRule_ValidJsonAndUserTitle() {
        // 测试有效的JSON和用户职位
        String jsonStr = "{\"1001\":{\"before\":7,\"after\":3},\"1002\":{\"before\":5,\"after\":2}}";
        Long userTitle = 1001L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertEquals(7, result);
    }

    @Test
    void testParseImeiRule_ValidJsonAfterRule() {
        // 测试获取after规则
        String jsonStr = "{\"1001\":{\"before\":7,\"after\":3},\"1002\":{\"before\":5,\"after\":2}}";
        Long userTitle = 1001L;
        String ruleType = "after";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertEquals(3, result);
    }

    @Test
    void testParseImeiRule_DifferentUserTitle() {
        // 测试不同的用户职位
        String jsonStr = "{\"1001\":{\"before\":7,\"after\":3},\"1002\":{\"before\":5,\"after\":2}}";
        Long userTitle = 1002L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertEquals(5, result);
    }

    @Test
    void testParseImeiRule_UserTitleNotFound() {
        // 测试用户职位不存在
        String jsonStr = "{\"1001\":{\"before\":7,\"after\":3},\"1002\":{\"before\":5,\"after\":2}}";
        Long userTitle = 1003L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_RuleTypeNotFound() {
        // 测试规则类型不存在
        String jsonStr = "{\"1001\":{\"before\":7,\"after\":3},\"1002\":{\"before\":5,\"after\":2}}";
        Long userTitle = 1001L;
        String ruleType = "unknown";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_EmptyJson() {
        // 测试空JSON
        String jsonStr = "{}";
        Long userTitle = 1001L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_NullJson() {
        // 测试null JSON
        String jsonStr = null;
        Long userTitle = 1001L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_BlankJson() {
        // 测试空白JSON
        String jsonStr = "";
        Long userTitle = 1001L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_NullUserTitle() {
        // 测试null用户职位
        String jsonStr = "{\"1001\":{\"before\":7,\"after\":3}}";
        Long userTitle = null;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_InvalidJson() {
        // 测试无效JSON
        String jsonStr = "{invalid json}";
        Long userTitle = 1001L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_StringValue() {
        // 测试字符串类型的规则值
        String jsonStr = "{\"1001\":{\"before\":\"7\",\"after\":\"3\"}}";
        Long userTitle = 1001L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertEquals(7, result);
    }

    @Test
    void testParseImeiRule_InvalidStringValue() {
        // 测试无效的字符串数字
        String jsonStr = "{\"1001\":{\"before\":\"abc\",\"after\":\"3\"}}";
        Long userTitle = 1001L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_NestedStructure() {
        // 测试嵌套结构不是预期格式
        String jsonStr = "{\"1001\":{\"rules\":{\"before\":7,\"after\":3}}}";
        Long userTitle = 1001L;
        String ruleType = "before";
        
        Integer result = JsonRuleUtil.parseImeiRule(jsonStr, userTitle, ruleType);
        
        assertNull(result);
    }

    @Test
    void testGetImeiRuleBefore() {
        // 测试获取before规则的便捷方法
        String jsonStr = "{\"1001\":{\"before\":7,\"after\":3}}";
        Long userTitle = 1001L;
        
        Integer result = JsonRuleUtil.getImeiRuleBefore(jsonStr, userTitle);
        
        assertEquals(7, result);
    }

    @Test
    void testGetImeiRuleAfter() {
        // 测试获取after规则的便捷方法
        String jsonStr = "{\"1001\":{\"before\":7,\"after\":3}}";
        Long userTitle = 1001L;
        
        Integer result = JsonRuleUtil.getImeiRuleAfter(jsonStr, userTitle);
        
        assertEquals(3, result);
    }

    @Test
    void testGetImeiRuleBefore_NotFound() {
        // 测试获取不存在的before规则
        String jsonStr = "{\"1002\":{\"before\":7,\"after\":3}}";
        Long userTitle = 1001L;
        
        Integer result = JsonRuleUtil.getImeiRuleBefore(jsonStr, userTitle);
        
        assertNull(result);
    }

    @Test
    void testGetImeiRuleAfter_NotFound() {
        // 测试获取不存在的after规则
        String jsonStr = "{\"1002\":{\"before\":7,\"after\":3}}";
        Long userTitle = 1001L;
        
        Integer result = JsonRuleUtil.getImeiRuleAfter(jsonStr, userTitle);
        
        assertNull(result);
    }

    @Test
    void testParseImeiRule_ComplexJson() {
        // 测试复杂的JSON结构
        String jsonStr = "{" +
                "\"1001\":{\"before\":7,\"after\":3,\"extra\":\"value\"}," +
                "\"1002\":{\"before\":5,\"after\":2}," +
                "\"1003\":{\"before\":10,\"after\":1}" +
                "}";
        
        assertEquals(7, JsonRuleUtil.getImeiRuleBefore(jsonStr, 1001L));
        assertEquals(3, JsonRuleUtil.getImeiRuleAfter(jsonStr, 1001L));
        assertEquals(5, JsonRuleUtil.getImeiRuleBefore(jsonStr, 1002L));
        assertEquals(2, JsonRuleUtil.getImeiRuleAfter(jsonStr, 1002L));
        assertEquals(10, JsonRuleUtil.getImeiRuleBefore(jsonStr, 1003L));
        assertEquals(1, JsonRuleUtil.getImeiRuleAfter(jsonStr, 1003L));
    }

    @Test
    void testParseImeiRule_ZeroValues() {
        // 测试零值
        String jsonStr = "{\"1001\":{\"before\":0,\"after\":0}}";
        Long userTitle = 1001L;
        
        assertEquals(0, JsonRuleUtil.getImeiRuleBefore(jsonStr, userTitle));
        assertEquals(0, JsonRuleUtil.getImeiRuleAfter(jsonStr, userTitle));
    }

    @Test
    void testParseImeiRule_NegativeValues() {
        // 测试负值
        String jsonStr = "{\"1001\":{\"before\":-1,\"after\":-2}}";
        Long userTitle = 1001L;
        
        assertEquals(-1, JsonRuleUtil.getImeiRuleBefore(jsonStr, userTitle));
        assertEquals(-2, JsonRuleUtil.getImeiRuleAfter(jsonStr, userTitle));
    }

    @Test
    void testParseImeiRule_LargeValues() {
        // 测试大数值
        String jsonStr = "{\"1001\":{\"before\":999999,\"after\":888888}}";
        Long userTitle = 1001L;
        
        assertEquals(999999, JsonRuleUtil.getImeiRuleBefore(jsonStr, userTitle));
        assertEquals(888888, JsonRuleUtil.getImeiRuleAfter(jsonStr, userTitle));
    }
}
