package com.mi.info.intl.retail.so.app.provider.upload;

import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * IMEI导入服务测试类
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@SpringBootTest
@ActiveProfiles("test")
public class ImeiImportServiceImplTest {

    @Resource
    private ImeiImportServiceImpl imeiImportService;

    @Test
    public void testImportImeiData() {
        // 创建测试请求
        ImeiImportRequest request = new ImeiImportRequest();
        request.setImportLogId(1L);
        request.setMiId(123456L);
        request.setSourceFileUrl("http://example.com/test.xlsx");

        // 调用方法（这里只是编译测试，不会真正执行）
        // CommonApiResponse<ImeiImportResponse> response = imeiImportService.importImeiData(request);
        
        // 验证编译通过
        assert true;
    }
}
