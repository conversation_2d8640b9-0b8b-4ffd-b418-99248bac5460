package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSkuAvailable;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSkuAvailableService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSkuAvailableMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <AUTHOR>
* @description 针对表【intl_sku_available(SKU 可用性信息表)】的数据库操作Service实现
* @createDate 2025-07-24 20:05:34
*/
@Service
public class IntlSkuAvailableServiceImpl extends ServiceImpl<IntlSkuAvailableMapper, IntlSkuAvailable>
    implements IntlSkuAvailableService {

    @Resource
    private IntlSkuAvailableMapper intlSkuAvailableMapper;


    @Override
    public IPage<IntlSkuAvailable> getData(int pageNum, int pageSize) {
        Page<IntlSkuAvailable> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<IntlSkuAvailable> queryWrapper = new LambdaQueryWrapper<>();
        IPage<IntlSkuAvailable> resultPage = intlSkuAvailableMapper.selectPage(page, queryWrapper);
        return resultPage;
    }


}




