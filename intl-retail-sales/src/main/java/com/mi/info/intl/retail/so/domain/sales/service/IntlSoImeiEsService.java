package com.mi.info.intl.retail.so.domain.sales.service;

import com.alibaba.cola.dto.PageResponse;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.core.es.EsQueryConverter;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateResponse;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IntlSoImeiEsService {
    
    private static final int MAX_PAGE_COUNT = 500;
    
    @Value("${es.index.imeiIndex}")
    private String indexName;
    
    @Autowired
    private ProductApiService productApiService;
    
    @Autowired
    private ElasticsearchRestTemplate template;
    
    @Resource
    private UserApiService userApiService;
    
    @Resource
    private RmsStoreService rmsStoreService;
    
    @Resource
    private IntlRetailerApiService retailerApiService;
    
    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    
    
    public void batchInsert(List<SoImeiIndex> list) {
        template.save(list, IndexCoordinates.of(indexName));
    }
    
    public SoImeiIndex getById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("id is null");
        }
        return template.get(String.valueOf(id), SoImeiIndex.class, IndexCoordinates.of(indexName));
    }
    
    public void addNewDataToES(IntlSoImei intlSoImei) {
        if (null == intlSoImei) {
            log.error("intlSoImei is null,addNewDataToES error");
        }
        addNewDataToES(intlSoImei, null, null, null);
    }
    
    /**
     * 数据初始化
     *
     * @param intlSoImei
     * @param storeInfoDto
     * @param positionInfoDto
     * @param salesmanUserInfo
     */
    public void addNewDataToES(IntlSoImei intlSoImei, RmsStoreInfoDto storeInfoDto, RmsPositionInfoRes positionInfoDto, IntlRmsUserNewDto salesmanUserInfo) {
        if (null == intlSoImei) {
            throw new BusinessException("sync imei to es error,  intlSoImei is null");
        }
        try {
            log.info("sync imei to es start, intlSoImei.id:{}", intlSoImei.getId());
            // 基础字段
            SoImeiIndex.SoImeiIndexBuilder builder = SoImeiIndex.of(intlSoImei);
            // 填充store其他扩展信息
            SoImeiIndex.fillWithStoreInfo(builder, storeInfoDto);
            
            if (storeInfoDto != null) {
                // Retailer信息
                IntlRetailerDTO retailerDto = retailerApiService.getRetailerByRetailerId(storeInfoDto.getRetailerId());
                SoImeiIndex.fillWithRetailerInfo(builder, retailerDto);
            }
            // position
            SoImeiIndex.fillWithPositionInfo(builder, positionInfoDto);
            // 销售人员信息
            if (null != salesmanUserInfo) {
                builder.salesmanAccount(salesmanUserInfo.getRmsUserid());
                builder.salesmanJobtitle(salesmanUserInfo.getJobId());
            }
            
            ProductInfoDTO product = productApiService.queryProductById(intlSoImei.getProductCode());
            if (null != product) {
                builder.skuName(product.getSkuName())
                        .spuNameEn(product.getSpuNameEn())
                        .productLineEn(product.getProductLineEn());
            }
            template.save(builder.build(), IndexCoordinates.of(indexName));
            log.info("sync imei to es successful, intlSoImei.id:{}", intlSoImei.getId());
        } catch (Exception e) {
            log.error("sync imei to es error. id:{}", intlSoImei.getId(), e);
        }
    }
    
    
    private BoolQueryBuilder buildBaseQuery(SalesImeiReqDto reqDto) {
        // 常规字段查询
        BoolQueryBuilder queryBuilder = EsQueryConverter.convert(reqDto);
        // 增加imei模糊查询
        String imei = reqDto.getImei();
        if (StringUtils.isNotBlank(imei)) {
            BoolQueryBuilder imeiQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery("imei1Mask", imei))
                    .should(QueryBuilders.wildcardQuery("imei2Mask", imei))
                    .minimumShouldMatch(1);
            queryBuilder.must(imeiQuery);
        }
        // 增加sn模糊查询
        String sn = reqDto.getSn();
        if (StringUtils.isNotBlank(sn)) {
            BoolQueryBuilder imeiQuery = QueryBuilders.boolQuery()
                    .should(QueryBuilders.wildcardQuery("snMask", sn))
                    .minimumShouldMatch(1);
            queryBuilder.must(imeiQuery);
        }
        return queryBuilder;
    }
    
    public void delete(List<SoImeiIndex> list) {
        BulkRequest bulkRequest = new BulkRequest();
        list.forEach(id ->
                bulkRequest.add(new DeleteRequest(indexName).id(String.valueOf(id.getId())))
        );
        template.delete(bulkRequest);
        template.execute(client -> client.bulk(bulkRequest, RequestOptions.DEFAULT)
        );
    }
    
    public PageResponse<SoImeiIndex> queryByPage(SalesImeiReqDto reqDto) {
        // TODO 增加Metric、异常处理
        // ES的pageIndex是从0开始，这里要减1
        int esPageIndex = reqDto.getPageIndex() - 1;
        int pageSize = reqDto.getPageSize() > MAX_PAGE_COUNT ? MAX_PAGE_COUNT : reqDto.getPageSize();
        String orderly = StringUtils.isEmpty(reqDto.getOrderBy()) ? "id" : reqDto.getOrderBy();
        Sort sort;
        if ("ASC".equalsIgnoreCase(reqDto.getOrderDirection())) {
            sort = Sort.by(orderly).ascending();
        } else {
            sort = Sort.by(orderly).descending();
        }
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(buildBaseQuery(reqDto))
                .withPageable(PageRequest.of(esPageIndex, pageSize, sort))
                // 浅翻页
                .withSearchAfter(reqDto.getSearchAfter())
                .build();
        SearchHits<SoImeiIndex> hits = template.search(query, SoImeiIndex.class, IndexCoordinates.of(indexName));
        List<SoImeiIndex> list = hits.stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
        return PageResponse.of(list, (int) hits.getTotalHits(), pageSize, reqDto.getPageIndex());
    }
    
    public void update(IntlSoImei intlSoImei) {
        if (null == intlSoImei) {
            // TODO 增加Metric、异常处理
            throw new BusinessException("intlSoImei is null");
        }
        SoImeiIndex index = getById(intlSoImei.getId());
        if (index == null) {
            // 走新增逻辑
            addNewDataToES(intlSoImei);
            return;
        }
        // 走更新逻辑，正常情况下只会更新以下字段
        index = SoImeiIndex.of(intlSoImei).build();
        Document document = Document.create();
        document.put("activation_time", intlSoImei.getActivationTime());
        document.put("activation_result", intlSoImei.getVerificationResult());
        document.put("activation_site", intlSoImei.getActivationSite());
        document.put("si_verify_result", intlSoImei.getSiVerifyResult());
        document.put("modified_on", intlSoImei.getModifiedOn());
        document.put("modified_by", intlSoImei.getModifiedBy());
        
        UpdateQuery query = UpdateQuery.builder(String.valueOf(index.getId()))
                .withDocument(document)
                .build();
        try {
            UpdateResponse response = template.update(query, IndexCoordinates.of(indexName));
            log.info("update {}-{} response:{}", indexName, index.getId(), response);
            if (response.getResult() != UpdateResponse.Result.UPDATED) {
                throw new BusinessException("update error");
            }
            log.info("update {}-{} success", indexName, index.getId());
        } catch (Exception e) {
            log.error("update index {} error:{}", indexName, e.getMessage());
            throw new BusinessException("update error");
        }
        
    }
    
    /**
     * 分组计数
     *
     * @param reqDto
     * @return
     */
    public Map<String, Long> groupByAgg(SalesImeiReqDto reqDto) {
        String groupBy = reqDto.getGroupBy();
        if (StringUtils.isBlank(groupBy)) {
            throw new IllegalArgumentException("GroupBy field is required");
        }
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(buildBaseQuery(reqDto))
                .withAggregations(AggregationBuilders.terms("groupByAgg").field(groupBy))
                .build();
        AggregationsContainer<?> container = template.search(query, SoImeiIndex.class, IndexCoordinates.of(indexName)).getAggregations();
        if (container == null) {
            return new HashMap<>();
        }
        Aggregations aggregations = (Aggregations) container.aggregations();
        Terms terms = aggregations.get("groupByAgg");
        return terms.getBuckets().stream()
                .collect(Collectors.toMap(Terms.Bucket::getKeyAsString, Terms.Bucket::getDocCount));
    }
}
