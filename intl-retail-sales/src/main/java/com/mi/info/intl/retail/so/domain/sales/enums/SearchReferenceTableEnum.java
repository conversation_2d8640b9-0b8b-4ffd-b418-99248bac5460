package com.mi.info.intl.retail.so.domain.sales.enums;

/**
 * 搜索参考表枚举
 *
 * <AUTHOR>
 * @date 2025/8/1
 */

public enum SearchReferenceTableEnum {
    
    INTL_RMS_POSITION("intl_rms_position", "RMS阵地表"),
    INTL_RMS_STORE("intl_rms_store", "RMS门店表"),
    INTL_RMS_USER("intl_rms_user", "RMS用户表"),
    INTL_RMS_RETAILER("intl_rms_retailer", "RMS零售商表"),
    INTL_RMS_CITY("intl_rms_city", "城市表"),
    INTL_RMS_PRODUCT("intl_rms_product", "国际新产品表"),
    
    ;
    
    private final String tableName;
    
    private final String description;
    
    
    SearchReferenceTableEnum(final String tableName, final String description) {
        this.tableName = tableName;
        this.description = description;
    }
    
    public static SearchReferenceTableEnum fromTableName(String tableName) {
        for (SearchReferenceTableEnum tableEnum : SearchReferenceTableEnum.values()) {
            if (tableEnum.tableName.equals(tableName)) {
                return tableEnum;
            }
        }
        return null;
    }
    
    public String tableName() {
        return this.tableName;
    }
    
    public String description() {
        return this.description;
    }
    
}
