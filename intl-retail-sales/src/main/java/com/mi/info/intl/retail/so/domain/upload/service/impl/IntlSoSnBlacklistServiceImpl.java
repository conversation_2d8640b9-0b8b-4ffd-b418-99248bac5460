package com.mi.info.intl.retail.so.domain.upload.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.so.rule.blacklist.SoBlacklistApiService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackRequest;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist;
import com.mi.info.intl.retail.so.domain.upload.enums.SerialNumberEnum;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoSnBlacklistService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoSnBlacklistMapper;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_sn_blacklist(销量SO序列号黑名单)】的数据库操作Service实现
 * @createDate 2025-07-24 20:05:41
 */
@Slf4j
@Service
public class IntlSoSnBlacklistServiceImpl extends ServiceImpl<IntlSoSnBlacklistMapper, IntlSoSnBlacklist>
        implements IntlSoSnBlacklistService, SoBlacklistApiService {
    
    @Resource
    private UserApiService userApiService;
    
    @Override
    public IPage<SnBlackDTO> pageList(SnBlackRequest request) {
        // 创建分页对象
        Page<IntlSoSnBlacklist> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        Page<SnBlackDTO> pageDTO = new Page<>(request.getPageNum(), request.getPageSize());
        
        // 构建查询条件
        Page<IntlSoSnBlacklist> blacklistPage = this.page(page, Wrappers.<IntlSoSnBlacklist>lambdaQuery()
                .eq(StringUtils.isNotEmpty(request.getId()), IntlSoSnBlacklist::getId, request.getId())
                .in(CollUtil.isNotEmpty(request.getCountryCodeList()), IntlSoSnBlacklist::getCountryCode, request.getCountryCodeList())
                .in(CollUtil.isNotEmpty(request.getTypeCodeList()), IntlSoSnBlacklist::getType, request.getTypeCodeList())
                .like(StringUtils.isNotEmpty(request.getSnImei()), IntlSoSnBlacklist::getSnImei,
                        // 先判断非空，再转大写；否则传 null（不影响，因为条件已不成立）
                        StringUtils.isNotEmpty(request.getSnImei()) ? request.getSnImei().toUpperCase() : null)
                .in(CollUtil.isNotEmpty(request.getStatusList()), IntlSoSnBlacklist::getStatus, request.getStatusList())
                .like(null != request.getCreatedOn(), IntlSoSnBlacklist::getCreatedOn, request.getCreatedOn())
                .like(StringUtils.isNotEmpty(request.getCreatedBy()), IntlSoSnBlacklist::getCreatedBy, request.getCreatedBy())
                .eq(IntlSoSnBlacklist::getStatus, Boolean.FALSE)
                .orderByDesc(IntlSoSnBlacklist::getId));
        
        // 如果查询结果为空，直接返回空的分页对象
        if (CollUtil.isEmpty(blacklistPage.getRecords())) {
            return pageDTO;
        }
        
        // 获取查询结果列表
        List<IntlSoSnBlacklist> blacklistList = blacklistPage.getRecords();
        
        // 合并创建人和修改人的ID集合
        Set<Long> userIdSet = new HashSet<>();
        userIdSet.addAll(blacklistList.stream()
                .map(IntlSoSnBlacklist::getCreatedBy)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet()));
        userIdSet.addAll(blacklistList.stream()
                .map(IntlSoSnBlacklist::getModifiedBy)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet()));
        
        // 创建人信息
        Map<Long, IntlRmsUserNewDto> rmsUserListMap = getRmsUserListByMiIds(new ArrayList<>(userIdSet));
        
        // 这里可以根据需要进行数据转换或处理
        // 例如：数据脱敏、状态转换等
        List<SnBlackDTO> processedList = blacklistList.stream()
                .map(item -> {
                    SnBlackDTO itemDTO = ComponentLocator.getConverter().convert(item, SnBlackDTO.class);
                    // 在这里添加数据处理逻辑
                    SerialNumberEnum serialNumber = SerialNumberEnum.getByCode(item.getType());
                    itemDTO.setType(null == serialNumber ? StringUtils.EMPTY : serialNumber.getName());
                    IntlRmsUserNewDto intlRmsUserNewDto = rmsUserListMap.get(item.getCreatedBy());
                    itemDTO.setCreatedByName(null == intlRmsUserNewDto ? StringUtils.EMPTY : intlRmsUserNewDto.getEnglishName());
                    IntlRmsUserNewDto updateRmsUserNewDto = rmsUserListMap.get(item.getModifiedBy());
                    itemDTO.setModifiedByName(null == updateRmsUserNewDto ? StringUtils.EMPTY : updateRmsUserNewDto.getEnglishName());
                    itemDTO.setCreatedOn(IntlTimeUtil.parseTimestampToAreaTime(request.getLocale(), item.getCreatedOn()));
                    itemDTO.setModifiedOn(IntlTimeUtil.parseTimestampToAreaTime(request.getLocale(), item.getModifiedOn()));
                    return itemDTO;
                }).collect(Collectors.toList());
        
        // 设置处理后的记录
        // 创建返回的分页对象
        pageDTO.setPages(blacklistPage.getPages());
        pageDTO.setTotal(blacklistPage.getTotal());
        pageDTO.setCurrent(blacklistPage.getCurrent());
        pageDTO.setSize(blacklistPage.getSize());
        pageDTO.setRecords(processedList);
        
        return pageDTO;
    }
    
    /**
     * 同步SO序列号黑名单
     *
     * @param content
     */
    @Override
    public void syncSoSnBlacklist(Object content) {
        IntlSoSnBlacklist intlSoSnBlacklist =
                JsonUtil.json2bean(JsonUtil.bean2json(content), IntlSoSnBlacklist.class);
        if (intlSoSnBlacklist != null && intlSoSnBlacklist.getId() != null) {
            LambdaQueryWrapper<IntlSoSnBlacklist> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlSoSnBlacklist::getId, intlSoSnBlacklist.getId());
            IntlSoSnBlacklist existRecord = this.baseMapper.selectOne(lambdaQuery);
            if (existRecord != null) {
                intlSoSnBlacklist.setModifiedOn(System.currentTimeMillis());
                this.baseMapper.updateById(intlSoSnBlacklist);
                log.info("addSoSnBlacklist_update:{}", intlSoSnBlacklist);
            } else {
                intlSoSnBlacklist.setCreatedOn(System.currentTimeMillis());
                intlSoSnBlacklist.setModifiedOn(System.currentTimeMillis());
                this.baseMapper.insert(intlSoSnBlacklist);
                log.info("addSoSnBlacklist_insert:{}", intlSoSnBlacklist);
            }
        }
    }
    
    private Map<Long, IntlRmsUserNewDto> getRmsUserListByMiIds(List<Long> miIds) {
        if (CollectionUtils.isEmpty(miIds)) {
            return new HashMap<>();
        }
        Optional<List<IntlRmsUserNewDto>> rmsUserOpt = userApiService.getUserListByMiIds(miIds);
        if (rmsUserOpt.isPresent()) {
            List<IntlRmsUserNewDto> intlRmsUserNewDtoList = rmsUserOpt.get();
            if (CollectionUtils.isNotEmpty(intlRmsUserNewDtoList)) {
                return intlRmsUserNewDtoList.stream().collect(Collectors.toMap(IntlRmsUserNewDto::getMiId, Function.identity()));
            }
        }
        return new HashMap<>();
    }
}




