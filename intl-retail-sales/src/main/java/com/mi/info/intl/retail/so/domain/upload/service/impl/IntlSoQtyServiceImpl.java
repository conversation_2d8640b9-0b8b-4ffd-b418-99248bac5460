package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.domain.datasync.entity.IntlDatasyncLog;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataAbnormalEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoQtyMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoUserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_qty(销量qty)】的数据库操作Service实现
 * @createDate 2025-07-24 19:21:34
 */
@Slf4j
@Service
public class IntlSoQtyServiceImpl extends ServiceImpl<IntlSoQtyMapper, IntlSoQty>
        implements IntlSoQtyService {

    @Resource
    private UserApiService userApiService;
    @Resource
    private IntlDatasyncLogMapper intlDatasyncLogMapper;
    @Resource
    private IntlSoOrgInfoMapper intlSoOrgInfoMapper;
    @Resource
    private IntlSoUserInfoMapper intlSoUserInfoMapper;
    @Resource
    private RmsStoreService rmsStoreService;
    @Resource
    private IntlRetailerApiService retailerApiService;
    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public IntlSoQty getIntlSoQtyByRmsId(String rmsId) {
        return this.lambdaQuery().eq(IntlSoQty::getRmsId, rmsId)
                .select(IntlSoQty::getId).one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doQtySave(RmsSyncQtyData data) {
        log.info("------------doQtySave------------,rmsId:{}", data.getRmsId());
        String rmsId = data.getRmsId();
        IntlSoQty intlSoQtyByRmsId = getIntlSoQtyByRmsId(rmsId);
        if (intlSoQtyByRmsId != null) {
            log.info("doQtySave qty 已存在, rmsId: {}, timestamp: {}", rmsId, System.currentTimeMillis());
            return;
        }

        IntlSoQty intlSoQty = new IntlSoQty();
        String createdbyMiidStr = data.getCreatedbyMiid();
        String salesManMiidStr = data.getSalesmanMiid();
        Long createdByMiId = parseLong(createdbyMiidStr);
        Long salesmanMiId = parseLong(salesManMiidStr);
        CompletableFuture<Optional<List<IntlRmsUserNewDto>>> getUserFuture =
                CompletableFuture.supplyAsync(
                        () -> userApiService.getUserListByMiIds(Arrays.asList(createdByMiId, salesmanMiId)));

        // 查询门店信息
        String storeCodeRMS = data.getStoreCodeRMS();
        String positionCodeRMS = data.getPositionCodeRMS();
        CompletableFuture<Optional<RmsStoreInfoDto>> storeInfoFuture =
                CompletableFuture.supplyAsync(() -> rmsStoreService.getStoreInfoByStoreCode(storeCodeRMS),
                        threadPoolTaskExecutor.getThreadPoolExecutor());
        CompletableFuture<Optional<RmsPositionInfoRes>> positionInfoFuture =
                CompletableFuture.supplyAsync(() -> rmsStoreService.getPositionIfoByPositionCode(positionCodeRMS),
                        threadPoolTaskExecutor.getThreadPoolExecutor());

        // 查询零售商信息
        String retailerCode = data.getRetailerCode();
        CompletableFuture<Optional<IntlRetailerDTO>> retailerFuture = CompletableFuture.supplyAsync(
                () -> retailerApiService.getRetailerByRetailerCode(
                        new IntlPositionDTO().setRetailerCode(retailerCode)),
                threadPoolTaskExecutor.getThreadPoolExecutor());

        // 等待所有异步任务完成
        CompletableFuture.allOf(getUserFuture, storeInfoFuture, positionInfoFuture, retailerFuture).join();
        ComponentLocator.getConverter().convert(data, intlSoQty);
        List<String> abnormalList = new ArrayList<>();

        // 处理用户信息
        IntlSoUserInfo intlSoUserInfo = handleUserInfo(getUserFuture.join(), createdByMiId, salesmanMiId, abnormalList);
        intlSoUserInfoMapper.insert(intlSoUserInfo);

        // 处理门店信息
        IntlSoOrgInfo intlSoOrgInfo =
                handleOrgInfo(storeInfoFuture.join(), positionInfoFuture.join(), retailerFuture.join(), data,
                        abnormalList);
        intlSoOrgInfoMapper.insert(intlSoOrgInfo);
        intlSoQty.setStoreRmsCode(data.getStoreRmsCode());
        intlSoQty.setPositionRmsCode(data.getPositionRmsCode());
        intlSoQty.setRetailerCode(data.getRetailerCode());
        //主数据
        intlSoQty.setUserInfoId(intlSoUserInfo.getId());
        intlSoQty.setOrgInfoId(intlSoOrgInfo.getId());
        intlSoQty.setSalesmanMid(salesmanMiId);
        intlSoQty.setCreatedby(createdByMiId);
        intlSoQty.setRrpCode(data.getRrpRMSCode());
        intlSoQty.setDataFrom(DataFromEnum.RMS.getCode());
        baseMapper.insert(intlSoQty);

        // 记录同步日志表
        recordSyncLog(data, DataSyncOperateTypeTypeEnum.CREATE, intlSoQty.getId(), abnormalList);
    }

    @Override
    public IntlSoQty getById(Long id) {
        return this.lambdaQuery().eq(IntlSoQty::getId, id)
                .select(IntlSoQty::getId).one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doQtyUpdate(RmsSyncQtyData data) {
        log.info("------------doQtyUpdate------------,rmsId:{}", data.getRmsId());
        String rmsId = data.getRmsId();
        IntlSoQty intlSoQty = getIntlSoQtyByRmsId(data.getRmsId());
        if (intlSoQty == null) {
            log.info("doQtyUpdate qty 不存在, rmsId: {}, timestamp: {}", rmsId, System.currentTimeMillis());
            return;
        }

        LambdaUpdateWrapper<IntlSoQty> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(IntlSoQty::getStatus, data.getStatus())
                .set(IntlSoQty::getModifiedon, data.getModifiedon())
                .set(IntlSoQty::getModifiedby, data.getModifiedby())
                .eq(IntlSoQty::getRmsId, data.getRmsId());
        baseMapper.update(updateWrapper);

        // 记录同步日志表
        recordSyncLog(data, DataSyncOperateTypeTypeEnum.UPDATE, intlSoQty.getId(), null);
    }

    private Long parseLong(String str) {
        if (StringUtils.isEmpty(str)) {
            return 0L;
        }
        try {
            return Long.valueOf(str);
        } catch (NumberFormatException e) {
            log.warn("Invalid number format: {}", str, e);
            return 0L;
        }
    }

    private void recordSyncLog(RmsSyncQtyData data, DataSyncOperateTypeTypeEnum operateType, Long intlSoQtyId,
                               List<String> abnormalList) {
        IntlDatasyncLog intlDatasyncLog = new IntlDatasyncLog()
                .setType(DataSyncDataTypeEnum.QTY.getCode())
                .setRmsId(data.getRmsId())
                .setRetailId(intlSoQtyId)
                .setMessage(JSON.toJSONString(data))
                .setOperateType(operateType.getCode());

        if (CollectionUtils.isNotEmpty(abnormalList)) {
            intlDatasyncLog.setIsDataAbnormal(DataAbnormalEnum.DATA_ABNORMAL.getCode());
            intlDatasyncLog.setAbnormalMessage(String.join(";", abnormalList));
        }
        intlDatasyncLogMapper.insert(intlDatasyncLog);
    }

    private IntlSoUserInfo handleUserInfo(Optional<List<IntlRmsUserNewDto>> userListOptional, Long createdByMiId,
                                          Long salesmanMiId, List<String> abnormalList) {
        IntlSoUserInfo intlSoUserInfo = new IntlSoUserInfo();
        intlSoUserInfo.setCreatedByMid(createdByMiId);
        intlSoUserInfo.setSalesmanMid(salesmanMiId);
        if (!userListOptional.isPresent()) {
            abnormalList.add("未查询到上报人员用户信息，上报人员米id：" + createdByMiId);
            abnormalList.add("未查询到销售人员用户信息，销售人员米id：" + salesmanMiId);
            return intlSoUserInfo;
        }
        List<IntlRmsUserNewDto> intlRmsUserNewDtos = userListOptional.get();
        Map<Long, IntlRmsUserNewDto> userGroupByMid =
                intlRmsUserNewDtos.stream().collect(Collectors.toMap(IntlRmsUserNewDto::getMiId, e -> e));
        if (userGroupByMid.containsKey(createdByMiId)) {
            IntlRmsUserNewDto createUserUserInfo = userGroupByMid.get(createdByMiId);
            intlSoUserInfo.setCreatedByRmsAccount(createUserUserInfo.getRmsUserid());
            intlSoUserInfo.setCreatedByJobTitle(createUserUserInfo.getJobId());
        } else {
            abnormalList.add("未查询到上报人员用户信息，上报人员米id：" + createdByMiId);
        }
        if (userGroupByMid.containsKey(salesmanMiId)) {
            IntlRmsUserNewDto salesyUserInfo = userGroupByMid.get(salesmanMiId);
            intlSoUserInfo.setSalesmanRmsAccount(salesyUserInfo.getRmsUserid());
            intlSoUserInfo.setSalesmanJobTitle(salesyUserInfo.getJobId());
        } else {
            abnormalList.add("未查询到销售人员用户信息，销售人员米id：" + salesmanMiId);
        }
        return intlSoUserInfo;
    }

    private IntlSoOrgInfo handleOrgInfo(Optional<RmsStoreInfoDto> storeInfoOpt,
                                        Optional<RmsPositionInfoRes> positionInfoOpt,
                                        Optional<IntlRetailerDTO> retailerOpt, RmsSyncQtyData data,
                                        List<String> abnormalList) {
        IntlSoOrgInfo intlSoOrgInfo = new IntlSoOrgInfo();
        intlSoOrgInfo.setStoreCode(data.getStoreCodeNew());
        intlSoOrgInfo.setStoreRmsCode(data.getStoreCodeRMS());
        intlSoOrgInfo.setPositionRmsCode(data.getPositionCodeRMS());
        intlSoOrgInfo.setPositionCode(data.getPositionCodeNew());
        intlSoOrgInfo.setRetailerCode(data.getRetailerCode());

        if (storeInfoOpt.isPresent()) {
            RmsStoreInfoDto rmsStoreInfoDto = storeInfoOpt.get();
            intlSoOrgInfo.setStoreType(rmsStoreInfoDto.getType());
            intlSoOrgInfo.setStoreGrade(rmsStoreInfoDto.getGrade());
            intlSoOrgInfo.setStoreHasPC(rmsStoreInfoDto.getHasPc());
            intlSoOrgInfo.setStoreHasSR(rmsStoreInfoDto.getHasSR());
            intlSoOrgInfo.setStoreChannelType(rmsStoreInfoDto.getChannelType());
            intlSoOrgInfo.setCountryCode(rmsStoreInfoDto.getCountryShortcode());
            intlSoOrgInfo.setStoreId(rmsStoreInfoDto.getId());
        } else {
            abnormalList.add("未查询到门店信息，storeCodeRms：" + data.getStoreCodeRMS());
        }

        if (positionInfoOpt.isPresent()) {
            RmsPositionInfoRes rmsPositionInfoDto = positionInfoOpt.get();
            intlSoOrgInfo.setPositionType(rmsPositionInfoDto.getType());
            intlSoOrgInfo.setPositionId(rmsPositionInfoDto.getId());
        } else {
            abnormalList.add("未查询到阵地信息，positionCodeRms：" + data.getPositionCodeRMS());
        }

        if (retailerOpt.isPresent()) {
            IntlRetailerDTO intlRetailerDTO = retailerOpt.get();
            intlSoOrgInfo.setRetailerId(intlRetailerDTO.getId());
        } else {
            abnormalList.add("未查询到零售商信息，retailerCode：" + data.getRetailerCode());
        }

        return intlSoOrgInfo;
    }

}
