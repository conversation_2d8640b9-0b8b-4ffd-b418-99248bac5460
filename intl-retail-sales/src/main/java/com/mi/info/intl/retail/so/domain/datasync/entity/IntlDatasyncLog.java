package com.mi.info.intl.retail.so.domain.datasync.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 数据同步日志
 *
 * @TableName intl_datasync_log
 */
@Accessors(chain = true)
@TableName(value = "intl_datasync_log")
@Data
public class IntlDatasyncLog {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 零售商ID
     */
    @TableField(value = "retail_id")
    private Long retailId;

    /**
     * RMS Id
     */
    @TableField(value = "rms_id")
    private String rmsId;
    /**
     * 类型（0：SO;1 Qty;2 SNblacklis）
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 同步类型 1新增 2修改
     */
    @TableField(value = "operate_type")
    private Integer operateType;
    /**
     * 消息
     */
    @TableField(value = "message")
    private String message;

    /**
     * 数据是否异常
     */
    @TableField(value = "is_data_abnormal")
    private Integer isDataAbnormal;

    /**
     * 异常信息
     */
    @TableField(value = "abnormal_message")
    private String abnormalMessage;

    /**
     * 同步时间
     */
    @TableField(value = "createdon")
    private LocalDateTime createdOn;

}