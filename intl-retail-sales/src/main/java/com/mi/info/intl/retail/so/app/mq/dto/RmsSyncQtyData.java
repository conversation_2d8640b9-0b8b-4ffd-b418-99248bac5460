package com.mi.info.intl.retail.so.app.mq.dto;

import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RmsSyncQtyData extends IntlSoQty {

    private static final long serialVersionUID = 7319261307145420913L;
    private String retailid;

    /**
     * RMS的rrp编码
     */
    private String rrpRMSCode;

    /**
     * RMS的门店编码
     */
    private String storeCodeRMS;

    /**
     * RMS的位置编码
     */
    private String positionCodeRMS;

    /**
     * 门店编码
     */
    private String storeCodeNew;

    /**
     * 位置编码
     */
    private String positionCodeNew;

    /**
     * 创建人米id
     */
    private String createdbyMiid;

    /**
     * 销售人米id
     */
    private String salesmanMiid;

}
