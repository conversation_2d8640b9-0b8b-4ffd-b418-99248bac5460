package com.mi.info.intl.retail.so.domain.sales.service;

import com.mi.info.intl.retail.api.file.FileUploadApiService;
import com.mi.info.intl.retail.api.file.dto.IntlFileUploadDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceRespDto;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.sales.enums.SearchReferenceEnum;
import com.mi.info.intl.retail.so.domain.sales.enums.SearchReferenceTableEnum;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import com.xiaomi.cnzone.storems.common.exception.ErrorCodeEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述：Sales Common 领域服务
 *
 * <AUTHOR>
 * @date 2025/8/5
 */
@Slf4j
@Service
public class IntlSoCommonDomainService {
    
    @Resource
    private FileUploadApiService fileUploadApiService;
    
    public List<SearchReferenceRespDto> getSearchReferencesData(String table, String keyWord, String parentKey) {
        SearchReferenceTableEnum tableEnum = SearchReferenceTableEnum.fromTableName(table);
        if (null == tableEnum) {
            log.error("[method]getSearchReferencesData:tableEnum is null......");
            throw new BusinessException(ErrorCodeEnums.BUS_EXCEPTION.getErrorCode(), "tableEnum is null");
        }
        return SearchReferenceEnum.valueOf(tableEnum.name()).getSearchReferencesData(keyWord, parentKey);
    }
    
    public List<String> getPicturesByRelatedIdAndModuleName(Long relatedId, String moduleName) {
        CommonApiResponse<List<IntlFileUploadDto>> fileUploadDtoResp = fileUploadApiService.getFileUploadListByRelatedIdAndModuleName(relatedId, moduleName);
        if (null == fileUploadDtoResp || 0 != fileUploadDtoResp.getCode()) {
            return new ArrayList<>();
        }
        List<IntlFileUploadDto> fileUploadDtoList = fileUploadDtoResp.getData();
        if (CollectionUtils.isEmpty(fileUploadDtoList)) {
            return new ArrayList<>();
        }
        List<String> pictureUrlList = new ArrayList<>();
        for (IntlFileUploadDto fileUploadDto : fileUploadDtoList) {
            String fdsUrl = fileUploadDto.getFdsUrl();
            if (StringUtils.isNotEmpty(fdsUrl)) {
                pictureUrlList.add(fdsUrl);
            }
        }
        return pictureUrlList;
    }
    
}
