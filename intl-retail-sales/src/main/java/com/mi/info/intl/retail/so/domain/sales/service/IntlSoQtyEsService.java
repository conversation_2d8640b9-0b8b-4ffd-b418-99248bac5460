package com.mi.info.intl.retail.so.domain.sales.service;

import com.alibaba.cola.dto.PageResponse;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.core.es.EsQueryConverter;
import com.mi.info.intl.retail.intlretail.infra.mq.RocketMQProducer;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
import com.mi.info.intl.retail.so.app.mq.dto.SyncSoToEsData;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.so.domain.sales.model.SoQtyIndex;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateResponse;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IntlSoQtyEsService {

    private static final int MAX_PAGE_COUNT = 500;

    @Value("${es.index.qtyIndex}")
    private String indexName;

    @Autowired
    private ElasticsearchRestTemplate template;
    @Autowired
    private ProductApiService productApiService;
    @Resource
    private RmsStoreService rmsStoreService;
    @Resource
    private IntlRetailerApiService retailerApiService;
    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private UserApiService userApiService;


    public void batchInsert(List<SoQtyIndex> soQtyIndexes) {
        template.save(soQtyIndexes, IndexCoordinates.of(indexName));
    }

    /**
     * 同步数据到ES,支持新增和更新
     *
     * @param intlSoQty
     */
    public void syncDataToEs(IntlSoQty intlSoQty) {
        if (null == intlSoQty || intlSoQty.getId() == null) {
            throw new IllegalArgumentException("soQty is null");
        }
        log.info("sync qty to es start, intlSoQty.id:{}", intlSoQty.getId());
        //查询用户信息
        CompletableFuture<Optional<IntlRmsUserNewDto>> getUserFuture = CompletableFuture.supplyAsync(
                () -> userApiService.getUserByMiId(intlSoQty.getSalesmanMid()));
        CompletableFuture<Optional<RmsStoreInfoDto>> storeInfoFuture =
                CompletableFuture.supplyAsync(() -> rmsStoreService.getStoreInfoByStoreCode(intlSoQty.getStoreRmsCode()),
                        threadPoolTaskExecutor.getThreadPoolExecutor());
        CompletableFuture<Optional<RmsPositionInfoRes>> positionInfoFuture =
                CompletableFuture.supplyAsync(() -> rmsStoreService.getPositionIfoByPositionCode(intlSoQty.getPositionRmsCode()),
                        threadPoolTaskExecutor.getThreadPoolExecutor());
        // 等待所有异步任务完成
        CompletableFuture.allOf(getUserFuture, storeInfoFuture, positionInfoFuture).join();
        Optional<IntlRmsUserNewDto> userOptionl = getUserFuture.join();
        Optional<RmsStoreInfoDto> storeOptionl = storeInfoFuture.join();
        Optional<RmsPositionInfoRes> positionOptionl = positionInfoFuture.join();
        IntlRmsUserNewDto salesmanDto = null;
        if (userOptionl.isPresent()) {
            salesmanDto = userOptionl.get();
        }
        RmsStoreInfoDto storeInfoDto = null;
        if (storeOptionl.isPresent()) {
            storeInfoDto = storeOptionl.get();
        }
        IntlRetailerDTO retailerDto = null;
        if (null != storeInfoDto) {
            // Retailer信息
            retailerDto = retailerApiService.getRetailerByRetailerId(storeInfoDto.getRetailerId());
        }
        RmsPositionInfoRes positionInfoDto = null;
        if (positionOptionl.isPresent()) {
            positionInfoDto = positionOptionl.get();
        }

        SoQtyIndex exists = template.get(String.valueOf(intlSoQty.getId()), SoQtyIndex.class, IndexCoordinates.of(indexName));
        if (null == exists) {
            //不存在就走一次新增流程
            this.addNewDataToEs(intlSoQty, storeInfoDto, positionInfoDto, retailerDto, salesmanDto);
        } else {
            this.updateDataToEs(intlSoQty, storeInfoDto, positionInfoDto, retailerDto);
        }

    }

    private void addNewDataToEs(IntlSoQty intlSoQty, RmsStoreInfoDto storeInfoDto, RmsPositionInfoRes positionInfoDto, IntlRetailerDTO retailerDto,
                                IntlRmsUserNewDto intlSoUserInfo) {
        if (null == intlSoQty || intlSoQty.getId() == null) {
            throw new BusinessException("sync qty to es error,  intlSoQty is null");
        }
        try {
            log.info("sync qty to es start, intlSoQty.id:{}", intlSoQty.getId());
            //基础字段
            SoQtyIndex.SoQtyIndexBuilder builder = SoQtyIndex.of(intlSoQty);
            // 填充store其他扩展信息
            SoQtyIndex.fillWithStoreInfo(builder, storeInfoDto);
            // position
            SoQtyIndex.fillWithPositionInfo(builder, positionInfoDto);
            // retailer
            SoQtyIndex.fillWithRetailerInfo(builder, retailerDto);
            //Product字段
            SoQtyIndex.fillWithProductInfo(builder, productApiService.queryProductById(intlSoQty.getProductCode()));
            // 销售人员信息
            if (null != intlSoUserInfo) {
                builder.salesmanAccount(intlSoUserInfo.getRmsUserid());
                builder.salesmanJobtitle(intlSoUserInfo.getJobId());
            }
            template.save(builder.build(), IndexCoordinates.of(indexName));
            log.info("sync qty to es successful, intlSoQty.id:{}", intlSoQty.getId());
        } catch (Exception e) {
            log.error("sync qty to es  error. id:{}", intlSoQty.getId(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    private void updateDataToEs(IntlSoQty intlSoQty, RmsStoreInfoDto storeInfoDto, RmsPositionInfoRes positionInfoDto, IntlRetailerDTO retailerDto) {
        if (null == intlSoQty || null == intlSoQty.getId()) {
            throw new BusinessException("update qty to es error,  intlSoQty is null");
        }
        try {
            log.info("update qty to es start, intlSoQty.id:{}", intlSoQty.getId());
            //TODO 更新更多字段
            Document document = Document.create();
            document.put("status", intlSoQty.getStatus());
            document.put("modified_on", intlSoQty.getModifiedon());
            document.put("modified_by", intlSoQty.getModifiedby());

            if (null != storeInfoDto) {
                document.put("store_code", storeInfoDto.getCode());
                document.put("store_name", storeInfoDto.getName());
            }
            if (null != positionInfoDto) {
                document.put("position_code", positionInfoDto.getCode());
                document.put("position_name", positionInfoDto.getName());
            }
            if (null != retailerDto) {
                document.put("retailer_code", retailerDto.getRetailerCode());
                document.put("retailer_name", retailerDto.getRetailerName());
            }
            UpdateQuery query = UpdateQuery.builder(String.valueOf(intlSoQty.getId()))
                    .withDocument(document)
                    .build();
            UpdateResponse response = template.update(query, IndexCoordinates.of(indexName));
            log.info("update {}-{} response:{}", indexName, intlSoQty.getId(), response);
            if (response.getResult() != UpdateResponse.Result.UPDATED) {
                throw new BusinessException("update error");
            }
            log.info("update {}-{} successful", indexName, intlSoQty.getId());
        } catch (Exception e) {
            log.error("sync qty to es  error. id:{}", intlSoQty.getId(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    public PageResponse<SoQtyIndex> search(SalesQtyReqDto reqDto) {
        // TODO 增加Metric、异常处理
        int esPageIndex = reqDto.getPageIndex() - 1;
        int pageSize = reqDto.getPageSize() > MAX_PAGE_COUNT ? MAX_PAGE_COUNT : reqDto.getPageSize();
        String orderly = StringUtils.isEmpty(reqDto.getOrderBy()) ? "id" : reqDto.getOrderBy();
        Sort sort;
        if ("ASC".equalsIgnoreCase(reqDto.getOrderDirection())) {
            sort = Sort.by(orderly).ascending();
        } else {
            sort = Sort.by(orderly).descending();
        }
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(EsQueryConverter.convert(reqDto))
                .withPageable(PageRequest.of(esPageIndex, pageSize, sort))
                // 浅翻页
                .withSearchAfter(reqDto.getSearchAfter())
                .build();
        SearchHits<SoQtyIndex> hits = template.search(query, SoQtyIndex.class, IndexCoordinates.of(indexName));
        List<SoQtyIndex> list = hits.stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
        return PageResponse.of(list, (int) hits.getTotalHits(), pageSize, reqDto.getPageIndex());
    }

    /**
     * 分组查询
     *
     * @param reqDto
     * @return
     */
    public Map<String, Long> groupByAgg(SalesQtyReqDto reqDto) {
        // 分组字段
        String groupBy = reqDto.getGroupBy();
        if (StringUtils.isBlank(groupBy)) {
            throw new IllegalArgumentException("GroupBy field is required");
        }

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(EsQueryConverter.convert(reqDto))
                .withAggregations(AggregationBuilders.terms("groupByAgg").field(groupBy).size(reqDto.getPageSize()))
                .build();
        AggregationsContainer<?> aggregationsContainer = template.search(query, SoQtyIndex.class, IndexCoordinates.of(indexName)).getAggregations();
        if (aggregationsContainer == null) {
            return new HashMap<>();
        }
        Aggregations aggregations = (Aggregations) aggregationsContainer.aggregations();
        Terms terms = aggregations.get("groupByAgg");
        return terms.getBuckets().stream()
                .collect(Collectors.toMap(Terms.Bucket::getKeyAsString, Terms.Bucket::getDocCount));
    }

}