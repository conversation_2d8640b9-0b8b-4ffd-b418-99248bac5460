package com.mi.info.intl.retail.so.infra.database.mapper.upload;

import com.mi.info.intl.retail.so.domain.upload.entity.IntlSkuAvailable;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【intl_sku_available(SKU 可用性信息表)】的数据库操作Mapper
* @createDate 2025-07-24 20:05:34
* @Entity com.mi.info.intl.retail.so.infra.entity.IntlSkuAvailable
*/
public interface IntlSkuAvailableMapper extends BaseMapper<IntlSkuAvailable> {

}




