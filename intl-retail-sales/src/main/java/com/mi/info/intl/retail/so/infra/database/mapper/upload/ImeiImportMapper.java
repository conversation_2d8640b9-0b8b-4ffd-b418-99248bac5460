package com.mi.info.intl.retail.so.infra.database.mapper.upload;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.so.app.provider.upload.dto.StoreUserRelationDto;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IMEI导入相关数据库操作Mapper
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Mapper
public interface ImeiImportMapper extends BaseMapper<IntlImportLog> {

    /**
     * 根据Store Code查询门店信息
     *
     * @param storeCode 门店编码
     * @return 门店信息
     */
    StoreInfoDto getStoreByCode(@Param("storeCode") String storeCode);

    /**
     * 查询用户门店关系
     *
     * @param storeCode 门店编码
     * @param miId 用户miId
     * @return 用户门店关系信息
     */
    StoreUserRelationDto getUserStoreRelation(@Param("storeCode") String storeCode, @Param("miId") Long miId);

    /**
     * 查询门店下所有阵地
     *
     * @param storeCode 门店编码
     * @return 阵地列表
     */
    List<PositionInfoDto> getPositionsByStoreCode(@Param("storeCode") String storeCode);

    /**
     * 门店信息DTO
     */
    class StoreInfoDto {
        private String storeId;
        private String code;
        private String crssCode;
        private String name;
        private String countryShortcode;
        private Long createdOn;
        private Integer stateCode;

        // getters and setters
        public String getStoreId() { return storeId; }
        public void setStoreId(String storeId) { this.storeId = storeId; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getCrssCode() { return crssCode; }
        public void setCrssCode(String crssCode) { this.crssCode = crssCode; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getCountryShortcode() { return countryShortcode; }
        public void setCountryShortcode(String countryShortcode) { this.countryShortcode = countryShortcode; }
        public Long getCreatedOn() { return createdOn; }
        public void setCreatedOn(Long createdOn) { this.createdOn = createdOn; }
        public Integer getStateCode() { return stateCode; }
        public void setStateCode(Integer stateCode) { this.stateCode = stateCode; }
    }

    /**
     * 阵地信息DTO
     */
    class PositionInfoDto {
        private String positionId;
        private String code;
        private String name;
        private String typeName;
        private Long createdOn;
        private Integer stateCode;

        // getters and setters
        public String getPositionId() { return positionId; }
        public void setPositionId(String positionId) { this.positionId = positionId; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getTypeName() { return typeName; }
        public void setTypeName(String typeName) { this.typeName = typeName; }
        public Long getCreatedOn() { return createdOn; }
        public void setCreatedOn(Long createdOn) { this.createdOn = createdOn; }
        public Integer getStateCode() { return stateCode; }
        public void setStateCode(Integer stateCode) { this.stateCode = stateCode; }
    }
}
