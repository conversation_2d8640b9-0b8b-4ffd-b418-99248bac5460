package com.mi.info.intl.retail.so.domain.sales.converter;

import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.converter.AbstractConvert;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesImeiRespDto;
import com.mi.info.intl.retail.so.domain.sales.enums.SalesDictEnum;
import com.mi.info.intl.retail.so.domain.sales.handler.SoSalesHandler;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 功能描述：Sales Imei转换类
 *
 * <AUTHOR>
 * @date 2025/8/1
 */
@Component
public class SalesImeiConverter extends AbstractConvert<SoImeiIndex, SalesImeiRespDto> {
    
    @Resource
    private SoSalesHandler soSalesHandler;
    
    @Override
    public List<SalesImeiRespDto> toTarget(List<SoImeiIndex> soImeiIndexList) {
        if (null == soImeiIndexList) {
            return new ArrayList<>();
        }
        List<SalesImeiRespDto> salesImeiRespDtoList = new ArrayList<>(soImeiIndexList.size());
        List<Long> miIds = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (SoImeiIndex soImeiIndex : soImeiIndexList) {
            salesImeiRespDtoList.add(super.toTarget(soImeiIndex));
            Long salesmanMid = soImeiIndex.getSalesmanMid();
            Long createOn = soImeiIndex.getCreatedOn();
            if (!miIds.contains(salesmanMid)) {
                miIds.add(salesmanMid);
            }
            if (!miIds.contains(createOn)) {
                miIds.add(createOn);
            }
            ids.add(soImeiIndex.getId());
        }
        
        Map<String, List<LabelValueDTO>> salesDictMap = soSalesHandler.getLabelValueList4Sales();
        Map<Long, IntlRmsUserNewDto> rmsUserMap = soSalesHandler.getRmsUserListByMiIds(miIds);
        Map<Long, IntlSoImei> soImeiMap = soSalesHandler.getIntlSoImeiListByIds(ids);
        Map<String, String> jobTitleMap = salesDictMap.get(SalesDictEnum.JOB_TITLE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> storeGradeMap = salesDictMap.get(SalesDictEnum.STORE_GRADE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> storeTypeMap = salesDictMap.get(SalesDictEnum.STORE_TYPE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> channelTypeMap = salesDictMap.get(SalesDictEnum.CHANNEL_TYPE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> yesorNoMap = salesDictMap.get(SalesDictEnum.YES_OR_NO.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> positionTypeMap = salesDictMap.get(SalesDictEnum.POSITION_TYPE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> verifyResultMap = salesDictMap.get(SalesDictEnum.VERIFY_RESULT.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> productLineEnMap = salesDictMap.get(SalesDictEnum.PRODUCT_LINE_EN.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> reportingtypeMap = salesDictMap.get(SalesDictEnum.REPORTING_TYPE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        
        for (SalesImeiRespDto salesImeiRespDto : salesImeiRespDtoList) {
            salesImeiRespDto.setSalesmanTitleDesc(jobTitleMap.get(String.valueOf(salesImeiRespDto.getSalesmanTitle())));
            salesImeiRespDto.setStoreGradeDesc(storeGradeMap.get(String.valueOf(salesImeiRespDto.getStoreGrade())));
            salesImeiRespDto.setStoreTypeDesc(storeTypeMap.get(String.valueOf(salesImeiRespDto.getStoreType())));
            salesImeiRespDto.setChannelTypeDesc(channelTypeMap.get(String.valueOf(salesImeiRespDto.getChannelType())));
            salesImeiRespDto.setHasSrDesc(yesorNoMap.get(String.valueOf(salesImeiRespDto.getHasSr())));
            salesImeiRespDto.setHasPcDesc(yesorNoMap.get(String.valueOf(salesImeiRespDto.getHasPc())));
            salesImeiRespDto.setPositionTypeDesc(positionTypeMap.get(String.valueOf(salesImeiRespDto.getPositionType())));
            salesImeiRespDto.setVerificationResultDesc(verifyResultMap.get(String.valueOf(salesImeiRespDto.getVerificationResult())));
            salesImeiRespDto.setProductLineEnDesc(productLineEnMap.get(salesImeiRespDto.getProductLineEn()));
            salesImeiRespDto.setReportingTypeDesc(reportingtypeMap.get(String.valueOf(salesImeiRespDto.getReportingType())));
            fillIntlSoImei(salesImeiRespDto, soImeiMap);
            fillSalesImeiRespDto4User(salesImeiRespDto, rmsUserMap);
        }
        return salesImeiRespDtoList;
    }
    
    @Override
    protected SalesImeiRespDto populateTarget(SoImeiIndex soImeiIndex) {
        if (null == soImeiIndex) {
            return null;
        }
        SalesImeiRespDto salesImeiRespDto = new SalesImeiRespDto();
        salesImeiRespDto.setId(soImeiIndex.getId());
        salesImeiRespDto.setSalesmanMid(soImeiIndex.getSalesmanMid());
        
        salesImeiRespDto.setSalesmanAccount(soImeiIndex.getSalesmanAccount());
        salesImeiRespDto.setSalesmanTitle(soImeiIndex.getSalesmanJobtitle());
        
        salesImeiRespDto.setStoreCode(soImeiIndex.getStoreCode());
        salesImeiRespDto.setStoreName(soImeiIndex.getStoreName());
        salesImeiRespDto.setStoreGrade(soImeiIndex.getStoreGrade());
        salesImeiRespDto.setStoreType(soImeiIndex.getStoreType());
        salesImeiRespDto.setChannelType(soImeiIndex.getStoreChannelType());
        salesImeiRespDto.setHasSr(soImeiIndex.getStoreHasSr());
        salesImeiRespDto.setHasPc(soImeiIndex.getStoreHasPc());
        salesImeiRespDto.setPositionCode(soImeiIndex.getPositionCode());
        salesImeiRespDto.setPositionName(soImeiIndex.getPositionName());
        salesImeiRespDto.setPositionType(soImeiIndex.getPositionType());
        salesImeiRespDto.setRetailerCode(soImeiIndex.getRetailerCode());
        salesImeiRespDto.setRetailerName(soImeiIndex.getRetailerName());
        salesImeiRespDto.setCountry(soImeiIndex.getCountryName());
        salesImeiRespDto.setProvince(soImeiIndex.getProvinceName());
        salesImeiRespDto.setCity(soImeiIndex.getCityName());
        salesImeiRespDto.setImei1(soImeiIndex.getImei1Mask());
        salesImeiRespDto.setImei2(soImeiIndex.getImei2Mask());
        salesImeiRespDto.setSn(soImeiIndex.getSnMask());
        
        salesImeiRespDto.setProductCode(soImeiIndex.getProductCode());
        salesImeiRespDto.setActivationTime(soImeiIndex.getActivationTime());
        salesImeiRespDto.setActivationTimeStr(IntlTimeUtil.getAreaTimeStr(soImeiIndex.getActivationTime()));
        salesImeiRespDto.setSalesTime(soImeiIndex.getSalesTime());
        salesImeiRespDto.setSalesTimeStr(IntlTimeUtil.getAreaTimeStr(soImeiIndex.getSalesTime()));
        
        salesImeiRespDto.setVerificationResult(soImeiIndex.getActivationResult());
        salesImeiRespDto.setSkuName(soImeiIndex.getSkuName());
        salesImeiRespDto.setSpuEn(soImeiIndex.getSpuNameEn());
        salesImeiRespDto.setProductLineEn(soImeiIndex.getProductLineEn());
        
        salesImeiRespDto.setRrp(soImeiIndex.getRrp());
        salesImeiRespDto.setCurrency(soImeiIndex.getCurrency());
        salesImeiRespDto.setReportingType(soImeiIndex.getReportingType());
        salesImeiRespDto.setCreatedBy(soImeiIndex.getCreatedBy());
        salesImeiRespDto.setCreatedOn(soImeiIndex.getCreatedOn());
        salesImeiRespDto.setCreatedOnDesc(IntlTimeUtil.getAreaTimeStr(soImeiIndex.getCreatedOn()));
        return salesImeiRespDto;
    }
    
    private void fillIntlSoImei(SalesImeiRespDto salesImeiRespDto, Map<Long, IntlSoImei> soImeiMap) {
        IntlSoImei intlSoImei = soImeiMap.get(salesImeiRespDto.getId());
        if (null != intlSoImei) {
            salesImeiRespDto.setResultDetail(intlSoImei.getVerifyResultDetail());
            salesImeiRespDto.setRepeatUser(intlSoImei.getRepeatUser());
            salesImeiRespDto.setIsPhotoExist(intlSoImei.getIsPhotoExist());
            salesImeiRespDto.setRemark(intlSoImei.getNote());
        }
    }
    
    private void fillSalesImeiRespDto4User(SalesImeiRespDto salesImeiRespDto, Map<Long, IntlRmsUserNewDto> rmsUserMap) {
        IntlRmsUserNewDto salesmanUser = rmsUserMap.get(salesImeiRespDto.getSalesmanMid());
        if (null != salesmanUser) {
            salesImeiRespDto.setSalesmanName(salesmanUser.getEnglishName());
        }
        IntlRmsUserNewDto createUser = rmsUserMap.get(salesImeiRespDto.getCreatedBy());
        if (null != createUser) {
            // 两个字段合并展示：englishname(mi_id)
            String createdByDesc = createUser.getEnglishName() + "(" + createUser.getMiId() + ")";
            salesImeiRespDto.setCreatedByDesc(createdByDesc);
        }
    }
    
}
