package com.mi.info.intl.retail.so.app.provider.upload.dto;

import lombok.Data;

/**
 * 用户门店关系DTO
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Data
public class StoreUserRelationDto {
    
    /**
     * 用户RMS ID
     */
    private String rmsUserid;
    
    /**
     * 用户miId
     */
    private Long miId;
    
    /**
     * 用户职位
     */
    private Integer jobId;
    
    /**
     * 门店ID
     */
    private String storeId;
    
    /**
     * 门店编码
     */
    private String storeCode;
    
    /**
     * 门店名称
     */
    private String storeName;
    
    /**
     * 阵地ID
     */
    private String positionId;
    
    /**
     * 阵地编码
     */
    private String positionCode;
    
    /**
     * 阵地名称
     */
    private String positionName;
}
