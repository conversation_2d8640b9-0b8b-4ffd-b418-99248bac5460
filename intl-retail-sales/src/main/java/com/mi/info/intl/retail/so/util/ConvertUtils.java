package com.mi.info.intl.retail.so.util;

import java.util.Map;

import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;

/**
 * 转换utils
 *
 * <AUTHOR>
 * @date 2025/08/07
 */
public class ConvertUtils {
    /**
     * 根据零售商构建出零售商的so规则信息实体
     *
     * @param retailerDto 零售商信息
     * @param userId 用户信息
     * @param soRuleEnum so规则类型，{@link SoRuleEnum}
     * @param ruleId
     * @return {@link IntlSoRuleRetailer }
     */
    public static IntlSoRuleRetailer buildSoRuleRetailer(IntlRetailerDTO retailerDto, String userId,
        SoRuleEnum soRuleEnum, long ruleId) {
        IntlSoRuleRetailer retailer = new IntlSoRuleRetailer();
        long time = System.currentTimeMillis();
        retailer.setRuleId(ruleId);
        retailer.setRegionCode(retailerDto.getRegionCode());
        retailer.setCountryCode(retailerDto.getCountryCode());
        retailer.setRetailerCode(retailerDto.getRetailerCode());
        retailer.setRetailerName(retailerDto.getRetailerName());
        retailer.setChannelType(retailerDto.getChannelType());
        retailer.setCreateRetailerTime(retailerDto.getCreatedAt());
        retailer.setImeiSwitch(SwitchEnum.OFF.getValue());
        retailer.setQtySwitch(SwitchEnum.OFF.getValue());
        retailer.setCategory(soRuleEnum.getValue());
        retailer.setCreatedBy(userId);
        retailer.setCreatedAt(time);
        retailer.setUpdatedBy(userId);
        retailer.setUpdatedAt(time);
        retailer.setCreateRetailerTime(retailer.getCreatedAt());
        return retailer;
    }

    /**
     * 构建零售商实体类
     *
     * @param retailerDto 零售商DTO
     * @param changeLogMap 更改日志图
     * @param soRuleEnum so规则类型，{@link SoRuleEnum}
     * @param ruleId 规则ID
     * @return {@link IntlSoRuleRetailer }
     */
    public static IntlSoRuleRetailer buildRetailerWithRetailerType(IntlRetailerDTO retailerDto,
        Map<String, String> changeLogMap, SoRuleEnum soRuleEnum, long ruleId) {
        IntlSoRuleRetailer soRuleRetailer = buildSoRuleRetailer(retailerDto, null, soRuleEnum, ruleId);
        soRuleRetailer.setChannelType(changeLogMap.getOrDefault(retailerDto.getChannelType(), null));
        return soRuleRetailer;
    }
}
