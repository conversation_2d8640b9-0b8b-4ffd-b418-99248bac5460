package com.mi.info.intl.retail.so.app.provider.sales;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesImeiRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.VerificationResultRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.provider.SoSalesImeiDubboProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoImeiDomainService;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoSalesImeiDubboProvider.class)
public class SoSalesImeiDubboProviderImpl implements SoSalesImeiDubboProvider {

    @Resource
    private IntlSoImeiDomainService intlSoImeiDomainService;

    @Override
    public PageResponse<SalesImeiRespDto> getSalesImeiForPage(SalesImeiReqDto salesImeiReqDto) {
        return intlSoImeiDomainService.getSalesImeiForPage(salesImeiReqDto);
    }

    @Override
    public SingleResponse<VerificationResultRespDto> getImeiVerificationResultData(SalesImeiReqDto salesImeiReqDto) {
        VerificationResultRespDto verificationResultRespDto = intlSoImeiDomainService.getImeiVerificationResultData(salesImeiReqDto);
        return SingleResponse.of(verificationResultRespDto);
    }

    @Override
    public SingleResponse<ReportingRoleRespDto> getImeiReportingRoleData(SalesImeiReqDto salesImeiReqDto) {
        ReportingRoleRespDto reportingRoleRespDto = intlSoImeiDomainService.getImeiReportingRoleData(salesImeiReqDto);
        return SingleResponse.of(reportingRoleRespDto);
    }

    @Override
    public SingleResponse<Boolean> isPlaintextImeiUser() {
        UserInfo userInfo = UserInfoUtil.getUserContext();
        if (null == userInfo) {
            return SingleResponse.buildFailure("500", "Failed to obtain login user information");
        }
        return SingleResponse.of(intlSoImeiDomainService.isPlaintextImeiUser(userInfo));
    }

    @Override
    public SingleResponse<String> export(SalesImeiReqDto salesImeiReqDto) {
        return SingleResponse.of(intlSoImeiDomainService.export(salesImeiReqDto));
    }
}
