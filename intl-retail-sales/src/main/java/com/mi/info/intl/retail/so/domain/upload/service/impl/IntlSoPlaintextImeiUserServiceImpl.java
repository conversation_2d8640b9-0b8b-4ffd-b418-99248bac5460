package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextUserRequest;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoPlaintextImeiUser;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoPlaintextImeiUserService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoPlaintextImeiUserMapper;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_plaintext_imei_user(明文IMEI用户列表)】的数据库操作Service实现
 * @createDate 2025-07-31 17:15:13
 */
@Service
public class IntlSoPlaintextImeiUserServiceImpl
        extends ServiceImpl<IntlSoPlaintextImeiUserMapper, IntlSoPlaintextImeiUser>
        implements IntlSoPlaintextImeiUserService {
    
    @Override
    public IPage<PlainTextImeiUserDTO> pageList(PlainTextUserRequest request) {
        Page<PlainTextImeiUserDTO> page = new Page<>(request.getPageNum(), request.getPageSize());
        request.setOffset((request.getPageNum() - 1) * request.getPageSize());
        int count = this.baseMapper.countPageList(request);
        List<PlainTextImeiUserDTO> plainTextImeiUserDTO = this.baseMapper.selectPageList(request);
        for (PlainTextImeiUserDTO textImeiUserDTO : plainTextImeiUserDTO) {
            textImeiUserDTO.setCreatedOn(IntlTimeUtil.parseTimestampToAreaTime(request.getLocale(), Long.valueOf(textImeiUserDTO.getCreatedOn())));
        }
        page.setTotal(count);
        page.setRecords(plainTextImeiUserDTO);
        return page;
    }
}




