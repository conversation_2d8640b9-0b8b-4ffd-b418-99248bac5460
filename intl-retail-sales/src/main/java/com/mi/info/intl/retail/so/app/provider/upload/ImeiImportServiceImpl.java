package com.mi.info.intl.retail.so.app.provider.upload;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.api.so.rule.resp.GetRetailerSoRuleResp;
import com.mi.info.intl.retail.core.utils.EasyExcelUtil;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.so.ImeiReportVerifyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SubmitImeiReq;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiDetailDto;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.app.provider.upload.dto.ImeiImportExcelData;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.domain.upload.service.IntlImportLogService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper;
import com.mi.info.intl.retail.so.app.provider.upload.dto.StoreUserRelationDto;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * IMEI数据导入服务实现类
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Slf4j
@Service
public class ImeiImportServiceImpl implements ImeiImportService {

    @Resource
    private IntlImportLogService intlImportLogService;

    @Resource
    private EasyExcelUtil easyExcelUtil;

    @Resource
    private FdsService fdsService;

    @Resource
    private RmsStoreService rmsStoreService;

    @Resource
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Resource
    private ImeiReportVerifyService imeiReportVerifyService;

    @Resource
    private ImeiUploadService imeiUploadService;

    @Resource
    private ImeiImportMapper imeiImportMapper;

    // 最大导入行数限制
    private static final int MAX_IMPORT_ROWS = 5000;

    // 错误信息常量
    private static final String ERROR_EXCEED_MAX_ROWS = "The number of import rows exceeds the maximum limit of 5000";
    private static final String ERROR_VALIDATION_FAILED = "Validation failed";
    private static final String ERROR_MANDATORY_FIELDS = "Items in red font are mandatory fields.";
    private static final String ERROR_DATE_WITHIN_45_DAYS = "Only data within 45 days can be reported.";
    private static final String ERROR_FUTURE_DATE = "The sales time can not be filled in with a future date.";
    private static final String ERROR_REPEATED_DATA = "Repeated data.";
    private static final String ERROR_STORE_NOT_CREATED = "This store has not been created at the sales time.";
    private static final String ERROR_NOT_IN_STORE = "You are not in this store.";
    private static final String ERROR_NO_SO_RULE = "No SO rule";
    private static final String ERROR_NO_IMEI_RULE = "No imei rule";
    private static final String ERROR_PRODUCT_VALIDATION_FAILED = "Product validation failed";
    private static final String ERROR_DUPLICATE_VALIDATION_FAILED = "Duplicate validation failed";
    private static final String ERROR_LEGALITY_VALIDATION_FAILED = "Legality validation failed";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<ImeiImportResponse> importImeiData(ImeiImportRequest request) {
        log.info("importImeiData start, request: {}", JSON.toJSONString(request));

        try {
            // 1. 参数校验
            if (request.getImportLogId() == null || request.getMiId() == null) {
                return new CommonApiResponse<>(400, "importLogId and miId are required", null);
            }

            // 2. 获取源文件URL
            String sourceFileUrl = getSourceFileUrl(request);
            if (StringUtils.isBlank(sourceFileUrl)) {
                return new CommonApiResponse<>(400, "Source file URL is required", null);
            }

            // 3. 下载并解析Excel文件
            List<ImeiImportExcelData> excelDataList = downloadAndParseExcel(sourceFileUrl);

            // 4. 检查数据量是否超过限制
            if (excelDataList.size() > MAX_IMPORT_ROWS) {
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_EXCEED_MAX_ROWS, null);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_EXCEED_MAX_ROWS, null);
            }

            // 5. 数据校验
            List<ImeiImportExcelData> validDataList = validateData(excelDataList, request.getMiId());

            // 6. 检查是否有校验失败的数据
            boolean hasErrors = validDataList.stream().anyMatch(data -> StringUtils.isNotBlank(data.getFailedReason()));

            if (hasErrors) {
                // 生成错误文件并上传
                String errorFileUrl = generateErrorFile(validDataList);
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, errorFileUrl);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, errorFileUrl);
            }

            // 7. 创建IMEI数据
            boolean createSuccess = createImeiData(validDataList, request.getMiId());

            if (createSuccess) {
                updateImportLogStatus(request.getImportLogId(), 1, null, null);
                return createSuccessResponse(request.getImportLogId());
            } else {
                updateImportLogStatus(request.getImportLogId(), 2, "IMEI creation failed", null);
                return createErrorResponse(request.getImportLogId(), 2, "IMEI creation failed", null);
            }

        } catch (Exception e) {
            log.error("importImeiData error", e);
            updateImportLogStatus(request.getImportLogId(), 2, "System error: " + e.getMessage(), null);
            return new CommonApiResponse<>(500, "System error: " + e.getMessage(), null);
        }
    }

    /**
     * 获取源文件URL
     */
    private String getSourceFileUrl(ImeiImportRequest request) {
        if (StringUtils.isNotBlank(request.getSourceFileUrl())) {
            return request.getSourceFileUrl();
        }

        // 根据importLogId查询
        IntlImportLog importLog = intlImportLogService.getById(request.getImportLogId());
        return importLog != null ? importLog.getSourceFileUrl() : null;
    }

    /**
     * 下载并解析Excel文件
     */
    private List<ImeiImportExcelData> downloadAndParseExcel(String fileUrl) throws IOException {
        log.info("开始下载文件: {}", fileUrl);

        File inputFile = null;
        try {
            // 下载文件
            inputFile = downloadFileFromUrl(fileUrl);

            // 解析Excel文件
            List<ImeiImportExcelData> dataList = new ArrayList<>();
            AtomicInteger rowIndex = new AtomicInteger(1);

            easyExcelUtil.readFromStream(
                Files.newInputStream(inputFile.toPath()),
                ImeiImportExcelData.class,
                new ImeiImportExcelListener(dataList, rowIndex),
                1
            );

            log.info("Excel文件解析完成，共{}行数据", dataList.size());
            return dataList;

        } finally {
            // 清理临时文件
            if (inputFile != null && inputFile.exists()) {
                inputFile.delete();
            }
        }
    }

    /**
     * 从URL下载文件
     */
    private File downloadFileFromUrl(String fileUrl) throws IOException {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(fileUrl).build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("文件下载失败，HTTP状态码: " + response.code());
            }

            // 创建临时文件
            File tempFile = File.createTempFile("imei_import_", ".xlsx");
            try (InputStream inputStream = response.body().byteStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            return tempFile;
        }
    }

    /**
     * 数据校验
     */
    private List<ImeiImportExcelData> validateData(List<ImeiImportExcelData> dataList, Long miId) {
        log.info("开始数据校验，数据量: {}", dataList.size());

        // 1. 必填项校验和合法性校验
        validateMandatoryAndLegality(dataList);

        // 2. 重复性校验
        validateDuplicates(dataList);

        // 3. 门店和用户权限校验
        validateStoreAndUserPermissions(dataList, miId);

        log.info("数据校验完成");
        return dataList;
    }

    /**
     * 必填项校验和合法性校验
     */
    private void validateMandatoryAndLegality(List<ImeiImportExcelData> dataList) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime fortyFiveDaysAgo = now.minusDays(45);

        for (ImeiImportExcelData data : dataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }

            // 必填字段校验
            if (StringUtils.isBlank(data.getStoreCode()) ||
                StringUtils.isBlank(data.getSalesTime()) ||
                (StringUtils.isBlank(data.getImei()) && StringUtils.isBlank(data.getSn()))) {
                data.setFailedReason(ERROR_MANDATORY_FIELDS);
                continue;
            }

            // 销售时间格式校验
            LocalDateTime salesTime = parseSalesTime(data.getSalesTime());
            if (salesTime == null) {
                data.setFailedReason(ERROR_MANDATORY_FIELDS);
                continue;
            }

            // 销售时间不能是未来时间
            if (salesTime.isAfter(now)) {
                data.setFailedReason(ERROR_FUTURE_DATE);
                continue;
            }

            // 销售时间必须在过去45天内
            if (salesTime.isBefore(fortyFiveDaysAgo)) {
                data.setFailedReason(ERROR_DATE_WITHIN_45_DAYS);
                continue;
            }
        }
    }

    /**
     * 解析销售时间
     */
    private LocalDateTime parseSalesTime(String salesTimeStr) {
        if (StringUtils.isBlank(salesTimeStr)) {
            return null;
        }

        try {
            // 尝试多种日期格式
            DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                DateTimeFormatter.ofPattern("dd/MM/yyyy")
            };

            for (DateTimeFormatter formatter : formatters) {
                try {
                    if (salesTimeStr.length() == 10) {
                        return LocalDateTime.of(LocalDateTime.parse(salesTimeStr + " 00:00:00",
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toLocalDate(),
                            LocalDateTime.MIN.toLocalTime());
                    } else {
                        return LocalDateTime.parse(salesTimeStr, formatter);
                    }
                } catch (DateTimeParseException ignored) {
                    // 继续尝试下一个格式
                }
            }
        } catch (Exception e) {
            log.warn("解析销售时间失败: {}", salesTimeStr, e);
        }

        return null;
    }

    /**
     * 重复性校验
     */
    private void validateDuplicates(List<ImeiImportExcelData> dataList) {
        // 记录已出现的IMEI/SN，用于检测重复
        Set<String> seenImeiSn = new HashSet<>();

        for (ImeiImportExcelData data : dataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }

            String imeiSn = StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn();
            if (StringUtils.isNotBlank(imeiSn)) {
                if (seenImeiSn.contains(imeiSn.toUpperCase())) {
                    data.setFailedReason(ERROR_REPEATED_DATA);
                } else {
                    seenImeiSn.add(imeiSn.toUpperCase());
                }
            }
        }
    }

    /**
     * 门店和用户权限校验
     */
    private void validateStoreAndUserPermissions(List<ImeiImportExcelData> dataList, Long miId) {
        // 按Store Code分组
        Map<String, List<ImeiImportExcelData>> storeGroups = dataList.stream()
            .filter(data -> StringUtils.isBlank(data.getFailedReason()))
            .collect(Collectors.groupingBy(ImeiImportExcelData::getStoreCode));

        for (Map.Entry<String, List<ImeiImportExcelData>> entry : storeGroups.entrySet()) {
            String storeCode = entry.getKey();
            List<ImeiImportExcelData> storeDataList = entry.getValue();

            try {
                // 1. 查询门店信息
                ImeiImportMapper.StoreInfoDto storeInfo = imeiImportMapper.getStoreByCode(storeCode);
                if (storeInfo == null) {
                    markStoreDataAsFailed(storeDataList, "Store not found: " + storeCode);
                    continue;
                }

                // 2. 校验销售日期是否小于门店创建时间
                validateSalesTimeAgainstStoreCreation(storeDataList, storeInfo);

                // 3. 查询人店关系
                StoreUserRelationDto userStoreRelation = imeiImportMapper.getUserStoreRelation(storeCode, miId);
                if (userStoreRelation == null) {
                    markStoreDataAsFailed(storeDataList, ERROR_NOT_IN_STORE);
                    continue;
                }

                // 4. 获取最优阵地
                List<ImeiImportMapper.PositionInfoDto> positions = imeiImportMapper.getPositionsByStoreCode(storeCode);
                ImeiImportMapper.PositionInfoDto bestPosition = getBestPosition(positions);
                if (bestPosition == null) {
                    markStoreDataAsFailed(storeDataList, "No valid position found for store: " + storeCode);
                    continue;
                }

                // 5. 查询IMEI上报规则
                GetRetailerSoRuleReq ruleReq = new GetRetailerSoRuleReq();
                ruleReq.setCountryCode(storeInfo.getCountryShortcode());
                ruleReq.setMiId(String.valueOf(miId));
                ruleReq.setPositionCode(bestPosition.getCode());
                ruleReq.setStoreCode(storeInfo.getCode());
                ruleReq.setUserTitleId(String.valueOf(userStoreRelation.getJobId()));

                GetRetailerSoRuleResp ruleResp;
                try {
                    ruleResp = intlSoRuleRetailerService.getRetailerSoRule(ruleReq);
                } catch (Exception e) {
                    markStoreDataAsFailed(storeDataList, ERROR_NO_SO_RULE);
                    continue;
                }

                if (ruleResp == null || ruleResp.getEnableImei() == null || ruleResp.getEnableImei() != 1) {
                    markStoreDataAsFailed(storeDataList, ERROR_NO_IMEI_RULE);
                    continue;
                }

                // 6. IMEI上报校验
                validateImeiReporting(storeDataList, userStoreRelation, storeInfo.getCountryShortcode());

            } catch (Exception e) {
                log.error("门店{}校验失败", storeCode, e);
                markStoreDataAsFailed(storeDataList, "Store validation failed: " + e.getMessage());
            }
        }
    }

    /**
     * 标记门店下所有数据为失败
     */
    private void markStoreDataAsFailed(List<ImeiImportExcelData> storeDataList, String errorMsg) {
        storeDataList.forEach(data -> {
            if (StringUtils.isBlank(data.getFailedReason())) {
                data.setFailedReason(errorMsg);
            }
        });
    }

    /**
     * 校验销售日期是否小于门店创建时间
     */
    private void validateSalesTimeAgainstStoreCreation(List<ImeiImportExcelData> storeDataList,
                                                       ImeiImportMapper.StoreInfoDto storeInfo) {
        if (storeInfo.getCreatedOn() == null) {
            return; // 如果门店创建时间为空，跳过校验
        }

        long storeCreatedTime = storeInfo.getCreatedOn();

        for (ImeiImportExcelData data : storeDataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue;
            }

            LocalDateTime salesTime = parseSalesTime(data.getSalesTime());
            if (salesTime != null) {
                // 将销售时间转换为时间戳（毫秒）
                long salesTimeMillis = salesTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();

                if (salesTimeMillis < storeCreatedTime) {
                    data.setFailedReason(ERROR_STORE_NOT_CREATED);
                }
            }
        }
    }

    /**
     * 获取最优阵地
     */
    private ImeiImportMapper.PositionInfoDto getBestPosition(List<ImeiImportMapper.PositionInfoDto> positions) {
        if (positions == null || positions.isEmpty()) {
            return null;
        }

        // 按照优先级排序：SIS-ES-DZ-DC-POS
        String[] priorityTypes = {"SIS", "ES", "DZ", "DC", "POS"};

        for (String priorityType : priorityTypes) {
            for (ImeiImportMapper.PositionInfoDto position : positions) {
                if (priorityType.equals(position.getTypeName())) {
                    return position;
                }
            }
        }

        // 如果没有找到优先级类型，返回创建时间最早的
        return positions.stream()
            .min((p1, p2) -> Long.compare(p1.getCreatedOn(), p2.getCreatedOn()))
            .orElse(positions.get(0));
    }

    /**
     * IMEI上报校验
     */
    private void validateImeiReporting(List<ImeiImportExcelData> storeDataList,
                                       StoreUserRelationDto userStoreRelation,
                                       String countryCode) {
        // 分批处理，每批最大100条
        int batchSize = 100;
        for (int i = 0; i < storeDataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, storeDataList.size());
            List<ImeiImportExcelData> batch = storeDataList.subList(i, endIndex);

            // 过滤出没有错误的数据
            List<ImeiImportExcelData> validBatch = batch.stream()
                .filter(data -> StringUtils.isBlank(data.getFailedReason()))
                .collect(Collectors.toList());

            if (validBatch.isEmpty()) {
                continue;
            }

            // 构建IMEI校验请求
            ImeiReportVerifyRequest verifyRequest = new ImeiReportVerifyRequest();
            verifyRequest.setUserTitle(userStoreRelation.getJobId());
            verifyRequest.setMiId(String.valueOf(userStoreRelation.getMiId()));
            verifyRequest.setCountryCode(countryCode);

            List<String> sns = validBatch.stream()
                .map(data -> StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn())
                .collect(Collectors.toList());
            verifyRequest.setSns(sns);

            try {
                CommonApiResponse<Object> verifyResponse = imeiReportVerifyService.imeiReportVerify(verifyRequest);
                if (verifyResponse.getData() instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<ImeiReportVerifyResponse> responseList = (List<ImeiReportVerifyResponse>) verifyResponse.getData();

                    // 处理校验结果
                    for (int j = 0; j < responseList.size() && j < validBatch.size(); j++) {
                        ImeiReportVerifyResponse response = responseList.get(j);
                        ImeiImportExcelData data = validBatch.get(j);

                        if (response.getVerifyResult() != null && response.getVerifyResult() != 0) {
                            String errorMsg = getVerifyErrorMessage(response.getVerifyResult());
                            data.setFailedReason(errorMsg);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("IMEI校验失败", e);
                validBatch.forEach(data -> data.setFailedReason("IMEI verification failed: " + e.getMessage()));
            }
        }
    }

    /**
     * 获取校验错误信息
     */
    private String getVerifyErrorMessage(Integer verifyResult) {
        switch (verifyResult) {
            case 1:
                return ERROR_PRODUCT_VALIDATION_FAILED;
            case 2:
                return ERROR_DUPLICATE_VALIDATION_FAILED;
            case 3:
                return ERROR_LEGALITY_VALIDATION_FAILED;
            default:
                return "Unknown verification error";
        }
    }

    /**
     * 获取IMEI校验结果
     */
    private Map<String, ImeiReportVerifyResponse> getImeiVerifyResults(List<ImeiImportExcelData> storeDataList,
                                                                       StoreUserRelationDto userStoreRelation,
                                                                       String countryCode) {
        Map<String, ImeiReportVerifyResponse> resultMap = new HashMap<>();

        // 分批处理，每批最大100条
        int batchSize = 100;
        for (int i = 0; i < storeDataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, storeDataList.size());
            List<ImeiImportExcelData> batch = storeDataList.subList(i, endIndex);

            // 构建IMEI校验请求
            ImeiReportVerifyRequest verifyRequest = new ImeiReportVerifyRequest();
            verifyRequest.setUserTitle(userStoreRelation.getJobId());
            verifyRequest.setMiId(String.valueOf(userStoreRelation.getMiId()));
            verifyRequest.setCountryCode(countryCode);

            List<String> sns = batch.stream()
                .map(data -> StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn())
                .collect(Collectors.toList());
            verifyRequest.setSns(sns);

            try {
                CommonApiResponse<Object> verifyResponse = imeiReportVerifyService.imeiReportVerify(verifyRequest);
                if (verifyResponse.getData() instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<ImeiReportVerifyResponse> responseList = (List<ImeiReportVerifyResponse>) verifyResponse.getData();

                    // 将结果放入map
                    for (int j = 0; j < responseList.size() && j < batch.size(); j++) {
                        ImeiReportVerifyResponse response = responseList.get(j);
                        ImeiImportExcelData data = batch.get(j);
                        String key = StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn();
                        resultMap.put(key, response);
                    }
                }
            } catch (Exception e) {
                log.error("IMEI校验失败", e);
            }
        }

        return resultMap;
    }

    /**
     * 构建SubmitImeiReq
     */
    private SubmitImeiReq buildSubmitImeiRequest(List<ImeiImportExcelData> storeDataList,
                                                 GetRetailerSoRuleResp ruleResp,
                                                 ImeiImportMapper.StoreInfoDto storeInfo,
                                                 ImeiImportMapper.PositionInfoDto bestPosition,
                                                 StoreUserRelationDto userStoreRelation,
                                                 Map<String, ImeiReportVerifyResponse> verifyResultMap) {
        SubmitImeiReq submitReq = new SubmitImeiReq();
        submitReq.setRuleId(ruleResp.getRuleId());
        submitReq.setCountryCode(storeInfo.getCountryShortcode());
        submitReq.setPositionCode(bestPosition.getCode());
        submitReq.setStoreCode(storeInfo.getCode());
        submitReq.setUserId(userStoreRelation.getRmsUserid());
        submitReq.setMiId(userStoreRelation.getMiId());
        submitReq.setUserTitle(userStoreRelation.getJobId().longValue());

        // 构建明细列表
        List<ImeiDetailDto> detailList = new ArrayList<>();
        for (ImeiImportExcelData data : storeDataList) {
            String key = StringUtils.isNotBlank(data.getImei()) ? data.getImei() : data.getSn();
            ImeiReportVerifyResponse verifyResult = verifyResultMap.get(key);

            if (verifyResult != null && verifyResult.getVerifyResult() != null && verifyResult.getVerifyResult() == 0) {
                ImeiDetailDto detail = new ImeiDetailDto();
                detail.setDetailId(""); // 赋值为空
                detail.setImei(verifyResult.getImei1());
                detail.setImei2(verifyResult.getImei2());
                detail.setSn(verifyResult.getSn());
                detail.setProductId(verifyResult.getProductId());
                detail.setProductCode(verifyResult.getProductId());
                detail.setReportingType(100000000); // ReportingTypeEnum.PC
                detail.setNote(data.getRemark());
                detail.setInputImei(key);
                // 使用Excel中的销售时间，转换为时间戳
                LocalDateTime salesTime = parseSalesTime(data.getSalesTime());
                long salesTimeMillis = salesTime != null ?
                    salesTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() :
                    System.currentTimeMillis();
                detail.setSalesTime(salesTimeMillis);
                detail.setSnhash(verifyResult.getSnHash());
                detail.setIsHashCountry(verifyResult.getIsHashCountry());

                detailList.add(detail);
            }
        }

        submitReq.setDetailList(detailList);
        submitReq.setPhotoList(new ArrayList<>()); // 空的图片列表

        return submitReq;
    }

    /**
     * 生成错误文件
     */
    private String generateErrorFile(List<ImeiImportExcelData> dataList) {
        File tempFile = null;
        ExcelWriter excelWriter = null;

        try {
            tempFile = File.createTempFile("imei_import_error_", ".xlsx");

            excelWriter = EasyExcel.write(tempFile, ImeiImportExcelData.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("Error Data").build();

            excelWriter.write(dataList, writeSheet);
            excelWriter.finish();

            // 上传到FDS服务器
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            String fileName = "imei_import_error_" + timestamp + ".xlsx";
            FdsUploadResult uploadResult = fdsService.upload(fileName, tempFile, true);
            String fileUrl = uploadResult.getUrl();

            log.info("错误数据文件已上传到FDS: {}", fileUrl);
            return fileUrl;

        } catch (Exception e) {
            log.error("生成错误文件失败", e);
            return null;
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 创建IMEI数据
     */
    private boolean createImeiData(List<ImeiImportExcelData> dataList, Long miId) {
        try {
            // 按Store Code分组
            Map<String, List<ImeiImportExcelData>> storeGroups = dataList.stream()
                .filter(data -> StringUtils.isBlank(data.getFailedReason()))
                .collect(Collectors.groupingBy(ImeiImportExcelData::getStoreCode));

            for (Map.Entry<String, List<ImeiImportExcelData>> entry : storeGroups.entrySet()) {
                String storeCode = entry.getKey();
                List<ImeiImportExcelData> storeDataList = entry.getValue();

                // 获取门店和用户信息
                ImeiImportMapper.StoreInfoDto storeInfo = imeiImportMapper.getStoreByCode(storeCode);
                StoreUserRelationDto userStoreRelation = imeiImportMapper.getUserStoreRelation(storeCode, miId);
                List<ImeiImportMapper.PositionInfoDto> positions = imeiImportMapper.getPositionsByStoreCode(storeCode);
                ImeiImportMapper.PositionInfoDto bestPosition = getBestPosition(positions);

                if (storeInfo == null || userStoreRelation == null || bestPosition == null) {
                    log.error("门店{}信息不完整，跳过IMEI创建", storeCode);
                    continue;
                }

                // 获取IMEI上报规则
                GetRetailerSoRuleReq ruleReq = new GetRetailerSoRuleReq();
                ruleReq.setCountryCode(storeInfo.getCountryShortcode());
                ruleReq.setMiId(String.valueOf(miId));
                ruleReq.setPositionCode(bestPosition.getCode());
                ruleReq.setStoreCode(storeInfo.getCode());
                ruleReq.setUserTitleId(String.valueOf(userStoreRelation.getJobId()));

                GetRetailerSoRuleResp ruleResp = intlSoRuleRetailerService.getRetailerSoRule(ruleReq);
                if (ruleResp == null) {
                    log.error("门店{}获取规则失败，跳过IMEI创建", storeCode);
                    continue;
                }

                // 获取IMEI校验结果
                Map<String, ImeiReportVerifyResponse> verifyResultMap = getImeiVerifyResults(storeDataList, userStoreRelation, storeInfo.getCountryShortcode());

                // 构建SubmitImeiReq
                SubmitImeiReq submitReq = buildSubmitImeiRequest(storeDataList, ruleResp, storeInfo, bestPosition, userStoreRelation, verifyResultMap);

                // 调用IMEI创建服务
                CommonApiResponse<Object> submitResult = imeiUploadService.submitImei(submitReq);
                if (submitResult.getCode() != 200) {
                    log.error("门店{}IMEI创建失败: {}", storeCode, submitResult.getMessage());
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("创建IMEI数据失败", e);
            return false;
        }
    }

    /**
     * 更新导入日志状态
     */
    private void updateImportLogStatus(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        IntlImportLog importLog = new IntlImportLog();
        importLog.setId(importLogId);
        importLog.setStatus(status);
        importLog.setErrorMsg(errorMsg);
        importLog.setResultFileUrl(resultFileUrl);
        importLog.setUpdatedAt(System.currentTimeMillis());
        intlImportLogService.updateById(importLog);
    }

    /**
     * 创建成功响应
     */
    private CommonApiResponse<ImeiImportResponse> createSuccessResponse(Long importLogId) {
        ImeiImportResponse response = new ImeiImportResponse();
        response.setImportLogId(importLogId);
        response.setStatus(1);
        return new CommonApiResponse<>(response);
    }

    /**
     * 创建错误响应
     */
    private CommonApiResponse<ImeiImportResponse> createErrorResponse(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        ImeiImportResponse response = new ImeiImportResponse();
        response.setImportLogId(importLogId);
        response.setStatus(status);
        response.setErrorMsg(errorMsg);
        response.setResultFileUrl(resultFileUrl);
        return new CommonApiResponse<>(response);
    }
}
