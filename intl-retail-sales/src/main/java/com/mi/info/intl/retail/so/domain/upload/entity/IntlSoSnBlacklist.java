package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 销量SO序列号黑名单
 *
 * @TableName intl_so_sn_blacklist
 */
@TableName(value = "intl_so_sn_blacklist")
@Data
public class IntlSoSnBlacklist {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联国家短代码
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 国家名称
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 类型：0=sn，1=imei（与注释枚举对应）
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 序列号（SN）或 IMEI 码
     */
    @TableField(value = "sn_imei")
    private String snImei;

    /**
     * SN/IMEI 的哈希值（用于敏感数据脱敏或快速校验）
     */
    @TableField(value = "snhash")
    private String snhash;

    /**
     * RMSID
     */
    @TableField(value = "rms_id")
    private String rmsId;

    /**
     * 数据来源（1：rms；2 retail）
     */
    @TableField(value = "data_from")
    private Integer dataFrom;

    /**
     * 是否启用（0：可用；1 停用）
     */
    @TableField(value = "status")
    private Boolean status;

    /**
     * 创建人mid
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 创建时间戳
     */
    @TableField(value = "created_on")
    private Long createdOn;

    /**
     * 修改人mid
     */
    @TableField(value = "modified_by")
    private Long modifiedBy;

    /**
     * 修改时间戳
     */
    @TableField(value = "modified_on")
    private Long modifiedOn;
}