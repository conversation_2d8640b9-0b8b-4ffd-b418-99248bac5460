package com.mi.info.intl.retail.so.domain.sales.converter;

import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.converter.AbstractConvert;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesQtyRespDto;
import com.mi.info.intl.retail.so.domain.sales.enums.SalesDictEnum;
import com.mi.info.intl.retail.so.domain.sales.handler.SoSalesHandler;
import com.mi.info.intl.retail.so.domain.sales.model.SoQtyIndex;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Project: intl-retail
 * @Description: SO销售数量转换器
 * @Author: 周楚强
 * @Date: 2025-08-01
 **/

@Component
public class SalesQtyConverter extends AbstractConvert<SoQtyIndex, SalesQtyRespDto> {
    
    @Resource
    private SoSalesHandler soSalesHandler;
    
    @Override
    public List<SalesQtyRespDto> toTarget(List<SoQtyIndex> indexList) {
        if (null == indexList) {
            return new ArrayList<>();
        }
        List<SalesQtyRespDto> salesQtyRespList = new ArrayList<>(indexList.size());
        List<Long> miIds = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (SoQtyIndex soQtyIndex : indexList) {
            salesQtyRespList.add(super.toTarget(soQtyIndex));
            Long salesmanMid = soQtyIndex.getSalesmanMid();
            Long createOn = soQtyIndex.getCreatedOn();
            if (!miIds.contains(salesmanMid)) {
                miIds.add(salesmanMid);
            }
            if (!miIds.contains(createOn)) {
                miIds.add(createOn);
            }
            ids.add(soQtyIndex.getId());
        }
        
        Map<String, List<LabelValueDTO>> salesDictMap = soSalesHandler.getLabelValueList4Sales();
        Map<Long, IntlRmsUserNewDto> rmsUserMap = soSalesHandler.getRmsUserListByMiIds(miIds);
        Map<Long, IntlSoQty> soQtyMap = soSalesHandler.getIntlSoQtyListByIds(ids);
        Map<String, String> jobTitleMap = salesDictMap.get(SalesDictEnum.JOB_TITLE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> storeGradeMap = salesDictMap.get(SalesDictEnum.STORE_GRADE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> storeTypeMap = salesDictMap.get(SalesDictEnum.STORE_TYPE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> channelTypeMap = salesDictMap.get(SalesDictEnum.CHANNEL_TYPE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> yesorNoMap = salesDictMap.get(SalesDictEnum.YES_OR_NO.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> positionTypeMap = salesDictMap.get(SalesDictEnum.POSITION_TYPE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> productLineEnMap = salesDictMap.get(SalesDictEnum.PRODUCT_LINE_EN.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> reportingtypeMap = salesDictMap.get(SalesDictEnum.REPORTING_TYPE.getCode())
                .stream().collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        
        for (SalesQtyRespDto salesQtyRespDto : salesQtyRespList) {
            salesQtyRespDto.setSalesmanTitleDesc(jobTitleMap.get(String.valueOf(salesQtyRespDto.getSalesmanTitle())));
            salesQtyRespDto.setStoreGradeDesc(storeGradeMap.get(String.valueOf(salesQtyRespDto.getStoreGrade())));
            salesQtyRespDto.setStoreTypeDesc(storeTypeMap.get(String.valueOf(salesQtyRespDto.getStoreType())));
            salesQtyRespDto.setChannelTypeDesc(channelTypeMap.get(String.valueOf(salesQtyRespDto.getChannelType())));
            salesQtyRespDto.setHasSrDesc(yesorNoMap.get(String.valueOf(salesQtyRespDto.getHasSr())));
            salesQtyRespDto.setHasPcDesc(yesorNoMap.get(String.valueOf(salesQtyRespDto.getHasPc())));
            salesQtyRespDto.setPositionTypeDesc(positionTypeMap.get(String.valueOf(salesQtyRespDto.getPositionType())));
            salesQtyRespDto.setProductLineEnDesc(productLineEnMap.get(salesQtyRespDto.getProductLineEn()));
            salesQtyRespDto.setReportingTypeDesc(reportingtypeMap.get(String.valueOf(salesQtyRespDto.getReportingType())));
            fillIntlSoQty(salesQtyRespDto, soQtyMap);
            fillSalesQtyRespDto4User(salesQtyRespDto, rmsUserMap);
        }
        return salesQtyRespList;
    }
    
    @Override
    protected SalesQtyRespDto populateTarget(SoQtyIndex soQtyIndex) {
        if (null == soQtyIndex) {
            return null;
        }
        SalesQtyRespDto salesQtyRespDto = new SalesQtyRespDto();
        salesQtyRespDto.setId(soQtyIndex.getId());
        salesQtyRespDto.setSalesmanMid(soQtyIndex.getSalesmanMid());
        
        salesQtyRespDto.setSalesmanAccount(soQtyIndex.getSalesmanAccount());
        salesQtyRespDto.setSalesmanTitle(soQtyIndex.getSalesmanJobtitle());
        
        salesQtyRespDto.setStoreCode(soQtyIndex.getStoreCode());
        salesQtyRespDto.setStoreName(soQtyIndex.getStoreName());
        salesQtyRespDto.setStoreGrade(soQtyIndex.getStoreGrade());
        salesQtyRespDto.setStoreType(soQtyIndex.getStoreType());
        salesQtyRespDto.setChannelType(soQtyIndex.getStoreChannelType());
        salesQtyRespDto.setHasSr(soQtyIndex.getStoreHasSr());
        salesQtyRespDto.setHasPc(soQtyIndex.getStoreHasPc());
        salesQtyRespDto.setPositionCode(soQtyIndex.getPositionCode());
        salesQtyRespDto.setPositionName(soQtyIndex.getPositionName());
        salesQtyRespDto.setPositionType(soQtyIndex.getPositionType());
        salesQtyRespDto.setRetailerCode(soQtyIndex.getRetailerCode());
        salesQtyRespDto.setRetailerName(soQtyIndex.getRetailerName());
        salesQtyRespDto.setCountry(soQtyIndex.getCountryName());
        salesQtyRespDto.setProvince(soQtyIndex.getProvinceName());
        salesQtyRespDto.setCity(soQtyIndex.getCityName());
        
        salesQtyRespDto.setCode69(soQtyIndex.getCode69());
        salesQtyRespDto.setProductCode(soQtyIndex.getProductCode());
        salesQtyRespDto.setSalesTime(soQtyIndex.getSalesTime());
        salesQtyRespDto.setSalesTimeStr(IntlTimeUtil.getAreaTimeStr(soQtyIndex.getSalesTime()));
        
        salesQtyRespDto.setSkuName(soQtyIndex.getSkuName());
        salesQtyRespDto.setSpuEn(soQtyIndex.getSpuNameEn());
        salesQtyRespDto.setProductLineEn(soQtyIndex.getProductLineEn());
        
        salesQtyRespDto.setRrp(soQtyIndex.getRrp());
        salesQtyRespDto.setCurrency(soQtyIndex.getCurrency());
        salesQtyRespDto.setReportingType(soQtyIndex.getReportingType());
        salesQtyRespDto.setCreatedBy(soQtyIndex.getCreatedBy());
        salesQtyRespDto.setCreatedOn(soQtyIndex.getCreatedOn());
        salesQtyRespDto.setCreatedOnDesc(IntlTimeUtil.getAreaTimeStr(soQtyIndex.getCreatedOn()));
        return salesQtyRespDto;
    }
    
    private void fillIntlSoQty(SalesQtyRespDto salesQtyRespDto, Map<Long, IntlSoQty> soQtyMap) {
        IntlSoQty intlSoQty = soQtyMap.get(salesQtyRespDto.getId());
        if (null != intlSoQty) {
            salesQtyRespDto.setIsPhotoExist(intlSoQty.getIsPhotoExist());
            salesQtyRespDto.setRemark(intlSoQty.getNote());
        }
    }
    
    private void fillSalesQtyRespDto4User(SalesQtyRespDto salesQtyRespDto, Map<Long, IntlRmsUserNewDto> rmsUserMap) {
        IntlRmsUserNewDto salesmanUser = rmsUserMap.get(salesQtyRespDto.getSalesmanMid());
        if (null != salesmanUser) {
            salesQtyRespDto.setSalesmanName(salesmanUser.getEnglishName());
        }
        IntlRmsUserNewDto createUser = rmsUserMap.get(salesQtyRespDto.getCreatedBy());
        if (null != createUser) {
            // 两个字段合并展示：englishname(mi_id)
            String createdByDesc = createUser.getEnglishName() + "(" + createUser.getMiId() + ")";
            salesQtyRespDto.setCreatedByDesc(createdByDesc);
        }
    }
    
}
