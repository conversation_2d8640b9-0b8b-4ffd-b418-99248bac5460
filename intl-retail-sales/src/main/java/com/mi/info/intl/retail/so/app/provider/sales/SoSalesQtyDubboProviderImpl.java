package com.mi.info.intl.retail.so.app.provider.sales;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingTypeRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesQtyRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.provider.SoSalesQtyDubboProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoQtyDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * @Project: intl-retail
 * @Description: 销量dubbo服务实现类
 * @Author: 周楚强
 * @Date: 2025-08-01
 **/

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoSalesQtyDubboProvider.class)
public class SoSalesQtyDubboProviderImpl implements SoSalesQtyDubboProvider {
    
    @Resource
    private IntlSoQtyDomainService intlSoQtyDomainService;
    
    @Override
    public PageResponse<SalesQtyRespDto> getSalesQtyForPage(SalesQtyReqDto salesQtyReqDto) {
        return intlSoQtyDomainService.queryEs(salesQtyReqDto);
    }
    
    @Override
    public SingleResponse<ReportingRoleRespDto> getQtyReportingRoleData(SalesQtyReqDto salesQtyReqDto) {
        ReportingRoleRespDto reportingRoleRespDto = intlSoQtyDomainService.getQtyReportingRoleData(salesQtyReqDto);
        return SingleResponse.of(reportingRoleRespDto);
        
    }
    
    @Override
    public SingleResponse<ReportingTypeRespDto> getQtyReportingTypeData(SalesQtyReqDto salesQtyReqDto) {
        ReportingTypeRespDto reportingTypeRespDto = intlSoQtyDomainService.getQtyReportingTypeData(salesQtyReqDto);
        return SingleResponse.of(reportingTypeRespDto);
    }

    @Override
    public SingleResponse<String> export(SalesQtyReqDto salesQtyReqDto) {
        return SingleResponse.of(intlSoQtyDomainService.export(salesQtyReqDto));
    }
}
