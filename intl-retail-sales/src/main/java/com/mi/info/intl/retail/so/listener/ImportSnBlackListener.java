package com.mi.info.intl.retail.so.listener;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.rms.RmsStoreTokenApiService;
import com.mi.info.intl.retail.api.rms.dto.RmsApiResponseBody;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist;
import com.mi.info.intl.retail.so.domain.upload.enums.BlackListDataFromEnum;
import com.mi.info.intl.retail.so.domain.upload.enums.SerialNumberEnum;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoSnBlacklistService;
import com.mi.info.intl.retail.utils.Md5Util;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mi.info.intl.retail.exception.ErrorCodes.SYS_ERROR;

/**
 * 导入黑名单监听器
 *
 * <AUTHOR>
 * @date 2025/7/29
 **/
@Slf4j
public class ImportSnBlackListener implements ReadListener<ImportSnBlackListener.SnBlackImportData> {

    private final IntlSoSnBlacklistService intlSoSnBlacklistService;

    private final List<String> hashCountryList;

    private final List<ImportSnBlackListener.SnBlackImportData> importDataList = new ArrayList<>();

    private final RmsStoreTokenApiService rmsStoreTokenApiService;

    private final FdsService fdsService;

    private final CountryTimeZoneApiService countryTimeZoneApiService;

    private final UserInfo userContext;

    private final TransactionTemplate transactionTemplate;

    private final String locale;

    private final String rmsUrl = "/api/data/v9.2/new_UpdateSNBlackListAction";

    /**
     * 获取异常数据文件URL
     *
     * @return 异常数据文件URL，如果没有异常数据则返回空字符串
     */
    // 存储异常数据文件URL
    @Getter
    private String errorDataFileUrl = "";

    public ImportSnBlackListener(IntlSoSnBlacklistService intlSoSnBlacklistService,
            CountryTimeZoneApiService countryTimeZoneApiService,
            RmsStoreTokenApiService rmsStoreTokenApiService,
            TransactionTemplate transactionTemplate,
            FdsService fdsService,
            List<String> hashCountryList,
            UserInfo userContext, String locale) {
        this.intlSoSnBlacklistService = intlSoSnBlacklistService;
        this.countryTimeZoneApiService = countryTimeZoneApiService;
        this.rmsStoreTokenApiService = rmsStoreTokenApiService;
        this.transactionTemplate = transactionTemplate;
        this.fdsService = fdsService;
        this.hashCountryList = hashCountryList;
        this.userContext = userContext;
        this.locale = locale;
    }

    @Override
    public void invoke(SnBlackImportData data, AnalysisContext analysisContext) {
        importDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 进行数据校验，返回校验结果
        ValidationResult validationResult = verifyData(importDataList);

        // 处理成功的数据
        if (!validationResult.getSuccessData().isEmpty()) {
            transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    try {
                        List<IntlSoSnBlacklist> intlSoSnBlacklistList = new ArrayList<>();

                        for (SnBlackImportData snBlackImportData : validationResult.getSuccessData()) {
                            IntlSoSnBlacklist intlSoSnBlacklist = new IntlSoSnBlacklist();
                            intlSoSnBlacklist.setCountryCode(snBlackImportData.getCountryCode());
                            intlSoSnBlacklist.setCountryName(snBlackImportData.getCountry());
                            SerialNumberEnum serialNumber = SerialNumberEnum.getByName(snBlackImportData.getBlackListType());
                            intlSoSnBlacklist.setType(null == serialNumber ? null : serialNumber.getCode());
                            // 转大写
                            String snImei = snBlackImportData.getSnImei().toUpperCase();
                            intlSoSnBlacklist.setSnImei(snImei);
                            intlSoSnBlacklist.setSnhash(Md5Util.generateSha256Hash(snImei));
                            intlSoSnBlacklist.setStatus(Boolean.FALSE);
                            intlSoSnBlacklist.setDataFrom(BlackListDataFromEnum.RETAIL.getCode());
                            intlSoSnBlacklist.setCreatedOn(System.currentTimeMillis());
                            intlSoSnBlacklist.setModifiedOn(System.currentTimeMillis());
                            intlSoSnBlacklist.setCreatedBy(Long.parseLong(userContext.getMiID()));
                            intlSoSnBlacklist.setModifiedBy(Long.parseLong(userContext.getMiID()));

                            intlSoSnBlacklistList.add(intlSoSnBlacklist);
                        }

                        // 批量保存成功数据
                        boolean saveBatch = intlSoSnBlacklistService.saveBatch(intlSoSnBlacklistList);

                        if (!saveBatch) {
                            throw new BizException(SYS_ERROR, "保存成功数据失败");
                        }

                        // 请求同步RMS
                        // 构造符合要求格式的JSON数据,rms接口只支持单条推送
                        for (IntlSoSnBlacklist intlSoSnBlacklist : intlSoSnBlacklistList) {
                            Map<String, Object> data = buildRmsData(intlSoSnBlacklist);
                            log.info("RMS请求数据：{}", data);
                            RmsApiResponseBody responseBody = rmsStoreTokenApiService.httpForRmsResponseBody(userContext.getAreaId(), rmsUrl, data, "POST");
                            log.info("RMS返回结果：{}", responseBody);

                            // 检查RMS返回结果，如果失败则抛出异常触发回滚
                            if (responseBody == null || responseBody.getCode() != 200) {
                                String errorMsg = "RMS同步失败";
                                if (responseBody != null && responseBody.getMessage() != null) {
                                    errorMsg += ": " + responseBody.getMessage();
                                }
                                throw new BizException(SYS_ERROR, errorMsg);
                            }
                        }

                        log.info("成功导入 {} 条黑名单数据", validationResult.getSuccessData().size());
                    } catch (Exception e) {
                        log.error("处理黑名单数据时发生异常，事务将回滚", e);
                        // 标记事务回滚
                        status.setRollbackOnly();
                        throw new BizException(SYS_ERROR, "处理黑名单数据异常");
                    }
                }
            });
        }

        // 处理异常数据，导出到Excel
        if (!validationResult.getErrorData().isEmpty()) {
            exportErrorDataToExcel(validationResult.getErrorData());
            log.warn("发现 {} 条异常数据，已导出到Excel", validationResult.getErrorData().size());
        }
    }


    /**
     * 构造发送给RMS的黑名单数据格式
     *
     * @param intlSoSnBlacklist 需要同步的黑名单数据列表
     * @return 符合RMS要求的数据格式列表
     */
    private Map<String, Object> buildRmsData(IntlSoSnBlacklist intlSoSnBlacklist) throws JsonProcessingException {
        Map<String, Object> rmsData = new HashMap<>();
        rmsData.put("SourceType", "retail");

        Map<String, Object> input = new HashMap<>();
        input.put("retailId", intlSoSnBlacklist.getId());
        input.put("countryCode", intlSoSnBlacklist.getCountryCode());
        input.put("type", intlSoSnBlacklist.getType());
        input.put("snImei", intlSoSnBlacklist.getSnImei());
        input.put("status", 0);
        input.put("dataFrom", intlSoSnBlacklist.getDataFrom());

        ObjectMapper objectMapper = new ObjectMapper();
        String inputObject = objectMapper.writeValueAsString(input);
        rmsData.put("Input", inputObject);

        return rmsData;
    }


    /**
     * 导出异常数据到Excel
     */
    private void exportErrorDataToExcel(List<ErrorData> errorDataList) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile("error_data_", ".xlsx");
            
            excelWriter = EasyExcel.write(tempFile, ErrorData.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("异常数据").build();
            
            excelWriter.write(errorDataList, writeSheet);
            excelWriter.finish();
            
            // 上传到FDS服务器
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            String fileUrl = fdsService.upload("error_data_" + timestamp + ".xlsx", tempFile, true).getUrl();
            log.info("异常数据已上传到FDS服务器: {}", fileUrl);

            // 更新异常数据文件URL
            errorDataFileUrl = fileUrl;
            
        } catch (Exception e) {
            log.error("导出异常数据失败", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 数据校验
     *
     * @param importDataList
     * @return 校验结果
     */
    private ValidationResult verifyData(List<SnBlackImportData> importDataList) {
        List<SnBlackImportData> successData = new ArrayList<>();
        List<ErrorData> errorData = new ArrayList<>();
        // 获取国家信息映射表（忽略大小写）
        List<CountryDTO> countryInfo = countryTimeZoneApiService.getCountryInfo();
        Map<String, CountryDTO> countryDTOMap = countryInfo.stream()
                .collect(Collectors.toMap(
                        country -> country.getCountryName().toLowerCase(),
                        Function.identity(),
                        (v1, v2) -> v1));
        for (SnBlackImportData importData : importDataList) {
            String errorMessage = validateSingleRecord(importData, countryDTOMap);
            
            if (StringUtils.isEmpty(errorMessage)) {
                // 校验成功
                successData.add(importData);
            } else {
                // 校验失败，添加到异常数据列表
                ErrorData errorDataItem = new ErrorData();
                errorDataItem.setCountry(importData.getCountry());
                errorDataItem.setBlackListType(importData.getBlackListType());
                errorDataItem.setSnImei(importData.getSnImei());
                errorDataItem.setErrorMessage(errorMessage);
                errorData.add(errorDataItem);
            }
        }
        
        return new ValidationResult(successData, errorData);
    }

    /**
     * 校验单条记录
     */
    private String validateSingleRecord(SnBlackImportData importData, Map<String, CountryDTO> countryDTOMap) {
        // 1. 基础必填字段校验（先校验空值，避免后续空指针）
        // 1.1 校验国家字段非空
        String country = importData.getCountry();
        if (StringUtils.isEmpty(country)) {
            return "Country cannot be empty (required field).";
        }
        // 1.2 校验黑名单类型非空
        String blackListType = importData.getBlackListType();
        if (StringUtils.isEmpty(blackListType)) {
            return "Blacklist Type cannot be empty (required field).";
        }
        // 1.3 校验SN/IMEI非空
        String snImei = importData.getSnImei();
        if (StringUtils.isEmpty(snImei)) {
            return "SN/IMEI cannot be empty (required field).";
        }

        // 2. 国家合法性校验（从映射表获取国家信息，处理不存在的情况）
        // 统一小写避免大小写匹配问题
        String countryLower = country.toLowerCase();
        CountryDTO countryDTO = countryDTOMap.get(countryLower);
        if (countryDTO == null) {
            return String.format("Country [%s] is invalid (does not exist in system).", country);
        }

        // 提取国家编码变量，减少重复调用
        String countryCode = countryDTO.getCountryCode();
        if (StringUtils.isEmpty(countryCode)) {
            return String.format("Country [%s] has no valid country code (system data exception).", country);
        }

        // 3. 导入国家与前端所选国家一致性校验（提前到前面）
        String selectedLocale = locale;
        if (!selectedLocale.equalsIgnoreCase(countryCode)) {
            return String.format("Country [%s] does not match the selected country [%s].", country, selectedLocale);
        }

        // 4. 特殊场景：前端选择"Global"时的区域限制校验
        if ("Global".equalsIgnoreCase(selectedLocale)) {
            // 用集合存储合法区域编码，避免字符串contains的子串匹配问题（如"IDG"和"IDG123"）
            String localeAreaCode = "1001,IDG,HMT,SEA,TME,LAR,AFR";
            Set<String> validAreaCodes = Arrays.stream(localeAreaCode.split(","))
                    .map(String::trim)
                    .collect(Collectors.toSet());
            if (!validAreaCodes.contains(countryDTO.getAreaCode())) {
                return String.format("Country [%s] (code: %s) is not allowed when selected country is Global. Allowed areas: %s",
                        country, countryCode, localeAreaCode);
            }
        }

        // 5. 黑名单类型合法性校验（提前校验，避免后续无效逻辑）
        if (!"IMEI".equals(blackListType) && !"SN".equals(blackListType)) {
            return "Blacklist Type must be either 'SN' or 'IMEI'.";
        }

        // 6. SN格式校验（仅针对SN类型）
        if ("SN".equals(blackListType) && !snImei.contains("/")) {
            return String.format("SN format is invalid: [%s] (must contain '/').", snImei);
        }

        // 7. 哈希国家特殊规则校验（仅允许导入SN）
        if (hashCountryList.contains(countryCode) && "IMEI".equals(blackListType)) {
            return String.format("Country [%s] (code: %s) is a hash country, only 'SN' type is allowed.",
                    country, countryCode);
        }

        // 校验通过，设置国家编码
        importData.setCountryCode(countryCode);
        return null;
    }





    /**
     * 校验结果类
     */
    @Data
    private static class ValidationResult {

        /**
         * 校验成功的数据
         */
        private final List<SnBlackImportData> successData;

        /**
         * 校验失败的数据
         *
         */
        private final List<ErrorData> errorData;
        

    }

    /**
     * 异常数据类
     */
    @Data
    public static class ErrorData {

        @ExcelProperty("Country")
        private String country;

        @ExcelProperty("Blacklist Type")
        private String blackListType;

        @ExcelProperty("SN/IMEI")
        private String snImei;

        @ExcelProperty("errorMessage")
        private String errorMessage;
    }

    /**
     * 导入数据类
     */
    @Data
    public static class SnBlackImportData {

        /**
         * 国家
         */
        @ExcelProperty("Country")
        private String country;

        /**
         * 黑名单类型
         */
        @ExcelProperty("Blacklist Type")
        private String blackListType;

        /**
         * 串码
         */
        @ExcelProperty("SN/IMEI")
        private String snImei;

        private String countryCode;
    }
}
