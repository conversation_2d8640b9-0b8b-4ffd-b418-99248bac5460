package com.mi.info.intl.retail.so.app.provider.upload.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * IMEI导入Excel数据模型
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Data
public class ImeiImportExcelData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 行号（用于内部处理，不在Excel中显示）
     */
    private Integer rowIndex;

    /**
     * Store Code
     */
    @ExcelProperty("Store Code")
    private String storeCode;

    /**
     * Sales Time
     */
    @ExcelProperty("Sales Time")
    private String salesTime;

    /**
     * IMEI
     */
    @ExcelProperty("IMEI")
    private String imei;

    /**
     * SN
     */
    @ExcelProperty("SN")
    private String sn;

    /**
     * Remark
     */
    @ExcelProperty("Remark")
    private String remark;

    /**
     * Failed Reason（仅在结果文件中使用）
     */
    @ExcelProperty("Failed Reason")
    private String failedReason;
}
