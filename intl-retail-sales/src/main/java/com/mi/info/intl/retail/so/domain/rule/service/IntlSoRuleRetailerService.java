package com.mi.info.intl.retail.so.domain.rule.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.api.so.rule.resp.GetRetailerSoRuleResp;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerStatisticsDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerValidateDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySuRuleRetailerReq;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/25 18:01
 */
public interface IntlSoRuleRetailerService extends IService<IntlSoRuleRetailer> {

    /**
     * 根据国家编码和类别查询零售商规则列表
     *
     * @param countryCode 国家编码
     * @param category 类别
     * @return 返回当前国家下零售商规则列表，根据类别查询是否主数据，或是变更时创建的记录
     */
    List<IntlSoRuleRetailer> getByCountryCodeAndCategory(String countryCode, Integer category);

    /**
     * 查询是否存在imei&qty都未启用的零售商
     *
     * @param retailerValidateDto 校验参数
     * @return true:存在 false:不存在
     */
    Boolean existsBothDisabledImeiAndQtySwitch(SoRuleRetailerValidateDTO retailerValidateDto);

    /**
     * 获取零售商规则
     *
     * @param req req
     * @return {@link GetRetailerSoRuleResp }
     */
    GetRetailerSoRuleResp getRetailerSoRule(GetRetailerSoRuleReq req);

    /**
     * 根据国家编码和创建人id，及传入的零售商编码查询零售商规则
     *
     * @param countryCode 国家编码
     * @param category 类别
     * @param retailerCodes 零售商编码列表
     * @return 零售商规则列表
     */
    List<IntlSoRuleRetailer> getByCountryCodeAndRetailerCodes(String countryCode, Integer category,
                                                              List<String> retailerCodes);

    /**
     * 根据条件获取零售商
     *
     * @param req req
     * @return {@link List }<{@link SoRuleRetailerDTO }>
     */
    IPage<SoRuleRetailerDTO> getRetailerByCondition(QuerySuRuleRetailerReq req);

    /**
     * 出口零售商列表
     *
     * @param req req
     * @return {@link String }
     */
    String exportRetailerList(QuerySuRuleRetailerReq req);

    /**
     * 根据传入的零售商规则创建的type值，返回SoRuleEnum枚举值
     * 1：代表新增，创建规则场景，将零售商规则分类设置为MASTER
     * 2：代表修改场景下新增，修改规则场景，将零售商规则分类设置为COPY
     *
     * @param type 零售商规则创建的type值
     * @return SoRuleEnum
     */
    SoRuleEnum getRetailerCategoryByCreateType(Integer type);

    /**
     * 启动所有国家零售商
     */
    void initAllCountryRetailer();

    /**
     * 处理增量零售商
     */
    void dealIncrementRetailer();

    /**
     * 统计retailer规则总数、启用的imei上报总数、启用的qty上报总数，以及禁用imei & qty上报总数
     *
     * @param retailerList 零售商列表
     * @return {@link SoRuleRetailerStatisticsDTO }
     */
    SoRuleRetailerStatisticsDTO getRetailerStatistics(List<IntlSoRuleRetailer> retailerList);

    /**
     * 根据soRuleDetailLogId导出当前变更的零售商列表，生成excel并上传到fds，用于审批流程
     *
     * @param soRuleDetailLogId 规则变更日志id
     * @param countryCode 国家编码
     * @return 上传到fds的excel文件的url
     */
    String exportRetailerForApproveFlow(Long soRuleDetailLogId, String countryCode);
}
