package com.mi.info.intl.retail.so.domain.rule.bpm;

import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BusinessBpmCallBack;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleApproveCallbackDTO;
import com.mi.info.intl.retail.so.domain.rule.aggregate.SoRuleAggregateService;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import javax.annotation.Resource;

/**
 * 处理so规则审批回调逻辑
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SoRuleBpmCallBack implements BusinessBpmCallBack {

    /**
     * 审批通过
     */
    private static final String AGREE = "agree";

    /**
     * 审批拒绝
     */
    private static final String REJECT = "reject";

    /**
     * 一级审批节点key
     */
    private static final String APPROVE_NODE1 = "Activity_1ewz0u3";

    /**
     * 二级审批节点key
     */
    private static final String APPROVE_NODE2 = "Activity_0hn92dr";

    /**
     * 发起节点
     */
    public static final int NODE0 = 0;

    /**
     * 审批节点1
     */
    public static final int NODE1 = 1;

    /**
     * 审批节点2
     */
    public static final int NODE2 = 2;

    @Resource
    private SoRuleAggregateService soRuleAggregateService;

    @Override
    public BpmApproveBusinessCodeEnum getBpmApproveTypeEnum() {
        return BpmApproveBusinessCodeEnum.SO_RULE;
    }

    @Override
    public void doCallback(BpmCallBackParamDto bpmCallBack) {
        log.info("SoApproveCallBack_response:{}", bpmCallBack);
        SoRuleApproveCallbackDTO soRuleApproveCallback =
                ComponentLocator.getConverter().convert(bpmCallBack, SoRuleApproveCallbackDTO.class);
        int approveNode = NODE0;
        if (Objects.equals(bpmCallBack.getTaskDefinitionKey(), APPROVE_NODE1)) {
            approveNode = NODE1;
        } else if (Objects.equals(bpmCallBack.getTaskDefinitionKey(), APPROVE_NODE2)) {
            approveNode = NODE2;
        }
        soRuleApproveCallback.setApproveNode(approveNode);
        if (Objects.equals(bpmCallBack.getStatus(), REJECT)) {
            // 审批拒绝
            soRuleApproveCallback.setApproveStatus(SoRuleDetailApproveStatus.REJECTED.getValue());
            soRuleAggregateService.approveRuleRejected(soRuleApproveCallback);
        } else if (Objects.equals(bpmCallBack.getStatus(), AGREE)) {
            soRuleApproveCallback.setApproveStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
            if (Objects.equals(bpmCallBack.getTaskDefinitionKey(), APPROVE_NODE1)) {
                // 一级审批通过，中间节点通过
                soRuleAggregateService.approveRuleAgree(soRuleApproveCallback);
            } else if (Objects.equals(bpmCallBack.getTaskDefinitionKey(), APPROVE_NODE2)) {
                // 二级审批通过，终审通过
                soRuleAggregateService.approveRuleCompleted(soRuleApproveCallback);
            } else {
                throw new BizException(ErrorCodes.SYS_ERROR,
                        String.format("Not support taskDefinitionKey : %s", bpmCallBack.getTaskDefinitionKey()));
            }
        } else {
            throw new BizException(ErrorCodes.SYS_ERROR,
                    String.format("Not support status : %s", bpmCallBack.getStatus()));
        }
    }
}


