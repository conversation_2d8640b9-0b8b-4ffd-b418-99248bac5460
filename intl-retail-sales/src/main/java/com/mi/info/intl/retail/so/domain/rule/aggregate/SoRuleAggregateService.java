package com.mi.info.intl.retail.so.domain.rule.aggregate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.fieldforce.user.IntlRmsUserApiService;
import com.mi.info.intl.retail.dto.IntlRmsUserDTO;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BpmService;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmUser;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.SoRuleFormDataEntity;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ApproverDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ApproverInfo;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ImeiRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.PhotoRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ProductLineItemDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleApproveCallbackDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailCreateDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailModifyDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailModifyRecallDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailQueryDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailResultDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerBatchCreateDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerItemDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerValidateDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.UserTitleItemDTO;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.domain.rule.bpm.SoRuleBpmCallBack;
import com.mi.info.intl.retail.so.domain.rule.constants.CommonConstants;
import com.mi.info.intl.retail.so.domain.rule.constants.SoRuleErrorMessage;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetailLog;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;
import com.mi.info.intl.retail.so.util.ConvertUtils;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.cnzone.commons.utils.DateUtils;
import com.xiaomi.newretail.common.tools.utils.UUIDUtils;

import cn.hutool.core.lang.Tuple;
import lombok.extern.slf4j.Slf4j;

/**
 * so规则创建、修改聚合服务
 *
 * <AUTHOR>
 * @date 2025/7/28 16:27
 */
@Slf4j
@Service
public class SoRuleAggregateService {

    /**
     * 规则最大条数
     */
    private static final int MAX_RULES = 10;

    /**
     * 有效期最大间隔
     */
    private static final int MAX_EFFECTIVE_INTERVAL = 100;

    /**
     * 有效期最小间隔
     */
    private static final int MIN_EFFECTIVE_INTERVAL = 1;

    @Resource
    private IntlSoRuleDetailService intlSoRuleDetailService;

    @Resource
    private IntlSoRuleChangeLogService intlSoRuleChangeLogService;

    @Resource
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Resource
    private DistributionLockService distributionLockService;

    @Resource
    private SoRuleTransactionalAggregateService soRuleTransactionalAggregateService;

    @Resource
    private IntlRetailerApiService intlRetailerApiService;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private BpmService bpmService;

    @Resource
    private IntlRmsUserApiService intlRmsUserApiService;

    public IPage<SoRuleDetailResultDTO> getRuleList(SoRuleDetailQueryDTO queryDto) {
        queryDto.setPageNum(Objects.nonNull(queryDto.getPageNum()) ? queryDto.getPageNum() : 1);
        queryDto.setPageSize(Objects.nonNull(queryDto.getPageSize()) ? queryDto.getPageSize() : 20);
        return intlSoRuleDetailService.pageList(queryDto);
    }

    /**
     * 校验当前国家下是否存在IMEI&QTY都未启用的零售商记录，若存在，返回false，不存在返回true
     *
     * @param retailerValidateDto 国家编码
     * @return true:不存在IMEI&QTY都未启用的零售商记录；false:存在
     */
    public Boolean validateRuleRetailers(SoRuleRetailerValidateDTO retailerValidateDto) {
        validateCountryCode(retailerValidateDto.getCountryCode());
        return !intlSoRuleRetailerService.existsBothDisabledImeiAndQtySwitch(retailerValidateDto);
    }

    /**
     * 获取规则详情
     *
     * @param ruleId 规则ID
     * @return 规则详情
     */
    public SoRuleDetailResultDTO getRuleDetail(Long ruleId) {
        IntlSoRuleDetail ruleDetail = intlSoRuleDetailService.getById(ruleId);
        if (Objects.isNull(ruleDetail)) {
            throw new BizException(ErrorCodes.SO_RULE_NOT_EXIST, ruleId);
        }
        return intlSoRuleDetailService.convertToRuleDetailResult(ruleDetail);
    }

    /**
     * 创建规则流程
     *
     * @param soRuleDetailCreateDto 规则创建参数
     * @return 规则ID
     */
    public CommonApiResponse<Long> createRuleFlow(@NotNull SoRuleDetailCreateDTO soRuleDetailCreateDto) {
        // 必填参数校验
        validateCountryCode(soRuleDetailCreateDto.getCountryCode());

        List<ImeiRuleDTO> imeiRuleList = soRuleDetailCreateDto.getImeiRuleList();
        List<PhotoRuleDTO> photoRuleList = soRuleDetailCreateDto.getPhotoRuleList();
        // imei规则校验
        String message = validateImeiRules(imeiRuleList);
        if (StringUtils.isNotBlank(message)) {
            return CommonApiResponse.failure(ErrorCodes.SYS_ERROR.getCode(), message);
        }
        // 图片规则校验
        message = validatePhotoRules(photoRuleList);
        if (StringUtils.isNotBlank(message)) {
            return CommonApiResponse.failure(ErrorCodes.SYS_ERROR.getCode(), message);
        }
        // 加分布式锁，处理创建规则逻辑
        try (DistributionLock ignore =
            distributionLockService.tryLock("so.rule.create.lock-{0}", soRuleDetailCreateDto.getCountryCode())) {
            // 校验当前国家是否已经存在规则，若存在，则返回错误提示
            boolean existRule = intlSoRuleDetailService.existRule(soRuleDetailCreateDto.getCountryCode());
            if (existRule) {
                return CommonApiResponse.failure(ErrorCodes.SYS_ERROR.getCode(),
                    SoRuleErrorMessage.COUNTRY_EXISTS_RULE);
            }
            Long ruleId = soRuleTransactionalAggregateService.createRule(soRuleDetailCreateDto);
            // 返回当前规则ID
            return CommonApiResponse.success(ruleId);
        }
    }

    public CommonApiResponse<Long> modifyRuleFlow(@NotNull SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        // 校验必填项
        validateCountryCode(soRuleDetailModifyDto.getCountryCode());

        List<ImeiRuleDTO> imeiRuleList = soRuleDetailModifyDto.getImeiRuleList();
        List<PhotoRuleDTO> photoRuleList = soRuleDetailModifyDto.getPhotoRuleList();
        // imei规则校验
        String message = validateImeiRules(imeiRuleList);
        if (StringUtils.isNotBlank(message)) {
            return CommonApiResponse.failure(ErrorCodes.SYS_ERROR.getCode(), message);
        }
        // 图片规则校验
        message = validatePhotoRules(photoRuleList);
        if (StringUtils.isNotBlank(message)) {
            return CommonApiResponse.failure(ErrorCodes.SYS_ERROR.getCode(), message);
        }
        Long ruleCopyId;
        try (DistributionLock ignore =
            distributionLockService.tryLock("so.rule.modify.lock-{0}", soRuleDetailModifyDto.getCountryCode())) {
            // 是否存在审批中的规则修改流程，若存在，则返回错误提示
            boolean existRule = intlSoRuleChangeLogService.existApprovingRule(soRuleDetailModifyDto.getCountryCode());
            if (existRule) {
                return CommonApiResponse.failure(ErrorCodes.SYS_ERROR.getCode(),
                    SoRuleErrorMessage.COUNTRY_EXISTS_APPROVING_RULE);
            }
            ruleCopyId = soRuleTransactionalAggregateService.modifyRule(soRuleDetailModifyDto);
        }
        // 发起审批流程
        approveRuleLaunch(soRuleDetailModifyDto, ruleCopyId);
        return CommonApiResponse.success(ruleCopyId);
    }

    /**
     * 批量新增SO规则零售商
     *
     * @param retailerBatchCreateDto 请求参数
     */
    public void addSoRuleRetailerList(@NotNull SoRuleRetailerBatchCreateDTO retailerBatchCreateDto) {
        // 校验必填参数
        Assert.notNull(retailerBatchCreateDto.getType(), "Type can't be null.");
        validateCountryCode(retailerBatchCreateDto.getCountryCode());

        // 登录用户账号
        UserInfo userInfo = UserInfoUtil.getUserContext();

        // retailerList参数，为空的话，初始化为空列表
        List<SoRuleRetailerItemDTO> retailerList = retailerBatchCreateDto.getRetailerList();
        retailerList = CollectionUtils.isNotEmpty(retailerList) ? retailerList : Lists.newArrayList();
        retailerBatchCreateDto.setRetailerList(retailerList);

        // 校验传入的零售商是否在当前国家零售商范围内
        List<String> retailerCodes =
            retailerList.stream().map(SoRuleRetailerItemDTO::getRetailerCode).collect(Collectors.toList());
        List<IntlRetailerDTO> retailerInfoList =
            intlRetailerApiService.getRetailerListByCountryCode(retailerBatchCreateDto.getCountryCode());
        Map<String, IntlRetailerDTO> retailerMap =
            retailerInfoList.stream().collect(Collectors.toMap(IntlRetailerDTO::getRetailerCode, Function.identity()));
        if (retailerCodes.stream().anyMatch(it -> !retailerMap.containsKey(it))) {
            throw new BizException(ErrorCodes.SYS_ERROR, "RetailerCode is not in the current country.");
        }

        if (Objects.equals(retailerBatchCreateDto.getType(), 1)) {
            // type = 1 新增，先初始化零售商数据， 再根据传入的规则更新零售商规则
            doCreateRuleWithRetailers(retailerBatchCreateDto, retailerInfoList, userInfo);
        } else if (Objects.equals(retailerBatchCreateDto.getType(), 2)) {
            // type = 2 修改时新增。
            // 若allAddImei或allAddQty不为空，则需要全量初始化一次零售商副本数据。再根据传入的规则更新零售商规则
            doModifyRuleWithRetailers(retailerBatchCreateDto, retailerInfoList, userInfo);
        } else {
            throw new BizException(ErrorCodes.SYS_ERROR, "Operate type is not supported.");
        }
    }

    /**
     * 发起审批流程
     *
     * @param soRuleDetailModifyDto 规则修改参数
     * @param ruleCopyId 规则副本ID
     */
    public void approveRuleLaunch(@NotNull SoRuleDetailModifyDTO soRuleDetailModifyDto, Long ruleCopyId) {
        IntlSoRuleDetailLog ruleDetailLog = intlSoRuleChangeLogService.getById(ruleCopyId);
        UserInfo userInfo = UserInfoUtil.getUserContext();
        SoRuleFormDataEntity soRuleFormData = new SoRuleFormDataEntity();
        // 此处不可能为空，前面已校验
        CountryDTO countryInfo =
            countryTimeZoneApiService.getCountryInfoByCode(ruleDetailLog.getCountryCode()).orElse(new CountryDTO());
        // Task Name : SO Rules Modification (Country Name)
        soRuleFormData.setTaskName(String.format("SO Rules Modification (%s)", countryInfo.getCountryName()));
        // 申请人：name(id)
        soRuleFormData.setApplicant(String.format("%s(%s)", userInfo.getUserName(), userInfo.getMiID()));
        // 申请时间
        soRuleFormData.setApplicationTime(IntlTimeUtil.getOffsetDateTimeByCountryCode(ruleDetailLog.getCountryCode()));
        // imei和photo规则
        setImeiAndPhotoRule(soRuleFormData, ruleDetailLog);
        // 零售商规则列表excel数据下载链接
        setRetailerRuleDataLink(soRuleFormData, ruleDetailLog);
        // 两个节点的所选审批人，邮箱前缀
        Tuple approvers = getApproverInfo(soRuleDetailModifyDto);
        // 生成businessKey
        String businessKey = UUIDUtils.randomUUID();
        // 将所选节点审批人放入变量中
        HashMap<String, Object> variables = Maps.newHashMap();
        variables.put("userFirst", approvers.get(0));
        variables.put("userSecond", approvers.get(1));
        try {
            log.info("So rule modify approve launch. formData: {}, variables: {}", soRuleFormData, variables);
            bpmService.create(soRuleFormData, BpmApproveBusinessCodeEnum.SO_RULE,
                UserInfoUtil.getEmailPrefix(userInfo.getEmail()), businessKey, variables);
        } catch (Exception e) {
            log.error("So rule modify approve launch failed. id: {}", ruleCopyId, e);
            throw new BizException(ErrorCodes.SO_RULE_LOG_APPROVAL_FAILED, e.getMessage());
        }
        // 审批流程ID，绑定审批流程ID
        ruleDetailLog.setApprovalId(businessKey);
        // 审批表单数据
        ruleDetailLog.setBpmBody(JSON.toJSONString(soRuleFormData));
        // 发起人ID
        String approveId = UserInfoUtil.getUserContext().getMiID();
        soRuleTransactionalAggregateService.approveRuleLaunch(ruleDetailLog, approveId);
    }

    /**
     * 发起审批流程时，将当前修改的零售商规则副本数据，生成excel文件并上传到fds，并返回文档下载链接，设置到审批表单中。
     *
     * @param soRuleFormData 审批表单
     * @param ruleDetailLog ruleDetailLog
     */
    private void setRetailerRuleDataLink(SoRuleFormDataEntity soRuleFormData, IntlSoRuleDetailLog ruleDetailLog) {
        String link = intlSoRuleRetailerService.exportRetailerForApproveFlow(ruleDetailLog.getId(),
            ruleDetailLog.getCountryCode());
        soRuleFormData.setLink(link);
    }

    /**
     * 发起审批表单数据，设置imei和photo规则
     *
     * @param soRuleFormData 审批表单
     * @param ruleDetailLog ruleDetailLog
     */
    private void setImeiAndPhotoRule(SoRuleFormDataEntity soRuleFormData, IntlSoRuleDetailLog ruleDetailLog) {
        List<ImeiRuleDTO> imeiRuleList = StringUtils.isNotBlank(ruleDetailLog.getImeiRuleList())
            ? JSON.parseArray(ruleDetailLog.getImeiRuleList(), ImeiRuleDTO.class) : Lists.newArrayList();
        List<PhotoRuleDTO> photoRuleList = StringUtils.isNotBlank(ruleDetailLog.getPhotoRuleList())
            ? JSON.parseArray(ruleDetailLog.getPhotoRuleList(), PhotoRuleDTO.class) : Lists.newArrayList();
        List<SoRuleFormDataEntity.ImeiActivationRule> imeiRuleTab = imeiRuleList.stream().map(it -> {
            List<UserTitleItemDTO> userTitleList =
                CollectionUtils.isNotEmpty(it.getUserTitleList()) ? it.getUserTitleList() : Lists.newArrayList();
            String userTitles =
                userTitleList.stream().map(UserTitleItemDTO::getUserTitle).collect(Collectors.joining(","));
            List<ProductLineItemDTO> productLineList =
                CollectionUtils.isNotEmpty(it.getProductLineList()) ? it.getProductLineList() : Lists.newArrayList();
            String productLines =
                productLineList.stream().map(ProductLineItemDTO::getProductLine).collect(Collectors.joining(","));
            SoRuleFormDataEntity.ImeiActivationRule rule = new SoRuleFormDataEntity.ImeiActivationRule();
            rule.setUserTitle(userTitles);
            rule.setCategory(productLines);
            rule.setTimeFrom(String.valueOf(it.getBefore()));
            rule.setTimeTo(String.valueOf(it.getAfter()));
            return rule;
        }).collect(Collectors.toList());

        List<SoRuleFormDataEntity.RequiringPhotoRule> photoRuleTab = photoRuleList.stream().map(it -> {
            List<UserTitleItemDTO> userTitleList =
                CollectionUtils.isNotEmpty(it.getUserTitleList()) ? it.getUserTitleList() : Lists.newArrayList();
            String userTitles =
                userTitleList.stream().map(UserTitleItemDTO::getUserTitle).collect(Collectors.joining(","));
            SoRuleFormDataEntity.RequiringPhotoRule photoRuleTable = new SoRuleFormDataEntity.RequiringPhotoRule();
            photoRuleTable.setUserTitle(userTitles);
            photoRuleTable.setImeiRequirePhoto(SwitchEnum.convert(it.getImeiRequirePhoto()).getDescription());
            photoRuleTable.setQtyRequirePhoto(SwitchEnum.convert(it.getQtyRequirePhoto()).getDescription());
            return photoRuleTable;
        }).collect(Collectors.toList());

        soRuleFormData.setImeiActivationRuleList(imeiRuleTab);
        soRuleFormData.setRequiringPhotoRuleList(photoRuleTab);
    }

    /**
     * 发起审批流程时，获取所选节点审批人信息
     *
     * @param soRuleDetailModifyDto 规则修改参数
     */
    private Tuple getApproverInfo(SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        List<ApproverDTO> approverList = CollectionUtils.isNotEmpty(soRuleDetailModifyDto.getApproverList())
            ? soRuleDetailModifyDto.getApproverList() : Lists.newArrayList();
        ApproverDTO firstApprover = CollectionUtils.isNotEmpty(approverList) ? approverList.get(0) : new ApproverDTO();
        ApproverInfo firstApproverInfo = CollectionUtils.isNotEmpty(firstApprover.getApproverList())
            ? firstApprover.getApproverList().get(0) : new ApproverInfo();
        ApproverDTO secondApprover = approverList.size() >= 2 ? approverList.get(1) : new ApproverDTO();
        ApproverInfo secondApproverInfo = CollectionUtils.isNotEmpty(secondApprover.getApproverList())
            ? secondApprover.getApproverList().get(0) : new ApproverInfo();

        List<Long> approverIds = Lists.newArrayList(firstApproverInfo.getUserId(), secondApproverInfo.getUserId())
            .stream().filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toList());
        Map<String, IntlRmsUserDTO> userMap = intlRmsUserApiService.getRmsUserByMiIds(approverIds).stream()
            .collect(Collectors.toMap(it -> String.valueOf(it.getMiId()), Function.identity()));
        IntlRmsUserDTO firstUser = userMap.get(firstApproverInfo.getUserId());
        IntlRmsUserDTO secondUser = userMap.get(secondApproverInfo.getUserId());
        String userFirst = Objects.nonNull(firstUser) ? UserInfoUtil.getEmailPrefix(firstUser.getEmail()) : null;
        String userSecond = Objects.nonNull(secondUser) ? UserInfoUtil.getEmailPrefix(secondUser.getEmail()) : null;
        return new Tuple(userFirst, userSecond);
    }

    /**
     * 审批通过回调，中间节点通过
     *
     * @param bpmCallBack 审批回调参数
     */
    public void approveRuleAgree(SoRuleApproveCallbackDTO bpmCallBack) {
        IntlSoRuleDetailLog ruleDetailLog = getAndValidateRuleDetailLog(bpmCallBack);
        IntlSoRuleDetail masterRule = getAndValidateMasterRule(ruleDetailLog.getMasterId());
        soRuleTransactionalAggregateService.approveRuleAgree(bpmCallBack, ruleDetailLog, masterRule);
    }

    /**
     * 审批完成回调 审批完成后，将当前修改规则的副本数据，更新到主规则数据中。并更新审批状态
     *
     * @param approveCompletedDTO 审批完成回调参数
     */
    public void approveRuleCompleted(SoRuleApproveCallbackDTO approveCompletedDTO) {
        IntlSoRuleDetailLog ruleDetailLog = getAndValidateRuleDetailLog(approveCompletedDTO);
        IntlSoRuleDetail masterRule = getAndValidateMasterRule(ruleDetailLog.getMasterId());
        soRuleTransactionalAggregateService.approveRuleCompleted(approveCompletedDTO, ruleDetailLog, masterRule);
    }

    /**
     * 审批拒绝回调
     *
     * @param bpmCallBack 审批拒绝回调参数
     */
    public void approveRuleRejected(SoRuleApproveCallbackDTO bpmCallBack) {
        IntlSoRuleDetailLog ruleDetailLog = getAndValidateRuleDetailLog(bpmCallBack);
        IntlSoRuleDetail masterRule = getAndValidateMasterRule(ruleDetailLog.getMasterId());
        soRuleTransactionalAggregateService.approveRuleRejected(bpmCallBack, ruleDetailLog, masterRule);
    }

    /**
     * 审批撤回回调
     *
     * @param modifyRecall 审批撤回回调参数
     */
    public void approveRecalled(SoRuleDetailModifyRecallDTO modifyRecall) {
        Assert.notNull(modifyRecall.getId(), "Id can not be null.");
        UserInfo userInfo = UserInfoUtil.getUserContext();
        // 获取规则变更记录
        IntlSoRuleDetailLog ruleDetailLog = intlSoRuleChangeLogService.getById(modifyRecall.getId());
        if (Objects.isNull(ruleDetailLog)) {
            throw new BizException(ErrorCodes.SO_RULE_LOG_NOT_EXIST, modifyRecall.getId());
        }
        // 只有审批中、被驳回的流程，才能撤回
        if (SoRuleDetailApproveStatus.HAS_PENDING_APPROVAL_NO.contains(ruleDetailLog.getStatus())) {
            throw new BizException(ErrorCodes.SO_RULE_LOG_STATUS_ILLEGAL,
                String.format("id=%s, status=%s", ruleDetailLog.getId(), ruleDetailLog.getStatus()));
        }

        // 调用bpm撤回接口
        try {
            log.info("Recall so rule approve flow. id: {}, approvalId: {}", ruleDetailLog.getId(),
                ruleDetailLog.getApprovalId());
            bpmService.recall(ruleDetailLog.getApprovalId(), UserInfoUtil.getEmailPrefix(userInfo.getEmail()),
                modifyRecall.getComment());
        } catch (Exception e) {
            log.error("Recall so rule approve flow failed.  id: {}, approvalId: {}", ruleDetailLog.getId(),
                ruleDetailLog.getApprovalId(), e);
            throw new BizException(ErrorCodes.SO_RULE_LOG_APPROVAL_FAILED, ruleDetailLog.getId());
        }
        // 转换成SoRuleApproveCallbackDTO，复用相应逻辑
        SoRuleApproveCallbackDTO approveCallback = new SoRuleApproveCallbackDTO();
        approveCallback.setBusinessKey(String.valueOf(modifyRecall.getId()));
        approveCallback.setApproveStatus(SoRuleDetailApproveStatus.RECALLED.getValue());
        approveCallback.setComment(modifyRecall.getComment());
        BpmUser approver = new BpmUser();
        approver.setPersonId(userInfo.getUserId());
        approver.setUserName(userInfo.getUserName());
        approveCallback.setAssignee(approver);
        // 更新规则副本记录状态
        IntlSoRuleDetail masterRule = getAndValidateMasterRule(ruleDetailLog.getMasterId());
        soRuleTransactionalAggregateService.approveRuleRecalled(approveCallback, ruleDetailLog, masterRule);
    }

    private IntlSoRuleDetail getAndValidateMasterRule(Long masterId) {
        IntlSoRuleDetail masterRule = intlSoRuleDetailService.getById(masterId);
        if (Objects.isNull(masterRule)) {
            throw new BizException(ErrorCodes.SO_RULE_NOT_EXIST, masterId);
        }
        return masterRule;
    }

    private IntlSoRuleDetailLog getAndValidateRuleDetailLog(SoRuleApproveCallbackDTO approveCallback) {
        Assert.isTrue(StringUtils.isNotBlank(approveCallback.getBusinessKey()), "BusinessKey is null.");
        String approvalId = approveCallback.getBusinessKey();
        IntlSoRuleDetailLog ruleDetailLog = intlSoRuleChangeLogService.getByApprovalId(approvalId);
        if (Objects.isNull(ruleDetailLog)) {
            throw new BizException(ErrorCodes.SO_RULE_LOG_NOT_EXIST, approvalId);
        }
        // 校验状态是否合法
        if (!Objects.equals(ruleDetailLog.getStatus(), SoRuleDetailApproveStatus.PENDING.getValue())) {
            throw new BizException(ErrorCodes.SO_RULE_LOG_STATUS_ILLEGAL,
                String.format("id=%s, status=%s", ruleDetailLog.getId(), ruleDetailLog.getStatus()));
        }
        // 解析节点审批人信息，更新审批节点状态
        updateApproveNodeStatus(ruleDetailLog, approveCallback);
        return ruleDetailLog;
    }

    /**
     * 更新审批节点状态
     *
     * @param ruleDetailLog 规则变更记录
     * @param approveCallback 审批回调参数
     */
    private void updateApproveNodeStatus(IntlSoRuleDetailLog ruleDetailLog, SoRuleApproveCallbackDTO approveCallback) {
        // 获取审批人信息
        BpmUser operator = Objects.nonNull(approveCallback.getAssignee()) ? approveCallback.getAssignee()
            : approveCallback.getOperator();
        if (Objects.isNull(operator)) {
            return;
        }
        String approverStr = ruleDetailLog.getApproverList();
        List<ApproverDTO> list = JSON.parseArray(approverStr, ApproverDTO.class);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 更新对应节点的审批状态
        if (Objects.equals(approveCallback.getApproveNode(), SoRuleBpmCallBack.NODE1)) {
            list.get(0).setStatus(approveCallback.getApproveStatus());
            // 备注信息
            list.get(0).setComment(approveCallback.getComment());
        } else if (Objects.equals(approveCallback.getApproveNode(), SoRuleBpmCallBack.NODE2) && list.size() >= 2) {
            list.get(1).setStatus(approveCallback.getApproveStatus());
            list.get(1).setComment(approveCallback.getComment());
        }
        // 重新更新审批节点信息
        ruleDetailLog.setApproverList(JSON.toJSONString(list));
    }

    /**
     * 校验photo规则列表
     *
     * @param photoRuleList photo规则列表
     * @return 校验错误信息
     */
    private String validatePhotoRules(List<PhotoRuleDTO> photoRuleList) {
        if (CollectionUtils.isEmpty(photoRuleList)) {
            return null;
        }
        // 校验最多允许添加10条规则
        if (photoRuleList.size() > MAX_RULES) {
            return String.format(SoRuleErrorMessage.RULES_GREATER_THAN_MAX, MAX_RULES);
        }
        // 判重：检测Requiring Photo Rules 中 User Title是否存在重复
        // 解释：即不同规则里，User Title不能重复
        Map<PhotoRuleDTO, Set<String>> cartesianMap = Maps.newHashMap();
        for (PhotoRuleDTO photoRule : photoRuleList) {
            Set<String> combSet =
                photoRule.getUserTitleList().stream().map(UserTitleItemDTO::getUserTitleId).collect(Collectors.toSet());
            for (Set<String> combs : cartesianMap.values()) {
                if (combs.stream().anyMatch(combSet::contains)) {
                    return SoRuleErrorMessage.PHOTO_RULE_REPEAT;
                }
            }
            cartesianMap.put(photoRule, combSet);
        }
        return null;
    }

    /**
     * imei规则校验
     *
     * @param imeiRuleList imei规则列表
     * @return 校验错误信息
     */
    private String validateImeiRules(List<ImeiRuleDTO> imeiRuleList) {
        if (CollectionUtils.isEmpty(imeiRuleList)) {
            return null;
        }
        // 校验最多允许添加10条规则
        if (imeiRuleList.size() > MAX_RULES) {
            return String.format(SoRuleErrorMessage.RULES_GREATER_THAN_MAX, MAX_RULES);
        }

        // 判重，校验IMEI Activation Rules中 User Title，Activation Product Line两项是否存在重复规则
        // 解释：即不同规则里， User Title，Activation Product Line不能同时重复

        // 每个规则里，需要将userTitle和productLine进行组合，然后跟其他规则进行比较，如果存在重复，则校验失败
        Map<ImeiRuleDTO, Set<String>> cartesianMap = Maps.newHashMap();

        for (ImeiRuleDTO imeiRule : imeiRuleList) {
            final List<UserTitleItemDTO> userTitleList = CollectionUtils.isNotEmpty(imeiRule.getUserTitleList())
                ? imeiRule.getUserTitleList() : Lists.newArrayList();
            final List<ProductLineItemDTO> productLineList = CollectionUtils.isNotEmpty(imeiRule.getProductLineList())
                ? imeiRule.getProductLineList() : Lists.newArrayList();
            // 将userTitle和productLine进行组合；
            Set<String> combSet = userTitleList.stream()
                .flatMap(userTitleItem -> productLineList.stream()
                    .map(productLineItem -> userTitleItem.getUserTitleId() + productLineItem.getProductLineId()))
                .collect(Collectors.toSet());
            // 判断在cartesianMap中某个规则的userTitle和productLine组合是否存在交集（重复），若存在，则校验失败，返回错误。
            for (Set<String> combs : cartesianMap.values()) {
                if (combs.stream().anyMatch(combSet::contains)) {
                    return SoRuleErrorMessage.IMEI_RULE_REPEAT;
                }
            }
            // 若当前规则与前面的规则没有重复项，则将当前规则的userTitle和productLine组合放入cartesianMap中
            cartesianMap.put(imeiRule, combSet);
        }
        // Activate Time From， Activate Time To 范围在1~100
        for (ImeiRuleDTO it : imeiRuleList) {
            if (invalidActivationTime(it)) {
                return SoRuleErrorMessage.ACTIVATE_TIME_RANGE_ERROR;
            }
        }
        return null;
    }

    /**
     * 校验激活时间范围是否有效
     *
     * @param imeiRule imei规则
     * @return 校验结果， 若为true，表示校验失败，若为false，表示校验成功
     */
    private boolean invalidActivationTime(ImeiRuleDTO imeiRule) {
        boolean activationTimeBeginError = Objects.nonNull(imeiRule.getBefore())
            && (imeiRule.getBefore() > MAX_EFFECTIVE_INTERVAL || imeiRule.getBefore() < MIN_EFFECTIVE_INTERVAL);
        boolean activationTimeEndError = Objects.nonNull(imeiRule.getAfter())
            && (imeiRule.getAfter() > MAX_EFFECTIVE_INTERVAL || imeiRule.getAfter() < MIN_EFFECTIVE_INTERVAL);
        return activationTimeBeginError || activationTimeEndError;
    }

    /**
     * 校验国家编码是否存在
     *
     * @param countryCode 国家编码
     */
    private void validateCountryCode(String countryCode) {
        Assert.isTrue(StringUtils.isNotBlank(countryCode), "CountryCode can't be blank.");
        Optional<CountryDTO> countryOptional = countryTimeZoneApiService.getCountryInfoByCode(countryCode);
        if (!countryOptional.isPresent()) {
            log.error("country code {} not exist ", countryCode);
            throw new BizException(ErrorCodes.COUNTRY_CODE_NOT_EXIST, countryCode);
        }
    }

    /**
     * 处理修改规则时，初始化零售商规则
     *
     * @param retailerBatchCreateDto 零售商规则配置对象
     * @param retailerInfoList 零售商信息列表
     * @param userInfo 登录用户
     */
    private void doModifyRuleWithRetailers(@NotNull SoRuleRetailerBatchCreateDTO retailerBatchCreateDto,
        List<IntlRetailerDTO> retailerInfoList, UserInfo userInfo) {
        // 若需要全量开启或关闭imei、qty，则需要全量初始化零售商副本数据
        boolean needInit = Objects.nonNull(retailerBatchCreateDto.getAllAddImei())
            || Objects.nonNull(retailerBatchCreateDto.getAllAddQty());
        List<IntlSoRuleRetailer> dbRetailerList =
            initSoRuleRetailerList(retailerBatchCreateDto, retailerInfoList, userInfo, needInit);

        List<SoRuleRetailerItemDTO> retailerList = retailerBatchCreateDto.getRetailerList();
        final long currentTime = DateUtils.getNowDate().getTime();

        // 若retailerList不为空，则根据retailerList来更新开关
        Map<String, IntlSoRuleRetailer> dbRetailerMap =
            dbRetailerList.stream().collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));
        // 此部分表示本次修改流程中，只修改了部分零售商的开关
        List<SoRuleRetailerItemDTO> newSoRuleRetailers = Lists.newArrayList();
        retailerList.forEach(retailer -> {
            IntlSoRuleRetailer dbRetailer = dbRetailerMap.get(retailer.getRetailerCode());
            if (Objects.nonNull(dbRetailer)) {
                dbRetailer.setImeiSwitch(retailer.getImeiSwitch());
                dbRetailer.setQtySwitch(retailer.getQtySwitch());
                dbRetailer.setUpdatedAt(currentTime);
                dbRetailer.setUpdatedBy(userInfo.getMiID());
            } else {
                newSoRuleRetailers.add(retailer);
            }
        });
        // 更新部分
        if (CollectionUtils.isNotEmpty(dbRetailerList)) {
            intlSoRuleRetailerService.updateBatchById(dbRetailerList);
        }

        if (CollectionUtils.isNotEmpty(newSoRuleRetailers)) {
            // 新增部分
            Map<String, IntlRetailerDTO> retailerMap = retailerInfoList.stream()
                .collect(Collectors.toMap(IntlRetailerDTO::getRetailerCode, Function.identity()));
            List<IntlSoRuleRetailer> newRetailerList =
                newSoRuleRetailers.stream()
                    .map(retailer -> ConvertUtils.buildSoRuleRetailer(retailerMap.get(retailer.getRetailerCode()),
                        userInfo.getMiID(), SoRuleEnum.COPY, CommonConstants.INIT_RULE_ID))
                    .collect(Collectors.toList());
            intlSoRuleRetailerService.saveBatch(newRetailerList);
        }
    }

    /**
     * 处理新创建规则时，初始化零售商规则
     *
     * @param retailerBatchCreateDto 零售商配置对象
     * @param retailerInfoList 零售商信息列表
     * @param userInfo 登录用户
     */
    private void doCreateRuleWithRetailers(SoRuleRetailerBatchCreateDTO retailerBatchCreateDto,
        List<IntlRetailerDTO> retailerInfoList, UserInfo userInfo) {
        List<IntlSoRuleRetailer> dbRetailerList =
            initSoRuleRetailerList(retailerBatchCreateDto, retailerInfoList, userInfo, true);
        long currentTime = DateUtils.getNowDate().getTime();
        // 若retailerList不为空，则根据retailerList来更新开关
        Map<String, IntlSoRuleRetailer> dbRetailerMap =
            dbRetailerList.stream().collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));
        retailerBatchCreateDto.getRetailerList().forEach(retailer -> {
            IntlSoRuleRetailer dbRetailer = dbRetailerMap.get(retailer.getRetailerCode());
            if (Objects.nonNull(dbRetailer)) {
                dbRetailer.setImeiSwitch(retailer.getImeiSwitch());
                dbRetailer.setQtySwitch(retailer.getQtySwitch());
                dbRetailer.setUpdatedAt(currentTime);
                dbRetailer.setUpdatedBy(userInfo.getMiID());
            }
        });
        // 批量更新
        intlSoRuleRetailerService.updateBatchById(dbRetailerList);
    }

    /**
     * 根据 addAllImei、addAllQty 来更新开关
     *
     * @param retailerList 零售商规则列表
     * @param allAddImei 全量添加imei规则
     * @param allAddQty 全量添加qty规则
     */
    private void updateSwitch(List<IntlSoRuleRetailer> retailerList, Integer allAddImei, Integer allAddQty) {
        if (Objects.equals(allAddImei, 1)) {
            // 生效所有零售商的imei规则
            retailerList.forEach(retailer -> retailer.setImeiSwitch(SwitchEnum.ON.getValue()));
        } else if (Objects.equals(allAddImei, 2)) {
            // 关闭所有零售商的imei规则
            retailerList.forEach(retailer -> retailer.setImeiSwitch(SwitchEnum.OFF.getValue()));
        }
        if (Objects.equals(allAddQty, 1)) {
            // 生效所有零售商的qty规则
            retailerList.forEach(retailer -> retailer.setQtySwitch(SwitchEnum.ON.getValue()));
        } else if (Objects.equals(allAddQty, 2)) {
            // 关闭所有零售商的qty规则
            retailerList.forEach(retailer -> retailer.setQtySwitch(SwitchEnum.OFF.getValue()));
        }
    }

    /**
     * 初始化零售商规则列表，并返回 零售商初始化规则： 1、新增场景： 查询国家下所有零售商，生成零售商规则列表，保存到表intl_so_rule_retailer中；
     * 所有用户使用同一套零售商规则数据，最终提交时，会合并所有针对这个国家的零售商规则修改。 （存在多个用户同时创建规则时，多个用户对零售商的修改都以最早一次提交动作时截止的数据为准，并加锁阻止其他用户的提交动作）
     * 2、修改场景：要保持主数据不动，对主规则的修改及零售商规则的修改，都生成副本数据，保存到表intl_so_rule_retailer中，category=COPY； 同时也保持一个国家一套零售商规则副本数据；
     * 主规则副本保存于intl_so_rule_detail_log中；
     * 3、当审批通过后，将本次修改的主规则赋值给主规则；将零售商规则副本数据，对应更新到主规则中intl_so_rule_retailer中，category=MASTER；
     *
     * @param retailerBatchCreateDto 批量创建零售商请求参数
     * @param retailerInfoList 零售商信息列表（实际的零售商信息）
     * @param userInfo 登录用户账号
     * @param needInit 是否需要全量初始化 在新增规则场景，需要全量初始化； 在修改规则场景，需要看是否全量开启或关闭imei、qty规则，若是则需要全量初始化。
     * @return 初始化后的零售商规则列表
     */
    private List<IntlSoRuleRetailer> initSoRuleRetailerList(SoRuleRetailerBatchCreateDTO retailerBatchCreateDto,
        List<IntlRetailerDTO> retailerInfoList, UserInfo userInfo, boolean needInit) {
        List<IntlSoRuleRetailer> dbRetailerList;
        if (needInit) {
            // 需要全量初始化零售商数据
            try (DistributionLock ignore = distributionLockService.tryLock("so.rule.retailer.init.lock-{0}",
                retailerBatchCreateDto.getCountryCode())) {
                dbRetailerList = soRuleTransactionalAggregateService.initOrGetRetailerList(retailerBatchCreateDto,
                    retailerInfoList, userInfo);
            }
        } else {
            // 若只修改了部分零售商规则，则只需要生成部分零售商规则数据；
            List<String> retailerCodes = retailerBatchCreateDto.getRetailerList().stream()
                .map(SoRuleRetailerItemDTO::getRetailerCode).collect(Collectors.toList());
            SoRuleEnum soRuleEnum =
                intlSoRuleRetailerService.getRetailerCategoryByCreateType(retailerBatchCreateDto.getType());
            dbRetailerList = intlSoRuleRetailerService.getByCountryCodeAndRetailerCodes(
                retailerBatchCreateDto.getCountryCode(), soRuleEnum.getValue(), retailerCodes);
        }

        // 优先根据 addAllImei、addAllQty 来更新开关
        updateSwitch(dbRetailerList, retailerBatchCreateDto.getAllAddImei(), retailerBatchCreateDto.getAllAddQty());

        Long current = DateUtils.getNowDate().getTime();
        dbRetailerList.forEach(it -> {
            it.setUpdatedAt(current);
            it.setUpdatedBy(userInfo.getMiID());
        });

        return dbRetailerList;
    }

}
