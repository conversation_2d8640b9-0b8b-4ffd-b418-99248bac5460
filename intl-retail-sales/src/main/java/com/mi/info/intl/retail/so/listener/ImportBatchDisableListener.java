package com.mi.info.intl.retail.so.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.rms.RmsStoreTokenApiService;
import com.mi.info.intl.retail.api.rms.dto.RmsApiResponseBody;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoSnBlacklistService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mi.info.intl.retail.exception.ErrorCodes.SYS_ERROR;

/**
 * <AUTHOR>
 * @date 2025/7/29
 **/
@Slf4j
public class ImportBatchDisableListener implements ReadListener<ImportBatchDisableListener.BatchDisableImportData> {


    private final IntlSoSnBlacklistService blacklistService;

    private final List<BatchDisableImportData> batchDisableImportDataList;

    private final CountryTimeZoneApiService countryTimeZoneApiService;

    private final String rmsUrl = "/api/data/v9.2/new_UpdateSNBlackListAction";

    private final UserInfo userContext;

    private final RmsStoreTokenApiService rmsStoreTokenApiService;

    private  final TransactionTemplate transactionTemplate;

    public ImportBatchDisableListener(IntlSoSnBlacklistService blacklistService,
                                      CountryTimeZoneApiService countryTimeZoneApiService,
                                      RmsStoreTokenApiService rmsStoreTokenApiService,
                                      TransactionTemplate transactionTemplate,
                                      List<BatchDisableImportData> data,
                                      UserInfo userContext) {
        this.blacklistService = blacklistService;
        this.countryTimeZoneApiService = countryTimeZoneApiService;
        this.rmsStoreTokenApiService = rmsStoreTokenApiService;
        this.transactionTemplate = transactionTemplate;
        this.batchDisableImportDataList = data;
        this.userContext = userContext;
    }

    @Override
    public void invoke(ImportBatchDisableListener.BatchDisableImportData batchDisableImportData, AnalysisContext analysisContext) {
        batchDisableImportDataList.add(batchDisableImportData);
    }


    @Override
public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    try {
        if (batchDisableImportDataList.isEmpty()) {
            log.warn("没有需要停用的记录");
            return;
        }
        log.info("开始处理批量停用数据，共{}条", batchDisableImportDataList.size());

        // 获取国家信息映射表（忽略大小写）
        List<CountryDTO> countryInfo = countryTimeZoneApiService.getCountryInfo();
        Map<String, CountryDTO> countryDTOMap = countryInfo.stream()
                .collect(Collectors.toMap(
                        country -> country.getCountryName().toLowerCase(),
                        Function.identity(),
                        (v1, v2) -> v1));

        // 根据snImei、country查询，查询的时候country要忽略大小写
        // 把查询出来的数据进行批量更新
        List<String> countryCodeList = batchDisableImportDataList.stream()
                .map(BatchDisableImportData::getCountry)
                .map(countryName -> {
                    CountryDTO countryDTO = countryDTOMap.get(countryName.toLowerCase());
                    return countryDTO != null ? countryDTO.getCountryCode() : null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 获取snImei列表、转大写
        List<String> snImeiList = batchDisableImportDataList.stream()
                .map(BatchDisableImportData::getSnImei)
                .filter(Objects::nonNull)
                .map(String::toUpperCase)
                .collect(Collectors.toList());

        // 检查列表是否为空，避免无效查询
        if (countryCodeList.isEmpty() || snImeiList.isEmpty()) {
            log.warn("国家代码列表或SN/IMEI列表为空，无法执行停用操作");
            return;
        }

        // 使用编程式事务处理数据库更新和RMS同步
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                try {
                    // 在事务内重新查询需要更新的黑名单记录，只查询启用状态的记录
                    List<IntlSoSnBlacklist> updateBlackList = blacklistService.list(Wrappers.<IntlSoSnBlacklist>lambdaQuery()
                            .in(IntlSoSnBlacklist::getCountryCode, countryCodeList)
                            .in(IntlSoSnBlacklist::getSnImei, snImeiList)
                            .eq(IntlSoSnBlacklist::getStatus, Boolean.FALSE));

                    if (updateBlackList.isEmpty()) {
                        log.warn("未找到匹配的启用状态记录进行停用");
                        return;
                    }

                    // 设置停用状态和更新信息
                    updateBlackList.forEach(blacklist -> {
                        blacklist.setStatus(Boolean.TRUE);
                        blacklist.setModifiedBy(Long.parseLong(userContext.getMiID()));
                        blacklist.setModifiedOn(System.currentTimeMillis());
                    });

                    // 执行批量更新
                    boolean updateResult = blacklistService.updateBatchById(updateBlackList);

                    if (updateResult) {
                        // 请求同步RMS
                        for (IntlSoSnBlacklist intlSoSnBlacklist : updateBlackList) {
                            Map<String, Object> data = buildRmsData(intlSoSnBlacklist);
                            log.info("RMS请求数据：{}", data);
                            RmsApiResponseBody responseBody = rmsStoreTokenApiService.httpForRmsResponseBody(userContext.getAreaId(), rmsUrl, data, "POST");
                            log.info("RMS返回结果：{}", responseBody);

                            // 检查RMS返回结果，如果失败则抛出异常触发回滚
                            if (responseBody == null || responseBody.getCode() != 200) {
                                String errorMsg = "RMS更新失败";
                                if (responseBody != null && responseBody.getMessage() != null) {
                                    errorMsg += ": " + responseBody.getMessage();
                                }
                                throw new RuntimeException(errorMsg);
                            }
                        }

                        log.info("批量停用完成，共停用{}条记录", updateBlackList.size());
                    } else {
                        log.error("批量停用失败，更新数据库时出现问题");
                        throw new BizException(SYS_ERROR, "批量停用失败，更新数据库时出现问题");
                    }
                } catch (Exception e) {
                    log.error("批量停用事务处理失败: {}", e.getMessage(), e);
                    status.setRollbackOnly(); // 标记事务回滚
                    throw new BizException(SYS_ERROR, "批量停用失败");
                }
            }
        });

    } catch (Exception e) {
        log.error("批量停用处理失败: {}", e.getMessage(), e);
        throw new BizException(SYS_ERROR, "批量停用失败");
    } finally {
        // 清理资源，避免内存泄漏
        batchDisableImportDataList.clear();
    }
}


    /**
     * 构造发送给RMS的黑名单数据格式
     *
     * @param intlSoSnBlacklist 需要同步的黑名单数据列表
     * @return 符合RMS要求的数据格式列表
     */
    private Map<String, Object> buildRmsData(IntlSoSnBlacklist intlSoSnBlacklist) throws JsonProcessingException {
        Map<String, Object> rmsData = new HashMap<>();
        rmsData.put("SourceType", "retail");

        Map<String, Object> input = new HashMap<>();
        input.put("retailId", intlSoSnBlacklist.getId());
        input.put("countryCode", intlSoSnBlacklist.getCountryCode());
        input.put("type", intlSoSnBlacklist.getType());
        input.put("snImei", intlSoSnBlacklist.getSnImei());
        input.put("status", 1);
        input.put("dataFrom", intlSoSnBlacklist.getDataFrom());
        ObjectMapper objectMapper = new ObjectMapper();
        String inputObject = objectMapper.writeValueAsString(input);
        rmsData.put("Input", inputObject);

        return rmsData;
    }


    @Data
    public static class BatchDisableImportData {
        /**
         *  国家
         */
        @ExcelProperty("Country")
        private String country;

        /**
         *  SN
         */
        @ExcelProperty("SN/IMEI")
        private String snImei;
    }
}
