package com.mi.info.intl.retail.so.domain.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.infra.database.dataobject.sys.IntlSysDict;
import com.mi.info.intl.retail.so.infra.database.mapper.sys.IntlSysDictMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 数据字典表 服务实现类
 *
 * <AUTHOR>
 * @date 2025/07/25
 */
@Service
public class IntlSysDictServiceImpl extends ServiceImpl<IntlSysDictMapper, IntlSysDict>
        implements IntlSysDictService {
    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Override
    public Map<String, List<LabelValueDTO>> getLabelValueListByDictCode(DictSysRequest request) {
        List<DictSysDTO> dictCodeList = request.getDictCodeList();
        Map<String, List<LabelValueDTO>> map = Maps.newHashMap();
        boolean country =
                dictCodeList.stream().anyMatch(dictCode -> Objects.equals(dictCode.getDictType(), "sys") &&
                        Objects.equals(dictCode.getDictCode(), "country"));
        if (country) {
            dictCodeList.removeIf(dictCode -> Objects.equals(dictCode.getDictType(), "sys") &&
                    Objects.equals(dictCode.getDictCode(), "country"));
            map.putAll(countryTimeZoneApiService.getAreaCountryTimeZoneList());
        }
        LambdaQueryWrapper<IntlSysDict> wrapper = new LambdaQueryWrapper<>();
        wrapper = wrapper.select(IntlSysDict::getDictCode, IntlSysDict::getDictLabel, IntlSysDict::getDictValue);
        for (DictSysDTO dictCode : request.getDictCodeList()) {
            wrapper = wrapper.or().eq(IntlSysDict::getDictType, dictCode.getDictType()).eq(IntlSysDict::getDictCode,
                    dictCode.getDictCode());
        }
        List<IntlSysDict> labelValueList = this.list(wrapper);
        map.putAll(labelValueList.stream().collect(Collectors.groupingBy(IntlSysDict::getDictCode)).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        t -> t.getValue().stream()
                                .map(dict -> new LabelValueDTO(dict.getDictValue(), dict.getDictLabel(),
                                        null)).collect(Collectors.toList()))));
        return map;
    }

}
