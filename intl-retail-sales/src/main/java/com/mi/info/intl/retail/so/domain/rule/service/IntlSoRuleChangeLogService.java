package com.mi.info.intl.retail.so.domain.rule.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogListDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleChangeLogReq;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleLogReq;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetailLog;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:06
 */
public interface IntlSoRuleChangeLogService extends IService<IntlSoRuleDetailLog> {

    /**
     * 按条件分页查询规则
     *
     * @param req 查询条件
     * @return 查询结果
     */
    IPage<SoRuleChangeLogListDTO> getSoRuleDetailLogList(QuerySoRuleLogReq req);

    /**
     * 通过ID获取SO规则细节日志
     *
     * @param req req
     * @return {@link SoRuleChangeLogDTO }
     */
    SoRuleChangeLogDTO getSoRuleChangeLogById(QuerySoRuleChangeLogReq req);

    /**
     * 获取国家下的规则详情
     *
     * @param countryCode 国家编码
     * @param status 地位
     * @return 规则详情
     */
    SoRuleChangeLogDTO getRuleDetailByCodeAndStatus(String countryCode, Integer status);

    /**
     * 判断国家下是否有审批中的规则
     *
     * @param countryCode 国家编码
     * @return true:有审批中的规则，false:没有审批中的规则
     */
    boolean existApprovingRule(String countryCode);

    /**
     * 根据审批ID获取规则变更详情
     * @param approvalId 审批ID
     * @return 规则变更详情
     */
    IntlSoRuleDetailLog getByApprovalId(String approvalId);
}
