package com.mi.info.intl.retail.so.domain.rule.aggregate;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmUser;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ApproverDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ImeiRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.PhotoRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleApproveCallbackDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailCreateDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailModifyDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerBatchCreateDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerStatisticsDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.rule.constants.CommonConstants;
import com.mi.info.intl.retail.so.domain.rule.constants.SoRuleDictKey;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetailLog;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;
import com.mi.info.intl.retail.so.util.ConvertUtils;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.cnzone.commons.utils.DateUtils;

import cn.hutool.core.lang.Assert;

/**
 * 规则创建聚合服务，处理事务逻辑
 *
 * <AUTHOR>
 * @date 2025/7/31 17:53
 */
@Service
public class SoRuleTransactionalAggregateService {

    @Resource
    private IntlSysDictService intlSysDictService;

    @Resource
    private IntlSoRuleDetailService intlSoRuleDetailService;

    @Resource
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private IntlSoRuleChangeLogService intlSoRuleChangeLogService;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Long createRule(SoRuleDetailCreateDTO soRuleDetailCreateDto) {
        // 根据当前用户id 和国家编码+category，找到当前用户所修改的零售商规则列表
        UserInfo userInfo = UserInfoUtil.getUserContext();
        List<IntlSoRuleRetailer> retailerList = intlSoRuleRetailerService
            .getByCountryCodeAndCategory(soRuleDetailCreateDto.getCountryCode(), SoRuleEnum.MASTER.getValue());

        // 创建规则明细记录
        IntlSoRuleDetail intlSoRuleDetail = buildSoRuleDetail(soRuleDetailCreateDto, retailerList, userInfo);
        intlSoRuleDetailService.save(intlSoRuleDetail);
        // 保存规则，获取ID
        Long ruleId = intlSoRuleDetail.getId();

        // 注： 新增场景下，会将当前国家下所有零售商规则进行初始化；增量新增的零售商，通过Notify接口进行初始化，并自动绑定当前已有的规则；
        // 对零售商规则绑定主规则ID,设置为主数据，并更新
        retailerList.forEach(it -> {
            it.setRuleId(ruleId);
            it.setCategory(SoRuleEnum.MASTER.getValue());
        });
        intlSoRuleRetailerService.updateBatchById(retailerList);

        return ruleId;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public List<IntlSoRuleRetailer> initOrGetRetailerList(@NotNull SoRuleRetailerBatchCreateDTO retailerBatchCreateDto,
        List<IntlRetailerDTO> retailerInfoList, UserInfo userInfo) {
        // 查询当前国家编码下是否存在零售商列表，若不存在，则进行初始化：
        SoRuleEnum soRuleEnum =
            intlSoRuleRetailerService.getRetailerCategoryByCreateType(retailerBatchCreateDto.getType());
        List<IntlSoRuleRetailer> ruleRetailerList = intlSoRuleRetailerService
            .getByCountryCodeAndCategory(retailerBatchCreateDto.getCountryCode(), soRuleEnum.getValue());
        // 若零售商规则列表存在，且数量与当前国家下所有零售商数量一致，则直接返回零售商规则列表
        if (CollectionUtils.isNotEmpty(ruleRetailerList) && retailerInfoList.size() == ruleRetailerList.size()) {
            return ruleRetailerList;
        }
        // 根据当前国家下所有零售商数据，生成零售商规则列表，保存到表intl_so_rule_retailer中；
        List<IntlSoRuleRetailer> soRuleRetailers = retailerInfoList.stream().map(
            it -> ConvertUtils.buildSoRuleRetailer(it, userInfo.getMiID(), soRuleEnum, CommonConstants.INIT_RULE_ID))
            .collect(Collectors.toList());
        // 若已经存在部分零售商规则，则拆分出本次要新增的
        List<String> retailerCodeList =
            ruleRetailerList.stream().map(IntlSoRuleRetailer::getRetailerCode).collect(Collectors.toList());
        List<IntlSoRuleRetailer> toAddList = soRuleRetailers.stream()
            .filter(it -> !retailerCodeList.contains(it.getRetailerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toAddList)) {
            intlSoRuleRetailerService.saveBatch(toAddList);
        }
        // 合并db已存在的+新增的
        CollectionUtils.addAll(ruleRetailerList, toAddList);
        return ruleRetailerList;
    }

    /**
     * 修改规则逻辑，创建规则副本，事务方法
     *
     * @param soRuleDetailModifyDto 规则修改参数
     * @return 规则副本ID
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Long modifyRule(@NotNull SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        // 若存在草稿状态的规则修改记录，则可返回，可以重新发起流程。
        SoRuleChangeLogDTO ruleDetailLog = intlSoRuleChangeLogService.getRuleDetailByCodeAndStatus(
            soRuleDetailModifyDto.getCountryCode(), SoRuleDetailApproveStatus.CREATE.getValue());
        if (Objects.nonNull(ruleDetailLog)) {
            return ruleDetailLog.getId();
        }
        IntlSoRuleDetail masterRule;
        if (Objects.nonNull(soRuleDetailModifyDto.getRuleId())) {
            masterRule = intlSoRuleDetailService.getById(soRuleDetailModifyDto.getRuleId());
        } else {
            Optional<IntlSoRuleDetail> optional =
                intlSoRuleDetailService.getByCountryCode(soRuleDetailModifyDto.getCountryCode());
            masterRule = optional.orElse(null);
        }
        Assert.notNull(masterRule, "Master rule entry not found.");
        UserInfo userInfo = UserInfoUtil.getUserContext();
        // 查询当前所有修改的零售商规则列表
        List<IntlSoRuleRetailer> retailerList = intlSoRuleRetailerService
            .getByCountryCodeAndCategory(soRuleDetailModifyDto.getCountryCode(), SoRuleEnum.COPY.getValue());
        // 创建规则明细记录副本，用于修改审批流程
        // 复用创建规则逻辑，然后复制到副本中
        IntlSoRuleDetail intlSoRuleDetail = buildSoRuleDetail(soRuleDetailModifyDto, retailerList, userInfo);
        IntlSoRuleDetailLog intlSoRuleDetailLog =
            ComponentLocator.getConverter().convert(intlSoRuleDetail, IntlSoRuleDetailLog.class);
        // 绑定主规则ID，并重置状态为新建
        intlSoRuleDetailLog.setMasterId(masterRule.getId());
        intlSoRuleDetailLog.setStatus(SoRuleDetailApproveStatus.CREATE.getValue());
        // 设置审批节点信息，初始化审批状态
        List<ApproverDTO> approverList = soRuleDetailModifyDto.getApproverList();
        approverList = CollectionUtils.isNotEmpty(approverList) ? approverList : Lists.newArrayList();
        approverList.forEach(it -> it.setStatus(0));
        intlSoRuleDetailLog.setApproverList(JSON.toJSONString(approverList));
        // 保存规则副本，获取ID
        intlSoRuleChangeLogService.save(intlSoRuleDetailLog);
        Long ruleCopyId = intlSoRuleDetailLog.getId();
        // 零售商规则副本，绑定规则ID为当前提交的规则副本ID
        retailerList.forEach(it -> it.setRuleId(ruleCopyId));
        intlSoRuleRetailerService.updateBatchById(retailerList);

        return ruleCopyId;
    }

    /**
     * 发起审批流程成功，更新审批状态
     *
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param approveId 审批人ID
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleLaunch(IntlSoRuleDetailLog ruleDetailLog, String approveId) {
        IntlSoRuleDetail masterRule = intlSoRuleDetailService.getById(ruleDetailLog.getMasterId());
        updateRuleApproveStatus(approveId, ruleDetailLog, SoRuleDetailApproveStatus.PENDING, masterRule,
            SoRuleDetailApproveStatus.PENDING);
    }

    /**
     * 审批完成回调，事务处理。 将当前修改规则的副本数据，更新到主规则数据中。并更新审批状态。
     *
     * @param approveCompletedDTO 审批完成回调参数
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param masterRule 主规则记录（db查询）
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleCompleted(SoRuleApproveCallbackDTO approveCompletedDTO, IntlSoRuleDetailLog ruleDetailLog,
        IntlSoRuleDetail masterRule) {
        // 审批人id
        BpmUser operator = getApproveId(approveCompletedDTO);
        String approveId = operator.getPersonId();
        String approveName = operator.getUserName();
        Long current = DateUtils.getNowDate().getTime();
        // 更新审批状态为审批通过
        ruleDetailLog.setEffectiveTime(current);
        ruleDetailLog.setStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
        ruleDetailLog.setUpdatedBy(approveId);
        ruleDetailLog.setUpdatedAt(current);
        intlSoRuleChangeLogService.updateById(ruleDetailLog);

        // 将当前修改的零售商规则副本数据，更新到零售商规则主数据中。

        // 本次修改的零售商规则副本数据
        List<IntlSoRuleRetailer> ruleRetailerCopyList = intlSoRuleRetailerService
            .getByCountryCodeAndCategory(ruleDetailLog.getCountryCode(), SoRuleEnum.COPY.getValue());
        Map<String, IntlSoRuleRetailer> ruleRetailerCopyMap = ruleRetailerCopyList.stream()
            .collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));

        // 零售商规则主数据
        List<IntlSoRuleRetailer> ruleRetailerList = intlSoRuleRetailerService
            .getByCountryCodeAndCategory(ruleDetailLog.getCountryCode(), SoRuleEnum.MASTER.getValue());
        Map<String, IntlSoRuleRetailer> ruleRetailerMap = ruleRetailerList.stream()
            .collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));

        List<IntlSoRuleRetailer> toUpdate = Lists.newArrayList();
        List<IntlSoRuleRetailer> toAdd = Lists.newArrayList();
        // 处理需要更新的
        for (IntlSoRuleRetailer soRuleRetailer : ruleRetailerList) {
            IntlSoRuleRetailer ruleRetailerCopy = ruleRetailerCopyMap.get(soRuleRetailer.getRetailerCode());
            if (Objects.nonNull(ruleRetailerCopy)) {
                // 副本规则数据复制给主数据
                soRuleRetailer.setImeiSwitch(ruleRetailerCopy.getImeiSwitch());
                soRuleRetailer.setQtySwitch(ruleRetailerCopy.getQtySwitch());
                soRuleRetailer.setUpdatedAt(current);
                soRuleRetailer.setUpdatedBy(approveId);
                toUpdate.add(soRuleRetailer);
            }
        }
        // 处理需要新增的
        for (IntlSoRuleRetailer ruleRetailerCopy : ruleRetailerCopyList) {
            if (!ruleRetailerMap.containsKey(ruleRetailerCopy.getRetailerCode())) {
                // 副本数据转换为主数据
                IntlSoRuleRetailer toMaster =
                    ComponentLocator.getConverter().convert(ruleRetailerCopy, IntlSoRuleRetailer.class);
                toMaster.setId(null);
                toMaster.setRuleId(masterRule.getId());
                toMaster.setCategory(SoRuleEnum.MASTER.getValue());
                toAdd.add(toMaster);
            }
        }
        if (CollectionUtils.isNotEmpty(toAdd)) {
            intlSoRuleRetailerService.saveBatch(toAdd);
        }
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            intlSoRuleRetailerService.updateBatchById(toUpdate);
        }

        // 重新查询所有零售商数据（包含更新、新增的数据）
        List<IntlSoRuleRetailer> allRetailers = intlSoRuleRetailerService
            .getByCountryCodeAndCategory(ruleDetailLog.getCountryCode(), SoRuleEnum.MASTER.getValue());
        SoRuleRetailerStatisticsDTO retailerStatistics = intlSoRuleRetailerService.getRetailerStatistics(allRetailers);

        // 将当前修改规则的副本数据，更新到主规则数据中。
        masterRule.setEffectiveTime(ruleDetailLog.getEffectiveTime());
        masterRule.setImeiRuleList(ruleDetailLog.getImeiRuleList());
        masterRule.setPhotoRuleList(ruleDetailLog.getPhotoRuleList());
        masterRule.setStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
        masterRule.setDefaultRetailersSwitch(ruleDetailLog.getDefaultRetailersSwitch());
        masterRule.setTotalRetailersCount(retailerStatistics.getTotalRetailersCount());
        masterRule.setNoRuleRetailersCount(retailerStatistics.getNoRuleRetailersCount());
        masterRule.setImeiRetailersCount(retailerStatistics.getImeiRetailersCount());
        masterRule.setQtyRetailersCount(retailerStatistics.getQtyRetailersCount());
        masterRule.setUpdatedAt(ruleDetailLog.getUpdatedAt());
        masterRule.setUpdatedBy(approveId);
        masterRule.setUpdatedByName(approveName);
        // 更新主规则数据
        intlSoRuleDetailService.updateById(masterRule);
    }

    /**
     * 审批拒绝回调，事务处理。
     *
     * @param approveRejectedDTO 审批拒绝回调参数
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param masterRule 主规则记录（db查询）
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleRejected(SoRuleApproveCallbackDTO approveRejectedDTO, IntlSoRuleDetailLog ruleDetailLog,
        IntlSoRuleDetail masterRule) {
        updateRuleApproveStatus(getApproveId(approveRejectedDTO).getPersonId(), ruleDetailLog,
            SoRuleDetailApproveStatus.REJECTED, masterRule, SoRuleDetailApproveStatus.PENDING);
    }

    /**
     * 审批撤回回调，事务处理。
     *
     * @param approveRecalledDTO 审批撤回回调参数
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param masterRule 主规则记录（db查询）
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleRecalled(SoRuleApproveCallbackDTO approveRecalledDTO, IntlSoRuleDetailLog ruleDetailLog,
        IntlSoRuleDetail masterRule) {
        updateRuleApproveStatus(getApproveId(approveRecalledDTO).getPersonId(), ruleDetailLog,
            SoRuleDetailApproveStatus.RECALLED, masterRule, SoRuleDetailApproveStatus.APPROVED);
    }

    /**
     * 中间节点审批通过
     *
     * @param approveCallback 审批回调参数
     * @param ruleDetailLog 审批回调参数
     * @param masterRule 主规则记录（db查询）
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleAgree(SoRuleApproveCallbackDTO approveCallback, IntlSoRuleDetailLog ruleDetailLog,
        IntlSoRuleDetail masterRule) {
        updateRuleApproveStatus(getApproveId(approveCallback).getPersonId(), ruleDetailLog,
            SoRuleDetailApproveStatus.PENDING, masterRule, SoRuleDetailApproveStatus.PENDING);
    }

    private BpmUser getApproveId(SoRuleApproveCallbackDTO approveCallback) {
        BpmUser operator = Objects.nonNull(approveCallback.getAssignee()) ? approveCallback.getAssignee()
            : approveCallback.getOperator();
        return Objects.nonNull(operator) ? operator : new BpmUser();
    }

    /**
     * 审批回调，根据审批状态，更新规则副本记录和主规则记录的审批状态。
     *
     * @param approveId 审批人ID
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param ruleDetailLogStatus 审批状态
     * @param masterRule 主规则记录（db查询）
     * @param mastRuleStatus 主规则记录审批状态
     */
    private void updateRuleApproveStatus(String approveId, IntlSoRuleDetailLog ruleDetailLog,
        SoRuleDetailApproveStatus ruleDetailLogStatus, IntlSoRuleDetail masterRule,
        SoRuleDetailApproveStatus mastRuleStatus) {
        // 更新修改规则副本记录审批状态为审批拒绝
        Long current = DateUtils.getNowDate().getTime();
        ruleDetailLog.setStatus(ruleDetailLogStatus.getValue());
        ruleDetailLog.setUpdatedAt(current);
        ruleDetailLog.setUpdatedBy(approveId);
        intlSoRuleChangeLogService.updateById(ruleDetailLog);
        // 更新规则主数据审批状态为审批通过（还原状态）
        masterRule.setStatus(mastRuleStatus.getValue());
        masterRule.setUpdatedAt(current);
        masterRule.setUpdatedBy(approveId);
        intlSoRuleDetailService.updateById(masterRule);
    }

    /**
     * 构建规则明细记录
     *
     * @param soRuleDetailCreateDto 规则创建参数
     * @param retailerList 零售商规则列表
     * @param userInfo 登录用户账号
     * @return 规则明细记录
     */
    private IntlSoRuleDetail buildSoRuleDetail(SoRuleDetailCreateDTO soRuleDetailCreateDto,
        List<IntlSoRuleRetailer> retailerList, UserInfo userInfo) {
        // 给userTitle、productLine名称赋值
        DictSysRequest dictRequest = new DictSysRequest(Lists.newArrayList());
        dictRequest.getDictCodeList().add(new DictSysDTO(SoRuleDictKey.SO_RULE, SoRuleDictKey.JOB_TITLE));
        dictRequest.getDictCodeList().add(new DictSysDTO(SoRuleDictKey.SO_RULE, SoRuleDictKey.PRODUCT_LINE));
        Map<String, List<LabelValueDTO>> dictMap = intlSysDictService.getLabelValueListByDictCode(dictRequest);
        // 国家信息，前面已经校验过，此处不可能为null。
        CountryDTO country = countryTimeZoneApiService.getCountryInfoByCode(soRuleDetailCreateDto.getCountryCode())
            .orElse(new CountryDTO());

        Map<String, String> jobTitleMap = dictMap.get(SoRuleDictKey.JOB_TITLE).stream()
            .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> productLineMap = dictMap.get(SoRuleDictKey.PRODUCT_LINE).stream()
            .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));

        List<ImeiRuleDTO> imeiRuleList = soRuleDetailCreateDto.getImeiRuleList();
        if (CollectionUtils.isNotEmpty(imeiRuleList)) {
            imeiRuleList.forEach(it -> {
                it.getUserTitleList().forEach(
                    userTitleItem -> userTitleItem.setUserTitle(jobTitleMap.get(userTitleItem.getUserTitleId())));
                it.getProductLineList().forEach(productLineItem -> productLineItem
                    .setProductLine(productLineMap.get(productLineItem.getProductLineId())));
            });
        }
        List<PhotoRuleDTO> photoRuleList = soRuleDetailCreateDto.getPhotoRuleList();
        if (CollectionUtils.isNotEmpty(photoRuleList)) {
            photoRuleList.forEach(it -> it.getUserTitleList()
                .forEach(userTitleItem -> userTitleItem.setUserTitle(jobTitleMap.get(userTitleItem.getUserTitleId()))));
        }

        // 格式化规则为json数据
        String defaultRetailerSwitchJson = JSON.toJSONString(soRuleDetailCreateDto.getDefaultRetailersSwitch());
        String imeiRuleJson = JSON.toJSONString(soRuleDetailCreateDto.getImeiRuleList());
        String photoRuleJson = JSON.toJSONString(soRuleDetailCreateDto.getPhotoRuleList());
        // 零售商规则统计
        SoRuleRetailerStatisticsDTO retailerStatistics = intlSoRuleRetailerService.getRetailerStatistics(retailerList);

        long currentTime = DateUtils.getNowDate().getTime();

        // 创建规则明细记录
        IntlSoRuleDetail intlSoRuleDetail =
            ComponentLocator.getConverter().convert(soRuleDetailCreateDto, IntlSoRuleDetail.class);
        // 新创建的规则，默认状态为已审批
        intlSoRuleDetail.setRegionCode(country.getAreaCode());
        intlSoRuleDetail.setCountryCode(country.getCountryCode());
        intlSoRuleDetail.setEffectiveTime(currentTime);
        intlSoRuleDetail.setStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
        intlSoRuleDetail.setImeiRuleList(imeiRuleJson);
        intlSoRuleDetail.setPhotoRuleList(photoRuleJson);
        intlSoRuleDetail.setDefaultRetailersSwitch(defaultRetailerSwitchJson);
        intlSoRuleDetail.setTotalRetailersCount(retailerStatistics.getTotalRetailersCount());
        intlSoRuleDetail.setNoRuleRetailersCount(retailerStatistics.getNoRuleRetailersCount());
        intlSoRuleDetail.setImeiRetailersCount(retailerStatistics.getImeiRetailersCount());
        intlSoRuleDetail.setQtyRetailersCount(retailerStatistics.getQtyRetailersCount());
        intlSoRuleDetail.setCreatedAt(currentTime);
        intlSoRuleDetail.setCreatedBy(userInfo.getMiID());
        intlSoRuleDetail.setCreatedByName(userInfo.getUserName());
        intlSoRuleDetail.setUpdatedAt(currentTime);
        intlSoRuleDetail.setUpdatedBy(userInfo.getMiID());
        intlSoRuleDetail.setUpdatedByName(userInfo.getUserName());

        return intlSoRuleDetail;
    }
}
