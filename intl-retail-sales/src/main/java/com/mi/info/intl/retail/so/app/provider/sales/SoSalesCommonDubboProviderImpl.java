package com.mi.info.intl.retail.so.app.provider.sales;

import com.alibaba.cola.dto.MultiResponse;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.provider.SoSalesCommonDubboProvider;
import com.mi.info.intl.retail.so.domain.sales.enums.SearchReferenceTableEnum;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoCommonDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoSalesCommonDubboProvider.class)
public class SoSalesCommonDubboProviderImpl implements SoSalesCommonDubboProvider {

    @Resource
    private IntlSoCommonDomainService intlSoCommonDomainService;
    
    @Override
    public MultiResponse<SearchReferenceRespDto> getSearchReferencesData(String table, String keyWord, String parentKey) {
        log.info("enter getSearchReferencesData method::params:[table:{}, keyWord:{}, parentKey:{}]", table, keyWord, parentKey);
        if (StringUtils.isBlank(table)) {
            return MultiResponse.buildFailure(String.valueOf(ErrorCodes.PARAM_IS_EMPTY.getCode()), "The parameter 'table' is empty");
        }
        if (StringUtils.isBlank(keyWord)) {
            return MultiResponse.buildFailure(String.valueOf(ErrorCodes.PARAM_IS_EMPTY.getCode()), "The parameter 'keyWord' is empty");
        }
        SearchReferenceTableEnum tableEnum = SearchReferenceTableEnum.fromTableName(table);
        if (null == tableEnum) {
            return MultiResponse.buildFailure(String.valueOf(ErrorCodes.PARAM_IS_EMPTY.getCode()), "The parameter 'table' is incorrect");
        }
        if (SearchReferenceTableEnum.INTL_RMS_CITY.tableName().equals(tableEnum.tableName()) && StringUtils.isBlank(parentKey)) {
            return MultiResponse.buildFailure(String.valueOf(ErrorCodes.PARAM_IS_EMPTY.getCode()), "The parameter 'parentKey' is empty");
        }
        List<SearchReferenceRespDto> searchReferenceRespDtoList = intlSoCommonDomainService.getSearchReferencesData(table, keyWord, parentKey);
        return MultiResponse.of(searchReferenceRespDtoList);
    }

    @Override
    public MultiResponse<String> getPicturesByRelatedIdAndModuleName(Long relatedId, String moduleName) {
        if (null == relatedId) {
            return MultiResponse.buildFailure(String.valueOf(ErrorCodes.PARAM_IS_EMPTY.getCode()), "The parameter 'relatedId' is empty");
        }
        if (StringUtils.isEmpty(moduleName)) {
            return MultiResponse.buildFailure(String.valueOf(ErrorCodes.PARAM_IS_EMPTY.getCode()), "The parameter 'moduleName' is empty");
        }
        List<String> pictureUrlList = intlSoCommonDomainService.getPicturesByRelatedIdAndModuleName(relatedId, moduleName);
        return MultiResponse.of(pictureUrlList);
    }


}
