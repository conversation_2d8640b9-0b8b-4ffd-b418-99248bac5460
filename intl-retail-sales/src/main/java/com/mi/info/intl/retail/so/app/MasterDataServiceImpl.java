package com.mi.info.intl.retail.so.app;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.api.rms.RmsStoreTokenApiService;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.fieldforce.user.IntlRmsUserApiService;
import com.mi.info.intl.retail.core.utils.EasyExcelUtil;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.MasterDataService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.*;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.upload.config.ImeiVerifyConfig;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoPlaintextImeiUser;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoPlaintextImeiUserService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoSnBlacklistService;
import com.mi.info.intl.retail.so.listener.ImportBatchDisableListener;
import com.mi.info.intl.retail.so.listener.ImportSnBlackListener;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.keycenter.okhttp3.OkHttpClient;
import com.xiaomi.keycenter.okhttp3.Request;
import com.xiaomi.keycenter.okhttp3.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;


import java.io.*;
import java.nio.file.Files;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Optional;

/**
 * 主数据服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Slf4j
@DubboService(group = "${center.dubbo.group:}", interfaceClass = MasterDataService.class)
@Service
public class MasterDataServiceImpl implements MasterDataService {

    @Resource
    private IntlSoSnBlacklistService intlSoSnBlacklistService;

    @Resource
    private FdsService fdsService;

    @Resource
    private ImeiVerifyConfig imeiVerifyConfig;

    @Resource
    private EasyExcelUtil easyExcelUtil;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private IntlRmsUserApiService intlRmsUserApiService;

    @Resource
    private IntlSoPlaintextImeiUserService intlSoPlaintextImeiUserService;

    @Resource
    private RmsStoreTokenApiService rmsStoreTokenApiService;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 查询sn黑名单
     * @param request
     */
    @Override
    public CommonApiResponse<IPage<SnBlackDTO>> querySnBlackList(SnBlackRequest request) {
       return  new CommonApiResponse<>(intlSoSnBlacklistService.pageList(request));
    }


    /**
     * 导入黑名单
     * @param fileUrl
     * @return 异常数据Excel文件URL，如果没有异常数据则返回空字符串
     */
    @Override
    public CommonApiResponse<String> importSnBlackList(String fileUrl, String locale) {

        log.info("开始导入黑名单，文件URL: {}", fileUrl);
        
        if (StringUtils.isBlank(fileUrl)) {
            log.error("文件URL不能为空");
            throw new RuntimeException("文件URL不能为空");
        }

        File inputFile = null;
        try {
            UserInfo userContext = UserInfoUtil.getUserContext();
            // 1.读取URL数据
            inputFile = getFileFromUrl(fileUrl);

            // 2.读取dayu配置进行sn的hash并保存数据
            List<String> hashCountryList = imeiVerifyConfig.getHashCountryList();
            //3. 解析Excel文件
            ImportSnBlackListener importSnBlackListener =
                    new ImportSnBlackListener(intlSoSnBlacklistService,
                            countryTimeZoneApiService,
                            rmsStoreTokenApiService,
                            transactionTemplate,
                            fdsService,
                            hashCountryList, userContext,
                            locale);

            // 4.读取Excel文件
            easyExcelUtil.readFromStream(Files.newInputStream(inputFile.toPath()),
                    ImportSnBlackListener.SnBlackImportData.class,
                    importSnBlackListener, 1);

            // 5.返回异常数据文件URL
            return new CommonApiResponse<>(importSnBlackListener.getErrorDataFileUrl());
            
        } catch (Exception e) {
            log.error("导入黑名单失败: {}", e.getMessage(), e);
            throw new RuntimeException("导入黑名单失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (inputFile != null && inputFile.exists()) {
                inputFile.delete();
            }
        }
    }


    @Override
    public CommonApiResponse<String> exportSnBlackList(SnBlackRequest request) {
        log.info("开始导出黑名单数据，查询条件: {}", request);
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile("snBlacklist", ".xlsx");

            excelWriter = EasyExcel.write(tempFile, SnBlackDTO.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("任务列表").build();

            long pageSize = 1000L;
            long currentPage = 1L;
            boolean hasNext = true;

            while (hasNext) {
                request.setPageNum(currentPage);
                request.setPageSize(pageSize);

                CommonApiResponse<IPage<SnBlackDTO>> iPageCommonApiResponse = this.querySnBlackList(request);
                List<SnBlackDTO> records = iPageCommonApiResponse.getData().getRecords();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }


                excelWriter.write(records, writeSheet);

                hasNext = currentPage * pageSize < iPageCommonApiResponse.getData().getTotal();
                currentPage++;
            }

            if (excelWriter != null) {
                excelWriter.finish();
            }
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            return new CommonApiResponse<>(fdsService.upload("snBlackList" + timestamp + ".xlsx", tempFile, true).getUrl());
        } catch (Exception e) {
            log.error("导出SN黑名单异常, request: {}", request, e);
            return CommonApiResponse.failure(500, "export error");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }

    }



    /**
     * 批量停用
     *
     * @param request
     */
    @Override
    public  CommonApiResponse<String> batchDisable(BatchDisableRequest request) {
        File inputFile = null;
        try {
            UserInfo userContext = UserInfoUtil.getUserContext();
            // 1.读取URL数据
            inputFile = getFileFromUrl(request.getFileUrl());

            //2. 解析Excel文件
            List<ImportBatchDisableListener.BatchDisableImportData> dataList = new ArrayList<>();

            ImportBatchDisableListener importBatchDisableListener =
                    new ImportBatchDisableListener(intlSoSnBlacklistService,
                            countryTimeZoneApiService,
                            rmsStoreTokenApiService,
                            transactionTemplate,
                            dataList, userContext);

            // 3.读取Excel文件
            easyExcelUtil.readFromStream(Files.newInputStream(inputFile.toPath()),
                    ImportBatchDisableListener.BatchDisableImportData.class,
                    importBatchDisableListener, 1);
        return new CommonApiResponse<>("success");
        } catch (Exception e) {
            log.error("批量停用失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量停用失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (inputFile != null && inputFile.exists()) {
                inputFile.delete();
            }
        }
    }

    /**
     * 分页查询明文IMEI用户
     *
     * @param request 请求参数
     * @return 查询结果
     *
     */
    @Override
    public CommonApiResponse<IPage<PlainTextImeiUserDTO>> pageQueryPlainTextUser(PlainTextUserRequest  request) {
        return  new CommonApiResponse<>(intlSoPlaintextImeiUserService.pageList(request));
    }

    /**
     * 添加明文IMEI用户
     *
     * @param request 请求参数
     * @return 添加结果
     */
    @Override
    public CommonApiResponse<String> addPlainTextUser(AddPlainTextImeiUserRequest request) {

        log.info("开始添加明文IMEI用户: {}", request);

        UserInfo userInfo = UserInfoUtil.getUserContext();

        boolean isMoreThanThree = null != request.getPhotoUrlList()
                && request.getPhotoUrlList().size() > 3;
        //todo 图片前端上传有效期只有7天，现需要持久化 @宣琨
        Assert.isTrue(!isMoreThanThree, "最多只能添加3张图片");

        IntlSoPlaintextImeiUser intlSoPlaintextImeiUser = new IntlSoPlaintextImeiUser();
        intlSoPlaintextImeiUser.setCountryName(request.getCountryName());
        intlSoPlaintextImeiUser.setCountryCode(request.getCountryCode());
        intlSoPlaintextImeiUser.setUserMid(request.getMiId());
        String photoUrl = Optional.ofNullable(request.getPhotoUrlList())
                .filter(list -> !list.isEmpty()) // 过滤空集合
                .map(list -> String.join(",", list)) // 非空则用逗号拼接
                .orElse(""); // 空则返回空字符串
        intlSoPlaintextImeiUser.setPhotoUrl(photoUrl);
        intlSoPlaintextImeiUser.setCreatedBy(Long.valueOf(userInfo.getMiID()));
        intlSoPlaintextImeiUser.setCreatedOn(System.currentTimeMillis());
        intlSoPlaintextImeiUser.setModifiedBy(Long.valueOf(userInfo.getMiID()));
        intlSoPlaintextImeiUser.setModifiedOn(System.currentTimeMillis());
        intlSoPlaintextImeiUserService.save(intlSoPlaintextImeiUser);

        log.info("添加明文IMEI用户成功: {}", intlSoPlaintextImeiUser);

        return new CommonApiResponse<>("success");
    }

    /**
     * 删除明文IMEI用户
     *
     * @param id 明文IMEI用户ID
     * @return
     */
    @Override
    public CommonApiResponse<String> deletePlainTextUser(String id) {

        log.info("开始删除明文IMEI用户: {}", id);

        IntlSoPlaintextImeiUser intlSoPlaintextImeiUser =
                intlSoPlaintextImeiUserService.getById(id);

        Assert.notNull(intlSoPlaintextImeiUser, "Plain Text Imei User Not Exists.");

        intlSoPlaintextImeiUser.setStatus(Boolean.TRUE);
        intlSoPlaintextImeiUserService.updateById(intlSoPlaintextImeiUser);

        return new CommonApiResponse<>("success");
    }


    /**
     * 从URL下载文件
     */
    private File getFileFromUrl(String fileUrl) throws IOException {

        log.info("开始获取文件: {}", fileUrl);

        // 使用OkHttp下载文件
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(fileUrl).build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("文件下载失败，HTTP状态码: {}", response.code());
                throw new IOException("文件下载失败，HTTP状态码: " + response.code());
            }

            // 创建临时文件
            File tempFile = File.createTempFile("import_", ".xlsx");
            try (InputStream inputStream = response.body().byteStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            
            return tempFile;
        }
    }



}
