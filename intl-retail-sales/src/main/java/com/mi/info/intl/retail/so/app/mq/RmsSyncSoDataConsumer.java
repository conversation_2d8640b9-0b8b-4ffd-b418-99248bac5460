package com.mi.info.intl.retail.so.app.mq;

import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import com.mi.info.intl.retail.so.domain.datasync.RmsSyncSoDataManage;
import com.mi.info.intl.retail.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Optional;
import javax.annotation.Resource;

/**
 *
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.so-sync.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.so-sync.topic}",
        consumerGroup = "${intl-retail.rocketmq.so-sync.group}")
public class RmsSyncSoDataConsumer implements RocketMQListener<String> {

    @Resource
    private RmsSyncSoDataManage rmsSyncData;

    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @SuppressWarnings("checkstyle:SeparatorWrap")
    @Override
    public void onMessage(String message) {
        log.info("RmsSyncImeiAndQtyConsumer message:{}", message);
        try {
            RmsSyncSoDataReqDto rmsSyncSoDataReqDto =
                    Optional.ofNullable(JsonUtil.json2bean(message, RmsSyncSoDataReqDto.class)).
                            orElseThrow(() -> new RuntimeException("RmsSyncImeiAndQtyRequest is null"));
            rmsSyncData.saveDb(rmsSyncSoDataReqDto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("RmsSyncImeiAndQtyConsumer error", e);
        }

    }

}