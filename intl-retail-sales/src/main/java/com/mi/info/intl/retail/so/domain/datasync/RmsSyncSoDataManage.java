package com.mi.info.intl.retail.so.domain.datasync;

import com.alibaba.fastjson.JSONObject;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import com.xiaomi.cnzone.commons.exception.CommonBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import javax.annotation.Resource;

@Slf4j
@Component
public class RmsSyncSoDataManage {
    @Resource
    private DistributionLockService distributionLockService;

    @Resource
    private IntlSoImeiService intlSoImeiService;

    @Resource
    private IntlSoQtyService qtyService;
    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;

    private static final String UNSUPPORTED_SYNC_TYPE = "unsupported sync type>>";
    private static final String UNSUPPORTED_OPERATE_TYPE = "unsupported operateType>>";

    public void saveDb(RmsSyncSoDataReqDto requestData) {
        String type = requestData.getType();
        String operateType = requestData.getOperateType();
        JSONObject data = requestData.getData();

        if (type == null || operateType == null || data == null) {
            throw new IllegalArgumentException("operateType and data cannot be null");
        }

        try {
            DataSyncDataTypeEnum dataTypeEnum = DataSyncDataTypeEnum.getEnumByMessage(type);
            if (dataTypeEnum == null) {
                throw new CommonBusinessException(UNSUPPORTED_SYNC_TYPE + type);
            }

            switch (dataTypeEnum) {
                case IMEI:
                    RmsSyncImeiData imeiData = convertToImeiData(data);
                    doImeiSync(operateType, requestData.getFields(), imeiData);
                    break;
                case QTY:
                    RmsSyncQtyData qtyData = convertToQtyData(data);
                    doQtySync(operateType, qtyData);
                    break;
                default:
                    throw new CommonBusinessException(UNSUPPORTED_SYNC_TYPE + type);
            }
        } catch (Exception e) {
            log.error("Error processing sync data request", e);
            throw new CommonBusinessException(e.getMessage());
        }
    }

    private RmsSyncImeiData convertToImeiData(Object data) {
        if (data instanceof RmsSyncImeiData) {
            return (RmsSyncImeiData) data;
        }
        return JsonUtil.json2bean(JsonUtil.bean2json(data), RmsSyncImeiData.class);
    }

    private RmsSyncQtyData convertToQtyData(Object data) {
        if (data instanceof RmsSyncQtyData) {
            return (RmsSyncQtyData) data;
        }
        return JsonUtil.json2bean(JsonUtil.bean2json(data), RmsSyncQtyData.class);
    }

    private void doImeiSync(String operateType, List<String> fields, RmsSyncImeiData data) {
        log.info("----------doImeiSync-----------, operatorType: {}", operateType);
        if (data == null) {
            throw new IllegalArgumentException("RmsSyncImeiData cannot be null");
        }
        String rmsId = data.getRmsId();
        String retailId = data.getRetailId();
        log.info("doImeiSync rmsId: {}，retailId：{}", rmsId, retailId);
        Integer dataFrom = data.getDataFrom();

        if (dataFrom == null || (dataFrom.equals(DataFromEnum.RETAIL.getCode()) && retailId == null)
                || (dataFrom.equals(DataFromEnum.RMS.getCode()) && rmsId == null)) {
            throw new IllegalArgumentException("Invalid dataFrom or missing id fields");
        }

        String lockId = dataFrom.equals(DataFromEnum.RETAIL.getCode()) ? retailId : rmsId;

        try (DistributionLock ignored = distributionLockService.tryLock(RedisKeyEnum.LOCK_SYNC_DATA_IMEI.getKey(),
                lockId)) {
            if (ignored == null) {
                log.warn("Failed to acquire IMEI sync lock for lockId: {}", lockId);
                throw new CommonBusinessException("Failed to acquire IMEI sync lock");
            }

            IntlSoImei intlSoImeiExist = findIntlSoImei(dataFrom, retailId, rmsId);
            if (intlSoImeiExist == null) {
                intlSoImeiService.doImeiSave(data);
            } else {
                doImeiUpdate(operateType, fields, data, intlSoImeiExist.getId());
            }
            //发送消息同步ES
            syncSoToEsProducer.sendSyncEsMsg(DataSyncDataTypeEnum.IMEI, data.getId());
        }
    }

    /**
     * 抽出更新的逻辑判断
     *
     * @param operateType
     * @param fields
     * @param data
     * @param intlSoImeiId
     */
    private void doImeiUpdate(String operateType, List<String> fields,
                              RmsSyncImeiData data, Long intlSoImeiId) {

        if (operateType == null) {
            throw new CommonBusinessException(UNSUPPORTED_OPERATE_TYPE + "null");
        }

        DataSyncOperateTypeTypeEnum operateTypeEnum = DataSyncOperateTypeTypeEnum.getEnumByMsg(operateType);

        if (operateTypeEnum == null) {
            throw new CommonBusinessException(UNSUPPORTED_OPERATE_TYPE + operateType);
        }
        //已存在，但operateType=create，则不处理
        if (operateTypeEnum.equals(DataSyncOperateTypeTypeEnum.CREATE)) {
            return;
        }

        if (operateTypeEnum == DataSyncOperateTypeTypeEnum.REPORT_VERIFICATION ||
                operateTypeEnum == DataSyncOperateTypeTypeEnum.ACTIVATE_VERIFICATION) {
            data.setId(intlSoImeiId);
            intlSoImeiService.doImeiUpdate(operateType, fields, data);
        } else {
            throw new CommonBusinessException(UNSUPPORTED_OPERATE_TYPE + operateType);
        }
    }

    private IntlSoImei findIntlSoImei(Integer dataFrom, String retailId, String rmsId) {
        if (dataFrom.equals(DataFromEnum.RETAIL.getCode())) {
            return intlSoImeiService.getIntlSoImeiByCondition(Long.valueOf(retailId), null);
        } else if (dataFrom.equals(DataFromEnum.RMS.getCode())) {
            return intlSoImeiService.getIntlSoImeiByCondition(null, rmsId);
        }
        return null;
    }

    public void doQtySync(String operateType, RmsSyncQtyData data) {
        log.info("----------doQtySync-----------, operatorType: {}", operateType);
        if (data == null || data.getRmsId() == null) {
            throw new IllegalArgumentException("RmsSyncQtyData or rmsId cannot be null");
        }
        log.info("----------doQtySync-----------, rmsId: {}", data.getRmsId());

        String rmsId = data.getRmsId();
        try (DistributionLock ignored = distributionLockService.tryLock(RedisKeyEnum.LOCK_SYNC_DATA_QTY.getKey(),
                rmsId)) {
            if (ignored == null) {
                log.warn("Failed to acquire QTY sync lock for rmsId: {}", rmsId);
                throw new CommonBusinessException("Failed to acquire QTY sync lock");
            }

            DataSyncOperateTypeTypeEnum operateTypeEnum = DataSyncOperateTypeTypeEnum.getEnumByMsg(operateType);
            if (operateTypeEnum == null) {
                throw new CommonBusinessException(UNSUPPORTED_OPERATE_TYPE + operateType);
            }

            switch (operateTypeEnum) {
                case CREATE:
                    qtyService.doQtySave(data);
                    break;
                case UPDATE:
                    qtyService.doQtyUpdate(data);
                    break;
                default:
                    throw new CommonBusinessException(UNSUPPORTED_OPERATE_TYPE + operateType);
            }
            //发送消息同步ES
            syncSoToEsProducer.sendSyncEsMsg(DataSyncDataTypeEnum.QTY, data.getId());
        }
    }
}
