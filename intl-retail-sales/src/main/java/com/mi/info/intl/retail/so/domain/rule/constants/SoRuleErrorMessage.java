package com.mi.info.intl.retail.so.domain.rule.constants;

import java.io.Serializable;

/**
 * SO规则返回错误信息常量
 *
 * <AUTHOR>
 * @date 2025/7/25 16:36
 */
public class SoRuleErrorMessage implements Serializable {

    /**
     * 规则条数大于最大条数
     */
    public static final String RULES_GREATER_THAN_MAX = "The maximum number of rules allowed to be added is %s.";

    /**
     * imei规则中，存在user title 和 activation product line重复的数据
     */
    public static final String IMEI_RULE_REPEAT =
            "IMEI Activation Rules：User Title and Activation Product Line cannot be repeated.";

    /**
     * photo规则中，存在 user title 重复的数据
     */
    public static final String PHOTO_RULE_REPEAT = "Photo Rules：User Title cannot be repeated.";

    /**
     * imei规则中，存在 activate time 范围错误的数据
     */
    public static final String ACTIVATE_TIME_RANGE_ERROR =
            " IMEI Activation Rules：Activate Time From， Activate Time To, range error.";

    /**
     * 当前国家已存在规则记录，无法创建；
     */
    public static final String COUNTRY_EXISTS_RULE = " The country already has a rule record, and cannot be created.";

    /**
     * 当前国家已存在审批中的规则记录；
     */
    public static final String COUNTRY_EXISTS_APPROVING_RULE =
            " The country already has a approving rule, and cannot be modified.";
}
