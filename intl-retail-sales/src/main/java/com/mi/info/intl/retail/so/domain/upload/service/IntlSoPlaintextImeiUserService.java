package com.mi.info.intl.retail.so.domain.upload.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextUserRequest;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoPlaintextImeiUser;

/**
* <AUTHOR>
* @description 针对表【intl_so_plaintext_imei_user(明文IMEI用户列表)】的数据库操作Service
* @createDate 2025-07-31 17:15:13
*/
public interface IntlSoPlaintextImeiUserService extends IService<IntlSoPlaintextImeiUser> {

    IPage<PlainTextImeiUserDTO> pageList(PlainTextUserRequest request);
}
