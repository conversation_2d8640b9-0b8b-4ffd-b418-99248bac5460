package com.mi.info.intl.retail.so.infra.service.impl;

import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.core.config.CommonThreadPoolConfig;
import com.mi.info.intl.retail.so.app.dto.SnImeiActiveInfoDTO;
import com.mi.info.intl.retail.so.app.dto.SnImeiActiveReq;
import com.mi.info.intl.retail.so.infra.service.SnImeiActiveQueryService;
import com.mi.info.intl.retail.so.infra.service.SnImeiInfoHandler;
import com.mi.info.intl.retail.utils.X5ProtocolHttpUtil;
import com.xiaomi.core.auth.x5.X5AppInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * SN IMEI激活信息查询服务实现类
 */
@Service
@Slf4j
public class SnImeiActiveQueryServiceImpl<V> implements SnImeiActiveQueryService<V> {

    /**
     * imei服务地址
     */
    @Value("${service-config.imei-active.url:}")
    private String url;

    /**
     * imei服务appId
     */
    @Value("${service-config.imei-active.appId:}")
    private String appId;

    /**
     * imei服务apiKey
     */
    @Value("${service-config.imei-active.appKey:}")
    private String appKey;

    /**
     * 定义线程池配置为类属性
     */
    private final CommonThreadPoolConfig threadPoolConfig = CommonThreadPoolConfig.BATCH_QUERY_DEVICE_INFO;

    /**
     * 定义超时时间为10分钟
     */
    private static final long BATCH_QUERY_TIMEOUT_MINUTES = 10L;

    /**
     * 定义每批次处理的数量
     */
    private static final int BATCH_SIZE = 50;

    /**
     * 定义最大IMEI数量
     */
    private static final int MAX_IMEI_COUNT = 3000;

    /**
     * 批量查询IMEI激活信息
     *
     * @param imeiList IMEI列表
     * @return IMEI激活信息列表，当列表超过3000个时返回null
     */
    @Override
    public List<SnImeiActiveInfoDTO> batchQueryImeiActiveInfo(List<SnImeiActiveReq> imeiList) {
        return (List<SnImeiActiveInfoDTO>) batchQueryImeiActiveInfo(imeiList, null);
    }

    /**
     * 批量查询IMEI激活信息，并在获取到单个IMEI信息后执行回调处理
     *
     * @param snList SN 详情列表
     * @param handler SN 信息处理器，用于处理单个IMEI信息
     * @return IMEI激活信息列表，当列表超过3000个时返回null
     */
    @Override
    public List<V> batchQueryImeiActiveInfo(List<SnImeiActiveReq> snList, SnImeiInfoHandler<V> handler) {
        if (snList == null || snList.isEmpty()) {
            return Collections.emptyList();
        }

        // 限制 imeiList 最大3000个
        if (snList.size() > MAX_IMEI_COUNT) {
            log.error("IMEI list size exceeds maximum limit, size: {}, max: {}", snList.size(), MAX_IMEI_COUNT);
            return null;
        }

        // 分批处理任务
        List<List<SnImeiActiveReq>> batches = IntStream.range(0, (snList.size() + BATCH_SIZE - 1) / BATCH_SIZE)
                .mapToObj(i -> snList.subList(i * BATCH_SIZE,
                        Math.min((i + 1) * BATCH_SIZE, snList.size())))
                .collect(Collectors.toList());

        // 存储所有批次的结果
        List<V> result = Collections.emptyList();

        for (List<SnImeiActiveReq> batch : batches) {
            // 创建CompletableFuture任务列表，使用类属性中的线程池
            List<CompletableFuture<V>> futures = batch.stream()
                    .map(snImeiActiveReq -> CompletableFuture.supplyAsync(() -> {
                        final SnImeiActiveInfoDTO snImeiActiveInfoDTO = querySnActiveInfo(snImeiActiveReq);

                        if (handler != null) {
                            // 如果提供了处理器，则对每个结果执行回调处理
                            return handler.handle(snImeiActiveReq, snImeiActiveInfoDTO);
                        }
                        return (V) snImeiActiveInfoDTO;

                    }, threadPoolConfig.getExecutor()))
                    .collect(Collectors.toList());

            // 等待当前批次所有任务完成，设置10分钟超时
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            try {
                allFutures.get(BATCH_QUERY_TIMEOUT_MINUTES, TimeUnit.MINUTES);

                // 将当前批次结果添加到总结果中
                if (result.isEmpty()) {
                    result = futures.stream()
                            .map(CompletableFuture::join)
                            .collect(Collectors.toList());
                } else {
                    result.addAll(futures.stream()
                            .map(CompletableFuture::join)
                            .collect(Collectors.toList()));
                }
            } catch (Exception e) {
                log.error("Batch query imei active info timeout or error, timeout: {} minutes",
                        BATCH_QUERY_TIMEOUT_MINUTES, e);
                Thread.currentThread().interrupt();
            }
        }

        return result;
    }

    /**
     * 查询单个IMEI激活信息
     *
     * @param req SN 详情
     * @return IMEI激活信息
     */
    private SnImeiActiveInfoDTO querySnActiveInfo(SnImeiActiveReq req) {
        SnImeiActiveInfoDTO snImeiActiveInfo = null;
        try {
            snImeiActiveInfo = X5ProtocolHttpUtil.doX5Post(url + "/imei/active/info",
                    new X5AppInfo(appId, appKey, "getImeiActiveInfo"), req, SnImeiActiveInfoDTO.class);
            log.info("querySnActiveInfo:{}", JSON.toJSONString(snImeiActiveInfo));
        } catch (Exception e) {
            log.error("querySnActiveInfo error:{}", e.getMessage());
        }
        return snImeiActiveInfo;
    }
}