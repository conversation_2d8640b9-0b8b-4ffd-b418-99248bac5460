package com.mi.info.intl.retail.so.app.provider.upload;

import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.enums.SerialNumberType;
import com.mi.info.intl.retail.intlretail.service.api.ldu.SnImeiService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.ImeiReportVerifyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.upload.config.ImeiVerifyConfig;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.enums.SalesmanJobTitleEnum;
import com.mi.info.intl.retail.so.domain.upload.enums.SerialNumberManagementEnum;
import com.mi.info.intl.retail.so.domain.upload.enums.VerifyResult;
import com.mi.info.intl.retail.so.domain.upload.enums.YesOrNo;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.util.MaskUtil;
import com.mi.info.intl.retail.utils.Md5Util;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ImeiReportVerifyServiceImpl implements ImeiReportVerifyService {
    @Resource
    private SnImeiService snImeiService;

    @Resource
    private ProductApiService productApiService;

    @Resource
    private IntlSoImeiMapper intlSoImeiMapper;

    @Resource
    private ImeiVerifyConfig imeiVerifyConfig;

    @Override
    public CommonApiResponse<Object> imeiReportVerify(ImeiReportVerifyRequest request) {
        log.info("imeiReportVerify_request:{}", JSON.toJSONString(request));

        try {
            // 1. 校验入参
            CommonApiResponse<Object> validateResult = validateRequest(request);
            if (validateResult != null) {
                return validateResult;
            }

            // 2. sns元素转成大写，并根据是否SN分组
            List<String> upperSns = request.getSns().stream()
                    .map(String::toUpperCase)
                    .collect(Collectors.toList());

            List<String> snList = new ArrayList<>();
            List<String> imeiList = new ArrayList<>();

            for (String sns : upperSns) {
                if (SnImeiValidateUtil.isSn(sns)) {
                    snList.add(sns);
                } else {
                    imeiList.add(sns);
                }
            }

            // 3.读取dayu配置进行sn的hash并保存数据
            List<String> hashCountryList = imeiVerifyConfig.getHashCountryList();
            Integer isHashCountry = (hashCountryList != null && hashCountryList.contains(request.getCountryCode()))
                    ? YesOrNo.YES.getValue()
                    : YesOrNo.NO.getValue();

            // 4. 调用IMEI服务 - 获取产品信息
            SnImeiQueryDto imeiQueryDto = new SnImeiQueryDto();
            imeiQueryDto.setSnList(snList);
            imeiQueryDto.setImeiList(imeiList);
            CommonResponse<List<SnImeiInfoDto>> snImeiInfoResponse = snImeiService.querySnImeiInfo(imeiQueryDto);
            List<SnImeiInfoDto> snImeiInfoList = snImeiInfoResponse != null && snImeiInfoResponse.getData() != null
                    ? snImeiInfoResponse.getData()
                    : new ArrayList<>();

            // 5. 处理SN列表 - 获取产品信息
            Map<String, ProductInfoDTO> productInfoMap = new HashMap<>();
            Map<String, String> snProductCodeMap = new HashMap<>();
            getProductInfo(snList, snImeiInfoList, snProductCodeMap, productInfoMap);

            // 6. 循环处理入参sns，校验合法性、产品信息、填充数据
            List<ImeiReportVerifyResponse> responseList = new ArrayList<>();
            for (String inputSns : upperSns) {
                ImeiReportVerifyResponse response = processVerification(inputSns, snImeiInfoList, snProductCodeMap, productInfoMap, isHashCountry);
                responseList.add(response);
            }

            // 7. 重复性校验
            checkDuplicates(responseList, request.getUserTitle(), request.getCountryCode());

            log.info("imeiReportVerify_response:{}", JSON.toJSONString(responseList));
            return new CommonApiResponse<>(responseList);
        } catch (Exception e) {
            log.error("ImeiReportVerifyServiceImpl error:{}", e.getMessage());
            return new CommonApiResponse<>(500, "ImeiReportVerifyServiceImpl Error:" + e.getMessage(), "");
        }

    }

    private CommonApiResponse<Object> validateRequest(ImeiReportVerifyRequest request) {
        if (CollectionUtils.isEmpty(request.getSns())) {
            return new CommonApiResponse<>(400, "sns cannot be empty", "");
        }
        if (request.getSns().size() > 100) {
            return new CommonApiResponse<>(400, "sns size cannot exceed 100", "");
        }
        if (StringUtils.isEmpty(request.getMiId())) {
            return new CommonApiResponse<>(400, "miId cannot be empty", "");
        }
        if (request.getUserTitle() == 0) {
            return new CommonApiResponse<>(400, "userTitle cannot be empty", "");
        }
        if (StringUtils.isEmpty(request.getCountryCode())) {
            return new CommonApiResponse<>(400, "countryCode cannot be empty", "");
        }
        return null;
    }

    private ImeiReportVerifyResponse processVerification(String inputSns, List<SnImeiInfoDto> snImeiInfoList,
                                                         Map<String, String> snProductCodeMap,
                                                         Map<String, ProductInfoDTO> productInfoMap, Integer isHashCountry) {
        ImeiReportVerifyResponse response = new ImeiReportVerifyResponse();
        response.setInputIMEI(inputSns);
        response.setIsHashCountry(isHashCountry);

        // 合法性校验
        if (!validateLegality(inputSns)) {
            response.setVerifyResult(VerifyResult.LEGALITY_VALIDATION_FAILED.getValue());
            return response;
        }

        // 产品校验和填充产品信息
        validateProductAndFillData(inputSns, response, snImeiInfoList, snProductCodeMap, productInfoMap, isHashCountry);

        return response;
    }

    private boolean validateLegality(String inputSns) {
        // 位数校验：length为13-25
        if (StringUtils.isEmpty(inputSns) || inputSns.length() < 13 || inputSns.length() > 25) {
            return false;
        }

        // 格式校验
        SerialNumberType type = SnImeiValidateUtil.getSerialNumberType(inputSns);
        return type != SerialNumberType.UNKNOWN;
    }

    private void getProductInfo(List<String> snList, List<SnImeiInfoDto> snImeiInfoList,
                                Map<String, String> snProductCodeMap, Map<String, ProductInfoDTO> productInfoMap) {

        List<Integer> productCodes = new ArrayList<>();
        if (!CollectionUtils.isEmpty(snList)) {
            // 构建字典sn-productCode，key是sn，value是"/"前的productCode转为String
            for (String sn : snList) {
                if (sn.contains("/")) {
                    String productCode = sn.substring(0, sn.indexOf("/"));
                    snProductCodeMap.put(sn, productCode);
                }
            }

            // 获取所有productId并调用ProductApiService查询产品信息
            if (!snProductCodeMap.isEmpty()) {
                productCodes = snProductCodeMap.values().stream()
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());

            }
        }
        // 从snImeiInfoList中提取goodsId并合并到productCodes
        List<Integer> imeiProductCodes = snImeiInfoList.stream()
                .filter(Objects::nonNull)
                .map(SnImeiInfoDto::getGoodsId)
                .filter(Objects::nonNull)
                .map(Integer::valueOf)  // 转换String到Integer
                .distinct()
                .collect(Collectors.toList());

        productCodes.addAll(imeiProductCodes);  // 合并而不是覆盖
        productCodes = productCodes.stream().distinct().collect(Collectors.toList()); // 最终去重

        Map<Integer, ProductInfoDTO> productInfoMapById = productApiService.queryProductsByGoodIds(productCodes);
        // 将productInfoMapById转换为以goodsId为key的Map
        if (productInfoMapById != null) {
            for (ProductInfoDTO productInfo : productInfoMapById.values()) {
                if (productInfo != null && productInfo.getGoodsId() != null) {
                    productInfoMap.put(productInfo.getGoodsId(), productInfo);
                }
            }
        }
    }

    private void validateProductAndFillData(String inputSns, ImeiReportVerifyResponse response, List<SnImeiInfoDto> snImeiInfoList,
                                            Map<String, String> snProductCodeMap, Map<String, ProductInfoDTO> productInfoMap, Integer isHashCountry) {

        SnImeiInfoDto matchedSnImei = findSnImeiInfo(inputSns, snImeiInfoList);
        ProductInfoDTO productInfo;
        // 产品校验
        if (SnImeiValidateUtil.isSn(inputSns)) {
            // 是SN
            String productCode = snProductCodeMap.get(inputSns);
            // productCode为空或不在主数据产品表productInfoMap中，校验失败
            if (productCode == null || !productInfoMap.containsKey(productCode)) {
                response.setVerifyResult(VerifyResult.PRODUCT_VALIDATION_FAILED.getValue());
                return;
            }

            // 非串码管理的商品，校验成功
            productInfo = productInfoMap.get(productCode);
            if (productInfo.getIsSn() == null || !productInfo.getIsSn().equals(SerialNumberManagementEnum.YES.getValue())) {
                fillResponseFromSnImeiInfo(inputSns, response, matchedSnImei, productInfo, isHashCountry);
                return;
            }

            // 串码管理的商品，判断是否可以在IMEI服务中查到
            if (matchedSnImei == null) {
                // 不存在，校验失败
                response.setVerifyResult(VerifyResult.PRODUCT_VALIDATION_FAILED.getValue());
                return;
            }

        } else {
            // 不是SN，判断该imei是否可以在IMEI服务中查到
            if (matchedSnImei == null) {
                // 不存在，校验失败
                response.setVerifyResult(VerifyResult.PRODUCT_VALIDATION_FAILED.getValue());
                return;
            }

            // IMEI：通过imei服务的goodsId获取产品信息
            productInfo = productInfoMap.get(matchedSnImei.getGoodsId());
        }

        // 填充赋值
        fillResponseFromSnImeiInfo(inputSns, response, matchedSnImei, productInfo, isHashCountry);
    }

    private SnImeiInfoDto findSnImeiInfo(String inputSns, List<SnImeiInfoDto> snImeiInfoList) {
        return snImeiInfoList.stream()
                .filter(info -> inputSns.equals(info.getSn()) ||
                        inputSns.equals(info.getImei()) ||
                        inputSns.equals(info.getImei2()))
                .findFirst()
                .orElse(null);
    }

    private void fillResponseFromSnImeiInfo(String inputSns, ImeiReportVerifyResponse response, SnImeiInfoDto snImeiInfo,
                                            ProductInfoDTO productInfo, Integer isHashCountry) {
        response.setVerifyResult(VerifyResult.SUCCESS.getValue());
        String sn = inputSns;
        if (snImeiInfo != null) {
            sn = snImeiInfo.getSn();
            response.setSapId(snImeiInfo.getB2bCustomerCode());
            response.setSaleChannel(snImeiInfo.getSaleChannel());
            if (isHashCountry.equals(YesOrNo.NO.getValue())) {
                // 非哈希国家，返回明文
                response.setSn(snImeiInfo.getSn());
                response.setImei1(snImeiInfo.getImei());
                response.setImei2(snImeiInfo.getImei2());
            } else {
                // 哈希国家，返回掩码
                response.setSn(MaskUtil.maskString(snImeiInfo.getSn()));
                response.setImei1(MaskUtil.maskString(snImeiInfo.getImei()));
                response.setImei2(MaskUtil.maskString(snImeiInfo.getImei2()));
            }
        }
        response.setSnHash(Md5Util.generateSha256Hash(sn));

        if (productInfo != null) {
            response.setProductCode(productInfo.getGoodsId());
            response.setProductId(productInfo.getId());
            response.setProductName(productInfo.getName());
            response.setProductSku(productInfo.getSkuName());
        }
    }


    private void checkDuplicates(List<ImeiReportVerifyResponse> responseList, Integer userTitle, String countryCode) {
        // 6. 获取验证成功的SN Hash
        List<String> successSnHashes = responseList.stream()
                .filter(r -> r.getVerifyResult() == VerifyResult.SUCCESS.getValue())
                .map(ImeiReportVerifyResponse::getSnHash)
                .filter(hash -> !StringUtils.isEmpty(hash))
                .collect(Collectors.toList());

        // 7-8. 重复性校验
        if (!CollectionUtils.isEmpty(successSnHashes)) {
            List<IntlSoImei> duplicateRecords = queryDuplicateRecords(successSnHashes, userTitle, countryCode);

            // 提取重复记录的snHash列表
            Set<String> duplicateSnHashSet = duplicateRecords.stream()
                    .map(IntlSoImei::getSnHash)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 9. 批量更新重复的记录
            responseList.forEach(response -> {
                if (duplicateSnHashSet.contains(response.getSnHash())) {
                    response.setVerifyResult(VerifyResult.DUPLICATE_VALIDATION_FAILED.getValue());
                }
            });
        }
    }

    private List<IntlSoImei> queryDuplicateRecords(List<String> snHashes, Integer userTitle, String countryCode) {
        // 计算三个月前的UTC时间戳
        Instant threeMonthsAgoInstant = Instant.now().minus(90, ChronoUnit.DAYS);
        long threeMonthsAgo = threeMonthsAgoInstant.toEpochMilli();

        // 根据角色获取职位代码列表
        List<Integer> jobTitleCodes;

        if (SalesmanJobTitleEnum.PROMOTER_CODES.contains(userTitle)) {
            jobTitleCodes = SalesmanJobTitleEnum.PROMOTER_CODES;
        } else {
            jobTitleCodes = SalesmanJobTitleEnum.ALL_CODES;
        }

        return intlSoImeiMapper.queryDuplicateRecords(
                snHashes, countryCode, threeMonthsAgo, jobTitleCodes);
    }
}