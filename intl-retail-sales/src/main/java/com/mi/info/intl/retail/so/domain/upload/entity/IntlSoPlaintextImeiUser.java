package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 明文IMEI用户列表
 * @TableName intl_so_plaintext_imei_user
 */
@TableName(value = "intl_so_plaintext_imei_user")
@Data
public class IntlSoPlaintextImeiUser implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联国家名称
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 关联国家短代码
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 联系人图片url
     */
    @TableField(value = "photo_url")
    private String photoUrl;

    /**
     * 用户mid
     */
    @TableField(value = "user_mid")
    private Long userMid;

    /**
     * 创建人mid
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 创建时间戳
     */
    @TableField(value = "created_on")
    private Long createdOn;

    /**
     * 修改人mid
     */
    @TableField(value = "modified_by")
    private Long modifiedBy;

    /**
     * 修改时间戳
     */
    @TableField(value = "modified_on")
    private Long modifiedOn;

    /**
     * 是否删除
     */
    @TableField(value = "status")
    private Boolean status;



}