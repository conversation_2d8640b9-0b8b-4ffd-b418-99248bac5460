package com.mi.info.intl.retail.so.domain.rule.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.exception.BizException;
import static com.mi.info.intl.retail.exception.ErrorCodes.SO_RULE_LOG_NOT_EXIST;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ApproverDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ImeiRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.PhotoRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogListDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleChangeLogReq;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleLogReq;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetailLog;
import com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleChangeLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:05
 */
@Slf4j
@Service
public class IntlSoRuleChangeLogServiceImpl extends ServiceImpl<IntlSoRuleChangeLogMapper, IntlSoRuleDetailLog>
        implements IntlSoRuleChangeLogService {

    @Override
    public IPage<SoRuleChangeLogListDTO> getSoRuleDetailLogList(QuerySoRuleLogReq req) {
        IPage<IntlSoRuleDetailLog> page = new Page<>(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getMasterId, req.getId());
        queryWrapper.orderByDesc(IntlSoRuleDetailLog::getUpdatedAt);

        IPage<IntlSoRuleDetailLog> logPage = this.page(page, queryWrapper);
        List<SoRuleChangeLogListDTO> collect = logPage.getRecords().stream().map(item -> {
            SoRuleChangeLogListDTO dto = new SoRuleChangeLogListDTO();
            dto.setId(item.getId());
            dto.setApprovalId(item.getApprovalId());
            dto.setStatus(item.getStatus());
            String approvalJson = item.getApproverList();
            List<ApproverDTO> approverList = JSON.parseArray(approvalJson, ApproverDTO.class);
            if (CollectionUtils.isNotEmpty(approverList)) {
                ApproverDTO approverDTO =
                        approverList.stream().min(Comparator.comparing(ApproverDTO::getSort)).get();
                dto.setApplicant(approverDTO.getApproverList().get(0).getUserName());
            }
            dto.setApplicationTime(item.getUpdatedAt());
            return dto;
        }).collect(Collectors.toList());
        IPage<SoRuleChangeLogListDTO> pageResult =
                new Page<>(logPage.getCurrent(), logPage.getSize(), logPage.getTotal());
        pageResult.setRecords(collect);
        return pageResult;

    }

    /**
     * 通过ID获取SO规则细节日志
     *
     * @param req req
     * @return {@link SoRuleChangeLogDTO }
     */
    @Override
    public SoRuleChangeLogDTO getSoRuleChangeLogById(QuerySoRuleChangeLogReq req) {

        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getId, req.getId());
        IntlSoRuleDetailLog intlSoRuleDetailLog = this.getOne(queryWrapper);
        if (intlSoRuleDetailLog == null) {
            log.error("The so rule does not exist in the approval record {}", req.getId());
            throw new BizException(SO_RULE_LOG_NOT_EXIST, req.getId());
        }
        return getSoRuleDetailLogDTO(intlSoRuleDetailLog);

    }

    @Override
    public SoRuleChangeLogDTO getRuleDetailByCodeAndStatus(String countryCode, Integer status) {
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getCountryCode, countryCode)
                .eq(status != null, IntlSoRuleDetailLog::getStatus, status);
        IntlSoRuleDetailLog intlSoRuleDetailLog = this.getOne(queryWrapper);
        if (intlSoRuleDetailLog == null) {
            log.error("The so rule does not exist in the approval record {}", countryCode);
            throw new BizException(SO_RULE_LOG_NOT_EXIST, countryCode);
        }
        return getSoRuleDetailLogDTO(intlSoRuleDetailLog);
    }

    public SoRuleChangeLogDTO getSoRuleDetailLogDTO(IntlSoRuleDetailLog intlSoRuleDetailLog) {
        SoRuleChangeLogDTO dto = new SoRuleChangeLogDTO();
        dto.setId(intlSoRuleDetailLog.getId());
        dto.setCountryCode(intlSoRuleDetailLog.getCountryCode());
        dto.setEffectiveTime(intlSoRuleDetailLog.getEffectiveTime());
        dto.setStatus(intlSoRuleDetailLog.getStatus());
        dto.setPhotoRuleList(JSON.parseArray(intlSoRuleDetailLog.getPhotoRuleList(), PhotoRuleDTO.class));
        dto.setImeiRuleList(JSON.parseArray(intlSoRuleDetailLog.getImeiRuleList(), ImeiRuleDTO.class));
        dto.setApproverList(JSON.parseArray(intlSoRuleDetailLog.getApproverList(), ApproverDTO.class));
        dto.setBpmBody(intlSoRuleDetailLog.getBpmBody());
        dto.setCreatedAt(intlSoRuleDetailLog.getCreatedAt());
        dto.setCreatedBy(intlSoRuleDetailLog.getCreatedBy());
        dto.setUpdatedAt(intlSoRuleDetailLog.getUpdatedAt());
        dto.setUpdatedBy(intlSoRuleDetailLog.getUpdatedBy());
        return dto;
    }

    @Override
    public boolean existApprovingRule(String countryCode) {
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getCountryCode, countryCode)
                .in(IntlSoRuleDetailLog::getStatus, SoRuleDetailApproveStatus.HAS_PENDING_APPROVAL_YES);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public IntlSoRuleDetailLog getByApprovalId(String approvalId) {
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getApprovalId, approvalId);
        return this.getOne(queryWrapper);
    }

}
