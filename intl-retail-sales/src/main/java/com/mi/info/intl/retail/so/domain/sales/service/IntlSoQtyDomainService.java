package com.mi.info.intl.retail.so.domain.sales.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingTypeRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesQtyRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
import com.mi.info.intl.retail.so.domain.sales.constant.SalesNrJobConstant;
import com.mi.info.intl.retail.so.domain.sales.converter.SalesQtyConverter;
import com.mi.info.intl.retail.so.domain.sales.enums.ReportingRoleEnum;
import com.mi.info.intl.retail.so.domain.sales.enums.ReportingTypeEnum;
import com.mi.info.intl.retail.so.domain.sales.model.SoQtyIndex;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import com.xiaomi.cnzone.storems.common.exception.ErrorCodeEnums;
import com.xiaomi.nr.job.admin.dto.TriggerJobRequestDTO;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.nr.job.core.biz.model.HandleMsg;
import com.xiaomi.nr.job.core.context.JobHelper;
import com.xiaomi.nr.job.core.handler.annotation.NrJob;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

/**
 * @Project: intl-retail
 * @Description: 销量领域服务
 * @Author: 周楚强
 * @Date: 2025-08-01
 **/
@Slf4j
@Service
public class IntlSoQtyDomainService {
    
    @Resource
    private IntlSoQtyEsService intlSoQtyEsService;
    
    @Resource
    private SalesQtyConverter salesQtyConverter;
    
    @Resource
    private FdsService fdsService;
    
    @DubboReference(group = "${store.dubbo.group}", check = false, interfaceClass = NrJobService.class)
    private NrJobService nrJobService;
    
    @Value("${job.salesQty.project.id:-1}")
    private Long projectId;
    
    /**
     * 获取总条数
     *
     * @param salesQtyReqDto 查询参数
     * @return 总条数
     */
    public PageResponse<SalesQtyRespDto> queryEs(SalesQtyReqDto salesQtyReqDto) {
        PageResponse<SoQtyIndex> pageResponse = intlSoQtyEsService.search(salesQtyReqDto);
        List<SalesQtyRespDto> target = salesQtyConverter.toTarget(pageResponse.getData());
        return PageResponse.of(target, pageResponse.getTotalCount(), salesQtyReqDto.getPageSize(), salesQtyReqDto.getPageIndex());
    }
    
    /**
     * 获取总条数
     *
     * @param salesQtyReqDto 查询参数
     * @return 总条数
     */
    public ReportingRoleRespDto getQtyReportingRoleData(SalesQtyReqDto salesQtyReqDto) {
        salesQtyReqDto.setGroupBy("salesmanJobtitle");
        Map<String, Long> statisticsMap = intlSoQtyEsService.groupByAgg(salesQtyReqDto);
        ReportingRoleRespDto reportingRoleRespDto = new ReportingRoleRespDto();
        
        reportingRoleRespDto.setPromoterCount(
                statisticsMap.get(ReportingRoleEnum.PROMOTER.getCode()) == null ? 0L : statisticsMap.get(ReportingRoleEnum.PROMOTER.getCode()));
        reportingRoleRespDto.setTemporaryPromoterCount(
                statisticsMap.get(ReportingRoleEnum.TEMPORARY_PROMOTER.getCode()) == null ?
                        0L : statisticsMap.get(ReportingRoleEnum.TEMPORARY_PROMOTER.getCode()));
        reportingRoleRespDto.setSupervisorCount(
                statisticsMap.get(ReportingRoleEnum.SUPERVISOR.getCode()) == null ? 0L : statisticsMap.get(ReportingRoleEnum.SUPERVISOR.getCode()));
        reportingRoleRespDto.setSupervisorWithoutPromoterCount(
                statisticsMap.get(ReportingRoleEnum.SUPERVISOR_WITHOUT_PROMOTERS.getCode()) == null ? 0L
                        : statisticsMap.get(ReportingRoleEnum.SUPERVISOR_WITHOUT_PROMOTERS.getCode()));
        reportingRoleRespDto.setMerchandiserCount(
                statisticsMap.get(ReportingRoleEnum.MERCHANDISER.getCode()) == null ? 0L : statisticsMap.get(ReportingRoleEnum.MERCHANDISER.getCode()));
        
        Long totalCount = statisticsMap.values().stream().mapToLong(Long::longValue).sum();
        Long othersCount = totalCount - reportingRoleRespDto.getPromoterCount() - reportingRoleRespDto.getTemporaryPromoterCount()
                - reportingRoleRespDto.getSupervisorCount() - reportingRoleRespDto.getSupervisorWithoutPromoterCount()
                - reportingRoleRespDto.getMerchandiserCount();
        reportingRoleRespDto.setOthersCount(othersCount);
        return reportingRoleRespDto;
    }
    
    /**
     * 获取报表类型数据（MySQL）
     *
     * @param salesQtyReqDto 报表请求参数
     * @return 报表类型数据
     */
    public ReportingTypeRespDto getQtyReportingTypeData(SalesQtyReqDto salesQtyReqDto) {
        salesQtyReqDto.setGroupBy("reportingType");
        Map<String, Long> statisticsMap = intlSoQtyEsService.groupByAgg(salesQtyReqDto);
        ReportingTypeRespDto reportingRoleRespDto = new ReportingTypeRespDto();
        reportingRoleRespDto.setAppCount(statisticsMap.get(ReportingTypeEnum.PC.getCode()) == null ? 0L : statisticsMap.get(ReportingTypeEnum.PC.getCode()));
        reportingRoleRespDto.setPcCount(statisticsMap.get(ReportingTypeEnum.APP.getCode()) == null ? 0L : statisticsMap.get(ReportingTypeEnum.APP.getCode()));
        return reportingRoleRespDto;
    }
    
    public String export(SalesQtyReqDto query) {
        TriggerJobRequestDTO jobReq = new TriggerJobRequestDTO();
        String account = RpcContext.getContext().getAttachments().get("$upc_account");
        BusinessException.when(StringUtils.isBlank(account), "无法获取当前用户");
        String taskName = String.format("Sales QTY列表_%s", DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss"));
        jobReq.setTaskName(taskName);
        jobReq.setTaskParam(JacksonUtil.toStr(query));
        jobReq.setTaskDesc("Sales QTY列表导出");
        jobReq.setJobKey(SalesNrJobConstant.SALES_QTY_DATA_EXPORT_JOB_KEY);
        jobReq.setOwner(account);
        jobReq.setProjectId(projectId);
        jobReq.setProjectName(SalesNrJobConstant.PROJECT_NAME);
        log.info("Sales QTY create export task request: {}", jobReq);
        Result<String> stringResult = triggerJobWithLogging(jobReq);
        validateResult(stringResult);
        return stringResult.getData();
    }
    
    @NrJob(SalesNrJobConstant.SALES_QTY_DATA_EXPORT_JOB_HANDLER)
    public Result<HandleMsg> exportHandler() {
        String param = JobHelper.getJobParam();
        log.info("exportHandler param:{}", param);
        SalesQtyReqDto query = JacksonUtil.parseObj(param, SalesQtyReqDto.class);
        try {
            String url = excelExport4SalesQty(query);
            HandleMsg resp = new HandleMsg();
            resp.setFileUrl(url);
            String fileUrl = JacksonUtil.toStr(resp);
            JobHelper.handleSuccess(fileUrl);
            log.info("exportHandler url:{}", fileUrl);
            return Result.success(resp);
        } catch (Exception e) {
            log.error("导出Sales QTY数据异常 param:{}", param, e);
            JobHelper.handleFail(e.getMessage());
            return null;
        }
    }
    
    public String excelExport4SalesQty(SalesQtyReqDto query) throws IOException {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        String excelName = "Sales QTY Details";
        try {
            tempFile = File.createTempFile(excelName, ".xlsx");
            excelWriter = EasyExcel.write(tempFile, SalesQtyRespDto.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(excelName).build();
            
            int pageSize = 1000;
            int currentPage = 1;
            boolean hasNext = true;
            while (hasNext) {
                query.setPageIndex(currentPage);
                query.setPageSize(pageSize);
                PageResponse<SoQtyIndex> soQtyIndexPage = intlSoQtyEsService.search(query);
                List<SoQtyIndex> soQtyIndexList = soQtyIndexPage.getData();
                if (CollUtil.isEmpty(soQtyIndexList)) {
                    hasNext = false;
                    continue;
                }
                List<SalesQtyRespDto> salesQtyRespDtoList = salesQtyConverter.toTarget(soQtyIndexList);
                excelWriter.write(salesQtyRespDtoList, writeSheet);
                
                hasNext = currentPage * pageSize < soQtyIndexPage.getTotalCount();
                currentPage++;
            }
            
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (currentPage == 1L) {
                throw new BusinessException(ErrorCodeEnums.BUS_EXCEPTION.getErrorCode(), "Data is missing");
            }
            
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            FdsUploadResult upload = fdsService.upload(excelName + timestamp + ".xlsx", tempFile, true);
            return upload.getUrl();
        } catch (Exception e) {
            log.error("Sales QTY Data Export Exception query: {}", JacksonUtil.toStr(query), e);
            throw new BusinessException(ErrorCodeEnums.BUS_EXCEPTION.getErrorCode(), "Sales QTY Data Export Exception");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                Files.delete(tempFile.toPath());
            }
        }
    }
    
    private Result<String> triggerJobWithLogging(TriggerJobRequestDTO request) {
        try {
            long start = System.currentTimeMillis();
            Result<String> result = nrJobService.triggerJob(request);
            long duration = System.currentTimeMillis() - start;
            log.info("Sales QTY create export task call triggerJob finished, cost time: {}ms, result: {}", duration, result);
            return result;
        } catch (Exception e) {
            log.error("Sales QTY create export task call nrJob error", e);
            throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "调用任务中心创建任务失败");
        }
    }
    
    private void validateResult(Result<String> result) {
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), result.getMessage());
        }
        
        String status = getStatusFromResult(result);
        handleStatusError(status);
    }
    
    private String getStatusFromResult(Result<String> result) {
        if (result.getAttachments() != null) {
            return result.getAttachments().get("status");
        }
        return null;
    }
    
    private void handleStatusError(String status) {
        if (StringUtils.isEmpty(status)) {
            return;
        }
        switch (status) {
            case SalesNrJobConstant.TASK_NUM_LIMIT_EXCEEDED_CODE:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "导出任务数量超出上限");
            case SalesNrJobConstant.TASK_IS_RUNNING_ERROR_CIDE:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "任务正在进行中，请稍后再试");
            default:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "调用任务中心创建任务失败");
        }
    }
    
}
