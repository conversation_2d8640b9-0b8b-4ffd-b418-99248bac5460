package com.mi.info.intl.retail.so.domain.upload.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackRequest;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【intl_so_sn_blacklist(销量SO序列号黑名单)】的数据库操作Service
* @createDate 2025-07-24 20:05:41
*/
public interface IntlSoSnBlacklistService extends IService<IntlSoSnBlacklist> {

    IPage<SnBlackDTO> pageList(SnBlackRequest request);

}
