package com.mi.info.intl.retail.so.domain.sales.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesImeiRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.VerificationResultRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.domain.sales.constant.SalesNrJobConstant;
import com.mi.info.intl.retail.so.domain.sales.converter.SalesImeiConverter;
import com.mi.info.intl.retail.so.domain.sales.enums.ReportingRoleEnum;
import com.mi.info.intl.retail.so.domain.sales.enums.VerificationResultEnum;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoPlaintextImeiUser;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoPlaintextImeiUserMapper;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import com.xiaomi.cnzone.storems.common.exception.ErrorCodeEnums;
import com.xiaomi.nr.job.admin.dto.TriggerJobRequestDTO;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.nr.job.core.biz.model.HandleMsg;
import com.xiaomi.nr.job.core.context.JobHelper;
import com.xiaomi.nr.job.core.handler.annotation.NrJob;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

/**
 * 功能描述：Sales IMEI 领域服务
 *
 * <AUTHOR>
 * @date 2025/8/1
 */
@Slf4j
@Service
public class IntlSoImeiDomainService {
    
    @Resource
    private IntlSoImeiEsService intlSoImeiEsService;
    
    @Resource
    private SalesImeiConverter salesImeiConverter;
    
    @Resource
    private IntlSoPlaintextImeiUserMapper intlSoPlaintextImeiUserMapper;
    
    @Resource
    private FdsService fdsService;
    
    @DubboReference(group = "${store.dubbo.group}", check = false, interfaceClass = NrJobService.class)
    private NrJobService nrJobService;
    
    @Value("${job.salesImei.project.id:-1}")
    private Long projectId;
    
    public PageResponse<SalesImeiRespDto> getSalesImeiForPage(SalesImeiReqDto salesImeiReqDto) {
        PageResponse<SoImeiIndex> soImeiIndexPage = intlSoImeiEsService.queryByPage(salesImeiReqDto);
        List<SalesImeiRespDto> salesImeiRespDtoList = salesImeiConverter.toTarget(soImeiIndexPage.getData());
        return PageResponse.of(salesImeiRespDtoList, soImeiIndexPage.getTotalCount(), salesImeiReqDto.getPageSize(), salesImeiReqDto.getPageIndex());
    }
    
    public VerificationResultRespDto getImeiVerificationResultData(SalesImeiReqDto salesImeiReqDto) {
        salesImeiReqDto.setGroupBy("activationResult");
        Map<String, Long> statisticsMap = intlSoImeiEsService.groupByAgg(salesImeiReqDto);
        VerificationResultRespDto reportingRoleRespDto = new VerificationResultRespDto();
        reportingRoleRespDto.setVerifyingCount(
                statisticsMap.get(VerificationResultEnum.VEFIRYING.getCode()) == null ? 0L : statisticsMap.get(VerificationResultEnum.VEFIRYING.getCode()));
        reportingRoleRespDto.setFailedCount(
                statisticsMap.get(VerificationResultEnum.FAILED.getCode()) == null ? 0L : statisticsMap.get(VerificationResultEnum.FAILED.getCode()));
        reportingRoleRespDto.setSuccessfullyCount(statisticsMap.get(VerificationResultEnum.SUCCESSFULLY.getCode()) == null ? 0L
                : statisticsMap.get(VerificationResultEnum.SUCCESSFULLY.getCode()));
        reportingRoleRespDto.setTimeoutCount(
                statisticsMap.get(VerificationResultEnum.TIMEOUT.getCode()) == null ? 0L : statisticsMap.get(VerificationResultEnum.TIMEOUT.getCode()));
        
        return reportingRoleRespDto;
    }
    
    public ReportingRoleRespDto getImeiReportingRoleData(SalesImeiReqDto salesImeiReqDto) {
        salesImeiReqDto.setGroupBy("salesmanJobtitle");
        Map<String, Long> statisticsMap = intlSoImeiEsService.groupByAgg(salesImeiReqDto);
        ReportingRoleRespDto reportingRoleRespDto = new ReportingRoleRespDto();
        
        reportingRoleRespDto.setPromoterCount(
                statisticsMap.get(ReportingRoleEnum.PROMOTER.getCode()) == null ? 0L : statisticsMap.get(ReportingRoleEnum.PROMOTER.getCode()));
        reportingRoleRespDto.setTemporaryPromoterCount(statisticsMap.get(ReportingRoleEnum.TEMPORARY_PROMOTER.getCode()) == null ?
                0L : statisticsMap.get(ReportingRoleEnum.TEMPORARY_PROMOTER.getCode()));
        reportingRoleRespDto.setSupervisorCount(
                statisticsMap.get(ReportingRoleEnum.SUPERVISOR.getCode()) == null ? 0L : statisticsMap.get(ReportingRoleEnum.SUPERVISOR.getCode()));
        reportingRoleRespDto.setSupervisorWithoutPromoterCount(
                statisticsMap.get(ReportingRoleEnum.SUPERVISOR_WITHOUT_PROMOTERS.getCode()) == null ? 0L
                        : statisticsMap.get(ReportingRoleEnum.SUPERVISOR_WITHOUT_PROMOTERS.getCode()));
        reportingRoleRespDto.setMerchandiserCount(
                statisticsMap.get(ReportingRoleEnum.MERCHANDISER.getCode()) == null ? 0L : statisticsMap.get(ReportingRoleEnum.MERCHANDISER.getCode()));
        
        Long totalCount = statisticsMap.values().stream().mapToLong(Long::longValue).sum();
        Long othersCount = totalCount - reportingRoleRespDto.getPromoterCount() - reportingRoleRespDto.getTemporaryPromoterCount()
                - reportingRoleRespDto.getSupervisorCount() - reportingRoleRespDto.getSupervisorWithoutPromoterCount()
                - reportingRoleRespDto.getMerchandiserCount();
        reportingRoleRespDto.setOthersCount(othersCount);
        
        return reportingRoleRespDto;
    }
    
    public Boolean isPlaintextImeiUser(UserInfo userInfo) {
        if (null == userInfo) {
            throw new BusinessException("'userInfo' is empty");
        }
        String miID = userInfo.getMiID();
        if (StringUtils.isEmpty(miID)) {
            throw new BusinessException("'miID' is empty");
        }
        LambdaQueryWrapper<IntlSoPlaintextImeiUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntlSoPlaintextImeiUser::getUserMid, miID)
                .eq(IntlSoPlaintextImeiUser::getStatus, 1);
        IntlSoPlaintextImeiUser soPlaintextImeiUser = intlSoPlaintextImeiUserMapper.selectOne(wrapper);
        return null != soPlaintextImeiUser ? Boolean.TRUE : Boolean.FALSE;
    }
    
    public String export(SalesImeiReqDto query) {
        TriggerJobRequestDTO jobReq = new TriggerJobRequestDTO();
        String account = RpcContext.getContext().getAttachments().get("$upc_account");
        BusinessException.when(StringUtils.isBlank(account), "无法获取当前用户");
        String taskName = String.format("Sales IMEI列表_%s", DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss"));
        jobReq.setTaskName(taskName);
        jobReq.setTaskParam(JacksonUtil.toStr(query));
        jobReq.setTaskDesc("Sales IMEI列表导出");
        jobReq.setJobKey(SalesNrJobConstant.SALES_IMEI_DATA_EXPORT_JOB_KEY);
        jobReq.setOwner(account);
        jobReq.setProjectId(projectId);
        jobReq.setProjectName(SalesNrJobConstant.PROJECT_NAME);
        log.info("Sales IMEI create export task request: {}", jobReq);
        Result<String> stringResult = triggerJobWithLogging(jobReq);
        validateResult(stringResult);
        return stringResult.getData();
    }
    
    @NrJob(SalesNrJobConstant.SALES_IMEI_DATA_EXPORT_JOB_HANDLER)
    public Result<HandleMsg> exportHandler() {
        String param = JobHelper.getJobParam();
        log.info("exportHandler param:{}", param);
        SalesImeiReqDto query = JacksonUtil.parseObj(param, SalesImeiReqDto.class);
        try {
            String url = excelExport4SalesImei(query);
            HandleMsg resp = new HandleMsg();
            resp.setFileUrl(url);
            String fileUrl = JacksonUtil.toStr(resp);
            JobHelper.handleSuccess(fileUrl);
            log.info("exportHandler url:{}", fileUrl);
            return Result.success(resp);
            
        } catch (Exception e) {
            log.error("导出Sales IMEI数据异常 param: {}", param, e);
            JobHelper.handleFail(e.getMessage());
            return null;
        }
    }
    
    public String excelExport4SalesImei(SalesImeiReqDto query) throws IOException {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        String excelName = "Sales IMEI Details";
        try {
            tempFile = File.createTempFile(excelName, ".xlsx");
            excelWriter = EasyExcel.write(tempFile, SalesImeiRespDto.class)
                    .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(excelName).build();
            
            int pageSize = 1000;
            int currentPage = 1;
            boolean hasNext = true;
            while (hasNext) {
                query.setPageIndex(currentPage);
                query.setPageSize(pageSize);
                PageResponse<SoImeiIndex> soImeiIndexPage = intlSoImeiEsService.queryByPage(query);
                List<SoImeiIndex> soImeiIndexList = soImeiIndexPage.getData();
                if (CollUtil.isEmpty(soImeiIndexList)) {
                    hasNext = false;
                    continue;
                }
                List<SalesImeiRespDto> salesImeiRespDtoList = salesImeiConverter.toTarget(soImeiIndexList);
                excelWriter.write(salesImeiRespDtoList, writeSheet);
                
                hasNext = currentPage * pageSize < soImeiIndexPage.getTotalCount();
                currentPage++;
            }
            
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (currentPage == 1L) {
                throw new BusinessException(ErrorCodeEnums.BUS_EXCEPTION.getErrorCode(), "Data is missing");
            }
            
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            FdsUploadResult upload = fdsService.upload(excelName + timestamp + ".xlsx", tempFile, true);
            return upload.getUrl();
        } catch (Exception e) {
            log.error("Sales IMEI Data Export Exception query: {}", JacksonUtil.toStr(query), e);
            throw new BusinessException(ErrorCodeEnums.BUS_EXCEPTION.getErrorCode(), "Sales IMEI Data Export Exception");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                Files.delete(tempFile.toPath());
            }
        }
    }
    
    private Result<String> triggerJobWithLogging(TriggerJobRequestDTO request) {
        try {
            long start = System.currentTimeMillis();
            Result<String> result = nrJobService.triggerJob(request);
            long duration = System.currentTimeMillis() - start;
            log.info("Sales IMEI create export task call triggerJob finished, cost time: {}ms, result: {}", duration, result);
            return result;
        } catch (Exception e) {
            log.error("Sales IMEI create export task call nrJob error", e);
            throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "调用任务中心创建任务失败");
        }
    }
    
    private void validateResult(Result<String> result) {
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), result.getMessage());
        }
        
        String status = getStatusFromResult(result);
        handleStatusError(status);
    }
    
    private String getStatusFromResult(Result<String> result) {
        if (result.getAttachments() != null) {
            return result.getAttachments().get("status");
        }
        return null;
    }
    
    private void handleStatusError(String status) {
        if (StringUtils.isEmpty(status)) {
            return;
        }
        switch (status) {
            case SalesNrJobConstant.TASK_NUM_LIMIT_EXCEEDED_CODE:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "导出任务数量超出上限");
            case SalesNrJobConstant.TASK_IS_RUNNING_ERROR_CIDE:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "任务正在进行中，请稍后再试");
            default:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "调用任务中心创建任务失败");
        }
    }
}
