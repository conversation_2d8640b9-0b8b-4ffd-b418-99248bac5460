package com.mi.info.intl.retail.so.domain.upload.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSkuAvailable;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【intl_sku_available(SKU 可用性信息表)】的数据库操作Service
* @createDate 2025-07-24 20:05:34
*/
public interface IntlSkuAvailableService extends IService<IntlSkuAvailable> {

    IPage<IntlSkuAvailable> getData(int pageNum, int pageSize);
}
