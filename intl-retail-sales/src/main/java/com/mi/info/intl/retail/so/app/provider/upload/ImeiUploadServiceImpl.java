package com.mi.info.intl.retail.so.app.provider.upload;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.mi.info.intl.retail.api.file.FileUploadApiService;
import com.mi.info.intl.retail.api.file.dto.PhotoDataInfoDTO;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ImeiRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.app.provider.enums.*;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsRrp;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoUserInfoService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleDetailMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsRrpMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.util.MaskUtil;
import com.mi.info.intl.retail.so.util.TimeZoneUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * IMEI数据提交服务实现
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = ImeiUploadService.class)
@ApiModule(value = "国际新零售平台", apiInterface = ImeiUploadService.class)
public class ImeiUploadServiceImpl implements ImeiUploadService {

    @Resource
    private IntlSoRuleDetailMapper intlSoRuleDetailMapper;

    @Resource
    private IntlRmsRrpMapper intlRmsRrpMapper;

    @Resource
    private IntlSoOrgInfoService intlSoOrgInfoService;

    @Resource
    private IntlSoUserInfoService intlSoUserInfoService;

    @Resource
    private IntlSoImeiService intlSoImeiService;
    @Resource
    private ProductApiService productApiService;

    @Resource
    private FileUploadApiService fileUploadApiService;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private UserApiService userApiService;

    @Resource
    private RmsStoreService rmsStoreService;

    @Resource
    private IntlSoImeiMapper intlSoImeiMapper;

    // 定义图片上传模块名称
    private static final String MODULE_NAME = "imei_upload";

    // 促销员职位代码
    private static final List<Long> PROMOTER_TITLES = Arrays.asList(500900001L, 100000027L, 100000026L);

    @ApiDoc(description = "IMEI数据提交", value = "/api/so/submitImei")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Object> submitImei(SubmitImeiReq request) {
        log.info("submitImei start, request: {}", request);

        try {
            // 1. 必填字段校验
            CommonApiResponse<Object> validateResult = validateRequest(request);
            if (validateResult != null) {
                return validateResult;
            }

            // 2. 查询SO上报规则
            IntlSoRuleDetail ruleDetail = queryRuleDetail(request.getRuleId());
            if (ruleDetail == null) {
                return new CommonApiResponse<>(400, "SO上报规则不存在", null);
            }

            // 3. 查询阵地和门店信息
            PositionStoreInfoDTO positionStoreInfo = queryPositionStoreInfo(request.getPositionCode());
            if (positionStoreInfo == null) {
                return new CommonApiResponse<>(400, "阵地或门店信息不存在", null);
            }

            // 4. 查询用户信息
            UserInfoDTO userInfo = queryUserInfo(request.getMiId());
            if (userInfo == null) {
                return new CommonApiResponse<>(400, "用户信息不存在", null);
            }

            // 5. 查询产品信息
            Map<Integer, ProductInfoDTO> productMap = queryProductInfo(request.getDetailList());
            if (productMap.isEmpty()) {
                return new CommonApiResponse<>(400, "产品信息不存在", null);
            }

            // 6. 查询产品价格信息
            Map<String, IntlRmsRrp> rrpMap = queryRrpInfo(productMap.values(), request.getCountryCode());

            // 8. 创建相关数据
            createImeiData(request, ruleDetail, positionStoreInfo, userInfo, productMap, rrpMap);

            log.info("submitImei success");
            return new CommonApiResponse<>(null);

        } catch (Exception e) {
            log.error("submitImei error", e);
            return new CommonApiResponse<>(500, "系统异常：" + e.getMessage(), null);
        }
    }

    /**
     * 校验请求参数
     */
    private CommonApiResponse<Object> validateRequest(SubmitImeiReq request) {
        if (request == null) {
            return new CommonApiResponse<>(400, "请求参数不能为空", null);
        }

        if (request.getRuleId() == null) {
            return new CommonApiResponse<>(400, "匹配规则ID不能为空", null);
        }

        if (StringUtils.isBlank(request.getCountryCode())) {
            return new CommonApiResponse<>(400, "国家编码不能为空", null);
        }

        if (StringUtils.isBlank(request.getPositionCode())) {
            return new CommonApiResponse<>(400, "阵地编码不能为空", null);
        }

        if (StringUtils.isBlank(request.getStoreCode())) {
            return new CommonApiResponse<>(400, "门店编码不能为空", null);
        }

        if (StringUtils.isBlank(request.getUserId())) {
            return new CommonApiResponse<>(400, "用户GUID不能为空", null);
        }

        if (request.getMiId() == null) {
            return new CommonApiResponse<>(400, "用户miId不能为空", null);
        }

        if (request.getUserTitle() == null) {
            return new CommonApiResponse<>(400, "职位编码不能为空", null);
        }

        if (CollectionUtils.isEmpty(request.getDetailList())) {
            return new CommonApiResponse<>(400, "IMEI明细不能为空", null);
        }

        // 校验明细字段
        for (ImeiDetailDto detail : request.getDetailList()) {
            if (StringUtils.isBlank(detail.getDetailId())) {
                return new CommonApiResponse<>(400, "明细ID不能为空", null);
            }
            if (StringUtils.isBlank(detail.getImei())) {
                return new CommonApiResponse<>(400, "IMEI不能为空", null);
            }
            if (StringUtils.isBlank(detail.getSn())) {
                return new CommonApiResponse<>(400, "序列号不能为空", null);
            }
            if (detail.getProductId() == null) {
                return new CommonApiResponse<>(400, "产品ID不能为空", null);
            }
            if (detail.getSalesTime() == null) {
                return new CommonApiResponse<>(400, "销售时间不能为空", null);
            }
        }

        return null;
    }

    /**
     * 查询SO上报规则
     */
    private IntlSoRuleDetail queryRuleDetail(Integer ruleId) {
        try {
            return intlSoRuleDetailMapper.selectById(ruleId);
        } catch (Exception e) {
            log.error("查询SO上报规则失败, ruleId: {}", ruleId, e);
            return null;
        }
    }

    /**
     * 查询阵地和门店信息
     */
    private PositionStoreInfoDTO queryPositionStoreInfo(String positionCode) {
        try {
            Optional<PositionStoreInfoDTO> result = intlPositionApiService.queryPositionWithStoreByCode(positionCode);
            return result.orElse(null);
        } catch (Exception e) {
            log.error("查询阵地门店信息失败, positionCode: {}", positionCode, e);
            return null;
        }
    }

    /**
     * 查询用户信息
     */
    private UserInfoDTO queryUserInfo(Long miId) {
        try {
            Optional<UserInfoDTO> result = userApiService.queryUserByMiId(miId);
            return result.orElse(null);
        } catch (Exception e) {
            log.error("查询用户信息失败, miId: {}", miId, e);
            return null;
        }
    }

    /**
     * 查询产品信息
     */
    private Map<Integer, ProductInfoDTO> queryProductInfo(List<ImeiDetailDto> detailList) {
        try {
            List<Integer> productIds = detailList.stream()
                    .map(ImeiDetailDto::getProductCode)
                    .distinct()
                    .collect(Collectors.toList());

            return productApiService.queryProductsByGoodIds(productIds);
        } catch (Exception e) {
            log.error("查询产品信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 查询产品价格信息
     */
    private Map<String, IntlRmsRrp> queryRrpInfo(Collection<ProductInfoDTO> products, String countryCode) {
        try {
            List<Long> productCodes = products.stream()
                    .map(ProductInfoDTO::getGoodsId)
                    .map(Long::valueOf)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(productCodes)) {
                return new HashMap<>();
            }

            Long currentTime = TimeZoneUtil.getCurrentTimestamp();
            List<IntlRmsRrp> rrpList = intlRmsRrpMapper.selectValidRrpByProductCodes(productCodes, currentTime,
                    countryCode);

            return rrpList.stream()
                    .collect(Collectors.toMap(
                            IntlRmsRrp::getProductCode,
                            rrp -> rrp,
                            (existing, replacement) -> existing // 保留第一个（按创建时间降序排序后的第一个）
                    ));
        } catch (Exception e) {
            log.error("查询产品价格信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 创建IMEI相关数据
     */
    private void createImeiData(SubmitImeiReq request, IntlSoRuleDetail ruleDetail,
            PositionStoreInfoDTO positionStoreInfo, UserInfoDTO userInfo,
            Map<Integer, ProductInfoDTO> productMap, Map<String, IntlRmsRrp> rrpMap) {

        // 1. 创建门店信息数据
        Long orgInfoId = createOrgInfo(positionStoreInfo, request.getCountryCode());

        // 2. 创建用户信息数据
        Long userInfoId = createUserInfo(userInfo, request.getMiId());

        // 3. 创建IMEI明细数据
        List<IntlSoImei> imeiList = createImeiDetails(request, ruleDetail, orgInfoId, userInfoId,
                productMap, rrpMap, positionStoreInfo);

        // 4. 设置isPhotoExist字段
        setPhotoExistFlag(request, imeiList);

        // 5. 批量插入IMEI数据
        if (CollectionUtils.isNotEmpty(imeiList)) {
            // 使用Service的saveBatch方法，这样可以获取到生成的ID
            intlSoImeiService.saveBatch(imeiList);
        }

        // 6. 创建图片数据
        createPhotoData(request, imeiList, userInfo);
    }

    /**
     * 创建门店信息数据
     */
    private Long createOrgInfo(PositionStoreInfoDTO positionStoreInfo, String countryCode) {
        // 先查询是否存在完全相同的数据（使用所有字段作为条件）
        QueryWrapper<IntlSoOrgInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("store_changelog_id", 0L)
                .eq("store_id", positionStoreInfo.getStoreId() != null ? positionStoreInfo.getStoreId() : 0)
                .eq("store_code", positionStoreInfo.getStoreCode() != null ? positionStoreInfo.getStoreCode() : "")
                .eq("store_grade", positionStoreInfo.getStoreGrade() != null ? positionStoreInfo.getStoreGrade() : 0)
                .eq("store_type", positionStoreInfo.getStoreType() != null ? positionStoreInfo.getStoreType() : 0)
                .eq("store_channel_type",
                        positionStoreInfo.getStoreChannelType() != null ? positionStoreInfo.getStoreChannelType() : 0)
                .eq("store_hasSR", positionStoreInfo.getStoreHasSR() != null ? positionStoreInfo.getStoreHasSR() : 0)
                .eq("store_hasPC", positionStoreInfo.getStoreHasPC() != null ? positionStoreInfo.getStoreHasPC() : 0)
                .eq("country_code", countryCode != null ? countryCode : "")
                .eq("position_changelog_id", 0L)
                .eq("position_id", positionStoreInfo.getPositionId() != null ? positionStoreInfo.getPositionId() : 0)
                .eq("position_code",
                        positionStoreInfo.getPositionCode() != null ? positionStoreInfo.getPositionCode() : "")
                .eq("position_type",
                        positionStoreInfo.getPositionType() != null ? positionStoreInfo.getPositionType() : 0)
                .eq("retailer_id", positionStoreInfo.getRetailerId() != null ? positionStoreInfo.getRetailerId() : 0)
                .eq("retailer_code",
                        positionStoreInfo.getRetailerCode() != null ? positionStoreInfo.getRetailerCode() : "");
        // 限制一条
        queryWrapper.last("limit 1");

        IntlSoOrgInfo existingOrgInfo = intlSoOrgInfoService.getOne(queryWrapper);
        if (existingOrgInfo != null) {
            return existingOrgInfo.getId();
        }

        // 创建新的门店信息
        IntlSoOrgInfo orgInfo = getIntlSoOrgInfo(positionStoreInfo, countryCode);

        intlSoOrgInfoService.save(orgInfo);
        return orgInfo.getId();
    }

    @NotNull
    private static IntlSoOrgInfo getIntlSoOrgInfo(PositionStoreInfoDTO positionStoreInfo, String countryCode) {
        IntlSoOrgInfo orgInfo = new IntlSoOrgInfo();
        orgInfo.setStoreChangelogId(0L);
        orgInfo.setStoreId(positionStoreInfo.getStoreId() != null ? positionStoreInfo.getStoreId() : 0);
        orgInfo.setStoreCode(positionStoreInfo.getStoreCode() != null ? positionStoreInfo.getStoreCode() : "");
        orgInfo.setStoreGrade(positionStoreInfo.getStoreGrade() != null ? positionStoreInfo.getStoreGrade() : 0);
        orgInfo.setStoreType(positionStoreInfo.getStoreType() != null ? positionStoreInfo.getStoreType() : 0);
        orgInfo.setStoreChannelType(
                positionStoreInfo.getStoreChannelType() != null ? positionStoreInfo.getStoreChannelType() : 0);
        orgInfo.setStoreHasSR(positionStoreInfo.getStoreHasSR() != null ? positionStoreInfo.getStoreHasSR() : 0);
        orgInfo.setStoreHasPC(positionStoreInfo.getStoreHasPC() != null ? positionStoreInfo.getStoreHasPC() : 0);
        orgInfo.setCountryCode(countryCode != null ? countryCode : "");
        orgInfo.setPositionChangelogId(0L);
        orgInfo.setPositionId(positionStoreInfo.getPositionId() != null ? positionStoreInfo.getPositionId() : 0);
        orgInfo.setPositionCode(positionStoreInfo.getPositionCode() != null ? positionStoreInfo.getPositionCode() : "");
        orgInfo.setPositionType(positionStoreInfo.getPositionType() != null ? positionStoreInfo.getPositionType() : 0);
        orgInfo.setRetailerId(positionStoreInfo.getRetailerId() != null ? positionStoreInfo.getRetailerId() : 0);
        orgInfo.setRetailerCode(positionStoreInfo.getRetailerCode() != null ? positionStoreInfo.getRetailerCode() : "");
        return orgInfo;
    }

    /**
     * 创建用户信息数据
     */
    private Long createUserInfo(UserInfoDTO userInfo, Long miId) {
        // 先查询是否存在完全相同的数据（使用所有字段作为条件）
        QueryWrapper<IntlSoUserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("createdby_mid", miId)
                .eq("createdby_rmsaccount", userInfo.getDomainName())
                .eq("createdby_jobtitle", userInfo.getJobId())
                .eq("salesman_mid", miId)
                .eq("salesman_rmsaccount", userInfo.getDomainName())
                .eq("salesman_jobtitle", userInfo.getJobId());
        // 限制一条
        queryWrapper.last("limit 1");

        IntlSoUserInfo existingUserInfo = intlSoUserInfoService.getOne(queryWrapper);
        if (existingUserInfo != null) {
            return existingUserInfo.getId();
        }

        // 创建新的用户信息
        IntlSoUserInfo soUserInfo = new IntlSoUserInfo();
        soUserInfo.setCreatedByMid(miId);
        soUserInfo.setCreatedByRmsAccount(userInfo.getDomainName());
        soUserInfo.setCreatedByJobTitle(userInfo.getJobId());
        soUserInfo.setSalesmanMid(miId);
        soUserInfo.setSalesmanRmsAccount(userInfo.getDomainName());
        soUserInfo.setSalesmanJobTitle(userInfo.getJobId());

        intlSoUserInfoService.save(soUserInfo);
        return soUserInfo.getId();
    }

    /**
     * 创建IMEI明细数据
     */
    private List<IntlSoImei> createImeiDetails(SubmitImeiReq request, IntlSoRuleDetail ruleDetail,
            Long orgInfoId, Long userInfoId,
            Map<Integer, ProductInfoDTO> productMap,
            Map<String, IntlRmsRrp> rrpMap, PositionStoreInfoDTO positionStoreInfo) {

        List<IntlSoImei> imeiList = new ArrayList<>();
        Long currentTime = TimeZoneUtil.getCurrentTimestamp();

        for (ImeiDetailDto detail : request.getDetailList()) {
            ProductInfoDTO product = productMap.get(detail.getProductId());
            if (product == null) {
                log.warn("产品信息不存在, productId: {}", detail.getProductId());
                continue;
            }

            IntlSoImei imei = new IntlSoImei();

            // 非哈希国家存明文
            if (detail.getIsHashCountry() != 1) {
                imei.setSn(detail.getSn());
                imei.setImei1(detail.getImei());
                imei.setImei2(detail.getImei2());
            }
            // 掩码处理
            imei.setSnMask(MaskUtil.maskString(detail.getSn()));
            imei.setImei1Mask(MaskUtil.maskString(detail.getImei()));
            imei.setImei2Mask(MaskUtil.maskString(detail.getImei2()));

            // 哈希值
            imei.setSnHash(detail.getSnhash());

            // 产品信息
            imei.setImeiFromHub(null);
            imei.setProductCode(Integer.valueOf(product.getGoodsId()));

            // 关联信息
            imei.setOrgInfoId(orgInfoId);
            imei.setUserInfoId(userInfoId);
            imei.setImeiRuleId(String.valueOf(ruleDetail.getId()));

            // 新增字段赋值
            imei.setStoreRmsCode(positionStoreInfo.getStoreCode());
            imei.setPositionRmsCode(positionStoreInfo.getPositionCode());
            imei.setIsPhotoExist(PhotoExistEnum.NOT_EXIST.getValue()); // 默认值为0，后续根据是否有图片进行更新

            // IMEI规则解析
            List<ImeiRuleDTO> list = JSON.parseArray(ruleDetail.getImeiRuleList(), ImeiRuleDTO.class);
            // 根据入参中的userTitle和productLineEn匹配IMEI规则（ImeiRuleDTO），获取before和after值
            Optional<ImeiRuleDTO> imeiRule = list.stream()
                    .filter(rule -> rule.getUserTitleList().stream()
                            .anyMatch(userTitleItem -> userTitleItem.getUserTitleId()
                                    .equals(String.valueOf(request.getUserTitle())))
                            && rule.getProductLineList().stream()
                                    .anyMatch(productLineItem -> productLineItem.getProductLine()
                                            .equals(product.getProductLineEn())))
                    .findFirst();
            if (imeiRule.isPresent()) {
                imei.setImeiRuleBefore(imeiRule.get().getBefore());
                imei.setImeiRuleAfter(imeiRule.get().getAfter());
            }

            // // IMEI规则解析
            // Integer ruleBefore =
            // JsonRuleUtil.getImeiRuleBefore(ruleDetail.getImeiRuleList(),
            // request.getUserTitle());
            // Integer ruleAfter =
            // JsonRuleUtil.getImeiRuleAfter(ruleDetail.getImeiRuleList(),
            // request.getUserTitle());
            // imei.setImeiRuleBefore(ruleBefore);
            // imei.setImeiRuleAfter(ruleAfter);

            // 时间信息
            imei.setActivationVerificationTime(currentTime);
            imei.setActivationTime(null);
            imei.setActivationFrequency(null);
            imei.setActivationSite(null);

            // 验证状态
            imei.setVerifyingState(VerifyingStateEnum.ABNORMAL.getValue()); // 默认abnormal
            imei.setVerificationResult(VerificationResultEnum.VERIFYING.getValue()); // 默认验证中
            imei.setVerifyResultDetail(null);
            imei.setFailedReason(null);
            imei.setFailedReasonDetail(null);
            imei.setSiVerifyResult(null);

            // 上报类型
            imei.setReportingType(
                    detail.getReportingType() != null ? detail.getReportingType() : ReportingTypeEnum.APP.getValue());

            // 其他字段
            imei.setFirstLevelAccountCode(null);
            imei.setFinalSalesCountry(null);
            imei.setAllowSalesCountry(null);
            imei.setNote(detail.getNote());

            // 价格信息
            IntlRmsRrp rrp = rrpMap.get(String.valueOf(product.getGoodsId()));
            if (rrp != null) {
                imei.setCurrency(rrp.getCurrency());
                imei.setRrpCode(rrp.getRrpCode());
                imei.setRrp(rrp.getRrp());
            }

            // 批次信息
            imei.setBatchId(null); // App端导入不赋值

            imei.setSalesTime(detail.getSalesTime());

            // 创建信息
            imei.setCreatedBy(request.getMiId());
            imei.setCreatedOn(currentTime);
            imei.setModifiedBy(request.getMiId());
            imei.setModifiedOn(currentTime);

            // 其他
            imei.setRmsId(null);
            imei.setDataFrom(DataFromEnum.MIRETAIL.getValue()); // 默认赋值mi retail
            imei.setDetailId(detail.getDetailId());

            imeiList.add(imei);
        }

        return imeiList;
    }

    /**
     * 设置IMEI数据的isPhotoExist字段
     */
    private void setPhotoExistFlag(SubmitImeiReq request, List<IntlSoImei> imeiList) {
        if (CollectionUtils.isEmpty(request.getPhotoList()) || CollectionUtils.isEmpty(imeiList)) {
            return;
        }

        // 建立detailId到IMEI对象的映射
        Map<String, IntlSoImei> detailIdToImeiMap = new HashMap<>();
        for (int i = 0; i < request.getDetailList().size() && i < imeiList.size(); i++) {
            String detailId = request.getDetailList().get(i).getDetailId();
            IntlSoImei imei = imeiList.get(i);
            detailIdToImeiMap.put(detailId, imei);
        }

        // 根据图片列表设置isPhotoExist字段（需要检查URL不为空）
        for (PhotoDto photo : request.getPhotoList()) {
            // 只有当图片的URL不为空时才设置为1
            if (StringUtils.isNotBlank(photo.getUrl())) {
                IntlSoImei imei = detailIdToImeiMap.get(photo.getDetailId());
                if (imei != null) {
                    imei.setIsPhotoExist(PhotoExistEnum.EXIST.getValue()); // 有图片且URL不为空则设置为1
                }
            }
        }
    }

    /**
     * 创建图片数据
     */
    private void createPhotoData(SubmitImeiReq request, List<IntlSoImei> imeiList, UserInfoDTO userInfo) {
        if (CollectionUtils.isEmpty(request.getPhotoList()) || CollectionUtils.isEmpty(imeiList)) {
            return;
        }

        // 建立detailId到imeiId的映射
        Map<String, Long> detailIdToImeiIdMap = new HashMap<>();
        for (int i = 0; i < request.getDetailList().size() && i < imeiList.size(); i++) {
            String detailId = request.getDetailList().get(i).getDetailId();
            Long imeiId = imeiList.get(i).getId();
            detailIdToImeiIdMap.put(detailId, imeiId);
        }

        List<PhotoDataInfoDTO> photoDataList = new ArrayList<>();
        Long currentTime = TimeZoneUtil.getCurrentTimestamp();

        for (PhotoDto photo : request.getPhotoList()) {
            Long relatedId = detailIdToImeiIdMap.get(photo.getDetailId());
            if (relatedId == null) {
                log.warn("找不到对应的IMEI记录, detailId: {}", photo.getDetailId());
                continue;
            }

            PhotoDataInfoDTO photoData = new PhotoDataInfoDTO();
            photoData.setRelatedId(relatedId);
            photoData.setIsOfflineUpload(0); // 默认为非离线
            photoData.setIsUploadedToBlob(1); // 默认已上传到Blob
            photoData.setModuleName(MODULE_NAME); // 模块名称 imei_upload
            photoData.setGuid(photo.getDetailId());

            // 设置上传者名称
            photoData.setUploaderName(userInfo.getEnglishName());
            photoData.setUploaderTime(photo.getUploadTime());

            photoData.setFdsUrl(photo.getUrl());
            photoData.setCreateTime(currentTime);
            photoData.setUpdateTime(currentTime);

            // 从URL中提取文件后缀
            String suffix = extractSuffixFromUrl(photo.getUrl());
            photoData.setSuffix(suffix);

            photoDataList.add(photoData);
        }

        // 批量插入图片数据
        if (CollectionUtils.isNotEmpty(photoDataList)) {
            fileUploadApiService.createPhotoData(photoDataList);
        }
    }

    /**
     * 从URL中提取文件后缀
     */
    private String extractSuffixFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return ".jpg"; // 默认后缀
        }

        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex);
        }

        return ".jpg"; // 默认后缀
    }

    @ApiDoc(description = "IMEI列表数据分页查询", value = "/api/so/queryImeiListByPage")
    @Override
    public CommonApiResponse<ImeiListQueryResp> queryImeiListByPage(ImeiListQueryReq request) {
        log.info("queryImeiListByPage start, request: {}", request);

        try {
            // 1. 入参必填字段校验
            if (StringUtils.isBlank(request.getCountryCode()) ||
                    StringUtils.isBlank(request.getUserId()) ||
                    request.getMiId() == null ||
                    request.getUserTitle() == null) {
                return new CommonApiResponse<>(400, "必填参数不能为空", null);
            }

            // 2. 判断userTitle，执行相应查询逻辑
            List<Integer> userStoreList = new ArrayList<>();
            if (!PROMOTER_TITLES.contains(request.getUserTitle())) {
                // 督导及其他角色，查询该用户关联的所有门店
                userStoreList = getUserStoreList(request.getMiId());
                if (userStoreList.isEmpty()) {
                    log.warn("用户没有关联的门店, miId: {}", request.getMiId());
                    return new CommonApiResponse<>(new ImeiListQueryResp());
                }
            }

            // 3. 构建查询条件并查询IMEI明细数据
            ImeiListQueryResp response = queryImeiDetailList(request, userStoreList);

            log.info("queryImeiListByPage success");
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("queryImeiListByPage error", e);
            return new CommonApiResponse<>(500, "系统异常：" + e.getMessage(), null);
        }
    }

    @ApiDoc(description = "IMEI明细详情查询", value = "/api/so/queryImeiDetail")
    @Override
    public CommonApiResponse<ImeiDetailQueryResp> queryImeiDetail(ImeiDetailQueryReq request) {
        log.info("queryImeiDetail start, request: {}", request);

        try {
            // 1. 入参校验
            if (StringUtils.isBlank(request.getImeiId()) ||
                    StringUtils.isBlank(request.getUserId()) ||
                    request.getMiId() == null ||
                    request.getUserTitle() == null) {
                return new CommonApiResponse<>(400, "必填参数不能为空", null);
            }

            // 2. 查询IMEI明细数据
            ImeiDetailQueryResp response = queryImeiDetailById(request.getImeiId());
            if (response == null) {
                return new CommonApiResponse<>(404, "IMEI明细不存在", null);
            }

            log.info("queryImeiDetail success");
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("queryImeiDetail error", e);
            return new CommonApiResponse<>(500, "系统异常：" + e.getMessage(), null);
        }
    }

    @ApiDoc(description = "IMEI汇总数据查询", value = "/api/so/queryImeiSummary")
    @Override
    public CommonApiResponse<ImeiSummaryQueryResp> queryImeiSummary(ImeiSummaryQueryReq request) {
        log.info("queryImeiSummary start, request: {}", request);

        try {
            // 1. 入参必填字段校验
            if (StringUtils.isBlank(request.getCountryCode()) ||
                    StringUtils.isBlank(request.getUserId()) ||
                    request.getMiId() == null ||
                    request.getUserTitle() == null) {
                return new CommonApiResponse<>(400, "必填参数不能为空", null);
            }

            // 2. 判断userTitle，执行相应查询逻辑
            List<Integer> userStoreList = new ArrayList<>();
            if (!PROMOTER_TITLES.contains(request.getUserTitle())) {
                // 督导及其他角色，查询该用户关联的所有门店
                userStoreList = getUserStoreList(request.getMiId());
                if (userStoreList.isEmpty()) {
                    log.warn("用户没有关联的门店, miId: {}", request.getMiId());
                    return new CommonApiResponse<>(new ImeiSummaryQueryResp());
                }
            }

            // 3. 查询IMEI汇总数据
            ImeiSummaryQueryResp response = queryImeiSummaryData(request, userStoreList);

            log.info("queryImeiSummary success");
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("queryImeiSummary error", e);
            return new CommonApiResponse<>(500, "系统异常：" + e.getMessage(), null);
        }
    }

    /**
     * 获取用户关联的门店列表
     */
    private List<Integer> getUserStoreList(Long miId) {
        try {
            // 通过RmsStoreService查询用户关联的门店
            return rmsStoreService.getUserStoreIdsByMiId(miId);
        } catch (Exception e) {
            log.error("获取用户门店列表失败, miId: {}", miId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询IMEI明细列表
     */
    private ImeiListQueryResp queryImeiDetailList(ImeiListQueryReq request, List<Integer> userStoreList) {
        // 分页参数
        int pageIndex = request.getPageIndex() != null ? request.getPageIndex() : 1;
        int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
        int offset = (pageIndex - 1) * pageSize;

        // 时间筛选参数
        Long startTime = null;
        Long endTime = null;
        if (StringUtils.isNotBlank(request.getDateFilterType())) {
            if ("1".equals(request.getDateFilterType()) &&
                    StringUtils.isNotBlank(request.getYear()) &&
                    StringUtils.isNotBlank(request.getMonth())) {
                // 年月筛选
                startTime = convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(),
                        true);
                endTime = convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(),
                        false);
            } else if ("2".equals(request.getDateFilterType()) &&
                    StringUtils.isNotBlank(request.getStartTime()) &&
                    StringUtils.isNotBlank(request.getEndTime())) {
                // 自定义时间段筛选
                startTime = convertToCountryTimestamp(request.getStartTime(), request.getCountryCode());
                endTime = convertToCountryTimestamp(request.getEndTime(), request.getCountryCode());
            }
        }

        // 查询明细数据
        List<Map<String, Object>> detailData = intlSoImeiMapper.queryImeiDetailList(
                request.getCountryCode(),
                PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                request.getSearch(),
                request.getReportingType(),
                request.getStoreCode(),
                request.getProductLine(),
                request.getStoreType(),
                request.getChannelType(),
                request.getVerifyResult(),
                startTime,
                endTime,
                offset,
                pageSize + 1 // 多查询一条用于判断是否有更多数据
        );

        // 判断是否有更多数据
        boolean hasMore = detailData.size() > pageSize;

        // 构建响应
        ImeiListQueryResp response = new ImeiListQueryResp();
        response.setMoreRecords(hasMore);

        // 处理明细数据
        List<ImeiListQueryResp.ImeiDetailItemDto> detailList = new ArrayList<>();
        int actualSize = hasMore ? pageSize : detailData.size();
        for (int i = 0; i < actualSize; i++) {
            Map<String, Object> item = detailData.get(i);
            ImeiListQueryResp.ImeiDetailItemDto dto = new ImeiListQueryResp.ImeiDetailItemDto();
            dto.setId((BigInteger) item.get("id"));
            dto.setProductName((String) item.get("productName"));
            dto.setImei1Mask((String) item.get("imei1Mask"));
            dto.setImei2Mask((String) item.get("imei2Mask"));
            dto.setSnMask((String) item.get("snMask"));
            dto.setNote((String) item.get("note"));
            dto.setStoreName((String) item.get("storeName"));
            dto.setVerifyResult((Integer) item.get("verifyResult"));
            dto.setVerifyResultDetail((String) item.get("verifyResultDetail"));


            // 处理时间字段 - 使用时区转换
            Long salesTimeTimestamp = (Long) item.get("sales_time");
            String countryCode = (String) item.get("country_code");
            if (salesTimeTimestamp != null) {
                String salesTimeStr = IntlTimeUtil.parseTimestampToAreaTime(countryCode, salesTimeTimestamp);
                if (StringUtils.isNotBlank(salesTimeStr)) {
                    dto.setSalesTime(salesTimeStr);
                    // 提取日期部分
                    dto.setSalesDate(salesTimeStr.substring(0, 10));
                }
            }

            detailList.add(dto);
        }
        response.setDetailList(detailList);

        // 构建日期分组数据 - 基于当前页的实际数据
        List<Map<String, Object>> dateGroupData = new ArrayList<>();
        if (!detailList.isEmpty()) {
            // 从当前页的数据中提取日期并统计
            Map<String, Integer> dateCountMap = new HashMap<>();
            for (int i = 0; i < actualSize; i++) {
                ImeiListQueryResp.ImeiDetailItemDto item = detailList.get(i);
                String salesDate = item.getSalesDate();
                if (StringUtils.isNotBlank(salesDate)) {
                    dateCountMap.put(salesDate, dateCountMap.getOrDefault(salesDate, 0) + 1);
                }
            }
            // 转换为返回格式并按日期倒序排列
            dateGroupData = dateCountMap.entrySet().stream()
                    .map(entry -> {
                        Map<String, Object> dateGroup = new HashMap<>();
                        dateGroup.put("date", entry.getKey());
                        dateGroup.put("count", entry.getValue());
                        return dateGroup;
                    })
                    .sorted((a, b) -> ((String) b.get("date")).compareTo((String) a.get("date")))
                    .collect(Collectors.toList());

            // 获取排序后末尾一天的日期，单独查询这一天的IMEI数量
            if (!dateGroupData.isEmpty()) {
                String lastDate = (String) dateGroupData.get(dateGroupData.size() - 1).get("date");
                Long lastDateTimestamp = IntlTimeUtil.parseAreaTimeToTimestamp(request.getCountryCode(),
                        lastDate + " 00:00:00");

                // 查询这一天的IMEI数量
                int countForLastDate = intlSoImeiMapper.countImeiDetailList(
                        request.getCountryCode(),
                        PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                        PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                        request.getSearch(),
                        request.getReportingType(),
                        request.getStoreCode(),
                        request.getProductLine(),
                        request.getStoreType(),
                        request.getChannelType(),
                        request.getVerifyResult(),
                        lastDateTimestamp,
                        lastDateTimestamp + 24 * 60 * 60 * 1000 // 加一天的时间戳
                );

                // 更新最后一天的数量
                dateGroupData.get(dateGroupData.size() - 1).put("count", countForLastDate);
            }
        }

        // 处理日期分组数据
        List<ImeiListQueryResp.DateGroupDto> dateGroupList = new ArrayList<>();
        for (Map<String, Object> item : dateGroupData) {
            ImeiListQueryResp.DateGroupDto dto = new ImeiListQueryResp.DateGroupDto();
            dto.setDate((String) item.get("date"));
            dto.setCount(((Number) item.get("count")).intValue());
            dateGroupList.add(dto);
        }
        response.setDateGroupList(dateGroupList);

        return response;
    }

    /**
     * 根据ID查询IMEI明细
     */
    private ImeiDetailQueryResp queryImeiDetailById(String imeiId) {
        List<Map<String, Object>> data = intlSoImeiMapper.queryImeiDetailById(imeiId);

        if (data.isEmpty()) {
            return null;
        }
        Map<String, Object> first = data.get(0);
        ImeiDetailQueryResp response = new ImeiDetailQueryResp();
        response.setId((BigInteger) first.get("id"));
        response.setProductName((String) first.get("productName"));
        response.setImei1Mask((String) first.get("imei1Mask"));
        response.setImei2Mask((String) first.get("imei2Mask"));
        response.setSnMask((String) first.get("snMask"));
        response.setNote((String) first.get("note"));
        response.setStoreName((String) first.get("storeName"));
        response.setCreatedBy((String) first.get("createdBy"));
        String createdOn = IntlTimeUtil.parseTimestampToFormatAreaTime("yyyy-MM-dd HH:mm:ss",
                (String) first.get("countryCode"),
                (Long) first.get("createdOn")
        );
        response.setCreatedOn(createdOn);
        response.setVerifyResultDetail((String) first.get("verifyResultDetail"));

        Integer verifyResult = (Integer) first.get("verifyResult");
        response.setVerifyResult(verifyResult);
        // 从枚举获取描述，取不到默认空值
        response.setReportingTypeLabel(
                Optional.ofNullable(VerificationResultEnum.getByValue(verifyResult))
                        .map(VerificationResultEnum::getName)
                        .orElse("")
        );
        Integer reportingType = (Integer) first.get("reportingType");
        response.setReportingType(reportingType);
        // 从枚举获取描述，取不到默认空值
        response.setReportingTypeLabel(
                Optional.ofNullable(ReportingTypeEnum.getByValue(reportingType))
                        .map(ReportingTypeEnum::getName)
                        .orElse("")
        );

        // 遍历数据,获取图片url
        List<String> photoUrls = new ArrayList<>();
        for (Map<String, Object> item : data) {
            String photoUrl = (String) item.get("url");
            if (StringUtils.isNotBlank(photoUrl)) {
                photoUrls.add(photoUrl);
            }
        }
        response.setPhotoUrls(photoUrls);

        return response;
    }

    /**
     * 查询IMEI汇总数据
     */
    private ImeiSummaryQueryResp queryImeiSummaryData(ImeiSummaryQueryReq request, List<Integer> userStoreList) {
        // 时间筛选参数
        Long startTime = null;
        Long endTime = null;
        if (StringUtils.isNotBlank(request.getDateFilterType())) {
            if ("1".equals(request.getDateFilterType()) &&
                    StringUtils.isNotBlank(request.getYear()) &&
                    StringUtils.isNotBlank(request.getMonth())) {
                // 年月筛选
                startTime = convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(),
                        true);
                endTime = convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(),
                        false);
            } else if ("2".equals(request.getDateFilterType()) &&
                    StringUtils.isNotBlank(request.getStartTime()) &&
                    StringUtils.isNotBlank(request.getEndTime())) {
                // 自定义时间段筛选
                startTime = convertToCountryTimestamp(request.getStartTime(), request.getCountryCode());
                endTime = convertToCountryTimestamp(request.getEndTime(), request.getCountryCode());
            }
        }

        // 查询汇总数据
        List<Map<String, Object>> summaryData = intlSoImeiMapper.queryImeiSummary(
                request.getCountryCode(),
                PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                request.getSearch(),
                request.getStoreCode(),
                request.getProductLine(),
                request.getStoreType(),
                request.getChannelType(),
                request.getVerifyResult(),
                startTime,
                endTime);

        // 构建响应
        ImeiSummaryQueryResp response = new ImeiSummaryQueryResp();
        int totalCount = 0;
        int successCount = 0;
        int verifyingCount = 0;
        int failedCount = 0;

        for (Map<String, Object> item : summaryData) {
            Integer verificationResult = (Integer) item.get("verification_result");
            Integer count = ((Number) item.get("count")).intValue();
            totalCount += count;

            if (verificationResult != null) {
                switch (verificationResult) {
                    case 100000000: // Verifying
                        verifyingCount += count;
                        break;
                    case 100000002: // Successfully
                        successCount += count;
                        break;
                    case 100000001: // Failed
                    case 100000003: // TimeOut
                        failedCount += count;
                        break;
                    default:
                        log.warn("未知的验证结果: {}, count: {}", verificationResult, count);
                        break;
                }
            }
        }

        response.setTotalCount(totalCount);
        response.setSuccessCount(successCount);
        response.setVerifyingCount(verifyingCount);
        response.setFailedCount(failedCount);

        return response;
    }

    /**
     * 将时间字符串转换为国家本地时间戳
     */
    private Long convertToCountryTimestamp(String timeStr, String countryCode) {
        try {
            // 如果timeStr是时间戳格式，直接转换
            if (timeStr.matches("\\d+")) {
                return Long.parseLong(timeStr);
            }

            // 使用IntlTimeUtil进行时区转换
            return IntlTimeUtil.parseAreaTimeToTimestamp(countryCode, timeStr);
        } catch (Exception e) {
            log.error("时间转换失败: timeStr={}, countryCode={}", timeStr, countryCode, e);
            return null;
        }
    }

    /**
     * 将年月转换为时间戳
     */
    private Long convertYearMonthToTimestamp(String year, String month, String countryCode, boolean isStart) {
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            // 使用IntlTimeUtil获取月份的开始和结束时间戳
            Long[] timestamps = IntlTimeUtil.getMonthStartAndEndTimestamp(countryCode, yearInt, monthInt);
            if (timestamps != null && timestamps.length == 2) {
                return isStart ? timestamps[0] : timestamps[1];
            }

            return null;
        } catch (Exception e) {
            log.error("年月时间转换失败: year={}, month={}, countryCode={}", year, month, countryCode, e);
            return null;
        }
    }
}
