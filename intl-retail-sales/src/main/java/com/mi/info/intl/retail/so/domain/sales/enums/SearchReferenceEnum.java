package com.mi.info.intl.retail.so.domain.sales.enums;

import cn.hutool.extra.spring.SpringUtil;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceRespDto;
import com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference;
import com.mi.info.intl.retail.so.infra.database.mapper.sales.SearchReferenceMapper;
import org.apache.commons.collections4.CollectionUtils;
import java.util.ArrayList;
import java.util.List;

/**
 * 搜索参考枚举
 *
 * <AUTHOR>
 * @date 2025/8/1
 */

public enum SearchReferenceEnum {

    INTL_RMS_POSITION {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(String keyWord, String parentKey) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsPosition(keyWord);
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },
    INTL_RMS_STORE {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(String keyWord, String parentKey) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsStore(keyWord);
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_USER {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(String keyWord, String parentKey) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsUser(keyWord);
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_RETAILER {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(String keyWord, String parentKey) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsRetailer(keyWord);
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_CITY {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(String keyWord, String parentKey) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlRmsCity(keyWord, parentKey);
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    },

    INTL_RMS_PRODUCT {
        @Override
        public List<SearchReferenceRespDto> getSearchReferencesData(String keyWord, String parentKey) {
            List<SearchReference> searchReferenceList = SEARCH_REFERENCE_MAPPER.getSearchReferencesFromIntlIntlRmsProduct(keyWord);
            return transform2SearchReferenceRespDtoList(searchReferenceList);
        }
    };

    private static List<SearchReferenceRespDto> transform2SearchReferenceRespDtoList(List<SearchReference> searchReferenceList) {
        if (CollectionUtils.isEmpty(searchReferenceList)) {
            return new ArrayList<>();
        }
        List<SearchReferenceRespDto> searchReferenceRespDtoList = new ArrayList<>();
        for (SearchReference searchReference : searchReferenceList) {
            SearchReferenceRespDto searchReferenceRespDto = new SearchReferenceRespDto();
            String title = searchReference.getName() + "(" + searchReference.getCode() + ")";
            searchReferenceRespDto.setId(searchReference.getCode());
            searchReferenceRespDto.setTitle(title);
            searchReferenceRespDtoList.add(searchReferenceRespDto);
        }
        return searchReferenceRespDtoList;
    }

    private static final SearchReferenceMapper SEARCH_REFERENCE_MAPPER = SpringUtil.getBean(SearchReferenceMapper.class);


    public abstract List<SearchReferenceRespDto> getSearchReferencesData(String keyWord, String parentKey);
}
