package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * RMS 建议零售价（RRP）
 *
 * @TableName intl_rms_rrp
 */
@TableName(value = "intl_rms_rrp")
@Data
public class IntlRmsRrp implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * rrpID
     */
    @TableField(value = "rrp_id")
    private String rrpId;

    /**
     * rrp名称
     */
    @TableField(value = "rrp_name")
    private String rrpName;

    /**
     * rrp编码
     */
    @TableField(value = "rrp_code")
    private String rrpCode;

    /**
     * 国家ID
     */
    @TableField(value = "country_id")
    private String countryId;

    /**
     * 国家名称
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 国家编码
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * procudtID
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * product名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * product编码
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 建议零售价（RRP）
     */
    @TableField(value = "rrp")
    private BigDecimal rrp;

    /**
     * 经销商价格（RDP）
     */
    @TableField(value = "rdp")
    private BigDecimal rdp;

    /**
     * 货币类型（如 USD、CNY 等）
     */
    @TableField(value = "currency")
    private String currency;

    /**
     * SPU
     */
    @TableField(value = "spu")
    private String spu;

    /**
     * SPU EN
     */
    @TableField(value = "spuen", property = "spuEn")
    private String spuEn;

    /**
     * 生效时间
     */
    @TableField(value = "valid_time")
    private Long validTime;

    /**
     * 失效时间
     */
    @TableField(value = "invalid_time")
    private Long invalidTime;

    /**
     * 是否启用（0：可用；1 停用）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建人mid
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 创建时间戳
     */
    @TableField(value = "created_on")
    private Long createdOn;

    /**
     * 写入时间
     */
    @TableField(value = "created_at")
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    private Long updatedAt;

    /**
     * 修改人mid
     */
    @TableField(value = "modified_by")
    private Long modifiedBy;

    /**
     * 修改时间戳
     */
    @TableField(value = "modified_on")
    private Long modifiedOn;

}