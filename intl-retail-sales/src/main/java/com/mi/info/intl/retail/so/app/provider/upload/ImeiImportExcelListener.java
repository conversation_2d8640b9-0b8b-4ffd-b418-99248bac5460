package com.mi.info.intl.retail.so.app.provider.upload;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.mi.info.intl.retail.so.app.provider.upload.dto.ImeiImportExcelData;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * IMEI导入Excel监听器
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Slf4j
public class ImeiImportExcelListener implements ReadListener<ImeiImportExcelData> {

    private final List<ImeiImportExcelData> dataList;
    private final AtomicInteger rowIndex;

    public ImeiImportExcelListener(List<ImeiImportExcelData> dataList, AtomicInteger rowIndex) {
        this.dataList = dataList;
        this.rowIndex = rowIndex;
    }

    @Override
    public void invoke(ImeiImportExcelData data, AnalysisContext context) {
        // 设置行号
        data.setRowIndex(rowIndex.getAndIncrement());
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel文件解析完成，共解析{}行数据", dataList.size());
    }
}
