package com.mi.info.intl.retail.so.domain.rule.service.impl;

import static java.util.stream.Collectors.groupingBy;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.api.so.rule.resp.GetRetailerSoRuleResp;
import com.mi.info.intl.retail.bean.BasePage;
import com.mi.info.intl.retail.core.feishu.service.SendMessageService;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.PhotoRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleChangeLogDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleDetailResultDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerStatisticsDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerValidateDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySuRuleRetailerReq;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.so.domain.rule.constants.CommonConstants;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;
import com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleRetailerMapper;
import com.mi.info.intl.retail.so.util.ConvertUtils;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/7/28 09:48
 */
@Slf4j
@Service
public class IntlSoRuleRetailerServiceImpl extends ServiceImpl<IntlSoRuleRetailerMapper, IntlSoRuleRetailer>
    implements IntlSoRuleRetailerService {

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private IntlSoRuleDetailService intlSoRuleDetailService;

    @Resource
    private IntlSoRuleChangeLogService intlSoRuleChangeLogService;

    @Resource
    private IntlRetailerApiService intlRetailerApiService;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private FdsService fdsService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private IntlSysDictService intlSysDictService;

    @Override
    public List<IntlSoRuleRetailer> getByCountryCodeAndCategory(String countryCode, Integer category) {
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode).eq(IntlSoRuleRetailer::getCategory, category);
        return this.list(queryWrapper);
    }

    @Override
    public Boolean existsBothDisabledImeiAndQtySwitch(SoRuleRetailerValidateDTO retailerValidateDto) {
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, retailerValidateDto.getCountryCode())
            .eq(IntlSoRuleRetailer::getCategory, getRetailerCategoryByCreateType(retailerValidateDto.getType()))
            .eq(IntlSoRuleRetailer::getImeiSwitch, SwitchEnum.OFF.getValue())
            .eq(IntlSoRuleRetailer::getQtySwitch, SwitchEnum.OFF.getValue());
        return this.count(queryWrapper) > 0;
    }

    @Override
    public GetRetailerSoRuleResp getRetailerSoRule(GetRetailerSoRuleReq req) {
        IntlPositionDTO intlPositionDTO = getRetailerCodeByPositionId(req);
        IntlSoRuleRetailer retailer =
            getRetailerByCode(intlPositionDTO.getRetailerCode(), SoRuleEnum.MASTER.getValue());
        IntlSoRuleDetail soRuleDetail = intlSoRuleDetailService.getById(retailer.getRuleId());
        if (soRuleDetail == null) {
            throw new BizException(ErrorCodes.SO_RULE_NOT_EXIST, retailer.getRuleId());
        }
        GetRetailerSoRuleResp resp = new GetRetailerSoRuleResp();
        resp.setRuleId(retailer.getRuleId());
        resp.setCountryCode(soRuleDetail.getCountryCode());
        resp.setRetailerCode(retailer.getRetailerCode());
        resp.setStoreCode(intlPositionDTO.getStoreCode());
        resp.setEnableImei(retailer.getImeiSwitch());
        resp.setEnableQty(retailer.getQtySwitch());
        String imeiRequirePhoto = soRuleDetail.getImeiRuleList();
        // 设置图片验证规则
        List<PhotoRuleDTO> list = JSON.parseArray(imeiRequirePhoto, PhotoRuleDTO.class);
        if (CollectionUtils.isNotEmpty(list)) {
            list.stream().filter(t -> CollectionUtils.isNotEmpty(t.getUserTitleList()))
                .filter(t -> t.getUserTitleList().stream()
                    .anyMatch(item -> Objects.equals(item.getUserTitleId(), req.getUserTitleId())))
                .findFirst().ifPresent(t -> {
                    resp.setImeiRequirePhoto(Objects.equals(t.getImeiRequirePhoto(), SwitchEnum.ON.getValue()));
                    resp.setQtyRequirePhoto(Objects.equals(t.getQtyRequirePhoto(), SwitchEnum.ON.getValue()));
                });
        }
        return resp;
    }

    /**
     * 按职位ID获取零售商代码
     *
     * @param req req
     * @return {@link IntlPositionDTO }
     */
    public IntlPositionDTO getRetailerCodeByPositionId(GetRetailerSoRuleReq req) {
        IntlPositionDTO dto = new IntlPositionDTO();
        dto.setPositionCode(req.getPositionCode());
        Optional<IntlPositionDTO> optional = intlPositionApiService.getRetailerByPositionCode(dto);
        if (!optional.isPresent()) {
            throw new BizException(ErrorCodes.POSITION_NOT_EXIST, req.getPositionCode());
        }
        return dto;
    }

    /**
     * 通过代码获取零售商
     *
     * @param retailerCode 零售商代码
     * @param category 类别
     * @return {@link IntlSoRuleRetailer }
     */
    public IntlSoRuleRetailer getRetailerByCode(String retailerCode, int category) {
        IntlSoRuleRetailer retailer = this
            .getOne(new QueryWrapper<IntlSoRuleRetailer>().eq("retailer_code", retailerCode).eq("category", category));
        if (retailer == null) {
            throw new BizException(ErrorCodes.RETAILER_NOT_EXIST, retailerCode);
        }
        return retailer;
    }

    @Override
    public List<IntlSoRuleRetailer> getByCountryCodeAndRetailerCodes(String countryCode, Integer category,
        List<String> retailerCodes) {
        if (CollectionUtils.isEmpty(retailerCodes) || StringUtils.isBlank(countryCode)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode).eq(IntlSoRuleRetailer::getCategory, category)
            .in(IntlSoRuleRetailer::getRetailerCode, retailerCodes);
        return this.list(queryWrapper);
    }

    /**
     * 根据条件获取零售商
     *
     * @param req req
     * @return {@link IPage }<{@link SoRuleRetailerDTO }>
     */
    @Override
    public IPage<SoRuleRetailerDTO> getRetailerByCondition(QuerySuRuleRetailerReq req) {
        CountryDTO countryInfo = getCountryDTO(req);
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper =
            getIntlSoRuleRetailerLambdaQueryWrapper(req, countryInfo.getCountryCode());

        Page<IntlSoRuleRetailer> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<IntlSoRuleRetailer> result = this.page(page, queryWrapper);

        // 构建分页参数
        List<SoRuleRetailerDTO> collect =
            result.getRecords().stream().map(t -> getSoRuleRetailerDTO(t, countryInfo)).collect(Collectors.toList());

        IPage<SoRuleRetailerDTO> resultPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        resultPage.setRecords(collect);

        return resultPage;
    }

    private CountryDTO getCountryDTO(QuerySuRuleRetailerReq req) {
        Assert.notNull(req.getType(), "type is null");
        return getCountryDTO(req.getCountryCode());
    }

    private CountryDTO getCountryDTO(String countryCode) {
        Assert.notNull(countryCode, "countryCode is null");
        Optional<CountryDTO> optional = countryTimeZoneApiService.getCountryInfoByCode(countryCode);
        if (!optional.isPresent()) {
            log.error("country code {} not exist ", countryCode);
            throw new BizException(ErrorCodes.COUNTRY_CODE_NOT_EXIST, countryCode);
        }
        return optional.get();
    }

    /**
     * 获得SO Rule Quare Do dto
     *
     * @param t t
     * @param countryInfo 国家信息
     * @return {@link SoRuleRetailerDTO }
     */
    private SoRuleRetailerDTO getSoRuleRetailerDTO(IntlSoRuleRetailer t, CountryDTO countryInfo) {
        SoRuleRetailerDTO dto = new SoRuleRetailerDTO();
        dto.setCountryName(countryInfo.getCountryName());
        dto.setRegionName(countryInfo.getArea());
        dto.setCountryCode(countryInfo.getCountryCode());
        dto.setCountryCode(countryInfo.getAreaCode());
        dto.setRetailerCode(t.getRetailerCode());
        dto.setChannelType(t.getChannelType());
        dto.setRetailerName(t.getRetailerName());
        dto.setImeiSwitch(t.getImeiSwitch());
        dto.setQtySwitch(t.getQtySwitch());
        dto.setCreateRetailerTime(t.getCreateRetailerTime());
        return dto;
    }

    private LambdaQueryWrapper<IntlSoRuleRetailer> getIntlSoRuleRetailerLambdaQueryWrapper(QuerySuRuleRetailerReq req,
        String countryCode) {
        // 构造基本查询参数
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getCountryCode, countryCode)
            .like(StringUtils.isNotEmpty(req.getRetailerCode()), IntlSoRuleRetailer::getRetailerCode,
                req.getRetailerCode())
            .like(StringUtils.isNotEmpty(req.getRetailerName()), IntlSoRuleRetailer::getRetailerName,
                req.getRetailerName())
            .eq(StringUtils.isNotEmpty(req.getImeiSwitch()), IntlSoRuleRetailer::getImeiSwitch, req.getImeiSwitch())
            .eq(StringUtils.isNotEmpty(req.getQtySwitch()), IntlSoRuleRetailer::getQtySwitch, req.getQtySwitch())
            .ge(req.getStartTime() != null, IntlSoRuleRetailer::getCreateRetailerTime, req.getStartTime())
            .le(req.getEndTime() != null, IntlSoRuleRetailer::getCreateRetailerTime, req.getEndTime());
        // 查询Type为1
        if (req.getType() == 1) {
            queryWrapper.eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue())
                .eq(IntlSoRuleRetailer::getRuleId, CommonConstants.INIT_RULE_ID);
        } else if (req.getType() == 2) {
            // 查询主数据关了的记录
            SoRuleDetailResultDTO soRuleDetail = intlSoRuleDetailService.getRuleDetailByCountryCode(countryCode);
            queryWrapper.eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue())
                .eq(IntlSoRuleRetailer::getRuleId, soRuleDetail.getId());
        } else if (req.getType() == 3) {
            // 查询审批中记录
            SoRuleChangeLogDTO soRuleDetail = intlSoRuleChangeLogService.getRuleDetailByCodeAndStatus(countryCode,
                SoRuleDetailApproveStatus.PENDING.getValue());
            queryWrapper.eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.COPY.getValue())
                .eq(IntlSoRuleRetailer::getRuleId, soRuleDetail.getId());
        } else {
            queryWrapper.eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue())
                .isNull(IntlSoRuleRetailer::getRuleId);
        }
        return queryWrapper;
    }

    @Override
    public String exportRetailerList(QuerySuRuleRetailerReq req) {
        CountryDTO countryInfo = getCountryDTO(req);
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper =
            getIntlSoRuleRetailerLambdaQueryWrapper(req, countryInfo.getCountryCode());
        List<IntlSoRuleRetailer> list = this.list(queryWrapper);
        return exportRetailerList(list, countryInfo);
    }

    @Override
    public void initAllCountryRetailer() {
        // 删除所有初始化零售商
        LambdaQueryWrapper<IntlSoRuleRetailer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntlSoRuleRetailer::getRuleId, CommonConstants.INIT_RULE_ID)
            .eq(IntlSoRuleRetailer::getCategory, SoRuleEnum.MASTER.getValue()).isNull(IntlSoRuleRetailer::getCreatedBy);
        // 先清空以前的数据，然后重新同步
        this.remove(wrapper);
        BasePage req = new BasePage();
        req.setPageNum(1);
        req.setPageSize(1000);
        Map<String, String> channelTypeMap = getChannelTypeMap();
        try {
            // 然后重新同步
            do {
                List<IntlRetailerDTO> list = intlRetailerApiService.getRetailerListByPage(req);
                if (list.isEmpty()) {
                    break;
                }
                req.setPageNum(req.getPageNum() + 1);
                List<IntlSoRuleRetailer> retailerList = list.stream()
                    .filter(
                        t -> StringUtils.isNotBlank(t.getCountryCode()) && StringUtils.isNotBlank(t.getRetailerCode()))
                    .map(t -> ConvertUtils.buildRetailerWithRetailerType(t, channelTypeMap, SoRuleEnum.MASTER,
                        CommonConstants.INIT_RULE_ID))
                    .collect(Collectors.toList());
                saveBatch(retailerList, 1000);
            } while (true);
        } catch (Exception e) {
            log.error("initAllCountryRetailer error {}", ExceptionUtils.getStackTrace(e));
            // 发生异常，则删除所有初始化零售商
            this.remove(wrapper);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dealIncrementRetailer() {
        List<IntlRetailerDTO> list = intlRetailerApiService.getIncrementRetailer();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, List<IntlRetailerDTO>> map = list.stream().collect(groupingBy(IntlRetailerDTO::getCountryCode));
        StringBuilder sb = new StringBuilder();
        Map<String, String> channelTypeMap = getChannelTypeMap();
        map.forEach((countryCode, retailerDtoList) -> {
            Optional<IntlSoRuleDetail> optional = intlSoRuleDetailService.getByCountryCode(countryCode);
            if (optional.isPresent()) {
                // 查询主数据关了的记录
                long id = optional.get().getId();
                List<Integer> retailersSwitchList =
                    JSON.parseArray(optional.get().getDefaultRetailersSwitch(), Integer.class);
                List<IntlSoRuleRetailer> retailerList = retailerDtoList.stream()
                    .map(t -> ConvertUtils.buildRetailerWithRetailerType(t, channelTypeMap, SoRuleEnum.MASTER, id))
                    .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(retailersSwitchList)) {
                    sb.append("●").append(countryCode).append("：新增").append(retailersSwitchList.size())
                        .append("个Retailer\n");
                    if (retailersSwitchList.contains(1)) {
                        retailerList.forEach(t -> t.setImeiSwitch(1));
                    }
                    if (retailersSwitchList.contains(2)) {
                        retailerList.forEach(t -> t.setQtySwitch(1));
                    }
                    saveBatch(retailerList, 100);
                }
            } else {
                // 新增的国家，未配置规则列表，那么写入临时数据
                List<IntlSoRuleRetailer> retailerList =
                    retailerDtoList.stream().map(t -> ConvertUtils.buildRetailerWithRetailerType(t, channelTypeMap,
                        SoRuleEnum.MASTER, CommonConstants.INIT_RULE_ID)).collect(Collectors.toList());
                saveBatch(retailerList, 100);
            }

        });
        // 更新新增数据为初始化的
        sendMessageService.sendTextMessage(sb.toString());
    }

    @Override
    public SoRuleEnum getRetailerCategoryByCreateType(Integer type) {
        return Objects.equals(type, 1) ? SoRuleEnum.MASTER : SoRuleEnum.COPY;
    }

    @Override
    public SoRuleRetailerStatisticsDTO getRetailerStatistics(List<IntlSoRuleRetailer> retailerList) {
        int retailersCount = retailerList.size();
        int noRuleRetailersCount = Math
            .toIntExact(retailerList.stream().filter(it -> Objects.equals(it.getImeiSwitch(), SwitchEnum.OFF.getValue())
                && Objects.equals(it.getQtySwitch(), SwitchEnum.OFF.getValue())).count());
        int imeiRetailersCount = Math.toIntExact(
            retailerList.stream().filter(it -> Objects.equals(it.getImeiSwitch(), SwitchEnum.ON.getValue())).count());
        int qtyRetailersCount = Math.toIntExact(
            retailerList.stream().filter(it -> Objects.equals(it.getQtySwitch(), SwitchEnum.ON.getValue())).count());
        return new SoRuleRetailerStatisticsDTO(retailersCount, noRuleRetailersCount, imeiRetailersCount,
            qtyRetailersCount);
    }

    @Override
    public String exportRetailerForApproveFlow(Long soRuleDetailLogId, String countryCode) {
        CountryDTO countryInfo = getCountryDTO(countryCode);
        LambdaQueryWrapper<IntlSoRuleRetailer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleRetailer::getRuleId, soRuleDetailLogId).eq(IntlSoRuleRetailer::getCountryCode,
            countryCode);
        List<IntlSoRuleRetailer> list = this.list(queryWrapper);
        return exportRetailerList(list, countryInfo);
    }

    private String exportRetailerList(List<IntlSoRuleRetailer> list, CountryDTO countryInfo) {
        List<SoRuleRetailerDTO> dtoList =
            list.stream().map(t -> getSoRuleRetailerDTO(t, countryInfo)).collect(Collectors.toList());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        String fileName = "Retailers_" + System.currentTimeMillis() + ".xlsx";
        EasyExcel.write(outputStream, SoRuleRetailerDTO.class)
            .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25)).sheet(countryInfo.getCountryName())
            .doWrite(dtoList);
        return fdsService.uploadFile(fileName, new ByteArrayInputStream(outputStream.toByteArray()));
    }

    private Map<String, String> getChannelTypeMap() {
        DictSysRequest request =
            new DictSysRequest(Arrays.asList(new DictSysDTO("Store", CommonConstants.CHANNEL_TYPE)));
        Map<String, List<LabelValueDTO>> map = intlSysDictService.getLabelValueListByDictCode(request);
        // 未获取到数据，返回为空
        if (MapUtils.isEmpty(map) || CollectionUtils.isEmpty(map.get(CommonConstants.CHANNEL_TYPE))) {
            return Maps.newHashMap();
        }
        return map.get(CommonConstants.CHANNEL_TYPE).stream()
            .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
    }
}
