package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.domain.datasync.entity.IntlDatasyncLog;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataAbnormalEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoImeiEsService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoUserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_imei(销量imei)】的数据库操作Service实现
 * @createDate 2025-07-25 16:36:39
 */
@Slf4j
@Service
public class IntlSoImeiServiceImpl extends ServiceImpl<IntlSoImeiMapper, IntlSoImei>
        implements IntlSoImeiService {

    @Resource
    private UserApiService userApiService;
    @Resource
    private IntlDatasyncLogMapper intlDatasyncLogMapper;
    @Resource
    private IntlSoOrgInfoMapper intlSoOrgInfoMapper;
    @Resource
    private IntlSoUserInfoMapper intlSoUserInfoMapper;
    @Resource
    private RmsStoreService rmsStoreService;
    @Resource
    private IntlRetailerApiService retailerApiService;
    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;

    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public IntlSoImei getIntlSoImeiByCondition(Long id, String rmsId) {
        return this.lambdaQuery().select(IntlSoImei::getId)
                .eq(ObjectUtils.isNotEmpty(id), IntlSoImei::getId, id)
                .eq(StringUtils.isNotEmpty(rmsId), IntlSoImei::getRmsId, rmsId).one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImeiSave(RmsSyncImeiData data) {
        log.info("----------doImeiSave-----------，rmsId：{}", data.getRmsId());
        IntlSoImei intlSoImei = new IntlSoImei();
        Long createdByMiId = parseLongSafely(data.getCreatedbyMiid());
        Long salesmanMiId = parseLongSafely(data.getSalesManMiid());
        Long modifiedByMiId = parseLongSafely(data.getCreatedbyMiid());

        //查询用户信息
        CompletableFuture<Optional<List<IntlRmsUserNewDto>>> getUserFuture = CompletableFuture.supplyAsync(
                () -> userApiService.getUserListByMiIds(Arrays.asList(createdByMiId, salesmanMiId)));

        // 查询门店信息
        String storeCodeRMS = data.getStoreCodeRMS();
        String positionCodeRMS = data.getPositionCodeRMS();
        CompletableFuture<Optional<RmsStoreInfoDto>> storeInfoFuture =
                CompletableFuture.supplyAsync(() -> rmsStoreService.getStoreInfoByStoreCode(storeCodeRMS),
                        threadPoolTaskExecutor.getThreadPoolExecutor());
        CompletableFuture<Optional<RmsPositionInfoRes>> positionInfoFuture =
                CompletableFuture.supplyAsync(() -> rmsStoreService.getPositionIfoByPositionCode(positionCodeRMS),
                        threadPoolTaskExecutor.getThreadPoolExecutor());

        // 查询零售商信息
        String retailerCode = data.getRetailerCode();
        CompletableFuture<Optional<IntlRetailerDTO>> retailerFuture = CompletableFuture.supplyAsync(
                () -> retailerApiService.getRetailerByRetailerCode(
                        new IntlPositionDTO().setRetailerCode(retailerCode)),
                threadPoolTaskExecutor.getThreadPoolExecutor());

        // 等待所有异步任务完成
        CompletableFuture.allOf(getUserFuture, storeInfoFuture, positionInfoFuture, retailerFuture).join();

        // 主数据
        ComponentLocator.getConverter().convert(data, intlSoImei);
        List<String> abnormalList = new ArrayList<>();

        // 处理用户信息
        IntlSoUserInfo intlSoUserInfo =
                handleUserInfo(getUserFuture.join(), createdByMiId, salesmanMiId, abnormalList);
        intlSoUserInfoMapper.insert(intlSoUserInfo);

        // 处理门店信息
        IntlSoOrgInfo intlSoOrgInfo =
                handleOrgInfo(storeInfoFuture.join(), positionInfoFuture.join(), retailerFuture.join(), data,
                        abnormalList);
        intlSoOrgInfoMapper.insert(intlSoOrgInfo);

        intlSoImei.setUserInfoId(intlSoUserInfo.getId());
        intlSoImei.setOrgInfoId(intlSoOrgInfo.getId());
        // 字段名不一致，手动设值
        intlSoImei.setStoreRmsCode(storeCodeRMS);
        intlSoImei.setPositionRmsCode(positionCodeRMS);
        intlSoImei.setRrp(data.getRrp());
        intlSoImei.setRrpCode(data.getRrpCode());
        intlSoImei.setVerificationResult(data.getVerifyResult());
        intlSoImei.setCreatedOn(data.getCreatedTime());
        intlSoImei.setCreatedBy(createdByMiId);
        intlSoImei.setModifiedBy(modifiedByMiId);
        intlSoImei.setModifiedOn(data.getModifiedon());
        intlSoImei.setSalesmanMid(salesmanMiId);
        intlSoImei.setDataFrom(DataFromEnum.RMS.getCode());
        intlSoImei.setReportingType(data.getReportType());
        intlSoImei.setRepeatUser(data.getRepeatUserDetail());
        baseMapper.insert(intlSoImei);

        // 记录同步日志表
        recordSyncLog(data, DataSyncOperateTypeTypeEnum.CREATE, intlSoImei.getId(), abnormalList);
    }

    private IntlSoUserInfo handleUserInfo(Optional<List<IntlRmsUserNewDto>> userListOptional, Long createdByMiId,
                                          Long salesmanMiId, List<String> abnormalList) {
        IntlSoUserInfo intlSoUserInfo = new IntlSoUserInfo();
        intlSoUserInfo.setCreatedByMid(createdByMiId);
        intlSoUserInfo.setSalesmanMid(salesmanMiId);
        if (!userListOptional.isPresent()) {
            abnormalList.add("未查询到上报人员用户信息，上报人员米id：" + createdByMiId);
            abnormalList.add("未查询到销售人员用户信息，销售人员米id：" + salesmanMiId);
            return intlSoUserInfo;
        }
        List<IntlRmsUserNewDto> intlRmsUserNewDtos = userListOptional.get();
        Map<Long, IntlRmsUserNewDto> userGroupByMid =
                intlRmsUserNewDtos.stream().collect(Collectors.toMap(IntlRmsUserNewDto::getMiId, e -> e));
        if (userGroupByMid.containsKey(createdByMiId)) {
            IntlRmsUserNewDto createUserUserInfo = userGroupByMid.get(createdByMiId);
            intlSoUserInfo.setCreatedByRmsAccount(createUserUserInfo.getRmsUserid());
            intlSoUserInfo.setCreatedByJobTitle(createUserUserInfo.getJobId());
        } else {
            abnormalList.add("未查询到上报人员用户信息，上报人员米id：" + createdByMiId);
        }
        if (userGroupByMid.containsKey(salesmanMiId)) {
            IntlRmsUserNewDto salesyUserInfo = userGroupByMid.get(salesmanMiId);
            intlSoUserInfo.setSalesmanRmsAccount(salesyUserInfo.getRmsUserid());
            intlSoUserInfo.setSalesmanJobTitle(salesyUserInfo.getJobId());
        } else {
            abnormalList.add("未查询到销售人员用户信息，销售人员米id：" + salesmanMiId);
        }
        return intlSoUserInfo;
    }

    private IntlSoOrgInfo handleOrgInfo(Optional<RmsStoreInfoDto> storeInfoOpt,
                                        Optional<RmsPositionInfoRes> positionInfoOpt,
                                        Optional<IntlRetailerDTO> retailerOpt, RmsSyncImeiData data,
                                        List<String> abnormalList) {
        IntlSoOrgInfo intlSoOrgInfo = new IntlSoOrgInfo();
        intlSoOrgInfo.setStoreCode(data.getStoreCodeNew());
        intlSoOrgInfo.setStoreRmsCode(data.getStoreCodeRMS());
        intlSoOrgInfo.setPositionRmsCode(data.getPositionCodeRMS());
        intlSoOrgInfo.setPositionCode(data.getPositionCodeNew());
        intlSoOrgInfo.setRetailerCode(data.getRetailerCode());

        if (storeInfoOpt.isPresent()) {
            RmsStoreInfoDto rmsStoreInfoDto = storeInfoOpt.get();
            intlSoOrgInfo.setStoreType(rmsStoreInfoDto.getType());
            intlSoOrgInfo.setStoreGrade(rmsStoreInfoDto.getGrade());
            intlSoOrgInfo.setStoreHasPC(rmsStoreInfoDto.getHasPc());
            intlSoOrgInfo.setStoreHasSR(rmsStoreInfoDto.getHasSR());
            intlSoOrgInfo.setStoreChannelType(rmsStoreInfoDto.getChannelType());
            intlSoOrgInfo.setCountryCode(rmsStoreInfoDto.getCountryShortcode());
            intlSoOrgInfo.setStoreId(rmsStoreInfoDto.getId());
        } else {
            abnormalList.add("未查询到门店信息，storeCodeRms：" + data.getStoreCodeRMS());
        }

        if (positionInfoOpt.isPresent()) {
            RmsPositionInfoRes rmsPositionInfoDto = positionInfoOpt.get();
            intlSoOrgInfo.setPositionType(rmsPositionInfoDto.getType());
            intlSoOrgInfo.setPositionId(rmsPositionInfoDto.getId());
        } else {
            abnormalList.add("未查询到阵地信息，positionCodeRms：" + data.getPositionCodeRMS());
        }

        if (retailerOpt.isPresent()) {
            IntlRetailerDTO intlRetailerDTO = retailerOpt.get();
            intlSoOrgInfo.setRetailerId(intlRetailerDTO.getId());
        } else {
            abnormalList.add("未查询到零售商信息，retailerCode：" + data.getRetailerCode());
        }

        return intlSoOrgInfo;
    }

    private Long parseLongSafely(String str) {
        if (StringUtils.isEmpty(str)) {
            return 0L;
        }
        try {
            return NumberUtils.createLong(str);
        } catch (NumberFormatException e) {
            log.warn("Failed to parse Long value: {}", str);
            return 0L;
        }
    }

    private void recordSyncLog(RmsSyncImeiData data, DataSyncOperateTypeTypeEnum operateType, Long intlSoImeiId,
                               List<String> abnormalList) {
        IntlDatasyncLog intlDatasyncLog = new IntlDatasyncLog()
                .setType(DataSyncDataTypeEnum.IMEI.getCode())
                .setRmsId(data.getRmsId())
                .setRetailId(intlSoImeiId)
                .setMessage(JSON.toJSONString(data))
                .setOperateType(operateType.getCode());
        if (CollectionUtils.isNotEmpty(abnormalList)) {
            intlDatasyncLog.setIsDataAbnormal(DataAbnormalEnum.DATA_ABNORMAL.getCode());
            intlDatasyncLog.setAbnormalMessage(String.join(";", abnormalList));
        }
        intlDatasyncLogMapper.insert(intlDatasyncLog);
    }

    /**
     * @param operatorType 修改类型
     * @param fields 修改的字段集合
     * @param data 修改的数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImeiUpdate(String operatorType, List<String> fields, RmsSyncImeiData data) {
        log.info("----------doImeiUpdate-----------，rmsId：{}，retailId：{}", data.getRmsId(), data.getRetailId());
        IntlSoImei intlSoImeiByCondition = getIntlSoImeiByCondition(data.getId(), data.getRmsId());
        if (intlSoImeiByCondition == null) {
            log.info("doImeiUpdate intlSoImei 数据不存在, rmsId: {},retailId：{}， timestamp: {}", data.getRmsId(),
                    data.getRetailId(),
                    System.currentTimeMillis());
            return;
        }

        LambdaUpdateWrapper<IntlSoImei> wrapper = Wrappers.lambdaUpdate(IntlSoImei.class)
                .eq(IntlSoImei::getId, data.getId())
                // 使用修改时间做乐观锁，同步的数据修改时间必须大于数据库中修改时间才做修改
                .lt(IntlSoImei::getModifiedOn, data.getModifiedon());

        wrapper.set(IntlSoImei::getImeiRuleIsActivingCheck, data.getImeiRuleIsActivingCheck())
                .set(IntlSoImei::getImeiRuleBefore, data.getImeiRuleBefore())
                .set(IntlSoImei::getImeiRuleAfter, data.getImeiRuleAfter())
                .set(IntlSoImei::getActivationVerificationTime, data.getActivationVerificationTime())
                .set(IntlSoImei::getVerifyingState, data.getVerifyingState())
                .set(IntlSoImei::getActivationTime, data.getActivationTime())
                .set(IntlSoImei::getActivationFrequency, data.getActivationFrequency())
                .set(IntlSoImei::getActivationSite, data.getActivationSite())
                .set(IntlSoImei::getSiVerifyResult, data.getSiVerifyResult())
                .set(IntlSoImei::getVerifyResultDetail, data.getVerifyResultDetail())
                .set(IntlSoImei::getVerificationResult, data.getVerifyResult())
                .set(IntlSoImei::getRepeatUser, data.getRepeatUserDetail())
                .set(IntlSoImei::getLastMid, data.getLastMid())
                .set(IntlSoImei::getFinalSalesCountry, data.getFinalSalesCountry())
                .set(IntlSoImei::getFailedReason, data.getFailedReason())
                .set(IntlSoImei::getFailedReasonDetail, data.getFailedReasonDetail())
                .set(IntlSoImei::getFirstLevelAccountCode, data.getFirstLevelAccountCode())
                .set(IntlSoImei::getModifiedOn, data.getModifiedon())
                .set(IntlSoImei::getModifiedBy, parseLongSafely(data.getModifiedbyMiId()));
        baseMapper.update(null, wrapper);

        // 记录同步日志表
        recordSyncLog(data, DataSyncOperateTypeTypeEnum.UPDATE, intlSoImeiByCondition.getId(), null);
    }

    @Override
    public List<IntlSoImei> getNeedVerifyImeiList(Integer verifyState, Integer verifyResult, Integer limit) {
        if (null == verifyState || null == verifyResult || null == limit || limit <= 0) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<IntlSoImei> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoImei::getVerifyingState, verifyState);
        queryWrapper.eq(IntlSoImei::getVerificationResult, verifyResult);
        queryWrapper.ne(IntlSoImei::getSnHash, "");
        queryWrapper.ne(IntlSoImei::getSalesTime, 0L);
        queryWrapper.orderByAsc(IntlSoImei::getActivationVerificationTime).last("limit " + limit);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public int batchSoftUpdateById(List<IntlSoImei> intlSoImeiList) {
        if (CollectionUtils.isEmpty(intlSoImeiList)) {
            return 0;
        }
        return baseMapper.batchUpdateById(intlSoImeiList);
    }
}
