<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo">
        <id property="id" column="id" />
        <result property="storeChangelogId" column="store_changelog_id" />
        <result property="storeRmsCode" column="store_rms_code" />
        <result property="storeCode" column="store_code" />
        <result property="storeGrade" column="store_grade" />
        <result property="storeType" column="store_type" />
        <result property="storeChannelType" column="store_channel_type" />
        <result property="storeHasSR" column="store_hasSR" />
        <result property="storeHasPC" column="store_hasPC" />
        <result property="countryCode" column="country_code" />
        <result property="positionChangelogId" column="position_changelog_id" />
        <result property="positionRmsCode" column="position_rms_code" />
        <result property="positionCode" column="position_code" />
        <result property="positionType" column="position_type" />
        <result property="retailerCode" column="retailer_code" />
        <result property="storeId" column="store_id" />
        <result property="positionId" column="position_id" />
        <result property="retailerId" column="retailer_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,store_changelog_id,store_rms_code,store_code,store_grade,store_type,
        store_channel_type,store_hasSR,store_hasPC,country_code,position_changelog_id,
        position_rms_code,position_code,position_type,retailer_code,store_id,
        position_id,retailer_id
    </sql>
</mapper>
