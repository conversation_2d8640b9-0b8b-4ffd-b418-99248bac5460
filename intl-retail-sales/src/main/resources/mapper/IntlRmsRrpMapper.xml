<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsRrpMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsRrp">
        <id property="id" column="id"/>
        <result property="rrpId" column="rrp_id"/>
        <result property="rrpName" column="rrp_name"/>
        <result property="rrpCode" column="rrp_code"/>
        <result property="countryId" column="country_id"/>
        <result property="countryName" column="country_name"/>
        <result property="countryCode" column="country_code"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="productCode" column="product_code"/>
        <result property="rrp" column="rrp"/>
        <result property="rdp" column="rdp"/>
        <result property="currency" column="currency"/>
        <result property="spu" column="spu"/>
        <result property="spuen" column="spuen"/>
        <result property="validTime" column="valid_time"/>
        <result property="invalidTime" column="invalid_time"/>
        <result property="status" column="status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdOn" column="created_on"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedOn" column="modified_on"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rrp_id, rrp_name, rrp_code, country_id, country_name, country_code, product_id, product_name, product_code,
          rrp, rdp, currency, spu, spuen, valid_time, invalid_time, status, created_by, created_on, modified_by, modified_on, created_at, updated_at
    </sql>

    <!-- 根据产品code列表查询有效的价格信息，按创建时间降序排序 -->
    <select id="selectValidRrpByProductCodes" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_rms_rrp
        WHERE product_code IN
        <foreach collection="productCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND status = 0
        AND country_code = #{countryCode}
        AND valid_time &lt;= #{currentTime}
        AND invalid_time &gt; #{currentTime}
        ORDER BY created_on DESC
    </select>
</mapper>
