<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoPlaintextImeiUserMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoPlaintextImeiUser">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="countryName" column="country_name" jdbcType="VARCHAR"/>
            <result property="countryCode" column="country_code" jdbcType="BIGINT"/>
            <result property="photoUrl" column="photo_url" jdbcType="VARCHAR"/>
            <result property="userMid" column="user_mid" jdbcType="BIGINT"/>
            <result property="createdby" column="createdby" jdbcType="BIGINT"/>
            <result property="createdon" column="createdon" jdbcType="BIGINT"/>
            <result property="modifiedby" column="modifiedby" jdbcType="BIGINT"/>
            <result property="modifiedon" column="modifiedon" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,country_name,country_code,
        photo_url,user_mid,createdby,
        createdon,modifiedby,modifiedon,
        status
    </sql>

    <select id="selectPageList" resultType="com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO">
        SELECT t1.id,t1.country_name,
               t1.photo_url,t1.created_by,
               t1.created_on,t1.modified_by,t1.modified_on,
               t1.status,t3.english_name as createByName,
               t3.mi_id as userAccount,
               t2.english_name as userName,t2.job_name as userTitle
        FROM intl_so_plaintext_imei_user t1
                 LEFT JOIN intl_rms_user t2 ON t1.user_mid = t2.mi_id
                 LEFT JOIN intl_rms_user t3 ON t1.created_by = t3.mi_id

         <where>
            t1.status = 0
            <if test="query.userName != null and query.userName != '' ">
             AND t2.english_name LIKE CONCAT('%',#{query.userName},'%')
            </if>

            <if test="query.createBy != null and query.createBy != '' ">
            AND t3.english_name LIKE  CONCAT('%',#{query.createBy},'%')
            </if>

            <if test="query.userTitle != null and query.userTitle != '' ">
          AND t2.job_id = #{query.userTitle}
            </if>

            <if test="query.countryCode != null and query.countryCode != '' ">
          AND t1.country_code = #{query.countryCode}
            </if>

            <if test="query.createStartOn != null and query.createStartOn != '' ">
          AND t1.created_on BETWEEN #{query.createStartOn} AND #{query.createEndOn}
            </if>

            <if test="query.id != null ">
          AND t1.id = #{query.id}
            </if>

        </where>
        ORDER BY t1.modified_on DESC
        LIMIT  #{query.pageSize} OFFSET  #{query.offset}
    </select>


    <select id="countPageList" resultType="java.lang.Integer">
        SELECT count(*)
        FROM intl_so_plaintext_imei_user t1
        LEFT JOIN intl_rms_user t2 ON t1.user_mid = t2.mi_id
        LEFT JOIN intl_rms_user t3 ON t1.created_by = t3.mi_id

        <where>
            t1.status = 0
            <if test="query.userName != null and query.userName != '' ">
                AND t2.english_name LIKE CONCAT('%',#{query.userName},'%')
            </if>

            <if test="query.createBy != null and query.createBy != '' ">
                AND t3.english_name LIKE  CONCAT('%',#{query.createBy},'%')
            </if>

            <if test="query.userTitle != null and query.userTitle != '' ">
                AND t2.job_id = #{query.userTitle}
            </if>

            <if test="query.countryCode != null and query.countryCode != '' ">
                AND t1.country_code = #{query.countryCode}
            </if>

            <if test="query.createStartOn != null and query.createStartOn != '' ">
                AND t1.created_on BETWEEN #{query.createStartOn} AND #{query.createEndOn}
            </if>

            <if test="query.id != null ">
                AND t1.id = #{query.id}
            </if>

        </where>
    </select>
</mapper>
