<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.sales.SearchReferenceMapper">

    <select id="getSearchReferencesFromIntlRmsPosition" resultType="com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference">
        select code,name from intl_rms_position
        where state_code = 0 and code like concat('%',#{keyWord},'%') or name like concat('%',#{keyWord},'%') limit 100
    </select>

    <select id="getSearchReferencesFromIntlRmsStore" resultType="com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference">
        select code,name from intl_rms_store
        where state_code = 0 and code like concat('%',#{keyWord},'%') or name like concat('%',#{keyWord},'%') limit 100
    </select>

    <select id="getSearchReferencesFromIntlRmsUser" resultType="com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference">
        select mi_id as code,english_name as name  from intl_rms_user
        where code like concat('%',#{keyWord},'%') or domain_name like concat('%',#{keyWord},'%') or
            english_name like concat('%',#{keyWord},'%') or mi_id like concat('%',#{keyWord},'%')
            limit 100
    </select>

    <select id="getSearchReferencesFromIntlRmsRetailer" resultType="com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference">
        select name as code,retailer_name as name from intl_rms_retailer
        where name like concat('%',#{keyWord},'%') or crm_code like concat('%',#{keyWord},'%') or retailer_name like concat('%',#{keyWord},'%')
        limit 100
    </select>

    <select id="getSearchReferencesFromIntlRmsCity" resultType="com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference">
        select city_code as code,city_name as name from intl_rms_city
        where country_shortcode = #{countryShortcode} and ( city_code like concat('%',#{keyWord},'%') or city_name like concat('%',#{keyWord},'%') )
        limit 100
    </select>

    <select id="getSearchReferencesFromIntlIntlRmsProduct" resultType="com.mi.info.intl.retail.so.infra.database.dataobject.sales.SearchReference">
        select goods_id as code,english_name as name  from intl_rms_product
        where goods_id like concat('%',#{keyWord},'%') or code69 like concat('%',#{keyWord},'%') or english_name like concat('%',#{keyWord},'%')
        limit 100
    </select>



</mapper>
