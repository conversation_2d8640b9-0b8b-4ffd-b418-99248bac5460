<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper">

    <!-- 根据Store Code查询门店信息 -->
    <select id="getStoreByCode" resultType="com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper$StoreInfoDto">
        SELECT 
            s.store_id as storeId,
            s.code,
            s.crss_code as crssCode,
            s.name,
            s.country_shortcode as countryShortcode,
            s.created_on as createdOn,
            s.state_code as stateCode
        FROM intl_rms_store s
        WHERE (s.code = #{storeCode} OR s.crss_code = #{storeCode})
        AND s.state_code = 0
        LIMIT 1
    </select>

    <!-- 查询用户门店关系 -->
    <select id="getUserStoreRelation" resultType="com.mi.info.intl.retail.so.app.provider.upload.dto.StoreUserRelationDto">
        SELECT 
            u.rms_userid as rmsUserid,
            u.mi_id as miId,
            u.job_id as jobId,
            s.store_id as storeId,
            s.code as storeCode,
            s.name as storeName,
            p.position_id as positionId,
            p.code as positionCode,
            p.name as positionName
        FROM intl_rms_store s 
        JOIN intl_rms_position p ON p.store_id = s.store_id 
        JOIN intl_rms_personnel_position up ON up.position_id = p.position_id
        JOIN intl_rms_user u ON u.rms_userid = up.user_id
        WHERE (s.code = #{storeCode} OR s.crss_code = #{storeCode})
        AND u.mi_id = #{miId}
        AND u.is_disabled = 0
        AND up.state_code = 0
        AND p.state_code = 0
        AND s.state_code = 0
        LIMIT 1
    </select>

    <!-- 查询门店下所有阵地 -->
    <select id="getPositionsByStoreCode" resultType="com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper$PositionInfoDto">
        SELECT 
            p.position_id as positionId,
            p.code,
            p.name,
            p.type_name as typeName,
            p.created_on as createdOn,
            p.state_code as stateCode
        FROM intl_rms_position p 
        JOIN intl_rms_store s ON p.store_id = s.store_id 
        WHERE (s.code = #{storeCode} OR s.crss_code = #{storeCode})
        AND p.state_code = 0
        AND s.state_code = 0
        ORDER BY 
            CASE p.type_name
                WHEN 'SIS' THEN 1
                WHEN 'ES' THEN 2
                WHEN 'DZ' THEN 3
                WHEN 'DC' THEN 4
                WHEN 'POS' THEN 5
                ELSE 6
            END,
            p.created_on ASC
    </select>

</mapper>
