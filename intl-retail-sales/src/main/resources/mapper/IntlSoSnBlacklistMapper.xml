<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoSnBlacklistMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist">
            <id property="id" column="id" />
            <result property="countryCode" column="country_code" />
            <result property="type" column="type" />
            <result property="snImei" column="sn_imei" />
            <result property="snhash" column="snhash" />
            <result property="rmsId" column="rms_id" />
            <result property="dataFrom" column="data_from" />
            <result property="status" column="status" />
            <result property="createdby" column="createdby" />
            <result property="createdon" column="createdon" />
            <result property="modifiedby" column="modifiedby" />
            <result property="modifiedon" column="modifiedon" />
    </resultMap>

    <sql id="Base_Column_List">
        id,country_code,type,sn_imei,snhash,rms_id,
        data_from,status,createdby,createdon,modifiedby,
        modifiedon
    </sql>

    <update id="updateStatusBatchByIds">
        UPDATE intl_so_sn_blacklist
        SET status = #{status},
            modifiedby = #{modifiedby},
        modifiedon = UNIX_TIMESTAMP()
        WHERE id IN
        <foreach item="item" index="index" collection="blacklistIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


</mapper>
