<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlImportTemplateMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlImportTemplate">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="description" column="description" />
        <result property="type" column="type" />
        <result property="fileId" column="file_id" />
        <result property="fileUrl" column="file_url" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
        <result property="createdBy" column="created_by" />
    </resultMap>

    <!-- 根据类型查询导入模板 -->
    <select id="getTemplateByType" resultMap="BaseResultMap">
        SELECT 
            id,
            name,
            description,
            type,
            file_id,
            file_url,
            created_at,
            updated_at,
            created_by
        FROM intl_import_template
        WHERE type = #{type}
        LIMIT 1
    </select>

</mapper> 