<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsSecondarychannelMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsSecondarychannel">
        <result property="id" column="id"/>
        <result property="channelId" column="channel_id"/>
        <result property="channelName" column="channel_name"/>
        <result property="channelBuychannelid" column="channel_buychannelid"/>
        <result property="channelEnglishname" column="channel_englishname"/>
        <result property="channelSuperiorchannel" column="channel_superiorchannel"/>
        <result property="channelCountryids" column="channel_countryids"/>
        <result property="channelSapchannelid" column="channel_sapchannelid"/>
        <result property="channelBusinessarea" column="channel_businessarea"/>
        <result property="channelOnlineandoffline" column="channel_onlineandoffline"/>
        <result property="status" column="status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdOn" column="created_on"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedOn" column="modified_on"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, channel_id, channel_name, channel_buychannelid, channel_englishname, channel_superiorchannel, channel_countryids, channel_sapchannelid,
          channel_businessarea, channel_onlineandoffline, status, created_by, created_on, modified_by, modified_on, created_at, updated_at
    </sql>

</mapper>

