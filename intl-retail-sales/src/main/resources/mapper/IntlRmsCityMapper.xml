<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsCityMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsCity">
        <result property="id" column="id"/>
        <result property="countryId" column="country_id"/>
        <result property="countryName" column="country_name"/>
        <result property="countryCode" column="country_code"/>
        <result property="countryShortcode" column="country_shortcode"/>
        <result property="provinceId" column="province_id"/>
        <result property="provinceName" column="province_name"/>
        <result property="provinceCode" column="province_code"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="status" column="status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdOn" column="created_on"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedOn" column="modified_on"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, country_id, country_name, country_code, country_shortcode, province_id, province_name, province_code, city_id,
          city_name, city_code, status, created_by, created_on, modified_by, modified_on, created_at, updated_at
    </sql>

</mapper>

