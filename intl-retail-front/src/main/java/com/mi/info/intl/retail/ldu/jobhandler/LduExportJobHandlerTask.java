package com.mi.info.intl.retail.ldu.jobhandler;


import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.ldu.dto.JobSuccessDto;
import com.xiaomi.nr.job.core.context.JobHelper;
import com.xiaomi.nr.job.core.handler.annotation.NrJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LduExportJobHandlerTask {

    @NrJob("lduExportHandleXxlJob")
    public void lduExportHandleXxlJob() {
        try {
            String jobParam = JobHelper.getJobParam();
            log.info("lduExportHandleXxlJob,jobParam:{}", jobParam);
            if (CharSequenceUtil.isNotBlank(jobParam)) {
                JobSuccessDto jobSuccessDto = JSONUtil.toBean(jobParam, JobSuccessDto.class);
                boolean handleSuccess = JobHelper.handleSuccess(JSONUtil.toJsonStr(jobSuccessDto));
                log.info("lduExportHandleXxlJob,handleSuccess:{}", handleSuccess);
            }
        } catch (Exception e) {
            log.error("adConfigExportJob 发生异常", e);
        }
    }
}
