package com.mi.info.intl.retail.org.domain.repository;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.InspectionSummaryDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.UnRemindedTaskDTO;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.dto.InspectionRecordDTO;

import java.util.List;

/**
 * 阵地巡检信息仓储接口
 */
public interface InspectionRecordRepository {
    
    /**
     * 保存阵地巡检记录
     *
     * @param inspectionRecordDomain 阵地巡检领域对象
     * @return 是否保存成功
     */
    boolean save(InspectionRecordDomain inspectionRecordDomain);
    
    /**
     * 通过ID获取阵地巡检记录
     *
     * @param id 巡检记录ID
     * @return 阵地巡检领域对象
     */
    InspectionRecordDomain getById(Long id);
    
    /**
     * 通过阵地代码获取巡检记录列表
     *
     * @param businessCode 阵地代码
     * @return 阵地巡检领域对象列表
     */
    List<InspectionRecordDomain> getByBusinessCode(String businessCode);

    List<InspectionRecordDomain> getByBusinessCodeList(List<String> businessCodes);
    
    /**
     * 通过阵地代码获取未审批完成的巡检记录列表
     *
     * @param businessCode 阵地代码
     * @return 未审批完成的阵地巡检领域对象列表
     */
    List<InspectionRecordDomain> getPendingApprovalByBusinessCode(String businessCode);

    /**
     * 更新阵地巡检记录
     *
     * @param inspectionRecordDomain 阵地巡检领域对象
     * @return 是否更新成功
     */
    boolean update(InspectionRecordDomain inspectionRecordDomain);
    
    /**
     * 通过规则编码和阵地代码获取巡检记录
     *
     * @param ruleCode 规则编码
     * @param businessCode 阵地代码
     * @return 阵地巡检领域对象
     */
    InspectionRecordDomain getByRuleCodeAndBusinessCode(String ruleCode, String businessCode);


    /**
     * 查询巡检记录汇总数据
     * 
     * @param ownerMiid 所有者MIID
     * @return 巡检记录汇总数据
     */
    InspectionSummaryDTO getInspectionSummary(PositionInspectionRequest request);


    boolean existsUnCompletedTaskByOwner(String inspectionOwner);

    /**
     * 查询未完成任务状态且规则状态为激活的巡检记录
     *
     * @return 符合条件的阵地巡检领域对象列表
     */
    List<UnRemindedTaskDTO> findActiveTasksWithActiveRules();

    /**
     * 通过国家列表查询未下发的巡检记录
     *
     * @param countries 国家列表
     * @return 未下发的阵地巡检领域对象列表
     */
    List<InspectionRecordDTO> getPendingInspectionsByCountries(List<String> countries);

    /**
     * 通过阵地代码和施工行为代码获取巡检记录
     *
     * @param businessCode 阵地代码
     * @param constructionActionCode 施工行为代码
     * @return 阵地巡检领域对象
     */
    InspectionRecordDomain getByBusinessCodeAndConstructionActionCode(String businessCode, String constructionActionCode);

}