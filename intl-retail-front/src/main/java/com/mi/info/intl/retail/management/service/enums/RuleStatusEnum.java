package com.mi.info.intl.retail.management.service.enums;

public enum RuleStatusEnum {

    DRAFT(1, "DRAFT"),
    IN_APPROVAL(2, "IN_APPROVAL"),
    IN_EFFECT(3, "IN_EFFECT");
    private final Integer code;
    private final String value;

    RuleStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
