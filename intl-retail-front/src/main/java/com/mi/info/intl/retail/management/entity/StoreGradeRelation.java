package com.mi.info.intl.retail.management.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.info.intl.retail.model.BaseCountryEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("store_grade_relation")
public class StoreGradeRelation extends BaseCountryEntity {
    
    @TableField("store_grade_rule_id")
    private Integer storeGradeRuleId;

    @TableField("store_code")
    private String storeCode;

    @TableField("store_grade")
    private String storeGrade;
}
