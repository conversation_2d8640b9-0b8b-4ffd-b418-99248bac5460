package com.mi.info.intl.retail.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * upc接口查询的商品信息，兼容3.1、3.2接口
 *
 * <AUTHOR>
 * @date 2025/7/8 15:37
 */
@Data
public class UpcGoodsInfoDto implements Serializable {

    private static final long serialVersionUID = 202507081537L;

    /**
     * 商品编码
     */
    @JsonProperty("sku")
    private String sku;

    /**
     * spu编码
     */
    @JsonProperty("spu")
    private String spu;

    /**
     * spu名称
     */
    @JsonProperty("spuNameCn")
    private String spuNameCn;

    /**
     * spu英文名称
     */
    @JsonProperty("spuNameEn")
    private String spuNameEn;

    /**
     * 供应链商品名称
     */
    @JsonProperty("scmName")
    private String scmName;

    /**
     * 物料组编码
     */
    @JsonProperty("scmCode")
    private String scmCode;

    /**
     * 商品id
     */
    @JsonProperty("goodsId")
    private String goodsId;

    /**
     * 商品重量
     */
    @JsonProperty("weight")
    private BigDecimal weight;

    /**
     * 体积
     */
    @JsonProperty("volume")
    private String volume;

    /**
     * 是否含电池
     */
    @JsonProperty("isBattery")
    private Integer isBattery;

    /**
     * 是否串码管理
     */
    @JsonProperty("isSn")
    private Integer isSn;

    /**
     * 是否单独发货
     */
    @JsonProperty("isAloneDelivery")
    private Integer isAloneDelivery;

    /**
     * 是否磁性
     */
    @JsonProperty("isMagnet")
    private Integer isMagnet;

    /**
     * 是否液体
     */
    @JsonProperty("isLiquid")
    private Integer isLiquid;

    /**
     * 承销商
     */
    @JsonProperty("underwriter")
    private String underwriter;

    /**
     * 承销商名称
     */
    @JsonProperty("underwriterName")
    private String underwriterName;

    /**
     * 分类id
     */
    @JsonProperty("categoryId")
    private Integer categoryId;

    /**
     * 分类名称(英文)
     */
    @JsonProperty("categoryNameEn")
    private String categoryNameEn;

    /**
     * 分类名称 (中文)
     */
    @JsonProperty("categoryNameCn")
    private String categoryNameCn;

    /**
     * 销售区域信息
     */
    @JsonProperty("skuBaseAreaList")
    private List<SaleAreaInfoDTO> saleAreaInfo;

    /**
     * 商品码
     */
    @JsonProperty("goodsCode")
    private String goodsCode;

    /**
     * 销售版本
     */
    @JsonProperty("salesVersion")
    private String salesVersion;

    /**
     * 销售版本名称
     */
    @JsonProperty("salesVersionName")
    private String salesVersionName;

    /**
     * 产品线ID
     */
    @JsonProperty("productLine")
    private String productLine;

    /**
     * 项目编码
     */
    @JsonProperty("productCode")
    private String productCode;

    /**
     * 项目名称
     */
    @JsonProperty("productName")
    private String productName;

    /**
     * 扩展属性信息
     */
    @JsonProperty("extInfo")
    private List<ExtInfoDTO> extInfo;

    /**
     * 项目id
     */
    @JsonProperty("projectId")
    private String projectId;

    /**
     * 项目名称
     */
    @JsonProperty("projectName")
    private String projectCode;

    /**
     * 项目名称(中文)
     */
    @JsonProperty("projectNameCn")
    private String projectNameCn;

    /**
     * 项目名称(英文)
     */
    @JsonProperty("projectNameEn")
    private String projectNameEn;

    /**
     * 69码
     */
    @JsonProperty("code69")
    private String code69;

    /**
     * 是否69码管理
     */
    @JsonProperty("is69")
    private Integer is69;

    /**
     * 69码主体
     */
    @JsonProperty("body69")
    private String body69;

    /**
     * 是否upc
     */
    @JsonProperty("isUpc")
    private Integer isUpc;

    /**
     * upc编码
     */
    @JsonProperty("codeUpc")
    private String codeUpc;

    /**
     * 主要部件
     */
    @JsonProperty("mainPart")
    private String mainPart;

    /**
     * 商品状态  0-不可用1-待处理2-可用
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    private String createTime;

    /**
     * 更新时间
     */
    @JsonProperty("updateTime")
    private String updateTime;

    /**
     * 操作员ID
     */
    @JsonProperty("operatorId")
    private Integer operatorId;

    /**
     * 操作员姓名
     */
    @JsonProperty("operator")
    private String operator;

    /**
     * 企业ID，1000代表小米
     */
    @JsonProperty("enterprise")
    private Integer enterprise;

    /**
     * 销售版本键
     */
    @JsonProperty("salesVersionKey")
    private String salesVersionKey;

    /**
     * 上市日期
     */
    @JsonProperty("marketTime")
    private String marketTime;


    @Data
    public static class SaleAreaInfoDTO implements Serializable {
        private static final long serialVersionUID = 202507081537L;

        /**
         * 货币
         */
        @JsonProperty("currency")
        private String currency;

        /**
         * 销售区域名称
         */
        @JsonProperty("name")
        private String name;

        /**
         * 销售区域简短名称
         */
        @JsonProperty("shortName")
        private String shortName;

        /**
         * 价格
         */
        @JsonProperty("price")
        private BigDecimal price;

        /**
         * 标准价格
         */
        @JsonProperty("stnPrice")
        private BigDecimal stnPrice;

        @JsonProperty("saleAreaCode")
        private String countryCode2;

        @JsonProperty("countryCode3")
        private String countryCode3;

    }

    @Data
    public static class ExtInfoDTO implements Serializable {

        private static final long serialVersionUID = 202507081537L;

        /**
         * id
         */
        @JsonProperty("id")
        private Integer id;

        /**
         * 扩展属性代码
         */
        @JsonProperty("extCode")
        private String extCode;

        /**
         * 扩展属性编码
         */
        @JsonProperty("code")
        private String code;

        /**
         * 扩展属性类型
         */
        @JsonProperty("extType")
        private String extType;

        /**
         * 名称
         */
        @JsonProperty("name")
        private String name;

        /**
         * 值
         */
        @JsonProperty("value")
        private transient Object value;

    }
}

