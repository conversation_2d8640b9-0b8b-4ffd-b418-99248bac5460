package com.mi.info.intl.retail.management.mapper;

import com.mi.info.intl.retail.model.RetailerChannelGradeCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StoreGradeMapper {
    
    /**
     * 查询零售商渠道类型和等级计算标志综合统计
     * @param countryCode 国家代码
     * @return 零售商渠道类型和等级计算标志综合统计列表
     */
    List<RetailerChannelGradeCount> selectRetailerChannelGradeCount(String countryCode);
    
    /**
     * 查询门店等级综合统计
     * @param countryCode 国家代码
     * @param retailerChannelType 零售商渠道类型
     * @param retailerChannelCode 零售商渠道代码
     * @return 门店等级综合统计列表
     */
    List<StoreGradeCompleteCount> selectStoreGradeCompleteCount(
            @Param("countryCode") String countryCode,
            @Param("retailerChannelType") String retailerChannelType,
            @Param("retailerChannelCode") String retailerChannelCode);
}
