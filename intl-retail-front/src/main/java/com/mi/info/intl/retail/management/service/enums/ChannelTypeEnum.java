package com.mi.info.intl.retail.management.service.enums;

public enum ChannelTypeEnum {
    IR(10, "IR"),
    NKA(20, "NKA"),
    DKA(30, "DKA"),
    <PERSON><PERSON>(40, "OPR"),
    <PERSON><PERSON>(50, "CES"),
    XIAOMI_STORE(60, "Xiaomi Store");

    private final int key;
    private final String value;

    ChannelTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    // 根据 key 获取对应的枚举值
    public static ChannelTypeEnum fromKey(int key) {
        for (ChannelTypeEnum channelType : ChannelTypeEnum.values()) {
            if (channelType.getKey() == key) {
                return channelType;
            }
        }
        throw new IllegalArgumentException("Invalid key: " + key);
    }
    
    public static ChannelTypeEnum fromValue(String value) {
        for (ChannelTypeEnum channelType : ChannelTypeEnum.values()) {
            if (channelType.getValue().equals(value)) {
                return channelType;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }
}
