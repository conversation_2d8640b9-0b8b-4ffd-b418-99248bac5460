package com.mi.info.intl.retail.ldu.infra.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RMS同步的商品信息表
 *
 * <AUTHOR>
 * @date 2025/7/11 11:14
 */
@Data
@TableName("intl_rms_product")
public class IntlRmsProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建人ID，映射到 systemuser
     */
    @TableField("created_by")
    private Long createdBy;

    /**
     * 创建人名称
     */
    @TableField("created_by_name")
    private String createdByName;

    /**
     * 创建时间
     */
    @TableField("created_on")
    private Date createdOn;

    /**
     * 修改人，映射到 systemuser
     */
    @TableField("modified_by")
    private Long modifiedBy;

    /**
     * 修改人名称
     */
    @TableField("modified_by_name")
    private String modifiedByName;

    /**
     * 修改时间
     */
    @TableField("modified_on")
    private Date modifiedOn;

    /**
     * 品牌id
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 品牌id名称
     */
    @TableField("brand_id_name")
    private String brandIdName;

    /**
     * 类别中文
     */
    @TableField("category_cn")
    private String categoryCn;

    /**
     * 类别英文
     */
    @TableField("category_en")
    private String categoryEn;

    /**
     * 类别ID，映射到 categorydata
     */
    @TableField("category_data_id")
    private Long categoryDataId;

    /**
     * 类型ID名称
     */
    @TableField("category_data_id_name")
    private String categoryDataIdName;

    /**
     * 按型号分类ID，映射到 categorywithmodel
     */
    @TableField("category_with_model_id")
    private Long categoryWithModelId;

    /**
     * 按型号分类ID名称
     */
    @TableField("category_with_model_id_name")
    private String categoryWithModelIdName;

    /**
     * 中文名称
     */
    @TableField("chinese_name")
    private String chineseName;

    /**
     * 69码
     */
    @TableField("code69")
    private String code69;

    /**
     * 中文颜色
     */
    @TableField("color")
    private String color;

    /**
     * 数字系列ID，映射到 digitalserie
     */
    @TableField("digitalserie_id")
    private Long digitalserieId;

    /**
     * 数字系列ID名称
     */
    @TableField("digitalserie_id_name")
    private String digitalserieIdName;

    /**
     * 版本
     */
    @TableField("edition")
    private String edition;

    /**
     * 英文颜色
     */
    @TableField("english_color")
    private String englishColor;

    /**
     * 英文产品名称
     */
    @TableField("english_name")
    private String englishName;

    /**
     * 是否高端产品，True, False
     */
    @TableField("highend_products")
    private Integer highendProducts;

    /**
     * 是否69码, 0: Not Maintained 1: No 2: Yes
     */
    @TableField("is_69")
    private Integer is69;

    /**
     * 是否上市 [0, 2]
     */
    @TableField("is_launch")
    private Integer isLaunch;

    /**
     * 是否串码管理, 0: Not Maintained 1: No 2: Yes
     */
    @TableField("is_sn")
    private Integer isSn;

    /**
     * IS UPC，0: Not Maintained 1: No 2: Yes
     */
    @TableField("is_upc")
    private Integer isUpc;

    /**
     * 二级项目编码
     */
    @TableField("item_code")
    private String itemCode;

    /**
     * 型号级别，10: Entry-Level 20: Mid-End 30: High-End
     */
    @TableField("model_level")
    private Integer modelLevel;

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 产品ID，对应goodsId
     */
    @TableField("goods_id")
    private String goodsId;

    /**
     * 产品品牌
     */
    @TableField("product_brand")
    private String productBrand;

    /**
     * UPC产品类型
     */
    @TableField("product_category")
    private String productCategory;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 产品线
     */
    @TableField("product_line")
    private String productLine;

    /**
     * 产品线EN
     */
    @TableField("product_line_en")
    private String productLineEn;

    /**
     * 产品SKU
     */
    @TableField("product_number")
    private String productNumber;

    /**
     * 产品类型，映射到 producttype
     */
    @TableField("product_type_id")
    private Long productTypeId;

    /**
     * 产品类型id名称
     */
    @TableField("product_type_id_name")
    private String productTypeIdName;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private String projectId;

    /**
     * 项目中文
     */
    @TableField("project_name_cn")
    private String projectNameCn;

    /**
     * 项目英文
     */
    @TableField("project_name_en")
    private String projectNameEn;

    /**
     * 项目编码
     */
    @TableField("project_code")
    private String projectCode;

    /**
     * RAM（G）
     */
    @TableField("ram")
    private String ram;

    /**
     * ROM（G）
     */
    @TableField("rom")
    private String rom;

    /**
     * 英文名称(RMS系统)
     */
    @TableField("rms_english_name")
    private String rmsEnglishName;

    /**
     * 销售版本编码
     */
    @TableField("sales_version_code")
    private String salesVersionCode;

    /**
     * 销售版本ID
     */
    @TableField("sales_version_id")
    private String salesVersionId;

    /**
     * 销售版本名称
     */
    @TableField("sales_version_name")
    private String salesVersionName;

    /**
     * SCM编码
     */
    @TableField("scm_code")
    private String scmCode;

    /**
     * SCM名称
     */
    @TableField("scm_name")
    private String scmName;

    /**
     * 二级类别ID
     */
    @TableField("second_category")
    private String secondCategory;

    /**
     * 简称
     */
    @TableField("shortname")
    private String shortname;

    /**
     * SKU
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * SKU名称
     */
    @TableField("sku_name")
    private String skuName;

    /**
     * SPU
     */
    @TableField("spu_id")
    private String spuId;

    /**
     * SPU英文
     */
    @TableField("spu_name_en")
    private String spuNameEn;

    /**
     * SPU名称
     */
    @TableField("spu_name")
    private String spuName;

    /**
     * 产品状态（枚举）0: Unavailable 1: Pending; 2: Launched;
     * 3: Newly Created; 4: In Approval; 5: Rejected; 6: Delisting; 100: Invalid
     */
    @TableField("product_status")
    private Integer status;

    /**
     * 产品线UPC
     * 0: Others; 1: Smartphones; 2: TV; 3: Ecological chain;
     * 4: Laptop; 5: Hardware; 6: You pin; 7: Three parties of ecological chain;
     * 8: Product merchant; 9: Internet 5; 10: Appliance; 12: Wearables
     */
    @TableField("upc_product_line")
    private Integer upcProductLine;

    /**
     * 记录创建日期
     */
    @TableField("overridden_create_don")
    private Date overriddenCreateDon;

    /**
     * 所有者，systemuser team
     */
    @TableField("owner_id")
    private Long ownerId;

    /**
     * 所有者名称
     */
    @TableField("owner_id_name")
    private String ownerIdName;

    /**
     * 所有者类型
     */
    @TableField("owner_id_type")
    private String ownerIdType;

    /**
     * 所有者有米名称
     */
    @TableField("owner_id_yomi_name")
    private String ownerIdYomiName;

    /**
     * 所属业务单元ID，映射到 businessunit
     */
    @TableField("owning_business_unit")
    private Long owningBusinessUnit;

    /**
     * 所属业务单元名称
     */
    @TableField("owning_business_unit_name")
    private String owningBusinessUnitName;

    /**
     * 所属团队，映射到 team
     */
    @TableField("owning_team")
    private Long owningTeam;

    /**
     * 所属用户，映射到 systemuser
     */
    @TableField("owning_user")
    private Long owningUser;

    /**
     * 状态, 0: Active 1: Inactive
     */
    @TableField("state_code")
    private Integer stateCode;

    /**
     * 状态原因，1: Active 2: Inactive
     */
    @TableField("status_code")
    private Integer statusCode;

    /**
     * 最后一次同步时间
     */
    @TableField("last_sync_time")
    private Long lastSyncTime;
}
