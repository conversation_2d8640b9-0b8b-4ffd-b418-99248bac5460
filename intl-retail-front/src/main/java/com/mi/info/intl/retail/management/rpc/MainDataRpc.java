package com.mi.info.intl.retail.management.rpc;

import cn.hutool.json.JSONUtil;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.maindataapi.api.StoreProvider;
import com.xiaomi.cnzone.maindataapi.model.OrgResponse;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgParamEntity;
import com.xiaomi.cnzone.maindataapi.model.req.store.EditPositionInfoRequest;
import com.xiaomi.cnzone.maindataapi.model.req.store.StoreListByOrgIdsRequest;
import com.xiaomi.cnzone.nr.common.utils.GsonUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import com.xiaomi.cnzone.storems.common.exception.ErrorCodeEnums;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

import static org.reflections.Reflections.log;

@Service
public class MainDataRpc {

    @Reference(group = "${maindata.dubbo.group:}", version = "1.0", check = false, interfaceClass = StoreProvider.class)
    private StoreProvider storeProvider;

    Integer success = 0;

    public OrgResponse selectStoreByOrgIds(List<String> orgIds) {
        StoreListByOrgIdsRequest request = new StoreListByOrgIdsRequest();
        request.setOrgId(orgIds);
        Result<OrgResponse> result = storeProvider.selectStoreByOrgIds(request);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new BusinessException(result.getMessage());
    }

    public Boolean pushEditStoreBeta(OrgParamEntity orgParamEntity) {
        log.info("StoreCarMasterV2GatewayImpl-pushEditStoreBeta 推送主数据 param{}", JacksonUtil.toStr(orgParamEntity));
        long start = System.currentTimeMillis();
        //MainDataCommonUtil.filterEditBeta(orgParamEntity);
        Result<String> result = storeProvider.editStoreBeta(orgParamEntity);
        log.info("请求主数据,消耗时间={}, 请求参数={},返回结果={}", System.currentTimeMillis() - start, GsonUtil.toJson(orgParamEntity),
                GsonUtil.toJson(result));
        // 处理返回数据
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "主数据推送失败:" + result.getMessage());
        }
        return Boolean.TRUE;
    }

}

