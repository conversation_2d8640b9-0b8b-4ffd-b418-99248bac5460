package com.mi.info.intl.retail.management.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("common_change_log")
public class CommonChangeLog {

    @TableId(value = "id")
    private Integer id;

    @TableField("business_key")
    private String businessKey;

    @TableField("business_id")
    private String businessId;

    @TableField("store_grade")
    private String storeGrade;

    @TableField("channel_type")
    private String channelType;

    @TableField("complete_status")
    private Integer completeStatus; // 使用Boolean类型来表示tinyInt（0或1）

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("change_type")
    private String changeType;

    @TableField("change_reason")
    private String changeReason;

    @TableField("store_grade_rule_id")
    private Integer storeGradeRuleId;
}
