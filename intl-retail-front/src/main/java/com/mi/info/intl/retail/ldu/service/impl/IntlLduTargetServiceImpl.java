package com.mi.info.intl.retail.ldu.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.ObjectId;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.BathConReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ImportIntlLduTargetReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerReqDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerResponseDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.ldu.config.LduConfig;
import com.mi.info.intl.retail.ldu.dto.JobSuccessDto;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduTargetEnExcel;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduTargetExcel;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduTargetExcelDto;
import com.mi.info.intl.retail.ldu.enums.ExportExcelEnum;
import com.mi.info.intl.retail.ldu.enums.LanguageEnum;
import com.mi.info.intl.retail.ldu.enums.RmsRetailerEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduTargetMapper;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.service.IntlLduSnImeiService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduTargetService;
import com.mi.info.intl.retail.ldu.util.ConstantMessageTemplate;
import com.mi.info.intl.retail.ldu.util.NrJobTaskUtils;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.retailer.mapper.read.IntlRmsRetailerReadMapper;
import com.xiaomi.keycenter.okhttp3.OkHttpClient;
import com.xiaomi.keycenter.okhttp3.Request;
import com.xiaomi.keycenter.okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = IntlLduTargetService.class)
public class IntlLduTargetServiceImpl extends ServiceImpl<IntlLduTargetMapper, IntlLduTarget> implements IntlLduTargetService {

    @Resource
    private FdsService fdsService;

    @Resource
    private IntlLduTargetMapper intlLduTargetMapper;

    @Resource
    private IntlLduSnImeiService intlLduSnImeiService;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;


    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;


    @Resource
    private NrJobTaskUtils nrJobTaskUtils;

    @Resource
    private IProductQueryService iProductQueryService;

    @Resource
    private LduConfig lduConfig;

    @Resource
    private IntlRmsRetailerReadMapper intlRmsRetailerReadMapper;

    private static final int DEFAULT_LIMIT = 50;

    @Override
    public IPage<IntlLduTargetDto> pageListTarget(IntlLduTargetReq query) {
        List<String> countrysCode = new ArrayList<>();
        String areaId = RpcContext.getContext().getAttachment("$area_id");
        if (!CollectionUtils.isEmpty(query.getCountryCode())) {
            countrysCode = query.getCountryCode();
        }
        if (CollectionUtils.isEmpty(countrysCode) && !"GLOBAL".equals(areaId) && areaId != null) {
            countrysCode.add(areaId);
        }
        Page<IntlLduTarget> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<IntlLduTarget> queryWrapper = Wrappers.<IntlLduTarget>lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getGoodsName()), IntlLduTarget::getGoodsName, query.getGoodsName())
                .eq(StringUtils.isNotEmpty(query.getGoodsId()), IntlLduTarget::getGoodsId, query.getGoodsId())
                .eq(StringUtils.isNotEmpty(query.getRetailerCode()), IntlLduTarget::getRetailerCode, query.getRetailerCode())
                .eq(StringUtils.isNotEmpty(query.getProductLine()), IntlLduTarget::getProductLine, query.getProductLine())
                .eq(StringUtils.isNotEmpty(query.getProjectCode()), IntlLduTarget::getProjectCode, query.getProjectCode())
                .in(CollectionUtils.isNotEmpty(countrysCode), IntlLduTarget::getCountryCode, countrysCode)
                .orderByDesc(IntlLduTarget::getTargetCreateDate);

        Page<IntlLduTarget> taskConfPage = this.page(page, queryWrapper);
        Page<IntlLduTargetDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        if (CollectionUtils.isEmpty(taskConfPage.getRecords())) {
            pageDTO.setCurrent(taskConfPage.getCurrent());
            pageDTO.setSize(taskConfPage.getSize());
            return pageDTO;
        }
        List<IntlLduTargetDto> list = ComponentLocator.getConverter()
                .convertList(taskConfPage.getRecords(), IntlLduTargetDto.class);

        List<ProductLineDto> productLineDtoList = iProductQueryService.queryProductLines();
        Map<String, ProductLineDto> productLineMap = productLineDtoList.stream().collect(
                Collectors.toMap(ProductLineDto::getProductLine, it -> it));

        List<String> skuList = list.stream().map(IntlLduTargetDto::getGoodsId).distinct().collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoBySkus(skuList);
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoMap = snImeiGoodsInfoDtoList.stream().collect(
                Collectors.toMap(SnImeiGoodsInfoDto::getGoodsId, it -> it));

        list.stream().forEach(item -> {
            if (StringUtils.isNotEmpty(item.getProductLine())) {
                String language = RpcContext.getContext().getAttachment("$language");
                if (LanguageEnum.EN_US.getCode().equals(language)) {
                    if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                        item.setProductLine(productLineMap.get(item.getProductLine()).getEnName());
                    }
                    if (!Objects.isNull(snImeiGoodsInfoMap.get(item.getGoodsId()))) {
                        item.setGoodsName(snImeiGoodsInfoMap.get(item.getGoodsId()).getGoodsNameEn());
                    }
                } else {
                    if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                        item.setProductLine(productLineMap.get(item.getProductLine()).getCnName());
                    }
                }
            }
        });

        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(list.size());
        pageDTO.setRecords(list);
        return pageDTO;
    }

    @Override
    public CommonApiResponse<IPage<IntlLduTargetDto>> pageList(IntlLduTargetReq query) {
        String areaId = RpcContext.getContext().getAttachment("$area_id");
        List<String> countrysCode = new ArrayList<>();
        if (!CollectionUtils.isEmpty(query.getCountryCode())) {
            countrysCode = query.getCountryCode();
        }
        if (CollectionUtils.isEmpty(countrysCode) && !"GLOBAL".equals(areaId) && areaId != null) {
            countrysCode.add(areaId);
        }
        Page<IntlLduTarget> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<IntlLduTarget> queryWrapper = Wrappers.<IntlLduTarget>lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getGoodsName()), IntlLduTarget::getGoodsName, query.getGoodsName())
                .eq(StringUtils.isNotEmpty(query.getGoodsId()), IntlLduTarget::getGoodsId, query.getGoodsId())
                .eq(StringUtils.isNotEmpty(query.getRetailerCode()), IntlLduTarget::getRetailerCode, query.getRetailerCode())
                .eq(StringUtils.isNotEmpty(query.getProductLine()), IntlLduTarget::getProductLine, query.getProductLine())
                .eq(StringUtils.isNotEmpty(query.getProjectCode()), IntlLduTarget::getProjectCode, query.getProjectCode())
                .in(CollectionUtils.isNotEmpty(countrysCode), IntlLduTarget::getCountryCode, countrysCode)
                .orderByDesc(IntlLduTarget::getTargetCreateDate);

        Page<IntlLduTarget> taskConfPage = this.page(page, queryWrapper);
        Page<IntlLduTargetDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        if (CollectionUtils.isEmpty(taskConfPage.getRecords())) {
            pageDTO.setCurrent(taskConfPage.getCurrent());
            pageDTO.setSize(taskConfPage.getSize());
            return new CommonApiResponse<>(pageDTO);
        }
        List<IntlLduTargetDto> list = ComponentLocator.getConverter()
                .convertList(taskConfPage.getRecords(), IntlLduTargetDto.class);

        List<ProductLineDto> productLineDtoList = iProductQueryService.queryProductLines();
        Map<String, ProductLineDto> productLineMap = productLineDtoList.stream().collect(
                Collectors.toMap(ProductLineDto::getProductLine, it -> it));

        List<String> skuList = list.stream().map(IntlLduTargetDto::getGoodsId).distinct().collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoBySkus(skuList);
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoMap = snImeiGoodsInfoDtoList.stream().collect(
                Collectors.toMap(SnImeiGoodsInfoDto::getGoodsId, it -> it));

        list.stream().forEach(item -> {
            if (StringUtils.isNotEmpty(item.getProductLine())) {
                String language = RpcContext.getContext().getAttachment("$language");
                if (LanguageEnum.EN_US.getCode().equals(language)) {
                    if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                        item.setProductLine(productLineMap.get(item.getProductLine()).getEnName());
                    }
                    if (!Objects.isNull(snImeiGoodsInfoMap.get(item.getGoodsId()))) {
                        item.setGoodsName(snImeiGoodsInfoMap.get(item.getGoodsId()).getGoodsNameEn());
                    }
                } else {
                    if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                        item.setProductLine(productLineMap.get(item.getProductLine()).getCnName());
                    }
                }
            }
        });
        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(list.size());
        pageDTO.setRecords(list);
        return new CommonApiResponse<>(pageDTO);
    }

    @Override
    public CommonApiResponse<String> create(IntlLduTargetDto confList) {
        List<IntlLduTargetDto> lduSnList = new ArrayList<>();
        //1、校验唯一性:国家id+零售商id+Product ID验重，如已有目标数据，则不允许再次创建
        IntlLduTarget conf = intlLduTargetMapper.selectByCountryRetailerProduct(
                confList.getCountryCode(), confList.getGoodsId(), confList.getRetailerCode());
        if (!Objects.isNull(conf)) {
            return CommonApiResponse.failure(0001, ConstantMessageTemplate.getTargetDataExistsMessageNoNum());
        }
        SearchRetailerReqDto searchRetailerReqDto = new SearchRetailerReqDto();
        searchRetailerReqDto.setKeyword(confList.getRetailerCode());
        List<SearchRetailerResponseDto> queryRetailerByNameOrCode = intlRmsRetailerReadMapper.queryRetailerByNameOrCode(DEFAULT_LIMIT, searchRetailerReqDto);
        if (!CollectionUtils.isEmpty(queryRetailerByNameOrCode)) {
            List<String> countryListCodes = queryRetailerByNameOrCode.stream().map(SearchRetailerResponseDto::getCountryCode).collect(Collectors.toList());
            if (!countryListCodes.contains(confList.getCountryCode())) {
                return CommonApiResponse.failure(0001, ConstantMessageTemplate.matchRetailerCodeAndCountry());
            }
        }
        lduSnList.add(confList);
        List<IntlLduTarget> intlLduTargets = ComponentLocator.getConverter()
                .convertList(lduSnList, IntlLduTarget.class);
        //填充主数据等其他数据
        addOtherBatchData(intlLduTargets);
        intlLduTargetMapper.batchInsert(intlLduTargets);
        return new CommonApiResponse<>("success");
    }

    @Override
    public CommonApiResponse<String> modify(IntlLduTargetDto confList) {
        IntlLduTarget conf = ComponentLocator.getConverter()
                .convert(confList, IntlLduTarget.class);
        addOtherData(conf);
        intlLduTargetMapper.updateById(conf);
        return new CommonApiResponse<>("success");
    }

    private String getValueById(String id) {
        if (StringUtils.isEmpty(id)) {
            return "";
        } else {
            return id;
        }
    }

    private void addOtherData(IntlLduTarget intlLduTarget) {
        List<SnImeiGoodsInfoDto> goodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoBySkus(
                Arrays.asList(intlLduTarget.getGoodsId()));

        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectByRetailerCode(Arrays.asList(intlLduTarget.getRetailerCode()));
        if (!CollectionUtils.isEmpty(intlRmsRetailers)) {
            intlLduTarget.setChannelType(RmsRetailerEnum.getEnumByCode(intlRmsRetailers.get(0).getRetailerChannelType()));
            intlLduTarget.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
        } else {
            intlLduTarget.setChannelType("");
            intlLduTarget.setRetailerName("");
        }
        List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectAll();
        List<IntlRmsCountryTimezone> rmsCountryTimezone = intlRmsCountryTimezones.stream()
                .filter(intlRmsCountryTimezone -> intlLduTarget.getCountryCode().equals(intlRmsCountryTimezone.getCountryCode()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(rmsCountryTimezone)) {
            intlLduTarget.setCountry(getValueById(rmsCountryTimezone.get(0).getCountryName()));
            intlLduTarget.setRegionCode(getValueById(rmsCountryTimezone.get(0).getAreaCode()));
            intlLduTarget.setRegion(getValueById(rmsCountryTimezone.get(0).getArea()));
        }
        Map<String, SnImeiGoodsInfoDto> goodsInfoMap = goodsInfoDtoList.stream()
                .collect(Collectors.toMap(SnImeiGoodsInfoDto::getSku, item -> item));
        if (!CollectionUtils.isEmpty(goodsInfoDtoList)) {
            intlLduTarget.setProductName(getValueById(goodsInfoMap.get(intlLduTarget.getGoodsId()).getProductName()));
            intlLduTarget.setProjectCode(getValueById(goodsInfoMap.get(intlLduTarget.getGoodsId()).getProjectCode()));
            intlLduTarget.setRamCapacity(getValueById(goodsInfoMap.get(intlLduTarget.getGoodsId()).getRam()));
            intlLduTarget.setRomCapacity(getValueById(goodsInfoMap.get(intlLduTarget.getGoodsId()).getRom()));
            intlLduTarget.setGoodsId(getValueById(goodsInfoMap.get(intlLduTarget.getGoodsId()).getGoodsId()));
            intlLduTarget.setGoodsName(getValueById(goodsInfoMap.get(intlLduTarget.getGoodsId()).getGoodsName()));
            intlLduTarget.setProductLine(getValueById(goodsInfoMap.get(intlLduTarget.getGoodsId()).getProductLine()));
        }

        intlLduTarget.setTargetUpdateDate(System.currentTimeMillis());
        intlLduTarget.setUpdateUserId(RpcContext.getContext().getAttachments().get("$upc_miID"));
        intlLduTarget.setUpdateUserName(RpcContext.getContext().getAttachments().get("$upc_userName"));
    }

    private CommonResponse<String> nrJobForExcelExport(String upload, String upcAccount) {

        JobSuccessDto build = JobSuccessDto.builder().fileUrl(upload).build();

        NrJobGoIn nrJobGoIn = NrJobGoIn.builder()
                .jobKey(ExportExcelEnum.LDU_TARGET_REPORT.getJobKey())
                .owner(upcAccount)
                .taskDesc(ExportExcelEnum.LDU_TARGET_REPORT.getExcelEnName())
                .taskParam(JSONUtil.toJsonStr(build))
                // 链路追踪ID
                .traceId(ObjectId.next())
                .taskName(ExportExcelEnum.LDU_TARGET_REPORT.getExcelName())
                .build();

        return nrJobTaskUtils.triggerNrJobTask(nrJobGoIn);
    }

    private void addOtherBatchData(List<IntlLduTarget> intlLduTargets) {
        //根据填写的SKU信息查询产品主数据对以下字段赋值数,如果项目代码、RMA、ROM无值，则不赋值
        if (CollectionUtils.isNotEmpty(intlLduTargets)) {
            //获取sku列表
            List<String> skuList = intlLduTargets.stream().map(IntlLduTarget::getGoodsId).collect(Collectors.toList());
            List<SnImeiGoodsInfoDto> goodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoByGoodsIds(skuList);
            if (CollectionUtils.isEmpty(goodsInfoDtoList)) {
                log.error("SKU查询主数据为空！");
                return;
            }
            Map<String, SnImeiGoodsInfoDto> goodsInfoMap = goodsInfoDtoList.stream()
                    .collect(Collectors.toMap(SnImeiGoodsInfoDto::getGoodsId, item -> item));
            //区域国家，进行填充
            List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectAll();
            Map<String, IntlRmsCountryTimezone> intlRmsCountryTimezoneMap = intlRmsCountryTimezones.stream()
                    .collect(Collectors.toMap(IntlRmsCountryTimezone::getCountryCode, v -> v));
            List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectByRetailerCode(
                    intlLduTargets.stream().map(IntlLduTarget::getRetailerCode).collect(Collectors.toList()));

            Map<String, IntlRmsRetailer> retailerMap = intlRmsRetailers.stream().distinct()
                    .collect(Collectors.toMap(IntlRmsRetailer::getName, v -> v));


            if (CollectionUtils.isNotEmpty(goodsInfoDtoList)) {
                intlLduTargets.forEach(intlLduTarget -> {
                    if (!Objects.isNull(goodsInfoMap.get(intlLduTarget.getGoodsId()))) {
                        intlLduTarget.setProductName(StringUtils.isEmpty(goodsInfoMap.get(intlLduTarget.getGoodsId()).getProductName()) ?
                                "" : goodsInfoMap.get(intlLduTarget.getGoodsId()).getProductName());
                        intlLduTarget.setProjectCode(StringUtils.isEmpty(goodsInfoMap.get(intlLduTarget.getGoodsId()).getProjectCode()) ?
                                "" : goodsInfoMap.get(intlLduTarget.getGoodsId()).getProjectCode());
                        intlLduTarget.setRamCapacity(StringUtils.isEmpty(goodsInfoMap.get(intlLduTarget.getGoodsId()).getRam()) ? "" :
                                goodsInfoMap.get(intlLduTarget.getGoodsId()).getRam());
                        intlLduTarget.setRomCapacity(StringUtils.isEmpty(goodsInfoMap.get(intlLduTarget.getGoodsId()).getRom()) ? "" :
                                goodsInfoMap.get(intlLduTarget.getGoodsId()).getRom());
                        intlLduTarget.setGoodsId(StringUtils.isEmpty(goodsInfoMap.get(intlLduTarget.getGoodsId()).getGoodsId()) ?
                                "" : goodsInfoMap.get(intlLduTarget.getGoodsId()).getGoodsId());
                        intlLduTarget.setGoodsName(StringUtils.isEmpty(goodsInfoMap.get(intlLduTarget.getGoodsId()).getGoodsName()) ?
                                "" : goodsInfoMap.get(intlLduTarget.getGoodsId()).getGoodsName());
                        intlLduTarget.setProductLine(StringUtils.isEmpty(goodsInfoMap.get(intlLduTarget.getGoodsId()).getProductLine()) ?
                                "" : goodsInfoMap.get(intlLduTarget.getGoodsId()).getProductLine());
                    }
                });
            }
            intlLduTargets.forEach(intlLduTarget -> {
                if (MapUtils.isNotEmpty(retailerMap)) {
                    intlLduTarget.setChannelType(RmsRetailerEnum.getEnumByCode(retailerMap.get(intlLduTarget.getRetailerCode()).getRetailerChannelType()));
                    intlLduTarget.setRetailerName(retailerMap.get(intlLduTarget.getRetailerCode()).getRetailerName());
                } else {
                    intlLduTarget.setChannelType("");
                    intlLduTarget.setRetailerName("");
                }
                IntlRmsCountryTimezone countryTimezone = intlRmsCountryTimezoneMap.get(intlLduTarget.getCountryCode());
                if (!Objects.isNull(countryTimezone)) {
                    intlLduTarget.setCountry(Objects.isNull(countryTimezone) ? "" : countryTimezone.getCountryName());
                    intlLduTarget.setRegionCode(Objects.isNull(countryTimezone) ? "" : countryTimezone.getAreaCode());
                    intlLduTarget.setRegion(Objects.isNull(countryTimezone) ? "" : countryTimezone.getArea());
                }
                intlLduTarget.setTargetCreateDate(System.currentTimeMillis());
                intlLduTarget.setCreateUserId(RpcContext.getContext().getAttachments().get("$upc_miID"));
                intlLduTarget.setCreateUserName(RpcContext.getContext().getAttachments().get("$upc_userName"));
            });
        }
    }


    @Override
    public CommonResponse<String> exportTargetMaintenance(IntlLduTargetReq query) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile("LDU目标维护_", ".xlsx");

            String upcAccount = RpcContext.getContext().getAttachment("$upc_account");
            String language = RpcContext.getContext().getAttachment("$language");
            if (LanguageEnum.EN_US.getCode().equals(language)) {
                excelWriter = EasyExcel.write(tempFile, IntlLduTargetEnExcel.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            } else {
                excelWriter = EasyExcel.write(tempFile, IntlLduTargetExcel.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            }

            WriteSheet writeSheet = EasyExcel.writerSheet("任务列表").build();

            long pageSize = 1000L;
            long currentPage = 1L;
            boolean hasNext = true;

            while (hasNext) {
                query.setPageNum(currentPage);
                query.setPageSize(pageSize);

                IPage<IntlLduTargetDto> searchResult = this.pageListTarget(query);

                List<IntlLduTargetDto> records = searchResult.getRecords();
                List<IntlLduTargetExcelDto> excelDtoList = new ArrayList<>();
                records.stream().forEach(record -> {
                    IntlLduTargetExcelDto excelDto = ComponentLocator.getConverter()
                            .convert(record, IntlLduTargetExcelDto.class);
                    excelDto.setTargetCreateDate(new Date(record.getTargetCreateDate()));
                    if (!Objects.isNull(record.getTargetUpdateDate()) && record.getTargetUpdateDate() != 0) {
                        excelDto.setTargetUpdateDate(new Date(record.getTargetUpdateDate()));
                    }
                    excelDtoList.add(excelDto);
                });
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }
                excelWriter.write(excelDtoList, writeSheet);

                hasNext = currentPage * pageSize < searchResult.getTotal();
                currentPage++;
            }

            if (excelWriter != null) {
                excelWriter.finish();
            }
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);

            FdsUploadResult upload = fdsService.upload("LDU目标维护" + timestamp + ".xlsx", tempFile, true);

            return nrJobForExcelExport(upload.getUrl(), upcAccount);
        } catch (Exception e) {
            log.error("LDU目标维护列表导出异常: {}", e);
            return CommonResponse.failure(0001, "导出失败");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    @Override
    public CommonApiResponse<List<String>> importTargetMaintenance(ImportIntlLduTargetReq query) {
        List<String> errorList = new ArrayList<>();

        try {
            // 创建 OkHttpClient 实例
            OkHttpClient client = new OkHttpClient();
            // 创建 HTTP 请求
            Request request = new Request.Builder().url(query.getUrl()).build();
            Response response = client.newCall(request).execute();
            List<IntlLduTarget> intlLduTargetList = new ArrayList<>();
            if (response.isSuccessful()) {
                // 获取响应的输入流
                InputStream inputStream = Objects.requireNonNull(response.body()).byteStream();
                Workbook workbook = new XSSFWorkbook(inputStream);
                Sheet sheet = workbook.getSheetAt(0);
                int num = 1;
                for (Row row : sheet) {
                    if (row.getRowNum() == 0) {
                        if (!"国家代码".equals(getCellValue(row.getCell(0))) && !"零售商编码".equals(getCellValue(row.getCell(1)))) {
                            errorList.add(ConstantMessageTemplate.getTemplateError());
                            return new CommonApiResponse<>(errorList);
                        }
                        continue;
                    }
                    String countryCode = this.getCellValue(row.getCell(0));
                    String retailerCode = this.getCellValue(row.getCell(1));
                    String sku = this.getCellValue(row.getCell(2));
                    if (sku.contains(".")) {
                        sku = sku.split("\\.")[0];
                    }
                    String targetCoverages = this.getCellValue(row.getCell(3));
                    String storeTargetCoverages = this.getCellValue(row.getCell(4));
                    // 校验数据
                    covertTargetMaintenance(intlLduTargetList, num, countryCode,
                            retailerCode, targetCoverages, storeTargetCoverages, sku, errorList);
                    num++;
                }
            }
            // 批量停用
            if (CollUtil.isNotEmpty(intlLduTargetList)) {
                //填充其他参数
                addOtherBatchData(intlLduTargetList);
                intlLduTargetMapper.batchInsert(intlLduTargetList);
            }
        } catch (Exception e) {
            log.error("批量新增目标维护异常: {}", e);
        }
        return new CommonApiResponse<>(errorList);
    }

    private boolean checkRetailerCodeAndCountry(String retailerCode, String countryCode) {
        SearchRetailerReqDto searchRetailerReqDto = new SearchRetailerReqDto();
        searchRetailerReqDto.setKeyword(retailerCode);
        searchRetailerReqDto.setCountryCode(Arrays.asList(countryCode));
        searchRetailerReqDto.setId(retailerCode);
        List<SearchRetailerResponseDto> queryRetailerByNameOrCode = intlRmsRetailerReadMapper.queryRetailerByNameOrCode(DEFAULT_LIMIT, searchRetailerReqDto);
        boolean flag = true;
        if (!CollectionUtils.isEmpty(queryRetailerByNameOrCode)) {
            flag = false;
        }
        return flag;
    }


    private void covertTargetMaintenance(List<IntlLduTarget> intlLduTargetList, int num, String countryCode, String retailerCode,
                                         String targetCoverages, String storeTargetCoverages, String sku, List<String> errorList) {
        StringBuilder errorMsg = new StringBuilder();
        IntlLduTarget intlLduTarget = new IntlLduTarget();
        //根据这些信息查询零售商信息，零售商名称等其他数据
        intlLduTarget.setRetailerCode(retailerCode);
        intlLduTarget.setTargetCoveredStores(StringUtils.isEmpty(targetCoverages) ? 0
                : (int) Double.parseDouble(targetCoverages));
        intlLduTarget.setTargetSampleOut(StringUtils.isEmpty(storeTargetCoverages) ? 0
                : (int) Double.parseDouble(storeTargetCoverages));
        intlLduTarget.setCountryCode(countryCode);
        intlLduTarget.setGoodsId(sku);

        //为空校验
        if (checkIsEmpty(num, retailerCode, sku, countryCode, targetCoverages, storeTargetCoverages, errorMsg)) {
            boolean flag = checkRetailerCodeAndCountry(retailerCode, countryCode);
            if (flag) {
                errorMsg.append(ConstantMessageTemplate.matchRetailerCodeAndCountry(num));
            }
            //校验数据：
            checkError(num, intlLduTarget, errorMsg);
        }
        if (StringUtils.isEmpty(errorMsg.toString())) {
            intlLduTargetList.add(intlLduTarget);
        } else {
            errorList.add(errorMsg.toString());
        }

    }

    private boolean checkIsEmpty(int num, String retailerCode, String sku, String countryCode,
                                 String targetCoverages, String storeTargetCoverages, StringBuilder errorMsg) {
        boolean flag = true;
        if (StringUtils.isEmpty(sku)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.productIdNotExistsMessage(num));
        } else if (StringUtils.isEmpty(targetCoverages)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.targetNumsNotExistsMessage(num));
        } else if (StringUtils.isEmpty(storeTargetCoverages)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.targetProductNumsNotExistsMessage(num));
        } else if (StringUtils.isEmpty(retailerCode)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.getRetailerCodeEmptyMessage(num));
        } else if (StringUtils.isEmpty(countryCode)) {
            flag = false;
            errorMsg.append(ConstantMessageTemplate.getXiaomiCountryCodeEmptyMessage(num));
        }
        return flag;
    }


    private void checkError(int num, IntlLduTarget intlLduTarget, StringBuilder errorMsg) {
        if (StringUtils.isEmpty(intlLduTarget.getCountryCode())) {
            errorMsg.append(ConstantMessageTemplate.getXiaomiCountryCodeEmptyMessage(num));
        } else if (StringUtils.isEmpty(intlLduTarget.getRetailerCode())) {
            errorMsg.append(ConstantMessageTemplate.getRetailerCodeEmptyMessage(num));
        } else if (!StringUtils.isEmpty(intlLduTarget.getCountryCode()) && !StringUtils.isEmpty(intlLduTarget.getRetailerCode()) &&
                !StringUtils.isEmpty(intlLduTarget.getGoodsId())) {
            IntlLduTarget conf = intlLduTargetMapper.selectByCountryRetailerProduct(
                    intlLduTarget.getCountryCode(), intlLduTarget.getGoodsId(), intlLduTarget.getRetailerCode());
            if (!Objects.isNull(conf)) {
                errorMsg.append(ConstantMessageTemplate.getTargetDataExistsMessage(num));
            }
        }
    }


    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    @Override
    public CommonApiResponse<String> downLoadLduTemp(BathConReq query) {
        return new CommonApiResponse<>(lduConfig.getTargetUploadUrl());
    }
}
