package com.mi.info.intl.retail.org.domain.repository;

import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.enums.OperationType;

import java.util.List;

/**
 * 前端检查历史Repository接口
 */
public interface InspectionHistoryRepository {
    
    /**
     * 保存检查历史记录
     *
     * @param inspectionHistoryDomain 检查历史领域对象
     * @return 是否保存成功
     */
    boolean save(InspectionHistoryDomain inspectionHistoryDomain);
    
    /**
     * 批量保存检查历史记录
     *
     * @param inspectionHistoryDomainList 检查历史领域对象列表
     * @return 是否保存成功
     */
    boolean saveBatch(List<InspectionHistoryDomain> inspectionHistoryDomainList);
    
    /**
     * 根据检查ID获取历史记录
     *
     * @param inspectionRecordId 检查ID
     * @return 检查历史领域对象列表
     */
    List<InspectionHistoryDomain> getByInspectionRecordId(Long inspectionRecordId);
    
    /**
     * 根据操作类型获取历史记录
     *
     * @param operationType 操作类型
     * @return 检查历史领域对象列表
     */
    List<InspectionHistoryDomain> getByOperationType(OperationType operationType);
}