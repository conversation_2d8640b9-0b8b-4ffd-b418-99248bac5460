package com.mi.info.intl.retail.ldu.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportReq;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Mapper
public interface IntlLduReportLogMapper extends BaseMapper<IntlLduReportLog> {


    void batchInsertReportLogs(@Param("intlLduReportLogs") List<IntlLduReportLog> intlLduReportLogs);

    List<IntlLduReportLog> pageList(@Param("query") IntlLduReportReq query);

    int pageListCount(@Param("query") IntlLduReportReq query);

    List<IntlLduReportLog> historyList(@Param("query") IntlLduReportReq query);

    int historyListCount(@Param("query") IntlLduReportReq query);

    @MapKey("metricName")
    Map<String, HashMap<String, Long>> statisticReportLog(@Param("intlLduReportLog") IntlLduReportLog intlLduReportLog);
}
