package com.mi.info.intl.retail.ldu.service.impl;

import com.google.common.collect.Lists;
import com.mi.info.intl.retail.enums.SerialNumberType;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.*;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.infra.repository.ISnImeiQueryService;
import com.mi.info.intl.retail.ldu.service.IntlLduSnImeiService;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/10 17:47
 */
@Slf4j
@Service
public class IntlLduSnImeiServiceImpl implements IntlLduSnImeiService {

    @Resource
    private ISnImeiQueryService snImeiQueryService;

    @Resource
    private IntlRmsProductService intlRmsProductService;

    @Resource
    private IProductQueryService productQueryService;


    @Override
    public List<SnImeiInfoDto> querySnImeiInfo(SnImeiQueryDto snImeiQueryDto) {
        return queryAllSnImeiInfo(snImeiQueryDto);
    }

    @Override
    public List<SnImeiValidationDto> validateSnImeiInfo(SnImeiQueryDto snImeiQueryDto) {
        List<SnImeiInfoDto> snImeiInfos = queryAllSnImeiInfo(snImeiQueryDto);
        List<String> goodsIds = snImeiInfos.stream().map(it -> String.valueOf(it.getGoodsId())).collect(Collectors.toList());
        List<String> code69List = snImeiQueryDto.getSixNineCodeList();
        // 查询商品信息，
        // 1、通过sn/imei查询到商品goodsId，再查询商品信息
        // 2、通过code69直接查询商品信息
        List<IntlRmsProduct> newProducts = intlRmsProductService.queryByGoodsIdList(goodsIds);
        List<IntlRmsProduct> newProductsByCode69 = intlRmsProductService.queryByCode69List(code69List);

        // 映射商品信息，goodsId -> 商品信息
        Map<String, IntlRmsProduct> goodsMap = newProducts.stream().collect(Collectors.toMap(IntlRmsProduct::getGoodsId, it -> it));
        // 映射商品信息，code69 -> 商品信息
        Map<String, IntlRmsProduct> code69GoodsMap = newProductsByCode69.stream()
                .filter(it -> code69List.contains(it.getCode69())).collect(Collectors.toMap(IntlRmsProduct::getCode69, Function.identity()));

        List<SnImeiValidationDto> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(snImeiQueryDto.getSnList())) {
            Map<String, SnImeiInfoDto> snMap = snImeiInfos.stream()
                    .filter(it -> snImeiQueryDto.getSnList().contains(it.getSn()))
                    .collect(Collectors.toMap(SnImeiInfoDto::getSn, Function.identity()));
            List<SnImeiValidationDto> list1 = snImeiQueryDto.getSnList().stream()
                    .map(sn -> validateSnImei(sn, snMap, goodsMap))
                    .collect(Collectors.toList());
            list.addAll(list1);
        }
        if (CollectionUtils.isNotEmpty(snImeiQueryDto.getImeiList())) {
            Map<String, SnImeiInfoDto> imeiMap = snImeiInfos.stream()
                    .filter(it -> snImeiQueryDto.getImeiList().contains(it.getImei()))
                    .collect(Collectors.toMap(SnImeiInfoDto::getImei, Function.identity()));
            List<SnImeiValidationDto> list2 = snImeiQueryDto.getImeiList().stream()
                    .map(imei -> validateSnImei(imei, imeiMap, goodsMap))
                    .collect(Collectors.toList());
            list.addAll(list2);
        }
        if (CollectionUtils.isNotEmpty(snImeiQueryDto.getSixNineCodeList())) {
            List<SnImeiValidationDto> list3 = snImeiQueryDto.getSixNineCodeList().stream()
                    .map(code69 -> validateCode69(code69, code69GoodsMap)).collect(Collectors.toList());
            list.addAll(list3);
        }
        return list;
    }

    @Override
    public List<SnImeiGoodsInfoDto> queryGoodsInfoBySnImeis(SnImeiQueryDto snImeiQueryDto) {
        // 根据sn/imei查询sn/imei信息
        List<SnImeiInfoDto> snImeiInfos = queryAllSnImeiInfo(snImeiQueryDto);
        List<String> goodsIds = snImeiInfos.stream().map(it -> String.valueOf(it.getGoodsId())).collect(Collectors.toList());
        List<String> code69List = snImeiQueryDto.getSixNineCodeList();
        // 查询商品信息
        List<IntlRmsProduct> newProducts = intlRmsProductService.queryByGoodsIdList(goodsIds);
        List<IntlRmsProduct> newProductsByCode69 = intlRmsProductService.queryByCode69List(code69List);
        newProducts.addAll(newProductsByCode69);
        // 映射商品信息，goodsId -> 商品信息
        Map<String, IntlRmsProduct> goodsMap = newProducts.stream().collect(Collectors.toMap(IntlRmsProduct::getGoodsId, it -> it));
        // 映射商品信息，code69 -> 商品信息
        Map<String, IntlRmsProduct> code69GoodsMap = newProducts.stream().collect(Collectors.toMap(IntlRmsProduct::getCode69, Function.identity()));
        // 构建响应数据列表
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoList = snImeiInfos.stream()
                .map(imei -> buildSnImeiGoodsInfo(imei, imei.getGoodsId(), goodsMap)).collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> code69GoodsInfoList = newProductsByCode69.stream()
                .map(goods -> buildSnImeiGoodsInfo(null, goods.getCode69(), code69GoodsMap)).collect(Collectors.toList());
        snImeiGoodsInfoList.addAll(code69GoodsInfoList);
        return snImeiGoodsInfoList;

    }

    @Override
    public SnImeiGoodsInfoDto getGoodsInfoBySnImei(String code) {
        SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(code);
        SnImeiQueryDto queryDto = new SnImeiQueryDto();
        if (serialNumberType == SerialNumberType.SN) {
            queryDto.setSnList(Lists.newArrayList(code));
        } else if (serialNumberType == SerialNumberType.IMEI) {
            queryDto.setImeiList(Lists.newArrayList(code));
        } else if (serialNumberType == SerialNumberType.CODE69) {
            queryDto.setSixNineCodeList(Lists.newArrayList(code));
        }
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtos = this.queryGoodsInfoBySnImeis(queryDto);
        if (CollectionUtils.isNotEmpty(snImeiGoodsInfoDtos)) {
            return snImeiGoodsInfoDtos.get(0);
        }
        return null;
    }

    @Override
    public List<SnImeiGoodsInfoDto> queryGoodsInfoBySkus(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Lists.newArrayList();
        }
        List<IntlRmsProduct> products = intlRmsProductService.queryBySkuList(skuList);
        Map<String, IntlRmsProduct> goodsMap = products.stream().collect(Collectors.toMap(IntlRmsProduct::getSkuId, Function.identity()));
        return products.stream().map(it -> buildSnImeiGoodsInfo(null, it.getSkuId(), goodsMap)).collect(Collectors.toList());
    }

    @Override
    public List<SnImeiGoodsInfoDto> queryGoodsInfoByGoodsIds(List<String> goodsIdList) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return Lists.newArrayList();
        }
        List<IntlRmsProduct> products = intlRmsProductService.queryByGoodsIdList(goodsIdList);
        Map<String, IntlRmsProduct> goodsMap = products.stream().collect(Collectors.toMap(IntlRmsProduct::getGoodsId, Function.identity()));
        return products.stream().map(it -> buildSnImeiGoodsInfo(null, it.getGoodsId(), goodsMap)).collect(Collectors.toList());
    }

    /**
     * 构建sn/imei/69码商品信息
     *
     * @param imei     snImei信息
     * @param identity goodsId或69码
     * @param goodsMap 商品信息映射
     * @return sn/imei/69码商品信息
     */
    private SnImeiGoodsInfoDto buildSnImeiGoodsInfo(SnImeiInfoDto imei, String identity, Map<String, IntlRmsProduct> goodsMap) {
        SnImeiGoodsInfoDto snImeiGoodsInfoDto = new SnImeiGoodsInfoDto();
        if (Objects.nonNull(imei)) {
            snImeiGoodsInfoDto.setGoodsType(imei.getGoodsType());
            snImeiGoodsInfoDto.setSn(imei.getSn());
            snImeiGoodsInfoDto.setImei(imei.getImei());
            snImeiGoodsInfoDto.setImei2(imei.getImei2());
            snImeiGoodsInfoDto.setSku(imei.getSku());
        }
        IntlRmsProduct goodsInfo = goodsMap.get(identity);
        if (Objects.nonNull(goodsInfo)) {
            snImeiGoodsInfoDto.setSku(goodsInfo.getSkuId());
            snImeiGoodsInfoDto.setGoodsId(goodsInfo.getGoodsId());
            snImeiGoodsInfoDto.setCode69(goodsInfo.getCode69());
            snImeiGoodsInfoDto.setScmName(goodsInfo.getScmName());
            snImeiGoodsInfoDto.setProductLine(goodsInfo.getProductLine());
            // 获取产品线中文、英文名称
            ProductLineDto productLineDto = productQueryService.getProductLineById(goodsInfo.getProductLine());
            snImeiGoodsInfoDto.setProductLineCn(Objects.nonNull(productLineDto) ? productLineDto.getCnName() : goodsInfo.getProductLine());
            snImeiGoodsInfoDto.setProductLineEn(Objects.nonNull(productLineDto) ? productLineDto.getEnName() : goodsInfo.getProductLineEn());
            snImeiGoodsInfoDto.setGoodsName(goodsInfo.getChineseName());
            snImeiGoodsInfoDto.setGoodsNameEn(goodsInfo.getEnglishName());
            snImeiGoodsInfoDto.setProjectCode(goodsInfo.getProjectCode());
            snImeiGoodsInfoDto.setRam(goodsInfo.getRam());
            snImeiGoodsInfoDto.setRom(goodsInfo.getRom());
            snImeiGoodsInfoDto.setProductId(goodsInfo.getProductId());
            snImeiGoodsInfoDto.setProductName(goodsInfo.getName());
            snImeiGoodsInfoDto.setColor(goodsInfo.getColor());
            snImeiGoodsInfoDto.setEnglishColor(goodsInfo.getEnglishColor());
        }
        return snImeiGoodsInfoDto;
    }

    /**
     * 根据69码校验商品信息
     *
     * @param code69   69码
     * @param goodsMap 商品信息映射
     * @return 校验结果
     */
    private SnImeiValidationDto validateCode69(String code69, Map<String, IntlRmsProduct> goodsMap) {
        SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(code69);
        IntlRmsProduct goods = goodsMap.get(code69);
        boolean isValid = Objects.nonNull(goods) && Objects.equals(goods.getIs69(), 1) && Objects.equals(goods.getStateCode(), 0);
        SnImeiValidationDto validationDto = new SnImeiValidationDto();
        validationDto.setSnType(serialNumberType.name());
        validationDto.setSnImei(code69);
        validationDto.setIsValid(isValid);
        String message = "";
        if (Objects.equals(serialNumberType, SerialNumberType.UNKNOWN)) {
            message = "不是有效的69码";
        }
        if (Objects.isNull(goods)) {
            message = "69码所对应的商品信息不存在";
        }
        validationDto.setMessage(message);
        return validationDto;
    }

    /**
     * 校验sn/imei信息是否有效
     *
     * @param snImei    当前sn/imei信息
     * @param snImeiMap sn信息映射
     * @param goodsMap  商品信息映射
     * @return 校验结果
     */
    private SnImeiValidationDto validateSnImei(String snImei, Map<String, SnImeiInfoDto> snImeiMap, Map<String, IntlRmsProduct> goodsMap) {
        // 校验sn信息是否有效
        // 1. SN或IMEI对应的IMEI基础信息存在
        // 2. 根据IMEI返回的goodsId，查询商品信息存在
        SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(snImei);
        SnImeiInfoDto snImeiInfoDto = snImeiMap.get(snImei);
        boolean isValid = false;
        if (Objects.nonNull(snImeiInfoDto)) {
            IntlRmsProduct goods = goodsMap.get(snImeiInfoDto.getGoodsId());
            isValid = Objects.nonNull(goods) && Objects.equals(goods.getIsSn(), 1);
        }
        SnImeiValidationDto validationDto = new SnImeiValidationDto();
        validationDto.setSnType(serialNumberType.name());
        validationDto.setSnImei(snImei);
        validationDto.setIsValid(isValid);
        String message = "";
        if (Objects.equals(serialNumberType, SerialNumberType.UNKNOWN)) {
            message = "串码不是有效的SN/IMEI/69码";
        }
        if (Objects.isNull(snImeiInfoDto)) {
            message = "串码信息不存在";
        }
        if (Objects.nonNull(snImeiInfoDto) && Objects.isNull(goodsMap.get(snImeiInfoDto.getGoodsId()))) {
            message = "串码所对应的商品信息不存在";
        }
        validationDto.setMessage(message);
        return validationDto;
    }

    /**
     * 查询所有sn/imei信息
     *
     * @param snImeiQueryDto 查询条件，包含sn、imei列表
     * @return IMEI信息列表
     */
    private List<SnImeiInfoDto> queryAllSnImeiInfo(SnImeiQueryDto snImeiQueryDto) {
        List<SnImeiInfoDto> list1 = snImeiQueryService.querySnImeiInfoBySns(snImeiQueryDto.getSnList());
        List<SnImeiInfoDto> list2 = snImeiQueryService.querySnImeiInfoByImeis(snImeiQueryDto.getImeiList());
        list1.addAll(list2);
        return list1;
    }
}
