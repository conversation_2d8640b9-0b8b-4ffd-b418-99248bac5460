package com.mi.info.intl.retail.ldu.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@ExcelIgnoreUnannotated
public class IntlLduSnExcel {

    /**
     * 区域
     */
    @ExcelProperty("区域")
    private String region;

    /**
     * 区域编码
     */
    @ExcelProperty("区域编码")
    private String regionCode;

    /**
     * 国家
     */
    @ExcelProperty("国家")
    private String country;

    /**
     * 国家/地区编码
     */
    @ExcelProperty("国家/地区编码")
    private String countryCode;

    /**
     * 渠道类型
     */
    @ExcelProperty("渠道类型")
    private String channelType;

    /**
     * 零售商编码
     */
    @ExcelProperty("零售商编码")
    private String retailerCode;

    /**
     * 零售商名称
     */
    @ExcelProperty("零售商名称")
    private String retailerName;

    /**
     * LDU类型:大货（Mass Production Version）、专样（Customized Version）
     */
    @ExcelProperty("LDU类型")
    private String lduType;

    /**
     * 产品线
     */
    @ExcelProperty("产品线")
    private String productLine;

    /**
     * 产品ID
     */
    @ExcelProperty("Product ID")
    private String goodsId;

    /**
     * 产品名称
     */
    @ExcelProperty("Product Name")
    private String goodsName;

    /**
     * 项目代码
     */
    @ExcelProperty("项目代码")
    private String projectCode;

    /**
     * RAM容量
     */
    @ExcelProperty("RAM")
    private String ramCapacity;

    /**
     * 计划创建日期
     */
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planCreateDate;


    /**
     * IMEI 1
     */
    @ExcelProperty("IMEI 1")
    private String imei1;

    /**
     * ROM容量
     */
    @ExcelProperty("ROM")
    private String romCapacity;

    /**
     * 序列号SN
     */
    @ExcelProperty("SN")
    private String sn;

    /**
     * IMEI 2
     */
    @ExcelProperty("IMEI 2")
    private String imei2;

    /**
     * 创建人ID
     */
    @ExcelProperty("创建人ID")
    private String createUserId;

    /**
     * 创建人姓名
     */
    @ExcelProperty("创建人姓名")
    private String createUserName;


    /**
     * 2新增计划外上报，0未核销  1已核销
     */
    @ExcelProperty("是否上报")
    private String isReport;

    /**
     * 状态:1有效，0是无效
     */
    @ExcelProperty("状态")
    private String status;


}
