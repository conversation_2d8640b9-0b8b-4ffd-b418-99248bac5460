package com.mi.info.intl.retail.org.domain;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.api.so.rule.blacklist.SoBlacklistApiService;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsSignRule;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.IntlRmsSignRuleMapper;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlRmsSignRuleReadMapper;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsCountryTimezoneReadMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsPositionReadMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsStoreReadMapper;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsCity;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsProvince;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsRrp;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsSecondarychannel;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsCityMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsProvinceMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsRrpMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsSecondarychannelMapper;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsPersonnelPositionMapper;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper;
import com.mi.info.intl.retail.user.infra.mapper.read.IntlRmsPersonnelPositionReadMapper;
import com.mi.info.intl.retail.user.infra.mapper.read.IntlRmsUserReadMapper;
import com.mi.info.intl.retail.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class RmsSyncDbManager {

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;
    @Resource
    private IntlRmsStoreReadMapper intlRmsStoreReadMapper;
    @Resource
    private IntlRmsPositionMapper intlRmsPositionMapper;
    @Resource
    private IntlRmsPositionReadMapper intlRmsPositionReadMapper;
    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;
    @Resource
    private IntlRmsCountryTimezoneReadMapper intlRmsCountryTimezoneReadMapper;
    @Resource
    private IntlRmsUserMapper intlRmsUserMapper;
    @Resource
    private IntlRmsUserReadMapper intlRmsUserReadMapper;
    @Resource
    private IntlRmsPersonnelPositionMapper intlRmsPersonnelPositionMapper;
    @Resource
    private IntlRmsPersonnelPositionReadMapper intlRmsPersonnelPositionReadMapper;
    @Resource
    private IntlRmsSignRuleMapper intlRmsSignRuleMapper;
    @Resource
    private IntlRmsSignRuleReadMapper intlRmsSignRuleReadMapper;
    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;
    @Resource
    private SoBlacklistApiService soBlacklistApiService;

    private IntlRmsRrpMapper intlRmsRrpMapper;
    @Resource
    private IntlRmsProvinceMapper intlRmsProvinceMapper;
    @Resource
    private IntlRmsCityMapper intlRmsCityMapper;
    @Resource
    private IntlRmsSecondarychannelMapper intlRmsSecondarychannelMapper;

    public void editDb(RmsDbRequest rmsDBRequest) {
        rmsDBRequest.getRmsDBContentList().forEach(rmsDBContentRequest -> {
            try {
                switch (rmsDBContentRequest.getTable()) {
                    case "intl_rms_store":
                        this.addStore(rmsDBContentRequest);
                        break;
                    case "intl_rms_user":
                        this.addUser(rmsDBContentRequest);
                        break;
                    case "intl_rms_position":
                        this.addPosition(rmsDBContentRequest);
                        break;
                    case "intl_rms_country_timezone":
                        this.addCountryTimezone(rmsDBContentRequest);
                        break;
                    case "intl_rms_personnel_position":
                        this.addPersonnelPosition(rmsDBContentRequest);
                        break;
                    case "intl_rms_sign_rule":
                        this.addSignRule(rmsDBContentRequest);
                        break;
                    case "intl_rms_retailer":
                        this.addRetailer(rmsDBContentRequest);
                        break;
                    case "intl_so_sn_blacklist":
                        this.addSoSnBlacklist(rmsDBContentRequest);
                        break;
                    case "intl_rms_rrp":
                        this.addRrp(rmsDBContentRequest);
                        break;
                    case "intl_rms_province":
                        this.addProvince(rmsDBContentRequest);
                        break;
                    case "intl_rms_city":
                        this.addCity(rmsDBContentRequest);
                        break;
                    case "intl_rms_secondarychannel":
                        this.addSecondaryChannel(rmsDBContentRequest);
                        break;
                    default:
                        throw new RuntimeException(
                                "SyncRmsDb:table not found : table" + rmsDBContentRequest.getTable());
                }
            } catch (Exception e) {

                log.error("SyncRmsDbError:{}, rmsDBContentRequest:{}", e.getMessage(), rmsDBContentRequest);
            }

        });
    }

    private void addSoSnBlacklist(RmsDbContentRequest rmsDBContentRequest) {
        log.info("SoSnBlacklist start :{}", rmsDBContentRequest);
        soBlacklistApiService.syncSoSnBlacklist(rmsDBContentRequest.getContent());
    }


    private void addRetailer(RmsDbContentRequest rmsDBContentRequest) {
        log.info("Retailer:{}", rmsDBContentRequest);
        IntlRmsRetailer intlRmsRetailer =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsRetailer.class);
        if (intlRmsRetailer != null && intlRmsRetailer.getRetailerId() != null) {
            LambdaQueryWrapper<IntlRmsRetailer> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsRetailer::getRetailerId, intlRmsRetailer.getRetailerId());
            IntlRmsRetailer haveStore = intlRmsRetailerMapper.selectOne(lambdaQuery);
            if (haveStore != null) {
                intlRmsRetailer.setId(haveStore.getId());
                intlRmsRetailer.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsRetailer.setIsNew(0);
                intlRmsRetailerMapper.updateById(intlRmsRetailer);
                log.info("updateRetailer_update:{}", intlRmsRetailer);
            } else {
                intlRmsRetailer.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsRetailer.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsRetailer.setIsNew(1);
                intlRmsRetailerMapper.insert(intlRmsRetailer);
                log.info("addRetailer_insert:{}", intlRmsRetailer);
            }
        }
    }

    private void addStore(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsStore intlRmsStore =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsStore.class);
        if (intlRmsStore != null && intlRmsStore.getStoreId() != null) {
            LambdaQueryWrapper<IntlRmsStore> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsStore::getStoreId, intlRmsStore.getStoreId());
            IntlRmsStore haveStore = intlRmsStoreReadMapper.selectOne(lambdaQuery);
            if (haveStore != null) {
                intlRmsStore.setId(haveStore.getId());
                intlRmsStore.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsStoreMapper.updateById(intlRmsStore);
                log.info("addStore_update:{}", intlRmsStore);
            } else {
                intlRmsStore.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsStore.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsStoreMapper.insert(intlRmsStore);
                log.info("addStore_insert:{}", intlRmsStore);
            }
        }
    }

    private void addUser(RmsDbContentRequest rmsDBContentRequest) {

        log.info("addUser_MQ:{}", rmsDBContentRequest);
        IntlRmsUser intlRmsUser = JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                IntlRmsUser.class);
        log.info("addUser_parsed result:{}", intlRmsUser);
        if (intlRmsUser != null && intlRmsUser.getRmsUserid() != null) {
            LambdaQueryWrapper<IntlRmsUser> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsUser::getRmsUserid, intlRmsUser.getRmsUserid());
            IntlRmsUser haveUser = intlRmsUserReadMapper.selectOne(lambdaQuery);
            if (haveUser != null) {
                intlRmsUser.setId(haveUser.getId());
                intlRmsUser.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsUserMapper.updateById(intlRmsUser);
            } else {
                intlRmsUser.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsUser.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsUserMapper.insert(intlRmsUser);
            }
            log.info("addUser_result:{}", "success");
        }
    }

    private void addPosition(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsPosition intlRmsPosition =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                        IntlRmsPosition.class);
        if (intlRmsPosition != null && intlRmsPosition.getPositionId() != null) {
            LambdaQueryWrapper<IntlRmsPosition> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsPosition::getPositionId, intlRmsPosition.getPositionId());
            IntlRmsPosition havePosition = intlRmsPositionReadMapper.selectOne(lambdaQuery);
            if (havePosition != null) {
                intlRmsPosition.setId(havePosition.getId());
                intlRmsPosition.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsPositionMapper.updateById(intlRmsPosition);
                log.info("addPosition_update:{}", intlRmsPosition);
            } else {
                intlRmsPosition.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsPosition.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsPositionMapper.insert(intlRmsPosition);
                log.info("addPosition_insert:{}", intlRmsPosition);
            }

        }
    }

    private void addCountryTimezone(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsCountryTimezone intlRmsCountryTimezone =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsCountryTimezone.class);
        if (intlRmsCountryTimezone != null && intlRmsCountryTimezone.getCountryCode() != null) {
            LambdaQueryWrapper<IntlRmsCountryTimezone> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsCountryTimezone::getCountryCode, intlRmsCountryTimezone.getCountryCode());
            IntlRmsCountryTimezone haveCountryTimezone = intlRmsCountryTimezoneReadMapper.selectOne(lambdaQuery);
            if (haveCountryTimezone != null) {
                intlRmsCountryTimezone.setId(haveCountryTimezone.getId());
                intlRmsCountryTimezone.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsCountryTimezoneMapper.updateById(intlRmsCountryTimezone);
                log.info("addCountryTimezone_update:{}", intlRmsCountryTimezone);
            } else {
                intlRmsCountryTimezone.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsCountryTimezone.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsCountryTimezoneMapper.insert(intlRmsCountryTimezone);
                log.info("addCountryTimezone_insert:{}", intlRmsCountryTimezone);
            }
        }
    }

    private void addPersonnelPosition(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsPersonnelPosition intlRmsPersonnelPosition =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                        IntlRmsPersonnelPosition.class);
        if (intlRmsPersonnelPosition != null && intlRmsPersonnelPosition.getAssociationId() != null) {
            LambdaQueryWrapper<IntlRmsPersonnelPosition> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsPersonnelPosition::getAssociationId, intlRmsPersonnelPosition.getAssociationId());
            IntlRmsPersonnelPosition havePersonnelPosition = intlRmsPersonnelPositionReadMapper.selectOne(lambdaQuery);
            if (havePersonnelPosition != null) {
                intlRmsPersonnelPosition.setId(havePersonnelPosition.getId());
                intlRmsPersonnelPosition.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsPersonnelPositionMapper.updateById(intlRmsPersonnelPosition);
                log.info("addPersonnelPosition_update:{}", intlRmsPersonnelPosition);
            } else {
                intlRmsPersonnelPosition.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsPersonnelPosition.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsPersonnelPositionMapper.insert(intlRmsPersonnelPosition);
                log.info("addPersonnelPosition_insert:{}", intlRmsPersonnelPosition);
            }
        }
    }

    private void addSignRule(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsSignRule intlRmsSignRule = JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                IntlRmsSignRule.class);
        if (intlRmsSignRule != null && intlRmsSignRule.getSignRuleId() != null) {
            LambdaQueryWrapper<IntlRmsSignRule> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsSignRule::getSignRuleId, intlRmsSignRule.getSignRuleId());
            IntlRmsSignRule haveSignRule = intlRmsSignRuleReadMapper.selectOne(lambdaQuery);
            if (haveSignRule != null) {
                intlRmsSignRule.setId(haveSignRule.getId());
                intlRmsSignRule.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsSignRuleMapper.updateById(intlRmsSignRule);
                log.info("addSignRule_update:{}", intlRmsSignRule);
            } else {
                intlRmsSignRule.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsSignRule.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsSignRuleMapper.insert(intlRmsSignRule);
                log.info("addSignRule_insert:{}", intlRmsSignRule);
            }
        }
    }

    private void addRrp(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsRrp intlRmsRrp =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsRrp.class);
        if (intlRmsRrp != null && intlRmsRrp.getRrpId() != null) {
            LambdaQueryWrapper<IntlRmsRrp> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsRrp::getRrpId, intlRmsRrp.getRrpId());
            IntlRmsRrp haveRrp = intlRmsRrpMapper.selectOne(lambdaQuery);
            if (haveRrp != null) {
                intlRmsRrp.setId(haveRrp.getId());
                intlRmsRrp.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsRrpMapper.updateById(intlRmsRrp);
                log.info("addRrp_update:{}", intlRmsRrp);
            } else {
                intlRmsRrp.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsRrp.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsRrpMapper.insert(intlRmsRrp);
                log.info("addRrp_insert:{}", intlRmsRrp);
            }
        }
    }

    private void addProvince(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsProvince intlRmsProvince =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsProvince.class);
        if (intlRmsProvince != null && intlRmsProvince.getProvinceId() != null) {
            LambdaQueryWrapper<IntlRmsProvince> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsProvince::getProvinceId, intlRmsProvince.getProvinceId());
            IntlRmsProvince haveProvince = intlRmsProvinceMapper.selectOne(lambdaQuery);
            if (haveProvince != null) {
                intlRmsProvince.setId(haveProvince.getId());
                intlRmsProvince.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsProvinceMapper.updateById(intlRmsProvince);
                log.info("addProvince_update:{}", intlRmsProvince);
            } else {
                intlRmsProvince.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsProvince.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsProvinceMapper.insert(intlRmsProvince);
                log.info("addProvince_insert:{}", intlRmsProvince);
            }
        }
    }

    private void addCity(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsCity intlRmsCity =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsCity.class);
        if (intlRmsCity != null && intlRmsCity.getCityId() != null) {
            LambdaQueryWrapper<IntlRmsCity> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsCity::getCityId, intlRmsCity.getCityId());
            IntlRmsCity haveCity = intlRmsCityMapper.selectOne(lambdaQuery);
            if (haveCity != null) {
                intlRmsCity.setId(haveCity.getId());
                intlRmsCity.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsCityMapper.updateById(intlRmsCity);
                log.info("addCity_update:{}", intlRmsCity);
            } else {
                intlRmsCity.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsCity.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsCityMapper.insert(intlRmsCity);
                log.info("addCity_insert:{}", intlRmsCity);
            }
        }
    }

    private void addSecondaryChannel(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsSecondarychannel intlRmsSecondarychannel =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsSecondarychannel.class);
        if (intlRmsSecondarychannel != null && intlRmsSecondarychannel.getChannelId() != null) {
            LambdaQueryWrapper<IntlRmsSecondarychannel> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsSecondarychannel::getChannelId, intlRmsSecondarychannel.getChannelId());
            IntlRmsSecondarychannel haveSecondaryChannel = intlRmsSecondarychannelMapper.selectOne(lambdaQuery);
            if (haveSecondaryChannel != null) {
                intlRmsSecondarychannel.setId(haveSecondaryChannel.getId());
                intlRmsSecondarychannel.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsSecondarychannelMapper.updateById(intlRmsSecondarychannel);
                log.info("addSecondaryChannel_update:{}", intlRmsSecondarychannel);
            } else {
                intlRmsSecondarychannel.setCreatedAt(System.currentTimeMillis() / 1000);
                intlRmsSecondarychannel.setUpdatedAt(System.currentTimeMillis() / 1000);
                intlRmsSecondarychannelMapper.insert(intlRmsSecondarychannel);
                log.info("addSecondaryChannel_insert:{}", intlRmsSecondarychannel);
            }
        }
    }

}
