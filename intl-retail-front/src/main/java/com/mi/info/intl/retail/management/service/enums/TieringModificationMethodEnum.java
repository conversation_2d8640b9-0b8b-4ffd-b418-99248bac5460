package com.mi.info.intl.retail.management.service.enums;

public enum TieringModificationMethodEnum {

    SYSTEM_CALCULATION(1, "System Calculation", "系统计算"),
    MANUAL_UPLOAD(2, "Manual Upload", "手动上传");

    private final Integer key;
    private final String value;
    private final String desc;

    TieringModificationMethodEnum(Integer key, String value, String desc) {
        this.key = key;
        this.value = value;
        this.desc = desc;
    }

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static TieringModificationMethodEnum fromKey(Integer key) {
        for (TieringModificationMethodEnum value : TieringModificationMethodEnum.values()) {
            if (value.key.equals(key)) {
                return value;
            }
        }
        return null;
    }
}
