package com.mi.info.intl.retail.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.management.entity.StoreGradeRelation;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StoreGradeRelationMapper extends BaseMapper<StoreGradeRelation> {

    /**
     * 根据规则ID和国家代码查询门店等级统计
     * @param ruleId 规则ID
     * @param countryCode 国家代码
     * @return 门店等级统计列表
     */
    List<StoreGradeCompleteCount> selectStoreGradeCountByRuleId(
            @Param("ruleId") Integer ruleId,
            @Param("countryCode") String countryCode);
    
    /**
     * 批量插入门店等级关联数据
     * @param entityList 实体列表
     * @return 插入记录数
     */
    void insertBatch(@Param("list") List<StoreGradeRelation> entityList);
} 