package com.mi.info.intl.retail.ldu.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsInfoDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsPagingQueryDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsPagingResultDto;
import com.mi.info.intl.retail.ldu.dto.UpcGoodsSyncReqDto;
import com.mi.info.intl.retail.ldu.enums.UpcGoodsExtInfoEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import com.mi.info.intl.retail.ldu.service.IntlProductSyncService;
import com.xiaomi.cnzone.commons.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品信息同步服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/11 11:26
 */
@Slf4j
@Service
public class IntlProductSyncServiceImpl implements IntlProductSyncService {

    private static final String PROGRESSING_CACHE_KEY = "upc_goods_sync_progressing";

    private static final String PROGRESSING_LAST_INDEX = "last_page";

    private static final int DEFAULT_PAGE_SIZE = 500;

    @Resource
    private IProductQueryService productQueryService;

    @Resource
    private IntlRmsProductService intlRmsProductService;


    /**
     * 全量同步商品信息
     *
     * @param syncReqDto 同步请求参数
     */
    @Override
    public void syncAllProductInfo(UpcGoodsSyncReqDto syncReqDto) {
        Assert.notNull(syncReqDto, "Sync request params can not be null");
        // 从缓存中获取最后一次的查询索引数据，首次查询是为null
        Integer lastPage = CacheUtils.hget(PROGRESSING_CACHE_KEY, PROGRESSING_LAST_INDEX, Integer.class);
        lastPage = Objects.nonNull(lastPage) ? lastPage : 1;
        Integer pageSize = Objects.nonNull(syncReqDto.getPageSize()) ? syncReqDto.getPageSize() : DEFAULT_PAGE_SIZE;
        // 开始时间为空时，默认查询当日有变更的记录
        String start = syncReqDto.getStart();
        String end = syncReqDto.getEnd();
        if (StringUtils.isBlank(start)) {
            start = DateUtil.format(DateUtil.beginOfDay(DateUtil.date()), "yyyy-MM-dd HH:mm:ss");
            end = DateUtil.format(DateUtil.endOfDay(DateUtil.date()), "yyyy-MM-dd HH:mm:ss");
        }
        // 是否全量同步完成
        boolean finished;
        // 同步计数器
        AtomicInteger syncCount = new AtomicInteger(0);
        AtomicInteger pageCount = new AtomicInteger(lastPage);
        // 分页查询参数
        UpcGoodsPagingQueryDto queryDto = new UpcGoodsPagingQueryDto();
        queryDto.setPageSize(pageSize);
        queryDto.setPage(lastPage);
        queryDto.setStart(start);
        queryDto.setEnd(end);
        queryDto.setFilterYouPin(syncReqDto.getFilterYouPin());
        queryDto.setQueryType(syncReqDto.getQueryType());
        queryDto.setQueryKey(syncReqDto.getQueryKey());
        do {
            finished = doFetchAndSync(queryDto, syncCount, pageCount);
            if (finished) {
                // 同步完成，清除缓存中的进度数据
                CacheUtils.hdel(PROGRESSING_CACHE_KEY, PROGRESSING_LAST_INDEX);
            }
            // 下一页
            queryDto.setPage(pageCount.incrementAndGet());
        } while (!finished);
    }

    /**
     * 分页查询UPC商品信息，并同步到intl-retail数据库中
     *
     * @param queryDto  分页查询参数
     * @param syncCount 同步计数器
     * @param pageCount 分页计数器
     * @return 是否全量同步完成
     */
    private boolean doFetchAndSync(UpcGoodsPagingQueryDto queryDto, AtomicInteger syncCount, AtomicInteger pageCount) {
        // 第一次查询，从第一页开始查询
        UpcGoodsPagingResultDto pagingResult;
        try {
            pagingResult = productQueryService.queryUpcGoodsInfosPaging(queryDto);
        } catch (Exception e) {
            log.error("Query upc fetch data failed, with params: {}", JSONUtil.toJsonStr(queryDto), e);
            //  预警通知，手动处理。或设置重试机制
            // 异常后终止循环查询
            return true;
        }
        if (Objects.isNull(pagingResult) || CollectionUtils.isEmpty(pagingResult.getData())) {
            log.info("All data sync finished, with params: {}", JSONUtil.toJsonStr(queryDto));
            return true;
        }

        List<UpcGoodsInfoDto> list = pagingResult.getData();

        // 转换商品信息，并批量保存存到数据库中
        List<IntlRmsProduct> newProducts = list.stream().map(this::convertToProduct).collect(Collectors.toList());
        intlRmsProductService.batchSaveOrUpdate(newProducts);
        syncCount.getAndAdd(list.size());
        log.info("Sync upc product progressing, with params: {}, sync count: {}, current page: {}",
                JSONUtil.toJsonStr(queryDto), syncCount.get(), pageCount.get());
        // 缓存同步进度
        CacheUtils.hput(PROGRESSING_CACHE_KEY, PROGRESSING_LAST_INDEX, pageCount.get());

        if (list.size() < queryDto.getPageSize()) {
            log.info("All data sync finished, with params: {}", JSONUtil.toJsonStr(queryDto));
            return true;
        }
        return false;
    }

    /**
     * 将UPC商品信息转换为intl-retail数据库中的商品信息
     *
     * @param upcGoodsInfo UPC商品信息
     * @return intl-retail数据库中的商品信息
     */
    private IntlRmsProduct convertToProduct(UpcGoodsInfoDto upcGoodsInfo) {
        IntlRmsProduct product = new IntlRmsProduct();
        product.setGoodsId(upcGoodsInfo.getGoodsId());
        product.setName(upcGoodsInfo.getProductName());
        product.setSkuId(upcGoodsInfo.getSku());
        product.setScmName(upcGoodsInfo.getScmName());
        product.setScmCode(upcGoodsInfo.getScmCode());
        product.setSpuId(upcGoodsInfo.getSpu());
        product.setSpuName(upcGoodsInfo.getSpuNameCn());
        product.setSpuNameEn(upcGoodsInfo.getSpuNameEn());
        product.setCategoryDataId(Objects.nonNull(upcGoodsInfo.getCategoryId()) ? Long.valueOf(upcGoodsInfo.getCategoryId()) : null);
        product.setCategoryCn(upcGoodsInfo.getCategoryNameCn());
        product.setCategoryEn(upcGoodsInfo.getCategoryNameEn());
        product.setProjectId(upcGoodsInfo.getProjectId());
        product.setProjectCode(upcGoodsInfo.getProjectCode());
        product.setProjectNameCn(upcGoodsInfo.getProjectNameCn());
        product.setProjectNameEn(upcGoodsInfo.getProjectNameEn());
        product.setProductLine(upcGoodsInfo.getProductLine());
        if (StringUtils.isNotBlank(upcGoodsInfo.getProductLine())) {
            // 赋值产品线中文、英文名称
            ProductLineDto productLineDto = productQueryService.getProductLineById(upcGoodsInfo.getProductLine());
            product.setProductLine(Objects.nonNull(productLineDto) ? productLineDto.getCnName() : null);
            product.setProductLineEn(Objects.nonNull(productLineDto) ? productLineDto.getEnName() : null);
        }
        product.setIsSn(upcGoodsInfo.getIsSn());
        product.setCode69(upcGoodsInfo.getCode69());
        product.setIsUpc(upcGoodsInfo.getIsUpc());
        product.setStatus(upcGoodsInfo.getStatus());
        product.setCreatedOn(DateUtils.parseDate(upcGoodsInfo.getCreateTime()));
        product.setModifiedOn(DateUtils.parseDate(upcGoodsInfo.getUpdateTime()));
        product.setModifiedBy(Long.valueOf(upcGoodsInfo.getOperatorId()));
        product.setModifiedByName(upcGoodsInfo.getOperator());
        product.setSalesVersionId(upcGoodsInfo.getSalesVersion());
        product.setSalesVersionName(upcGoodsInfo.getSalesVersionName());
        product.setSalesVersionCode(upcGoodsInfo.getSalesVersionKey());
        product.setLastSyncTime(System.currentTimeMillis());
        product.setShortname(getPriorityShortName(upcGoodsInfo));
        if (CollectionUtils.isNotEmpty(upcGoodsInfo.getExtInfo())) {
            Map<Integer, UpcGoodsInfoDto.ExtInfoDTO> extInfoMap = upcGoodsInfo.getExtInfo()
                    .stream().collect(Collectors.toMap(UpcGoodsInfoDto.ExtInfoDTO::getId, Function.identity()));
            // ram容量信息
            if (extInfoMap.containsKey(UpcGoodsExtInfoEnum.RAM.getKey())) {
                product.setRam(getActualValue(extInfoMap.get(UpcGoodsExtInfoEnum.RAM.getKey()).getValue()));
            }
            // rom容量信息
            if (extInfoMap.containsKey(UpcGoodsExtInfoEnum.ROM.getKey())) {
                product.setRom(getActualValue(extInfoMap.get(UpcGoodsExtInfoEnum.ROM.getKey()).getValue()));
            }
        }

        return product;
    }

    /**
     * 从扩展信息value中解析中具体的ram、rom值。由于value结构不固定，此处直接解析json。
     *
     * @param value 扩展信息value
     * @return 实际值
     */
    private String getActualValue(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        Map<?, ?> map = JSON.parseObject(JSON.toJSONString(value), Map.class);
        Object item = CollectionUtils.isNotEmpty(map.values()) ? map.values().stream().findFirst().orElse(null) : null;
        if (Objects.isNull(item)) {
            return null;
        }
        JSONArray array = JSON.parseArray(JSON.toJSONString(item));
        final AtomicReference<String> actualValue = new AtomicReference<>();
        array.stream().findFirst().ifPresent(o -> {
            Map<?, ?> data = JSON.parseObject(JSON.toJSONString(o), Map.class);
            String val = Objects.nonNull(data.get("name")) ? data.get("name").toString() : null;
            actualValue.set(val);
        });
        return actualValue.get();
    }

    /**
     * 按照货币优先级获取销售区域简称（优先SGD，其次CNY）
     * @return 匹配的shortName，如果都不存在则返回null
     */
    public String getPriorityShortName(UpcGoodsInfoDto upcGoodsInfo) {
        if (CollectionUtils.isEmpty(upcGoodsInfo.getSaleAreaInfo())) {
            return null;
        }

        // 1. 先查找 SGD 对应的 shortName
        for (UpcGoodsInfoDto.SaleAreaInfoDTO area : upcGoodsInfo.getSaleAreaInfo()) {
            if ("SGD".equals(area.getCurrency()) && StringUtils.isNotBlank(area.getShortName())) {
                return area.getShortName();
            }
        }

        // 2. 如果 SGD 没有找到，再查找 CNY 对应的 shortName
        for (UpcGoodsInfoDto.SaleAreaInfoDTO area : upcGoodsInfo.getSaleAreaInfo()) {
            if ("CNY".equals(area.getCurrency()) && StringUtils.isNotBlank(area.getShortName())) {
                return area.getShortName();
            }
        }

        // 3. 如果都没有找到，返回 null
        return null;
    }
}
