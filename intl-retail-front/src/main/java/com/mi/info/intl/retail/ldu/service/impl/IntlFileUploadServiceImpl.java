package com.mi.info.intl.retail.ldu.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.mi.info.intl.retail.ldu.enums.ResultCodeEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlFileUploadMapper;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IntlFileUploadServiceImpl extends ServiceImpl<IntlFileUploadMapper, IntlFileUpload> implements IntlFileUploadService {


    @Autowired
    private IntlFileUploadMapper intlFileUploadMapper;


    @Override
    public CommonApiResponse<Object> updateByGuid(Object requestBody, JsonNode rootNode) {

        JsonNode photosNode = rootNode.path("Photos");
        if (photosNode.isArray() && photosNode.size() > 0) {
            JsonNode photoNode = photosNode.get(0);
            String moduleName = photoNode.path("ModuleName").asText();

            String guid = photoNode.path("GUID").asText();
            String fdsUrl = photoNode.path("fdsUrl").asText();

            if (StringUtils.isNotBlank(guid) && StringUtils.isNotBlank(fdsUrl)) {
                UpdateWrapper<IntlFileUpload> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("guid", guid);
                updateWrapper.set("module_name", moduleName);
                updateWrapper.set("fds_url", fdsUrl);
                updateWrapper.set("suffix", getFileSuffix(fdsUrl));

                intlFileUploadMapper.update(updateWrapper);
            } else {
                log.warn("GUID or fdsUrl is blank in the request.");
            }
        } else {
            log.warn("Photos array is empty or not present in the request.");
        }
        return new CommonApiResponse<>(ResultCodeEnum.SUCCESS.getCode(), "ok", "");
    }

    public static String getFileSuffix(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }

        int lastDotIndex = url.lastIndexOf(".");
        int lastSlashIndex = url.lastIndexOf("/");

        if (lastDotIndex > lastSlashIndex && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex + 1); // 返回类似 ".jpg"
        }

        return "";
    }
}
