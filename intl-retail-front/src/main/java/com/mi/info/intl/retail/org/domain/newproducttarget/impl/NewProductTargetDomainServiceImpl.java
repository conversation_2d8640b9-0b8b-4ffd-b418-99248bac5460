package com.mi.info.intl.retail.org.domain.newproducttarget.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums.NewProductStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums.NewProductTargetTypeEnum;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.newproducttarget.NewProductTargetDomainService;
import com.mi.info.intl.retail.org.infra.entity.IntlNewProductTarget;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlNewProductTargetMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: chuang
 * @since: 2025/8/1
 */
@Slf4j
@Service
public class NewProductTargetDomainServiceImpl implements NewProductTargetDomainService {
    @Autowired
    IntlNewProductTargetMapper intlNewProductTargetMapper;
    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    /**
     * 主要的业务逻辑会在这里
     */
    @Override
    public Page<NewProductTargetItem> listNewProductTarget(NewProductTargetReq request) {
        log.info("NewProductTargetDomainServiceImpl#listNewProductTarget request:{}", RetailJsonUtil.toJson(request));
        Page<IntlNewProductTarget> page = new Page<>(request.getPageNum(), request.getPageSize());
        Page<NewProductTargetItem> resultPage = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<IntlNewProductTarget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollUtil.isEmpty(request.getCategoryCode()), IntlNewProductTarget::getCategory,
                request.getCategoryCode());
        queryWrapper.in(!CollUtil.isEmpty(request.getSeriess()), IntlNewProductTarget::getSeries, request.getSeriess());
        queryWrapper.in(!CollUtil.isEmpty(request.getProjects()), IntlNewProductTarget::getProject,
                request.getProjects());
        queryWrapper.eq(StringUtils.isNotBlank(request.getStatus()), IntlNewProductTarget::getStatus,
                request.getStatus());
        queryWrapper.eq(StringUtils.isNotBlank(request.getTargetType()), IntlNewProductTarget::getTargetType,
                request.getTargetType());
        queryWrapper.in(!CollUtil.isEmpty(request.getCountryCode()), IntlNewProductTarget::getCountry,
                request.getCountryCode());
        //TODO 权限判断 如果是 总部侧 可以看到全部; 国家侧只能看到自己国家的(对比 操作人与数据的国家字段);
        page = intlNewProductTargetMapper.selectPage(page, queryWrapper);
        List<NewProductTargetItem> newProductTargetItemList =
                Convert.toList(NewProductTargetItem.class, page.getRecords());
        resultPage.setTotal(page.getTotal());
        resultPage.setRecords(newProductTargetItemList);
        return resultPage;
    }

    @Override
    public NewProductTargetItem getNewProductTargetItemById(Long id) {
        log.info("NewProductTargetDomainServiceImpl#getNewProductTargetItemById id:{}", RetailJsonUtil.toJson(id));
        IntlNewProductTarget intlNewProductTarget = intlNewProductTargetMapper.selectById(id);
        NewProductTargetItem result = Convert.convert(NewProductTargetItem.class, intlNewProductTarget);
        return result;
    }

//    // 转换为领域对象
//        return entities.stream().map(entity -> {
//        RuleConfigDomain domain = new RuleConfigDomain();
//        BeanUtils.copyProperties(entity, domain);
//        return domain;
//    }).collect(Collectors.toList());

//    List<InspectionTaskConfDTO> inspectionTaskConfDTOS = intlInspectionTaskConfList.stream().map(item -> {
//        InspectionTaskConfDTO inspectionTaskConfDTO = ComponentLocator.getConverter()
//                .convert(item, InspectionTaskConfDTO.class);
//        inspectionTaskConfDTO.setIsNewProductPromotion(hasBigPromotionMap.getOrDefault(item.getCountryCode(), false));
//        IntlBigPromotionConf promotionConf = promotionConfMap.getOrDefault(item.getCountryCode(), new IntlBigPromotionConf());
//        inspectionTaskConfDTO.setPromotionStartTime(promotionConf.getStartTime());
//        inspectionTaskConfDTO.setPromotionEndTime(promotionConf.getEndTime());
//        inspectionTaskConfDTO.setCreateTime(DateTimeUtil.timestampToDate(Long.valueOf(item.getCreatedAt())));
//        inspectionTaskConfDTO.setUpdateTime(DateTimeUtil.timestampToDate(Long.valueOf(item.getUpdatedAt())));
//        return inspectionTaskConfDTO;
//    }).collect(Collectors.toList());

    @Transactional
    public CommonApiResponse<List<String>> addNewProductTarget(@Valid NewProductTargetAddReq req) {
        log.info("NewProductTargetDomainServiceImpl#addNewProductTarget req:{}", RetailJsonUtil.toJson(req));
        //TODO 如何获得创建人?
        String mid = RpcContext.getContext().getAttachments().get("$upc_miID");
        String mname = RpcContext.getContext().getAttachments().get("$upc_userName");
        CommonApiResponse apiResponse = null;
        apiResponse = new CommonApiResponse(null);
        IntlNewProductTarget addTmp = null;
        List<String> data = new LinkedList<>();
        List<IntlNewProductTarget> addListTmp = new LinkedList<>();
        List<IntlNewProductTarget> canAddListTmp = new LinkedList<>();
        long curr = System.currentTimeMillis();
        LambdaQueryWrapper<IntlRmsCountryTimezone> queryRms = new LambdaQueryWrapper<>();
        queryRms.in(IntlRmsCountryTimezone::getCountryCode, req.getCountryCode());
        List<IntlRmsCountryTimezone> listRms = intlRmsCountryTimezoneMapper.selectList(queryRms);
        Map<String, IntlRmsCountryTimezone> msRms =
                listRms.stream().collect(Collectors.toMap(IntlRmsCountryTimezone::getCountryCode, x -> x, (o, n) -> o));

        IntlRmsCountryTimezone rmsForArea = null;
        //拆分-组装数据
        for (String project : req.getProjects()) {
            for (String country : req.getCountryCode()) {
                addTmp = new IntlNewProductTarget();
                addTmp.setStatus(NewProductStatusEnum.DONE.getCode());
                BeanUtil.copyProperties(req, addTmp);
                addTmp.setCategory(req.getCategoryCode());
                addTmp.setCategoryName(req.getCategoryName());
                addTmp.setProject(project);
                addTmp.setCountry(country);
                addTmp.setCreatedTime(curr);
                addTmp.setCreatedBy(mid);
                addTmp.setCreatedName(mname);
                rmsForArea = msRms.get(country);
                if (rmsForArea != null) {
                    addTmp.setRegion(rmsForArea.getAreaCode());
                }
                addListTmp.add(addTmp);

            }
        }
        //查询 看看哪些已经存在
        List<IntlNewProductTarget> exists = intlNewProductTargetMapper.selectListByConditionForExists(addListTmp);
        if (exists.size() > 0) { //只要存在1条就不能新增
            for (IntlNewProductTarget e : exists) {
                data.add(String.format("category: %s; series: %s; project: %s; country: %s; already existed",
                        e.getCategory(), e.getSeries(), e.getProject(), e.getCountry()));
            }
        } else {
            //进行目标类型的扩展,然后批量新增
            for (IntlNewProductTarget sub : addListTmp) {
                sub.setTargetType(NewProductTargetTypeEnum.FIRST.getCode());
                IntlNewProductTarget sub1 = new IntlNewProductTarget();
                BeanUtil.copyProperties(sub, sub1);
                sub1.setTargetType(NewProductTargetTypeEnum.LIFE_CIRCLE.getCode());
                canAddListTmp.add(sub);
                canAddListTmp.add(sub1);
            }
            intlNewProductTargetMapper.batchInsert(canAddListTmp);
        }
        apiResponse = new CommonApiResponse(data);
        return apiResponse;
    }

    @Override
    @Transactional
    public CommonApiResponse<String> updateNewProductTarget(NewProductTargetUpdateReq req) {
        log.info("NewProductTargetDomainServiceImpl#updateNewProductTarget req:{}", RetailJsonUtil.toJson(req));
        CommonApiResponse apiResponse = null;
        apiResponse = new CommonApiResponse("");
        long curr = System.currentTimeMillis();
        IntlNewProductTarget upTmp = null;
        //TODO 如何获得创建人?
        String mid = RpcContext.getContext().getAttachments().get("$upc_miID");
        String mname = RpcContext.getContext().getAttachments().get("$upc_userName");
        for (NewProductTargetUpdateReq.NewProductTargetUpdateItemReq upSub : req.getItems()) {
            //TODO 更新人
            upTmp = Convert.convert(IntlNewProductTarget.class, upSub);
            upTmp.setUpdatedTime(curr);
            upTmp.setUpdatedBy(mid);
            upTmp.setUpdatedName(mname);
            upTmp.setStatus(NewProductStatusEnum.MOIDFY.getCode());
            intlNewProductTargetMapper.updateById(upTmp);
        }
        return apiResponse;
    }

    @Override
    public CommonApiResponse<List<NewProdcutTargetMetaResp>> listMeta(NewProdcutTargetMetaReq req) {
        log.info("NewProductTargetDomainServiceImpl#listMeta req:{}", RetailJsonUtil.toJson(req));
        CommonApiResponse apiResponse = null;
        List<IntlNewProductTarget> resultTmp = intlNewProductTargetMapper.listMeta(req);
        List<NewProdcutTargetMetaResp> result = Convert.toList(NewProdcutTargetMetaResp.class, resultTmp);
        apiResponse = new CommonApiResponse(result);
        return apiResponse;
    }
}