package com.mi.info.intl.retail.management.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.mi.info.intl.retail.model.BaseCountryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 门店等级规则实体类
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("store_grade_rule")
public class StoreGradeRule extends BaseCountryEntity {



    /**
     * 渠道类型
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 计算方法
     */
    @TableField("method")
    private String method;

    /**
     * 零售商名称
     */
    @TableField("retailer_name")
    private String retailerName;

    /**
     * 零售商代码
     */
    @TableField("retailer_code")
    private String retailerCode;

    /**
     * S级最小数量
     */
    @TableField("s_min_count")
    private Long sMinCount;

    /**
     * A级最小数量
     */
    @TableField("a_min_count")
    private Long aMinCount;

    /**
     * B级最小数量
     */
    @TableField("b_min_count")
    private Long bMinCount;

    /**
     * C级最小数量
     */
    @TableField("c_min_count")
    private Long cMinCount;

    /**
     * D级最小数量
     */
    @TableField("d_min_count")
    private Long dMinCount;

    /**
     * 规则状态
     */
    @TableField("rules_status")
    private Integer rulesStatus;

    /**
     * 申请时间
     */
    @TableField("application_time")
    private LocalDateTime applicationTime;

    /**
     * 审批时间
     */
    @TableField("approved_time")
    private LocalDateTime approvedTime;

    /**
     * 最后计算时间
     */
    @TableField("last_calculation_time")
    private LocalDateTime lastCalculationTime;

    /**
     * 审批状态
     */
    @TableField("approve_status")
    private Integer approveStatus;
    
    /**
     * 文件上传地址
     */
    @TableField("file_url")
    private String fileUrl;
}