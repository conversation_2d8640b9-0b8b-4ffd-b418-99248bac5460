package com.mi.info.intl.retail.org.domain.material.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.cooperation.task.domain.RmsCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.core.utils.IntlRetailAssert;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialPhotoGroup;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.SubmitMaterialInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.UploadMaterialData;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TargetTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterFinishReq;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialInspectionStatus;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.VerifyStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.upload.FileUploadInfo;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.enums.OperationType;
import com.mi.info.intl.retail.org.domain.material.service.NewProductInspectionDomainService;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.domain.util.ContextUtil;
import com.mi.info.intl.retail.org.domain.util.RpcUtil;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.xiaomi.cnzone.maindataapi.model.dto.common.ConfigKV2;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import com.xiaomi.cnzone.storems.api.model.req.store.CommonConfigReq;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Validator;

@Slf4j
@Service
public class NewProductInspectionDomainServiceImpl implements NewProductInspectionDomainService {

    @Resource
    private NewProductInspectionRepository newProductInspectionRepository;

    @DubboReference(group = "${maindata.dubbo.group:}", version = "1.0", check = false, interfaceClass = StoreRelateProvider.class)
    private StoreRelateProvider storeRelateProvider;

    @Autowired
    private InspectionRecordRepository inspectionRecordRepository;

    @Resource
    private PositionRepository positionRepository;

    @Autowired
    private IntlFileUploadService fileUploadService;

    @Resource
    private IntlRmsUserService intlRmsUserService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RuleConfigRepository ruleConfigRepository;

    @Autowired
    private TaskCenterServiceRpc taskCenterServiceRpc;

    @Autowired
    private InspectionHistoryRepository inspectionHistoryRepository;

    @Autowired
    private Validator validator;

    @Autowired
    private RmsCountryTimezoneService rmsCountryTimezoneService;

    @Override
    public Page<MaterialInspectionItem> pageMaterialInspection(MaterialInspectionReq request) {
        Page<MaterialInspectionItem> page = new Page<>(request.getPageNum(), request.getPageSize());
        return newProductInspectionRepository.pageMaterialInspection(page, request);
    }

    @Override
    public boolean hasUnCompletedTask(String account) {
        return newProductInspectionRepository.existsUnCompletedTaskByOwner(account);
    }

    @Override
    public CommonApiResponse<MaterialInspectionDetailResponse> getMaterialInspectionDetail(
            MaterialInspectionDetailRequest request) {
        // 参数验证
        if (request.getMaterialInspectionId() == null || request.getMaterialInspectionId() <= 0) {
            log.error("新品物料巡检ID不能为空");
            return new CommonApiResponse<>(500, "新品物料巡检ID不能为空", null);
        }
        InspectionRecordDomain inspectionRecordDomain = inspectionRecordRepository.getById(
                request.getMaterialInspectionId());
        if (inspectionRecordDomain == null) {
            log.error("新品物料巡检记录不存在: positionInspectionId={}", request.getMaterialInspectionId());
            return new CommonApiResponse<>(500, "新品物料巡检记录不存在", null);
        }
        // 获取阵地信息
        PositionDomain positionDomain = positionRepository.getByPositionCode(inspectionRecordDomain.getPositionCode());
        if (positionDomain == null) {
            log.error("阵地信息不存在: positionCode={}", inspectionRecordDomain.getPositionCode());
            return new CommonApiResponse<>(500, "阵地信息不存在", null);
        }
        // 构建响应对象
        MaterialInspectionDetailResponse response = new MaterialInspectionDetailResponse();
        // 设置门店信息

        MaterialInspectionDetailResponse.StoreInfo storeInfo = new MaterialInspectionDetailResponse.StoreInfo();
        storeInfo.setStoreName(positionDomain.getStoreName());
        storeInfo.setFrontName(positionDomain.getPositionName());
        if (inspectionRecordDomain.getPositionConstructionType() != null) {
            storeInfo.setPositionConstructionType(inspectionRecordDomain.getPositionConstructionType().getDesc());
        }
        storeInfo.setCreationTime(inspectionRecordDomain.getPositionCreationTime());
        // 安全设置状态，避免空指针
        storeInfo.setStatus(
                inspectionRecordDomain.getInspectionStatus() != null ? inspectionRecordDomain.getInspectionStatus()
                        .getDesc() : null);
        // 安全设置remark，避免空指针
        if (inspectionRecordDomain.getDisapproveReason() != null && DisapproveReasonEnum.OTHER.getCode()
                .equals(inspectionRecordDomain.getDisapproveReason().getCode())) {
            storeInfo.setRemark(inspectionRecordDomain.getRemark());
        } else {
            storeInfo.setRemark(
                    inspectionRecordDomain.getDisapproveReason() != null ? inspectionRecordDomain.getDisapproveReason()
                            .getDesc() : null);
        }
        response.setStoreInfo(storeInfo);
        // 解析上传的数据
        List<UploadMaterialData> sections = new ArrayList<>();
        String uploadDataStr = inspectionRecordDomain.getUploadData();
        if (StringUtils.isNotEmpty(uploadDataStr)) {
            UploadMaterialData[] uploadData = JsonUtil.json2bean(uploadDataStr, UploadMaterialData[].class);
            if (uploadData != null) {
                Map<String, List<String>> phontMap = getPhotoMap(uploadData);
                for (UploadMaterialData db : uploadData) {
                    UploadMaterialData item = new UploadMaterialData();
                    item.setMaterialKey(db.getMaterialKey());
                    item.setMaterialValue(processUploadDataImages(db.getMaterialValue(), phontMap));
                    sections.add(item);
                }
            }
        }
        response.setSections(sections);
        return new CommonApiResponse<>(response);
    }

    @Override
    public CommonApiResponse<String> submitMaterialInspection(SubmitMaterialInspectionRequest request) {
        // 参数验证
        if (request.getMaterialInspectionId() == null) {
            log.error("新品物料巡检提交参数不完整");
            return new CommonApiResponse<>(500, "请求参数不完整", "");
        }
        log.info("接收到新品物料巡检提交请求: materialInspectionId={}", request.getMaterialInspectionId());
        // 获取阵地巡检信息
        InspectionRecordDomain inspectionRecordDomain = inspectionRecordRepository.getById(
                request.getMaterialInspectionId());
        if (inspectionRecordDomain == null) {
            log.error("新品物料巡检记录不存在: positionInspectionId={}", request.getMaterialInspectionId());
            return new CommonApiResponse<>(500, "新品物料巡检记录不存在", "");
        }
        if (inspectionRecordDomain.getTaskStatus() != TaskStatusEnum.NOT_COMPLETED) {
            log.error("新品物料巡检记录状态错误: positionInspectionId={}", request.getMaterialInspectionId());
            return new CommonApiResponse<>(500, "新品物料巡检记录状态为非未完成状态", "");
        }
        // 当前登陆人信息从Token获取
        IntlRmsUserDto intlRmsUserDto = intlRmsUserService.getIntlRmsUserByDomainName(request.getOwner());
        IntlRetailAssert.nonNull(intlRmsUserDto, "未获取到当前登录人信息: owner=" + request.getOwner());
        IntlRetailAssert.nonNull(intlRmsUserDto.getMiId(), "未获取到当前登录人MiId: owner=" + request.getOwner());
        if (!intlRmsUserDto.getMiId().equals(inspectionRecordDomain.getInspectionOwnerMiId())) {
            log.error("当前登录人与巡检负责人不一致: currentUserMiId={}, inspectionOwnerMiId={}",
                    request.getCurrentUserMiId(), inspectionRecordDomain.getInspectionOwnerMiId());
            return new CommonApiResponse<>(500, "当前登录人与巡检负责人不一致", "");
        }

        // 获取阵地信息(数字门店阵地编码转RMS编码)
        PositionDomain positionDomain = positionRepository.getByPositionCode(inspectionRecordDomain.getPositionCode());
        if (positionDomain == null) {
            log.error("阵地信息不存在: positionCode={}", inspectionRecordDomain.getPositionCode());
            return new CommonApiResponse<>(500, "阵地信息不存在", null);
        }

        // 获取当前时间,到毫秒级
        Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);
        inspectionRecordDomain.setModifiedOn(utcInstant.toEpochMilli());
        // 记录修改人为巡检负责人
        inspectionRecordDomain.setModifiedBy(inspectionRecordDomain.getInspectionOwner());
        // 提交后更新巡检记录状态
        inspectionRecordDomain.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
        //清空被驳回二次提交时校验驳回的时间
        if (inspectionRecordDomain.getVerificationTime() != null) {
            //设置成0L，前端会自动识别为空
            inspectionRecordDomain.setVerificationTime(0L);
        }
        boolean result;
        // 更新fileUpload表，建立guid和fds_url映射
        List<FileUploadInfo.MetaData> metaDataList = extractMetaDataFromRequest(request.getSections());
        // 调用文件上传服务保存guid和url映射关系
        if (!CollectionUtils.isEmpty(metaDataList)) {
            try {
                fileUploadService.saveSimple(metaDataList, FileUploadEnum.MATERIAL_INSPECTION, request.getOwner());
                log.info("成功保存文件上传映射关系，共{}条记录", metaDataList.size());
            } catch (Exception e) {
                log.error("保存文件上传映射关系失败", e);
                // 这里不抛出异常，避免影响主流程
            }
        }
        // 将数据转换为JSON字符串
        String uploadDataJson;
        try {
            uploadDataJson = objectMapper.writeValueAsString(request.getSections());
            log.info("生成的上传数据JSON: {}", uploadDataJson);
        } catch (Exception e) {
            log.error("JSON序列化失败", e);
            return new CommonApiResponse<>(500, "数据处理失败", "");
        }
        // 保存到阵地巡检记录
        inspectionRecordDomain.setUploadData(uploadDataJson);
        // 任务中心状态变更为结束
        TaskCenterFinishReq taskCenterFinishReq = new TaskCenterFinishReq();
        // 查询TaskBatchId
        RuleConfigDomain ruleConfig = ruleConfigRepository.getByRuleCode(inspectionRecordDomain.getRuleCode());
        taskCenterFinishReq.setTaskBatchId(ruleConfig.getTaskBatchId());
        taskCenterFinishReq.setMid(inspectionRecordDomain.getInspectionOwnerMiId());
        taskCenterFinishReq.setOperatorMid(inspectionRecordDomain.getInspectionOwnerMiId());
        taskCenterFinishReq.setOrgId(positionDomain.getPositionCode());
        taskCenterServiceRpc.outerTaskFinish(taskCenterFinishReq);
        // 提交后更新任务状态-已完成
        inspectionRecordDomain.setTaskStatus(TaskStatusEnum.COMPLETED);

        inspectionRecordDomain.setTaskCompletionTime(utcInstant.toEpochMilli());
        // 补充事务用来回滚状态
        result = inspectionRecordRepository.update(inspectionRecordDomain);
        log.info("更新阵地巡检记录: id={}, result={}", inspectionRecordDomain.getId(), result);
        if (!result) {
            log.error("阵地巡检数据更新失败");
            return new CommonApiResponse<>(500, "数据保存失败", "");
        } else {
            // 新建历史操作记录单
            InspectionHistoryDomain history = new InspectionHistoryDomain();
            history.setInspectionRecordId(inspectionRecordDomain.getId());
            history.setOperationType(OperationType.SUBMIT);
            history.setOperator(inspectionRecordDomain.getInspectionOwner());
            history.setOperationTime(utcInstant.toEpochMilli());
            history.setCreateTime(utcInstant.toEpochMilli());
            history.setUploadData(uploadDataJson);
            boolean historyResult = inspectionHistoryRepository.save(history);
            if (!historyResult) {
                log.warn("保存新品物料巡检历史记录失败，但主流程已完成");
            }
            // 记录history对象的详细信息到日志
            log.info("保存的新品物料巡检历史记录信息: {}", history);
            return new CommonApiResponse<>("");
        }

    }

    @Override
    public List<String> getMaterialSample(Long materialInspectionId) {
        InspectionRecordDomain inspectionRecordDomain = inspectionRecordRepository.getById(materialInspectionId);
        if (inspectionRecordDomain == null) {
            log.warn("position inspection record not found. id: {}", materialInspectionId);
            return Collections.emptyList();
        }
        String ruleCode = inspectionRecordDomain.getRuleCode();
        RuleConfigDomain ruleConfig = ruleConfigRepository.getByRuleCode(ruleCode);
        String posmMaterials = ruleConfig.getPosmMaterials();
        if (StringUtils.isNotBlank(posmMaterials)) {
            // posmMaterials = "{\"posmMaterials\": [\"poster\", \"sticker\"]}";
            List<String> metaDataList = new ArrayList<>();
            try {
                JSONObject jsonObject = JSONObject.parseObject(posmMaterials);
                JSONArray jsonArray = jsonObject.getJSONArray("posmMaterials");
                for (int i = 0; i < jsonArray.size(); i++) {
                    String materialName = jsonArray.getString(i);
                    metaDataList.add(materialName);
                }
            } catch (Exception e) {
                log.error("解析POSM材质列表失败", e);
                return Collections.emptyList();
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<MaterialInspectionOperationHistoryResponse> getMaterialInspectionOperationHistory(
            Long materialInspectionId) {
        InspectionRecordDomain record = inspectionRecordRepository.getById(materialInspectionId);
        if (record == null) {
            log.warn("position inspection record not found. id: {}", materialInspectionId);
            return Collections.emptyList();
        }
        List<InspectionHistoryDomain> historyList = inspectionHistoryRepository.getByInspectionRecordId(
                materialInspectionId);
        List<MaterialInspectionOperationHistoryResponse> historyItems = new ArrayList<>();
        for (InspectionHistoryDomain historyDomain : historyList) {
            MaterialInspectionOperationHistoryResponse historyItem = new MaterialInspectionOperationHistoryResponse();
            historyItem.setOperationTypeDesc(I18nDesc.safeGetDesc(historyDomain.getOperationType()));
            historyItem.setOperator(historyDomain.getOperator());
            historyItem.setDisapproveReasonDesc(
                    I18nDesc.getDescByCode(DisapproveReasonEnum.class, historyDomain.getDisapproveReason()));
            historyItem.setOperationTime(historyDomain.getOperationTime());
            historyItem.setDisapproveReasonDesc(
                    I18nDesc.getDescByCode(DisapproveReasonEnum.class, historyDomain.getDisapproveReason()));
            if (historyDomain.getUploadData() != null) {
                UploadMaterialData[] uploadData = JsonUtil.json2bean(historyDomain.getUploadData(),
                        UploadMaterialData[].class);
                Map<String, List<String>> photoMap = getPhotoMap(uploadData);
                List<UploadMaterialData> sections = new ArrayList<>();
                for (UploadMaterialData uploadDatum : uploadData) {
                    UploadMaterialData data = new UploadMaterialData();
                    data.setMaterialValue(processUploadDataImages(uploadDatum.getMaterialValue(), photoMap));
                    data.setMaterialKey(uploadDatum.getMaterialKey());
                    sections.add(data);
                }
                historyItem.setSections(sections);
            }
            historyItems.add(historyItem);
        }
        return historyItems;
    }

    private List<FileUploadInfo.MetaData> extractMetaDataFromRequest(List<UploadMaterialData> sections) {
        List<FileUploadInfo.MetaData> metaDataList = new ArrayList<>();
        for (UploadMaterialData db : sections) {
            FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData(db.getMaterialValue().getGuid(),
                    db.getMaterialValue().getImages());
            metaDataList.add(metaData);
        }
        return metaDataList;
    }

    private Map<String, List<String>> getPhotoMap(UploadMaterialData[] uploadData) {
        List<String> guids = new ArrayList<>();
        for (UploadMaterialData db : uploadData) {
            if (db.getMaterialValue() != null && StringUtils.isNotBlank(db.getMaterialValue().getGuid())) {
                guids.add(db.getMaterialValue().getGuid());
            }
        }
        return fileUploadService.getUrlsByModuleAndGuids(FileUploadEnum.POSITION_INSPECTION, guids);
    }

    private MaterialPhotoGroup processUploadDataImages(MaterialPhotoGroup materialValue,
            Map<String, List<String>> phontMap) {
        if (materialValue == null) {
            return null;
        }
        MaterialPhotoGroup result = new MaterialPhotoGroup();
        result.setName(materialValue.getName());
        result.setGuid(materialValue.getGuid());
        result.setImages(materialValue.getImages());
        if (phontMap != null && phontMap.containsKey(materialValue.getGuid())) {
            result.setImages(phontMap.get(materialValue.getGuid()));
        }
        return result;
    }

    @Override
    public MaterialInspectionRecordSelectorItem getSelectorItemList() {
        MaterialInspectionRecordSelectorItem item = new MaterialInspectionRecordSelectorItem();
        String language = RequestContextInfo.getLanguage();
        CommonConfigReq req = new CommonConfigReq();
        req.setBusinessScene("channelRetail");
        req.setLanguage(language);
        CommonConfigDTO2 commonConfig = RpcUtil.getRpcResult(storeRelateProvider.get3CCommonConfig(req));
        // 从3CCommonConfig获取数据
        item.setChannel(toOptionalItems(commonConfig.getChannelType()));
        item.setPositionType(toOptionalItems(commonConfig.getPositionType()));
        item.setCategory(toOptionalItems(commonConfig.getPositionCategory()));
        item.setStoreGrade(toOptionalItems(commonConfig.getStoreGradings(), ConfigKV2::getValue, ConfigKV2::getValue));
        // 从枚举列表获取数据
        List<OptionalItem<Integer>> taskStatusItemList = I18nDesc.toOptionalItems(TaskStatusEnum.class);
        taskStatusItemList.removeIf(e -> StringUtils.isEmpty(e.getValue()));
        item.setTaskStatus(taskStatusItemList);
        item.setInspectionStatus(I18nDesc.toOptionalItems(MaterialInspectionStatus.class));
        item.setTaskType(toOptionalItems(TaskTypeEnum.class, TaskTypeEnum::getCode, TaskTypeEnum::getName));

        return item;
    }

    @Override
    public void verifyMaterialInspection(MaterialInspectionVerifyRequest request) {
        IntlRetailAssert.check(validator.validate(request));
        // todo 拒绝原因校验
        Long id = request.getId();
        MaterialInspectionDomain domain = newProductInspectionRepository.getById(id);
        IntlRetailAssert.nonNull(domain, "Inspection record not found. id:" + id);
        domain.checkAndSetVerifyStatus(request.getVerifyStatus());

        Long miID = ContextUtil.getMiID();
        IntlRmsUserDto user = intlRmsUserService.getIntlRmsUserByMiId(miID).orElse(null);
        IntlRetailAssert.nonNull(user, "User info not found. id:" + miID);

        domain.setVerifierMiId(miID);
        domain.setVerifier(user.getDomainName());
        domain.setUpdatedBy(user.getDomainName());
        long currentTimeStamp = System.currentTimeMillis();
        domain.setVerificationTime(currentTimeStamp);
        domain.setUpdatedTime(currentTimeStamp);
        domain.calcInspectionStatus();
        domain.setDisapproveReason(request.getDisapproveReason());
        domain.setRemark(request.getRemark());

        int updated = newProductInspectionRepository.updateVerifyStatus(domain);
        if (updated <= 0) {
            log.warn("another thread updated this positions's verification status.");
            return;
        }

        if (VerifyStatusEnum.FAILED.equals(domain.getVerifyStatus())) {
            // 判断是否是首销期
            RuleConfigDomain rule = ruleConfigRepository.getByRuleCode(domain.getRuleCode());
            IntlRetailAssert.nonNull(rule, "Inspection rule not found. code:" + domain.getRuleCode());
            TargetTypeEnum taskType = rule.getTargetType();
            if (TargetTypeEnum.FIRST_SALES_PERIOD.equals(taskType) && domain.isWithinPeriod()) {
                taskCenterServiceRpc.resetUserCurrentTaskEventStatus(domain);
            }
        }

    }


    @Override
    public PageResponse<InspectionRecordPageItem> getMaterialInspectionRecordPageItem(
            InspectionRecordPageRequest request) {

        Page<InspectionRecordPageItem> page = newProductInspectionRepository.getMaterialInspectionRecordPageItem(
                request);

        List<InspectionRecordPageItem> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            List<String> domainNames = new ArrayList<>();
            List<String> guids = new ArrayList<>();
            List<String> countryCodes = new ArrayList<>();
            for (InspectionRecordPageItem record : records) {
                if (!domainNames.contains(record.getVerifier())) {
                    domainNames.add(record.getVerifier());
                }
                if (!countryCodes.contains(record.getCountryCode())) {
                    countryCodes.add(record.getCountryCode());
                }
                String uploadData = record.getUploadData();
                if (!StringUtils.isEmpty(uploadData)) {
                    List<UploadMaterialData> uploadMaterialDataList = JsonUtil.jsonArr2beanList(uploadData,
                            UploadMaterialData.class);
                    List<String> subGuids = uploadMaterialDataList.stream().map(UploadMaterialData::getMaterialValue)
                            .map(MaterialPhotoGroup::getGuid).collect(
                                    Collectors.toList());
                    record.setSections(uploadMaterialDataList);
                    guids.addAll(subGuids);
                }
            }

            List<IntlRmsUserDto> userDtos = intlRmsUserService.getIntlRmsUserByDomainNames(domainNames);
            Map<String, IntlRmsUserDto> userDtoMap =
                    userDtos.stream().collect(Collectors.toMap(IntlRmsUserDto::getDomainName, Function.identity()));

            Map<String, String> countryAreaMap =
                    rmsCountryTimezoneService.getCountryAreaMapByCountryCodes(countryCodes);

            Map<String, List<String>> guidMap =
                    fileUploadService.getUrlsByModuleAndGuids(FileUploadEnum.MATERIAL_INSPECTION, guids);

            for (InspectionRecordPageItem record : records) {
                record.setCountry(countryAreaMap.getOrDefault(record.getCountryCode(), record.getCountryCode()));
                record.setRegion(countryAreaMap.getOrDefault(record.getRegionCode(), record.getRegionCode()));
                IntlRmsUserDto user = userDtoMap.get(record.getVerifier());
                if (user != null) {
                    record.setVerifier(user.getEnglishName() + "(" + user.getDomainName() + ")");
                }
                List<String> pictures =
                        Optional.ofNullable(record.getSections()).orElse(Collections.emptyList()).stream()
                                .map(UploadMaterialData::getMaterialValue).map(MaterialPhotoGroup::getGuid)
                                .map(guidMap::get).filter(Objects::nonNull).flatMap(List::stream)
                                .collect(Collectors.toList());
                record.setPictures(pictures);
            }
        }

        return new PageResponse<>(page.getTotal(), request.getPageNum(), request.getPageSize(), records);
    }

    private static <T extends Enum<T>, R extends Serializable> List<OptionalItem<R>> toOptionalItems(Class<T> clz,
                                                                                                     Function<T, R> kf,
                                                                                                     Function<T, String> vf) {
        return Arrays.stream(clz.getEnumConstants()).map(e -> new OptionalItem<>(kf.apply(e), vf.apply(e)))
                .collect(Collectors.toList());
    }

    private List<OptionalItem<String>> toOptionalItems(List<ConfigKV2> configKVs, Function<ConfigKV2, Object> kf,
            Function<ConfigKV2, Object> vf) {
        if (CollectionUtils.isEmpty(configKVs)) {
            return Collections.emptyList();
        }
        return configKVs.stream().map(kv -> new OptionalItem<>(Objects.toString(kf.apply(kv), null),
                Objects.toString(vf.apply(kv), null))).collect(Collectors.toList());
    }

    private List<OptionalItem<String>> toOptionalItems(List<ConfigKV2> configKVs) {
        return toOptionalItems(configKVs, ConfigKV2::getKey, ConfigKV2::getValue);
    }
}
