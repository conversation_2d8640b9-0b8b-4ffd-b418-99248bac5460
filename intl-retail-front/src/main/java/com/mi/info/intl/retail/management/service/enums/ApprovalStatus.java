package com.mi.info.intl.retail.management.service.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApprovalStatus {
    /**
     * 审批中
     */
    APPROVAL_ING(1, "approval"),
    /**
     *已撤回
     */
    ROLLBACK(2, "rollback"),
    
    /**
     * 驳回
     */
    REJECT(3, "reject"),
    
    /**
     * 通过
     */
    PASS(4, "pass");
    
    private final Integer code;
    private final String value;
}
