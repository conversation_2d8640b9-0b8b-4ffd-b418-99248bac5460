package com.mi.info.intl.retail.management.service.enums;

import java.util.Arrays;
import java.util.Objects;

public enum SubmitTypeEnum {
    SUBMIT(1, "提交"),
    //保存
    SAVE(2, "保存");

    SubmitTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private final Integer type;
    private final String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static SubmitTypeEnum getByType(Integer type) {
        for (SubmitTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 是否为提交
     */
    public static boolean isSubmit(Integer type) {
        return Objects.equals(SUBMIT.getType(), type);
    }

    /**
     * 是否为保存
     */
    public static boolean isSave(Integer type) {
        return Objects.equals(SAVE.getType(), type);
    }

    /**
     * 是否为正确的枚举类型
     *
     * @param type
     * @return
     */
    public static boolean isRightType(Integer type) {
        if (Objects.isNull(type)) {
            return false;
        }
        boolean b = Arrays.stream(values()).noneMatch(e -> Objects.equals(e.getType(), type));
        return b;
    }
}
