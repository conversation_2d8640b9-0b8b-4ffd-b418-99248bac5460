package com.mi.info.intl.retail.ldu.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
@ExcelIgnoreUnannotated
public class IntlLduTargetExcel {


    /**
     * 区域
     */
    @ExcelProperty(value = "区域", index = 0)
    private String region;

    /**
     * 区域编码
     */
    @ExcelProperty(value = "区域编码", index = 1)
    private String regionCode;

    /**
     * 国家
     */
    @ExcelProperty(value = "国家", index = 2)
    private String country;

    /**
     * 国家编码
     */
    @ExcelProperty(value = "国家编码", index = 3)
    private String countryCode;

    /**
     * 渠道类型
     */
    @ExcelProperty(value = "渠道类型", index = 4)
    private String channelType;

    /**
     * 零售商编码
     */
    @ExcelProperty(value = "零售商编码", index = 5)
    private String retailerCode;

    /**
     * 零售商名称
     */
    @ExcelProperty(value = "零售商名称", index = 6)
    private String retailerName;

    /**
     * 产品线
     */
    @ExcelProperty(value = "产品线", index = 7)
    private String productLine;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID", index = 8)
    private String goodsId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称", index = 9)
    private String goodsName;

    /**
     * 项目代码
     */
    @ExcelProperty(value = "项目代码", index = 10)
    private String projectCode;

    /**
     * RAM容量
     */
    @ExcelProperty(value = "RAM容量", index = 11)
    private String ramCapacity;

    /**
     * 目标创建日期
     */
    @ExcelProperty(value = "目标创建日期", index = 12)
    private Date targetCreateDate;

    /**
     * 目标修改日期
     */
    @ExcelProperty(value = "目标修改日期", index = 13)
    private Date targetUpdateDate;

    /**
     * ROM容量
     */
    @ExcelProperty(value = "ROM容量", index = 14)
    private String romCapacity;

    /**
     * 创建人ID
     */
    @ExcelProperty(value = "创建人ID", index = 15)
    private String createUserId;

    /**
     * 创建人姓名
     */
    @ExcelProperty(value = "创建人姓名", index = 16)
    private String createUserName;

    /**
     * 修改人ID
     */
    @ExcelProperty(value = "修改人ID", index = 17)
    private String updateUserId;

    /**
     * 修改人名称
     */
    @ExcelProperty(value = "修改人名称", index = 18)
    private String updateUserName;


    /**
     * 目标覆盖门店数
     */
    @ExcelProperty(value = "目标覆盖门店数", index = 19)
    private int targetCoveredStores;

    /**
     * 实际覆盖门店数
     */
    @ExcelProperty(value = "实际覆盖门店数", index = 20)
    private int actualCoveredStores;

    /**
     * 目标销出样品数
     */
    @ExcelProperty(value = "目标销出样品数", index = 21)
    private int targetSampleOut;

    /**
     * 实际销出样品数
     */
    @ExcelProperty(value = "实际销出样品数", index = 22)
    private int actualSampleOut;
}
