package com.mi.info.intl.retail.ldu.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@ExcelIgnoreUnannotated
public class IntlLduTargetEnExcel {

    /**
     * 区域
     */
    @ExcelProperty(value = "Region", index = 0)
    private String region;

    /**
     * 区域编码
     */
    @ExcelProperty(value = "Region Code", index = 1)
    private String regionCode;

    /**
     * 国家
     */
    @ExcelProperty(value = "Country", index = 2)
    private String country;

    /**
     * 国家编码
     */
    @ExcelProperty(value = "Country Code", index = 3)
    private String countryCode;

    /**
     * 渠道类型
     */
    @ExcelProperty(value = "Channel Type", index = 4)
    private String channelType;

    /**
     * 零售商编码
     */
    @ExcelProperty(value = "Retailer Code", index = 5)
    private String retailerCode;

    /**
     * 零售商名称
     */
    @ExcelProperty(value = "Retailer Name", index = 6)
    private String retailerName;

    /**
     * 产品线
     */
    @ExcelProperty(value = "Product Line", index = 7)
    private String productLine;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "Product ID", index = 8)
    private String goodsId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "Product Name", index = 9)
    private String goodsName;

    /**
     * 项目代码
     */
    @ExcelProperty(value = "Project Code", index = 10)
    private String projectCode;

    /**
     * RAM容量
     */
    @ExcelProperty(value = "RAM Capacity", index = 11)
    private String ramCapacity;

    /**
     * 目标创建日期
     */
    @ExcelProperty(value = "Target Create Date", index = 12)
    private Date targetCreateDate;

    /**
     * 目标修改日期
     */
    @ExcelProperty(value = "Target Update Date", index = 13)
    private Date targetUpdateDate;

    /**
     * ROM容量
     */
    @ExcelProperty(value = "ROM Capacity", index = 14)
    private String romCapacity;

    /**
     * 创建人ID
     */
    @ExcelProperty(value = "Creator ID", index = 15)
    private String createUserId;

    /**
     * 创建人姓名
     */
    @ExcelProperty(value = "Creator Name", index = 16)
    private String createUserName;

    /**
     * 修改人ID
     */
    @ExcelProperty(value = "Updater ID", index = 17)
    private String updateUserId;

    /**
     * 修改人名称
     */
    @ExcelProperty(value = "Updater Name", index = 18)
    private String updateUserName;

    /**
     * 目标覆盖门店数
     */
    @ExcelProperty(value = "Target Covered Stores", index = 19)
    private int targetCoveredStores;

    /**
     * 实际覆盖门店数
     */
    @ExcelProperty(value = "Actual Covered Stores", index = 20)
    private int actualCoveredStores;

    /**
     * 目标销出样品数
     */
    @ExcelProperty(value = "Target Samples Sold", index = 21)
    private int targetSampleOut;

    /**
     * 实际销出样品数
     */
    @ExcelProperty(value = "Actual Samples Sold", index = 22)
    private int actualSampleOut;

}
