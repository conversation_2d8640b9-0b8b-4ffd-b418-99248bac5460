package com.mi.info.intl.retail.ldu.enums;

import lombok.Getter;

@Getter
public enum ExportExcelEnum {

    /**
     * LDU 统计列表
     */
    LDU_REPORT("LDU 上报明细", "LDUReportDetails", "lduExportHandleXxlJob"),

    LDU_TARGET_REPORT("LDU 目标维护明细", "LDUTargetReportDetails", "lduExportHandleXxlJob"),

    LDU_PLAN_REPORT("LDU 计划维护明细", "LDUPlanReportDetails", "lduExportHandleXxlJob");

    private final String excelName;
    private final String excelEnName;
    private final String jobKey;

    ExportExcelEnum(String excelName, String excelEnName, String jobKey) {
        this.excelName = excelName;
        this.excelEnName = excelEnName;
        this.jobKey = jobKey;
    }

}


