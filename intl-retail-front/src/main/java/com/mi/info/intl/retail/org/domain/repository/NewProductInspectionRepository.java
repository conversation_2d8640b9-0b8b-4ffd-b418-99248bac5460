package com.mi.info.intl.retail.org.domain.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;

import java.util.List;

public interface NewProductInspectionRepository {
    
    Page<MaterialInspectionItem> pageMaterialInspection(Page<MaterialInspectionItem> page, MaterialInspectionReq req);
    
    boolean existsUnCompletedTaskByOwner(String inspectionOwner);

    Page<InspectionRecordPageItem> getMaterialInspectionRecordPageItem(
            InspectionRecordPageRequest inspectionRecordPageRequest);

    void save(MaterialInspectionDomain record);

    MaterialInspectionDomain getLatestModifiedRecord(String businessCode);

    MaterialInspectionDomain getById(Long id);

    int updateVerifyStatus(MaterialInspectionDomain record);

    void updateInspectionRecordToExpired(List<Long> taskInstanceIds);
}
