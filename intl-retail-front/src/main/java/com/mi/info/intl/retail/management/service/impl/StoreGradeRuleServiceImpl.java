package com.mi.info.intl.retail.management.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BpmService;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeRuleService;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.*;
import com.mi.info.intl.retail.management.dto.StoreGradeBatchData;
import com.mi.info.intl.retail.management.entity.CommonApproveLog;
import com.mi.info.intl.retail.management.entity.StoreGradeRelation;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.CommonApproveLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.service.enums.*;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.commons.utils.StringUtils;
import com.xiaomi.cnzone.storeapi.model.v2.assets.req.AssetRecycleSubmitRequest;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店等级规则服务实现类
 */

@DubboService(group = "${retail.dubbo.group:}", interfaceClass = StoreGradeRuleService.class)
@Slf4j
@Service
public class StoreGradeRuleServiceImpl extends ServiceImpl<StoreGradeRuleMapper, StoreGradeRule> implements StoreGradeRuleService {

    @Resource
    private CommonApproveLogMapper commonApproveLogMapper;

    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Resource
    private BpmService bpmService;

    @Resource
    private StoreGradeRelationMapper storeGradeRelationMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Boolean> saveStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq) {
        try {
            log.info("Start saving store grade rule, request parameters: {}", storeGradeRuleReq);

            if (storeGradeRuleReq == null) {
                log.error("Store grade rule request object is empty");
                throw new RuntimeException("Store grade rule request object is empty");
            }

            // 验证零售商列表
            if (!Objects.equals("IR", storeGradeRuleReq.getChannelType()) && Objects.isNull(storeGradeRuleReq.getRetailerCode())) {
                log.error("Retailer is empty");
                throw new RuntimeException("Retailer is empty");
            }
            StoreGradeRule oldStoreGradeRule = null;
            if (Objects.isNull(storeGradeRuleReq.getId()) || Objects.isNull(storeGradeRuleReq.getRuleLogId())) {
                if (Objects.equals("IR", storeGradeRuleReq.getChannelType())) {
                    LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(StoreGradeRule::getCountryCode, storeGradeRuleReq.getCountryCode())
                            .eq(StoreGradeRule::getChannelType, storeGradeRuleReq.getChannelType());

                    StoreGradeRule existingRule = this.getOne(queryWrapper);
                    if (existingRule != null) {
                        log.warn("IR rule already exists for current country {}, skipping save", storeGradeRuleReq.getCountryCode());
                        throw new RuntimeException("IR rule already exists for current country, please do not add duplicate");
                    }
                } else {
                    LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(StoreGradeRule::getRetailerCode, storeGradeRuleReq.getRetailerCode())
                            .eq(StoreGradeRule::getChannelType, storeGradeRuleReq.getChannelType());

                    StoreGradeRule existingRule = this.getOne(queryWrapper);
                    if (existingRule != null) {
                        log.warn("Rule already exists for retailer code {}, skipping save", storeGradeRuleReq.getRetailerCode());
                        throw new RuntimeException("Rule already exists for retailer, please do not add duplicate");
                    }
                }
            }
            if (Objects.nonNull(storeGradeRuleReq.getId())) {
                oldStoreGradeRule = this.getById(storeGradeRuleReq.getId());
            }

            /*if (Objects.equals("IR", storeGradeRuleReq.getChannelType())) {
                LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(StoreGradeRule::getCountryCode, storeGradeRuleReq.getCountryCode())
                        .eq(StoreGradeRule::getChannelType, storeGradeRuleReq.getChannelType());

                StoreGradeRule existingRule = this.getOne(queryWrapper);
                if (existingRule != null) {
                    log.warn("当前国家 {} 已存在IR规则，跳过保存", storeGradeRuleReq.getCountryCode());
                    throw new RuntimeException("当前国家已存在IR规则，请勿重复添加");
                }
            } else {
                LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(StoreGradeRule::getRetailerCode, storeGradeRuleReq.getRetailerCode())
                        .eq(StoreGradeRule::getChannelType, storeGradeRuleReq.getChannelType());

                StoreGradeRule existingRule = this.getOne(queryWrapper);
                if (existingRule != null) {
                    log.warn("零售商代码 {} 已存在规则，跳过保存", storeGradeRuleReq.getRetailerCode());
                    throw new RuntimeException("零售商已存在规则，请勿重复添加");
                }
            }*/

            boolean submitFlag = SubmitTypeEnum.SUBMIT.getType().equals(storeGradeRuleReq.getSubmitType());

            StoreGradeRule storeGradeRule = new StoreGradeRule();

            // 复制基本属性
            BeanUtils.copyProperties(storeGradeRuleReq, storeGradeRule);

            // 设置零售商信息
            LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
            retailerQueryWrapper.eq(IntlRmsRetailer::getCrmCode, storeGradeRuleReq.getRetailerCode());
            List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
            if (CollectionUtils.isEmpty(intlRmsRetailers)) {
                log.error("Retailer information is empty");
                throw new RuntimeException("Retailer information is empty");
            }
            storeGradeRule.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
            storeGradeRule.setRetailerCode(storeGradeRuleReq.getRetailerCode());

            // 设置时间戳
            storeGradeRule.setApplicationTime(LocalDateTime.now());

            if (submitFlag) {
                // 设置规则状态为待审核
                storeGradeRule.setRulesStatus(RuleStatusEnum.IN_APPROVAL.getCode());
            } else {
                // 待保存状态
                storeGradeRule.setRulesStatus(RuleStatusEnum.DRAFT.getCode());
            }

            //TODO 判断是否是第一次提交
            if (Objects.isNull(storeGradeRule.getId())) {
                this.save(storeGradeRule);
            } else if (Objects.nonNull(oldStoreGradeRule) && !Objects.equals(RuleStatusEnum.IN_EFFECT.getCode(), oldStoreGradeRule.getRulesStatus())) {
                this.updateById(storeGradeRule);
            }
            //storeGradeRules.add(storeGradeRule);
            CommonApproveLog commonApproveLog = new CommonApproveLog();
            if (Objects.nonNull(storeGradeRuleReq.getRuleLogId())) {
                commonApproveLog = commonApproveLogMapper.selectById(storeGradeRuleReq.getRuleLogId());
                commonApproveLog.setTargetBody(JacksonUtil.toStr(storeGradeRule));
            } else {
                commonApproveLog.setBusinessKey("store_grade_rule");
                commonApproveLog.setBusinessId(storeGradeRule.getId().toString());
                commonApproveLog.setFlowStatus(0);
                commonApproveLog.setCreatedAt(System.currentTimeMillis());
                commonApproveLog.setUpdatedAt(System.currentTimeMillis());
                commonApproveLog.setApplicationTime(new Date());
                commonApproveLog.setTargetBody(JacksonUtil.toStr(storeGradeRule));
                if (Objects.nonNull(oldStoreGradeRule)) {
                    commonApproveLog.setOriginBody(JacksonUtil.toStr(oldStoreGradeRule));
                }
            }
            if (submitFlag) {
                //TODO fz调用审批接口
                commonApproveLog.setFlowInstId("");
            }
            if (Objects.isNull(commonApproveLog.getId())) {
                commonApproveLogMapper.insert(commonApproveLog);
            } else {
                commonApproveLogMapper.updateById(commonApproveLog);
            }

            if (Objects.equals(TieringModificationMethodEnum.MANUAL_UPLOAD.getValue(), storeGradeRuleReq.getMethod())) {
                //TODO fz手动上传特殊处理
            }
            // 构建审批log表
            // 保存记录

            return new CommonApiResponse<>(true);

        } catch (Exception e) {
            log.error("Failed to save store grade rule", e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Boolean> updateStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq) {
        try {
            log.info("Start updating store grade rule, request parameters: {}", storeGradeRuleReq);

            if (storeGradeRuleReq == null || storeGradeRuleReq.getId() == null) {
                log.error("Store grade rule request object is empty or ID is empty");
                throw new RuntimeException("Store grade rule request object is empty or ID is empty");
            }

            // 查询现有记录
            StoreGradeRule existingRule = this.getById(storeGradeRuleReq.getId());
            if (existingRule == null) {
                log.error("Store grade rule with ID {} not found", storeGradeRuleReq.getId());
                throw new RuntimeException("Store grade rule with ID " + storeGradeRuleReq.getId() + " not found");
            }
            StoreGradeRule newStoreGradeRule = new StoreGradeRule();

            // 更新基本属性
            BeanUtils.copyProperties(storeGradeRuleReq, newStoreGradeRule);
            newStoreGradeRule.setId(existingRule.getId());
            newStoreGradeRule.setApplicationTime(existingRule.getApplicationTime());
            newStoreGradeRule.setRulesStatus(0);
            //newStoreGradeRule.setLastCalculationTime(existingRule.getLastCalculationTime());
            newStoreGradeRule.setApproveStatus(0);
            // 如果有零售商信息，更新第一个零售商的信息
            if (Objects.nonNull(storeGradeRuleReq.getRetailerCode())) {
                LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
                retailerQueryWrapper.eq(IntlRmsRetailer::getCrmCode, storeGradeRuleReq.getRetailerCode());
                List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
                if (CollectionUtils.isEmpty(intlRmsRetailers)) {
                    log.error("Retailer information is empty");
                    throw new RuntimeException("Retailer information is empty");
                }
                newStoreGradeRule.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
                newStoreGradeRule.setRetailerCode(storeGradeRuleReq.getRetailerCode());
            }

            // 设置更新时间
            existingRule.setLastCalculationTime(LocalDateTime.now());
            CommonApproveLog commonApproveLog = new CommonApproveLog();
            commonApproveLog.setBusinessKey("store_grade_rule");
            commonApproveLog.setBusinessId(existingRule.getId().toString());
            commonApproveLog.setFlowStatus(0);
            commonApproveLog.setCreatedAt(System.currentTimeMillis());
            commonApproveLog.setUpdatedAt(System.currentTimeMillis());
            commonApproveLog.setApplicationTime(new Date());
            commonApproveLog.setOriginBody(JacksonUtil.toStr(existingRule));
            commonApproveLog.setTargetBody(JacksonUtil.toStr(newStoreGradeRule));
            //TODO fz调用审批接口
            commonApproveLog.setFlowInstId("");
            commonApproveLogMapper.insert(commonApproveLog);

            // 更新记录
            existingRule.setRulesStatus(0);
            existingRule.setApproveStatus(0);
            boolean result = this.updateById(existingRule);

            log.info("Store grade rule update completed, updated record ID: {}", storeGradeRuleReq.getId());
            return new CommonApiResponse<>(result);

        } catch (Exception e) {
            log.error("Failed to update store grade rule", e);
            throw e;
        }
    }

    @Override
    public CommonApiResponse<StoreGradeRulePageResp> query(RuleQueryReq request) {
        if (request == null || (request.getRuleId() == null && request.getRuleLogId() == null) || request.getNode() == null) {
            return null;
        }
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        if (request.getRuleId() != null) {
            queryWrapper.eq(CommonApproveLog::getBusinessId, request.getRuleId());
        }
        if (request.getRuleLogId() != null) {
            queryWrapper.eq(CommonApproveLog::getId, request.getRuleLogId());
        }
        /*if (Objects.equals("create", request.getNode())) {
            queryWrapper.isNull(CommonApproveLog::getOriginBody);
        } else {
            queryWrapper.isNotNull(CommonApproveLog::getOriginBody);
        }*/
        queryWrapper.eq(CommonApproveLog::getBusinessKey, "store_grade_rule")
                .orderByDesc(CommonApproveLog::getId);
        List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(commonApproveLogs)) {
            return null;
        }
        StoreGradeRule storeGradeRule = JacksonUtil.parseObj(commonApproveLogs.get(0).getTargetBody(), StoreGradeRule.class);
        StoreGradeRulePageResp storeGradeRulePageResp = new StoreGradeRulePageResp();
        BeanUtils.copyProperties(storeGradeRule, storeGradeRulePageResp);
        return new CommonApiResponse<>(storeGradeRulePageResp);
    }

    @Override
    public CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> queryDetail(RuleQueryReq request) {
        try {
            log.info("Start querying store grade rule details, request parameters: {}", request);
            
            if (request == null || request.getRuleId() == null) {
                throw new RuntimeException("Parameter error");
            }
            
            // 查询规则表填充基础信息
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getId, request.getRuleId());
            
            StoreGradeRule storeGradeRule = this.getOne(queryWrapper);
            if (storeGradeRule == null) {
                throw new RuntimeException("Corresponding store grade rule not found");
            }
            
            // 构建规则详情
            StoreGradeRuleDetailResp.RuleDetails ruleDetails = buildRuleDetails(storeGradeRule);
            
            log.info("Query store grade rule details completed, rule ID: {}", request.getRuleId());
            return new CommonApiResponse<>(ruleDetails);
            
        } catch (Exception e) {
            log.error("Failed to query store grade rule details", e);
            throw e;
        }
    }

    @Override
    public CommonApiResponse<List<StoreGradeRuleDetailResp.RuleModificationLog>> queryApprovalList(RuleQueryReq request) {
        try {
            log.info("Start querying store grade rule approval list, request parameters: {}", request);
            
            if (request == null || request.getRuleId() == null) {
                throw new RuntimeException("Parameter error");
            }
            
            // 查询审批日志表填充修改记录
            List<StoreGradeRuleDetailResp.RuleModificationLog> modificationLogs = buildModificationLogs(request.getRuleId());
            
            log.info("Query store grade rule approval list completed, rule ID: {}", request.getRuleId());
            return new CommonApiResponse<>(modificationLogs);
            
        } catch (Exception e) {
            log.error("Failed to query store grade rule approval list", e);
            throw e;
        }
    }
    
    /**
     * 构建规则详情
     *
     * @param storeGradeRule 门店等级规则实体
     * @return 规则详情
     */
    private StoreGradeRuleDetailResp.RuleDetails buildRuleDetails(StoreGradeRule storeGradeRule) {
        StoreGradeRuleDetailResp.RuleDetails ruleDetails = new StoreGradeRuleDetailResp.RuleDetails();
        
        // 设置基本信息
        ruleDetails.setId(storeGradeRule.getId());
        ruleDetails.setChannelType(storeGradeRule.getChannelType());
        ruleDetails.setModificationMethod(""); // TODO: 根据业务逻辑设置修改方式

        // 设置数量信息
        ruleDetails.setSMinCount(storeGradeRule.getSMinCount() != null ? storeGradeRule.getSMinCount().intValue() : null);
        ruleDetails.setAMinCount(storeGradeRule.getAMinCount() != null ? storeGradeRule.getAMinCount().intValue() : null);
        ruleDetails.setBMinCount(storeGradeRule.getBMinCount() != null ? storeGradeRule.getBMinCount().intValue() : null);
        ruleDetails.setCMinCount(storeGradeRule.getCMinCount() != null ? storeGradeRule.getCMinCount().intValue() : null);
        ruleDetails.setDMinCount(storeGradeRule.getDMinCount() != null ? storeGradeRule.getDMinCount().intValue() : null);
        
        // 设置零售商信息
        ruleDetails.setRetailerCode(storeGradeRule.getRetailerCode());
        ruleDetails.setRetailerName(storeGradeRule.getRetailerName());
        
        return ruleDetails;
    }
    
    /**
     * 构建修改日志
     *
     * @param ruleId 规则ID
     * @return 修改日志列表
     */
    private List<StoreGradeRuleDetailResp.RuleModificationLog> buildModificationLogs(Integer ruleId) {
        List<StoreGradeRuleDetailResp.RuleModificationLog> modificationLogs = new ArrayList<>();
        
        try {
            // 查询审批日志
            LambdaQueryWrapper<CommonApproveLog> logQueryWrapper = Wrappers.lambdaQuery();
            logQueryWrapper.eq(CommonApproveLog::getBusinessKey, "store_grade_rule")
                    .eq(CommonApproveLog::getBusinessId, ruleId.toString())
                    .orderByDesc(CommonApproveLog::getCreatedAt);
            
            List<CommonApproveLog> approveLogs = commonApproveLogMapper.selectList(logQueryWrapper);
            
            for (CommonApproveLog approveLog : approveLogs) {
                StoreGradeRuleDetailResp.RuleModificationLog modificationLog = new StoreGradeRuleDetailResp.RuleModificationLog();
                
                // 设置修改人（这里需要根据实际业务逻辑获取）
                modificationLog.setChangedBy(approveLog.getCreatedBy());
                
                // 设置修改时间
                modificationLog.setChangedTime(approveLog.getCreatedAt());
                
                // 解析原始数据和新数据
                if (StrUtil.isNotBlank(approveLog.getOriginBody())) {
                    StoreGradeRule originRule = JacksonUtil.parseObj(approveLog.getOriginBody(), StoreGradeRule.class);
                    modificationLog.setOldValues(buildRuleValues(originRule));
                }
                
                if (StrUtil.isNotBlank(approveLog.getTargetBody())) {
                    StoreGradeRule targetRule = JacksonUtil.parseObj(approveLog.getTargetBody(), StoreGradeRule.class);
                    modificationLog.setNewValues(buildRuleValues(targetRule));
                }
                
                modificationLogs.add(modificationLog);
            }
            
        } catch (Exception e) {
            log.error("Failed to build modification logs", e);
        }
        
        return modificationLogs;
    }
    
    /**
     * 构建规则值
     *
     * @param storeGradeRule 门店等级规则实体
     * @return 规则值
     */
    private StoreGradeRuleDetailResp.RuleValues buildRuleValues(StoreGradeRule storeGradeRule) {
        if (storeGradeRule == null) {
            return null;
        }
        
        StoreGradeRuleDetailResp.RuleValues ruleValues = new StoreGradeRuleDetailResp.RuleValues();
        ruleValues.setSMinCount(storeGradeRule.getSMinCount() != null ? storeGradeRule.getSMinCount().intValue() : null);
        ruleValues.setAMinCount(storeGradeRule.getAMinCount() != null ? storeGradeRule.getAMinCount().intValue() : null);
        ruleValues.setBMinCount(storeGradeRule.getBMinCount() != null ? storeGradeRule.getBMinCount().intValue() : null);
        ruleValues.setCMinCount(storeGradeRule.getCMinCount() != null ? storeGradeRule.getCMinCount().intValue() : null);
        ruleValues.setDMinCount(storeGradeRule.getDMinCount() != null ? storeGradeRule.getDMinCount().intValue() : null);
        
        return ruleValues;
    }

    @Override
    public CommonApiResponse<List<RuleModifyVersionResp>> queryVersionList(RuleQueryReq req) {
        if (req == null || req.getRuleId() == null) {
            throw new RuntimeException("Parameter error");
        }
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CommonApproveLog::getBusinessId, req.getRuleId())
                .eq(CommonApproveLog::getBusinessKey, "store_grade_rule")
                .orderByDesc(CommonApproveLog::getId);
        List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(commonApproveLogs)) {
            return new CommonApiResponse<>(Lists.newArrayList());
        }
        List<RuleModifyVersionResp> ruleModifyVersionRespList = new ArrayList<>();
        for (CommonApproveLog commonApproveLog : commonApproveLogs) {
            if (Objects.isNull(commonApproveLog.getOriginBody())) {
                continue;
            }
            RuleModifyVersionResp ruleModifyVersionResp = new RuleModifyVersionResp();
            ruleModifyVersionResp.setVersionId(commonApproveLog.getId());
            String versionTemplate = "{} Modification";
            ruleModifyVersionResp.setVersionName(StrUtil.format(versionTemplate, DateUtil.formatDate(commonApproveLog.getApplicationTime())));
            ruleModifyVersionResp.setModifyTime(commonApproveLog.getApplicationTime().getTime());
            ruleModifyVersionRespList.add(ruleModifyVersionResp);
        }
        //TODO fz后续补充查询新建或修改数据
        //TODO fz补充返回值，是否需要标识
        return new CommonApiResponse<>(ruleModifyVersionRespList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Boolean> deleteStoreGradeRule(Integer id) {
        try {
            log.info("Start deleting store grade rule, rule ID: {}", id);

            if (id == null) {
                log.error("Rule ID is empty");
                throw new RuntimeException("Parameter error");
            }

            // 查询现有记录
            StoreGradeRule existingRule = this.getById(id);
            if (existingRule == null) {
                log.error("Store grade rule with ID {} not found", id);
                throw new RuntimeException("Parameter error");
            }

            // 删除记录
            boolean result = this.removeById(id);

            log.info("Store grade rule deletion completed, deleted record ID: {}", id);
            return new CommonApiResponse<>(result);

        } catch (Exception e) {
            log.error("Failed to delete store grade rule", e);
            throw e;
        }
    }

    /**
     * 流程提交
     */
    @Override
    public SubmitResp submit(StoreGradeRuleReq req) {

        if (ObjectUtil.isEmpty(req) || req.getId() == null || StringUtils.isBlank(req.getCountryCode())) {
            throw new BusinessException("The request data is incorrect");
        }
        String countryCode = req.getCountryCode();
        List<Object> approvers = getApprovers(countryCode);
        StoreGradeRuleFormDataReq formDataReq = getFormDataReq(req);
        String businessKey = UUID.randomUUID().toString().replace("-", "");
        String procInst = createProcInst(approvers, formDataReq, businessKey);
        SubmitResp resp = new SubmitResp();
        resp.setFlowInstId(businessKey);
        resp.setRequestApprovalBody(procInst);
        return resp;
    }

    /**
     * 创建流程实例
     * @param approvers
     * @param formDataReq
     */
    private String createProcInst(List<Object> approvers, StoreGradeRuleFormDataReq formDataReq, String businessKey) {

        //发起流程实例
        String storeGradeRule = bpmService.create(formDataReq, BpmApproveBusinessCodeEnum.STORE_GRADE_RULE, getAccount(), businessKey, null);
        log.info("发起流程实例成功，流程实例ID：{}", storeGradeRule);
        return storeGradeRule;
    }

    /**
     * 获取当前登录人信息
     * @return
     */
    private String getAccount() {
        String currentAccount = RpcContextUtil.getCurrentAccount();
        if (StringUtils.isBlank(currentAccount)) {
            throw new BusinessException("The current user is not logged in");
        }
        return currentAccount;
    }


    /**
     * 封装表单数据
     * @param req
     * @return
     */
    private StoreGradeRuleFormDataReq getFormDataReq(StoreGradeRuleReq req) {
        StoreGradeRuleFormDataReq formDataReq = new StoreGradeRuleFormDataReq();
        formDataReq.setRetailerName(req.getRetailer().getName());
        formDataReq.setRetailerCode(req.getRetailer().getCode());
        formDataReq.setChannelType(req.getChannelType());
        formDataReq.setCountry(req.getCountryCode());
        formDataReq.setApplicationURL(req.getApplicationURL());
        if (req.getMethod().equals(TieringModificationMethodEnum.SYSTEM_CALCULATION.getValue())) {
            
            // 根据最小值设置容量范围
            if (req.getSMinCount() != null) {
                formDataReq.setCapaS("≥" + req.getSMinCount());
            }

            if (req.getAMinCount() != null) {
                // 这里需要根据业务逻辑确定最大值
                formDataReq.setCapaA(req.getAMinCount() + "-" + getAMaxCount(req));
            }

            if (req.getBMinCount() != null) {
                formDataReq.setCapaB(req.getBMinCount() + "-" + getBMaxCount(req));
            }

            if (req.getCMinCount() != null) {
                formDataReq.setCapaC(req.getCMinCount() + "-" + getCMaxCount(req));
            }

            if (req.getDMinCount() != null) {
                formDataReq.setCapaD("≤" + getDMaxCount(req));
            }

            formDataReq.setFileLink(req.getFileLink());
        } else if (req.getMethod().equals(TieringModificationMethodEnum.MANUAL_UPLOAD.getValue())) {
            AssetRecycleSubmitRequest.Multimedia excelUrl = JacksonUtil.parseObj(req.getFileData(), AssetRecycleSubmitRequest.Multimedia.class);
            StoreGradeRuleFormDataReq.UploadObject uploadObject = new StoreGradeRuleFormDataReq.UploadObject();
            uploadObject.setFileName(excelUrl.getName());
            uploadObject.setOriginFileName(excelUrl.getName());
            uploadObject.setUri(excelUrl.getUrl());
            formDataReq.getFile().add(uploadObject);
        }

        return formDataReq;
    }

    // 辅助方法，根据业务逻辑确定各等级的最大值
    private Integer getAMaxCount(StoreGradeRuleReq req) {
        return req.getSMinCount() != null ? req.getSMinCount() - 1 : null;
    }

    private Integer getBMaxCount(StoreGradeRuleReq req) {
        return req.getAMinCount() != null ? req.getAMinCount() - 1 : null;
    }

    private Integer getCMaxCount(StoreGradeRuleReq req) {
        return req.getBMinCount() != null ? req.getBMinCount() - 1 : null;
    }

    private Integer getDMaxCount(StoreGradeRuleReq req) {
        return req.getCMinCount() != null ? req.getCMinCount() - 1 : null;
    }

    /**
     * 获取审批人  //todo  获取审批人信息待完善
     * @param countryCode
     * @return
     */
    private List<Object> getApprovers(String countryCode) {
        return null;
    }

    /**
     * 流程撤回
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean recall(StoreGradeRuleRecallReq  req) {
        if (req.getStoreGradeRuleId() == null) {
            throw new BusinessException("The request data is incorrect");
        }
        StoreGradeRule existingRule = this.getById(req.getStoreGradeRuleId());
        if (existingRule == null) {
            throw new BusinessException("The store level rule for the corresponding ID was not found");
        }

        if (!existingRule.getApproveStatus().equals("")) {
            throw new BusinessException("The current store tier rule status is not allowed to be withdrawn");
        }

        CommonApproveReq   commonApproveReq = new CommonApproveReq();
        commonApproveReq.setBusinessId(existingRule.toString());
        commonApproveReq.setFlowStatus(ApprovalStatus.APPROVAL_ING.getValue());
        CommonApproveLog commonApproveResp = getCommonApproveResp(commonApproveReq);

        if (!commonApproveResp.getFlowStatus().equals(ApprovalStatus.APPROVAL_ING.getValue())) {
            throw new BusinessException("The current approval record status does not allow retraction");
        }
        
        recallProcInst(req, commonApproveReq.getFlowInstId());

        return Boolean.TRUE;
    }

    /**
     * 流程撤回请求
     * @param req
     */
    private void recallProcInst(StoreGradeRuleRecallReq req, String businessKey) {
        bpmService.recall(businessKey, getAccount(), req.getComment());
        log.info("BPM_RECALL_LOG,businessKey:{}----->user:{}", businessKey, "store_grade_rule");
    }

    /**
     * 根据条件查询审批记录
     * @param
     * @return
     */
    private CommonApproveLog getCommonApproveResp(CommonApproveReq   req) {
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getBusinessId()), CommonApproveLog::getBusinessId, req.getBusinessId())
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getBusinessKey()), CommonApproveLog::getBusinessKey, req.getBusinessKey())
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getFlowInstId()), CommonApproveLog::getFlowInstId, req.getFlowInstId())
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getFlowStatus()), CommonApproveLog::getFlowStatus, req.getFlowStatus());
        CommonApproveLog approveLog = this.commonApproveLogMapper.selectOne(queryWrapper);
        if (approveLog == null) {
            throw new BusinessException("No corresponding approval record found");
        }
        return approveLog;
    }

    /**
     * 查看审批记录
     */
    @Override
    public CommonApiResponse<List<StoreGradeApprovaResp>> listProcessLog(Long id) {
        CommonApproveReq req = new CommonApproveReq();
        req.setBusinessId(id.toString());
        CommonApproveLog commonApproveResp = getCommonApproveResp(req);
        if (ObjectUtil.isEmpty(commonApproveResp)) {
            throw new BusinessException("No corresponding approval record found");
        }
        
        return getListProcessLog(commonApproveResp);
    }
    
    @Override
    public CommonApiResponse<StoreGradeResp> queryStoreGrade(StoreGradeReq req) {
        StoreGradeResp resp = new StoreGradeResp();
        if (ObjectUtil.isNotEmpty(req) && StringUtils.isNotBlank(req.getRetailerCode()) && req.getKapa() != null) {
            StoreGradeRule storeGradeRule = this.lambdaQuery().eq(StoreGradeRule::getRetailerCode, req.getRetailerCode())
                    .eq(StoreGradeRule::getRulesStatus, ApprovalStatus.PASS.getCode()).one();
            if (storeGradeRule != null) {
                Long kapa = req.getKapa();
                String grade = determineGradeByKapa(kapa, storeGradeRule);
                resp.setStoreGrade(grade);
                resp.setRetailerCode(req.getRetailerCode());
            }
        }
        return new CommonApiResponse<>(resp);
    }
    
    @Override
    public Boolean batchUpdateStoreGrade(MultipartFile file, StoreGradeRuleReq req) {
        List<StoreGradeBatchData> dataList = parseExcelFile(file);
        checkDataList(dataList, req);
        // todo 封装参数发起流程实例
        List<Object> approvers = getApprovers(req.getCountryCode());
        StoreGradeRuleFormDataReq formDataReq = getFormDataReq(req);
        String businessKey = UUID.randomUUID().toString().replace("-", "");
        String procInst = createProcInst(approvers, formDataReq, businessKey);

        List<StoreGradeRelation> storeGradeRelations = new ArrayList<>();
        for (StoreGradeBatchData data : dataList) {
            StoreGradeRelation storeGradeRelation = new StoreGradeRelation();
            storeGradeRelation.setStoreCode(data.getStoreCode());
            storeGradeRelation.setStoreGradeRuleId(req.getId());
            storeGradeRelation.setStoreGrade(data.getStoreTiering());
            storeGradeRelations.add(storeGradeRelation);
        }
        if (!CollectionUtils.isEmpty(storeGradeRelations)) {
            this.storeGradeRelationMapper.insertBatch(storeGradeRelations);
        }

        return Boolean.TRUE;
    }
    
    /**
     * 判断数据是否合法
     */
    private void checkDataList(List<StoreGradeBatchData> dataList, StoreGradeRuleReq req) {
        if (CollectionUtil.isEmpty(dataList)) {
            throw new BusinessException("No data found in the Excel file");
        }
        String firstRetailerCode = dataList.get(0).getRetailerCode();
        boolean allSameRetailer = dataList.stream()
                .allMatch(data -> firstRetailerCode.equals(data.getRetailerCode()));

        if (!allSameRetailer) {
            throw new BusinessException("The retailer code in the Excel file is not consistent");
        }
        String retailerCode = dataList.get(0).getRetailerCode();
        if (!retailerCode.equals(req.getRetailerCode())) {
            throw new BusinessException("The retailer in the Excel file is different from the one you selected");
        }
        // 查询当前零售商编码下审批状态为进行中或审批通过的数据集合
        List<StoreGradeRule> existingRules = this.lambdaQuery()
                .eq(StoreGradeRule::getRetailerCode, req.getRetailerCode())
                .in(StoreGradeRule::getRulesStatus, Arrays.asList(ApprovalStatus.APPROVAL_ING.getCode(),
                        ApprovalStatus.PASS.getCode()))
                .list();
        if (CollectionUtil.isNotEmpty(existingRules)) {
            throw new BusinessException("This Retailer is already included in other rules");
        }
        LambdaQueryWrapper<IntlRmsRetailer> queryRetailerWrapper = Wrappers.lambdaQuery();
        queryRetailerWrapper.eq(IntlRmsRetailer::getName, req.getRetailerCode());
        IntlRmsRetailer retailer = intlRmsRetailerMapper.selectOne(queryRetailerWrapper);
        if (retailer.getRetailerChannelType() == null) {
            throw new BusinessException("This Retailer's Channel Type is empty in RMS, " +
                    "please maintain it in RMS-retailer");
        }

        // 获取dataList中的storeCode集合
        List<String> storeCodes = dataList.stream()
                .map(StoreGradeBatchData::getStoreCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(storeCodes)) {
            // 根据storeCode集合查询门店信息
            LambdaQueryWrapper<IntlRmsStore> queryStoreWrapper = Wrappers.lambdaQuery();
            queryStoreWrapper.in(IntlRmsStore::getCode, storeCodes);
            List<IntlRmsStore> stores = intlRmsStoreMapper.selectList(queryStoreWrapper);

            // 检查是否有未找到的门店
            if (stores.size() != storeCodes.size()) {
                throw new BusinessException("Some store codes in the Excel file could not be found");
            }

            // 检查所有门店的retailerId是否与请求中的retailerCode对应的retailer一致
            for (IntlRmsStore store : stores) {
                if (!req.getRetailerCode().equals(store.getRetailerId())) {
                    throw new BusinessException("The stores in the Excel file does not belong to the retailer you selected");
                }
                if (store.getChannelType() != ChannelTypeEnum.fromValue(req.getChannelType()).getKey()) {
                    throw new BusinessException("The form store channel is from the wrong source");
                }
            }
        }
    }


    /**
     * 解析Excel文件为List数据
     *
     * @param file 上传的Excel文件
     * @return 解析后的数据列表
     */
    private List<StoreGradeBatchData> parseExcelFile(MultipartFile file) {
        List<StoreGradeBatchData> dataList = new ArrayList<>();

        try {
            // 使用POI解析Excel文件
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // 从第二行开始读取数据（跳过表头）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }

                StoreGradeBatchData data = new StoreGradeBatchData();

                // 读取Store Code（第1列）
                Cell storeCodeCell = row.getCell(0);
                if (storeCodeCell != null) {
                    data.setStoreCode(getCellValueAsString(storeCodeCell));
                }

                // 读取Retailer Code（第2列）
                Cell retailerCodeCell = row.getCell(1);
                if (retailerCodeCell != null) {
                    data.setRetailerCode(getCellValueAsString(retailerCodeCell));
                }

                // 读取Store Tiering（第3列）
                Cell storeTieringCell = row.getCell(2);
                if (storeTieringCell != null) {
                    data.setStoreTiering(getCellValueAsString(storeTieringCell));
                }

                // 只有当关键字段不为空时才添加到列表中
                if (StringUtils.isNotBlank(data.getStoreCode()) ||
                        StringUtils.isNotBlank(data.getRetailerCode()) ||
                        StringUtils.isNotBlank(data.getStoreTiering())) {
                    dataList.add(data);
                }
            }

            workbook.close();

        } catch (Exception e) {
            log.error("解析Excel文件失败", e);
            throw new BusinessException("Parsing the Excel file failed: " + e.getMessage());
        }


        return dataList;
    }

    /**
     * 获取单元格值为字符串
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return DateUtil.format(cell.getDateCellValue(), "yyyy-MM-dd");
                } else {
                    // 对于数值类型，转换为整数字符串（避免科学计数法）
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    @Override
    public CommonApiResponse<String> getFileTemplate() {


        return new CommonApiResponse<>("");
    }

    @Override
    public CommonApiResponse<List<RetailerQueryResp>> queryRetailerList(RetailerQueryReq request) {
        
        List<RetailerQueryResp> resps = new ArrayList<>();
        LambdaQueryWrapper<IntlRmsRetailer> queryRetailerWrapper = Wrappers.lambdaQuery();
        queryRetailerWrapper.eq(StringUtils.isNotBlank(request.getCountryCode()), IntlRmsRetailer::getCountryCode, request.getCountryCode());
        queryRetailerWrapper.eq(StringUtils.isNotBlank(request.getChannelType()),
                IntlRmsRetailer::getRetailerChannelType, ChannelTypeEnum.fromValue(request.getChannelType()).getKey());
        List<IntlRmsRetailer> retailers = intlRmsRetailerMapper.selectList(queryRetailerWrapper);
        if (CollectionUtil.isEmpty(retailers)) {
            return new CommonApiResponse<>(resps);
        }
        for (IntlRmsRetailer retailer : retailers) {
            RetailerQueryResp resp = new RetailerQueryResp();
            resp.setRetailerCode(retailer.getName());
            resp.setCrmCode(retailer.getCrmCode());
            resp.setRetailerName(retailer.getRetailerName());
            resp.setCountryId(retailer.getCountryId());
            resp.setCountryName(retailer.getCountryName());
            resp.setCountryCode(retailer.getCountryCode());
            resp.setChannelType(ChannelTypeEnum.fromKey(retailer.getRetailerChannelType()).getValue());
            resps.add(resp);
        }

        return new CommonApiResponse<>(resps);
    }

    @Override
    public CommonApiResponse<UploadManuallyRuleDetailsResp> getUploadManuallyRuleDetails(Long ruleId) {

        UploadManuallyRuleDetailsResp detailsResp = new UploadManuallyRuleDetailsResp();
        StoreGradeRule rule = baseMapper.selectById(ruleId);
        if (rule == null) {
            throw new BusinessException("Invalid ruleId: " + ruleId);
        }
        DetailsTopmostResp topmostResp = new DetailsTopmostResp();
        topmostResp.setChannelType(rule.getChannelType());
        topmostResp.setExcelFile(rule.getFileUrl());
        topmostResp.setRetailerCode(rule.getRetailerCode());
        topmostResp.setRetailerName(rule.getRetailerName());
        topmostResp.setId(ruleId);
        // 除了生效中，别的状态都为未生效
        String rulesCurrentStatus = rule.getRulesStatus().equals(RuleStatusEnum.IN_EFFECT.getCode()) ? RuleStatusEnum.IN_EFFECT.getValue() : "Not In Effect";
        topmostResp.setRulesCurrentStatus(rulesCurrentStatus);
        detailsResp.setDetailsTopmostResp(topmostResp);

        List<DetailsBelowResp> detailsBelowRespList = new ArrayList<>();
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CommonApproveLog::getBusinessId, ruleId);
        queryWrapper.orderByAsc(CommonApproveLog::getId);
        List<CommonApproveLog> logs = commonApproveLogMapper.selectList(queryWrapper);
        String createBy = rule.getCreateBy();
        LocalDateTime applicationTime = rule.getApplicationTime();
        if (CollectionUtil.isEmpty(logs)) {
            DetailsBelowResp belowResp = new DetailsBelowResp();
            belowResp.setChangedBy(createBy);
            belowResp.setChangedTime(applicationTime);
            belowResp.setNewValues(rule.getFileUrl());
            detailsBelowRespList.add(belowResp);
        } else {
            String oldValues = null;
            for (CommonApproveLog log : logs) {
                DetailsBelowResp belowResp = new DetailsBelowResp();
                belowResp.setChangedBy(createBy);
                belowResp.setChangedTime(applicationTime);
                String originBody = log.getOriginBody();
                if (StrUtil.isNotBlank(originBody)) {
                    Map<String, Object> originMap = JacksonUtil.parseObj(originBody, Map.class);
                    String url = (String) originMap.get("fileUrl");
                    belowResp.setOldValues(oldValues);
                    belowResp.setNewValues(url);
                    oldValues = url;
                }
                detailsBelowRespList.add(belowResp);
            }
        }
        detailsResp.setDetailsBelowRespList(detailsBelowRespList);

        return new CommonApiResponse<>(detailsResp);
    }
    
    /**
     * 根据kapa值和等级规则确定门店等级
     * 按照S->A->B->C->D的顺序从上到下判断
     *
     * @param kapa 实际容量值
     * @param rule 等级规则
     * @return 对应的等级
     */
    private String determineGradeByKapa(Long kapa, StoreGradeRule rule) {
        // S等级判断
        if (rule.getSMinCount() != null && kapa >= rule.getSMinCount()) {
            return "S";
        }
        // A等级判断
        if (rule.getAMinCount() != null && kapa >= rule.getAMinCount()) {
            return "A";
        }
        // B等级判断
        if (rule.getBMinCount() != null && kapa >= rule.getBMinCount()) {
            return "B";
        }
        // C等级判断
        if (rule.getCMinCount() != null && kapa >= rule.getCMinCount()) {
            return "C";
        }
        // D等级判断（默认等级）
        if (rule.getDMinCount() != null && kapa >= rule.getDMinCount()) {
            return "D";
        }
        
        // 如果都不满足，返回最低等级D
        return "D";
    }
    
    /**
     * 获取审批记录
     *
     * @param approveLog
     * @return
     */
    private CommonApiResponse<List<StoreGradeApprovaResp>> getListProcessLog(CommonApproveLog approveLog) {
        
        // 获取审批记录
        return null;
    }

    /**
     * 分页查询门店等级规则
     *
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    @Override
    public CommonApiResponse<StoreGradeRulePageResp> pageQuery(StoreGradeRulePageQueryReq request) {
        try {
            log.info("Start paging to query store level rules and request parameters: {}", request);

            if (request == null) {
                log.error("Paging query request object is empty");
                throw new RuntimeException("Incorrect parameters");
            }

            // 构建查询条件
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();

            // 规则状态查询（数组）
            if (request.getRuleStatus() != null && !request.getRuleStatus().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getRulesStatus, request.getRuleStatus());
            }

            // 审批状态查询（数组）
            if (request.getApprovalStatus() != null && !request.getApprovalStatus().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getApproveStatus, request.getApprovalStatus());
            }

            // 渠道类型查询（数组）
            if (request.getChannelType() != null && !request.getChannelType().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getChannelType, request.getChannelType());
            }

            // 零售商名称或编码模糊搜索
            if (StrUtil.isNotBlank(request.getRetailerNameCode())) {
                queryWrapper.and(qw -> qw
                        .like(StoreGradeRule::getRetailerName, request.getRetailerNameCode())
                        .or()
                        .like(StoreGradeRule::getRetailerCode, request.getRetailerNameCode())
                );
            }

            // 申请日期区间查询
            if (request.getApplicationDate() != null) {
                if (StrUtil.isNotBlank(request.getApplicationDate().getStart())) {
                    queryWrapper.ge(StoreGradeRule::getApplicationTime,
                            LocalDateTime.parse(request.getApplicationDate().getStart() + "T00:00:00"));
                }
                if (StrUtil.isNotBlank(request.getApplicationDate().getEnd())) {
                    queryWrapper.le(StoreGradeRule::getApplicationTime,
                            LocalDateTime.parse(request.getApplicationDate().getEnd() + "T23:59:59"));
                }
            }

            // 审批日期区间查询
            if (request.getApprovedDate() != null) {
                if (StrUtil.isNotBlank(request.getApprovedDate().getStart())) {
                    queryWrapper.ge(StoreGradeRule::getApprovedTime,
                            LocalDateTime.parse(request.getApprovedDate().getStart() + "T00:00:00"));
                }
                if (StrUtil.isNotBlank(request.getApprovedDate().getEnd())) {
                    queryWrapper.le(StoreGradeRule::getApprovedTime,
                            LocalDateTime.parse(request.getApprovedDate().getEnd() + "T23:59:59"));
                }
            }

            // 按申请时间倒序排列
            queryWrapper.orderByDesc(StoreGradeRule::getApplicationTime);

            // 执行分页查询
            Page<StoreGradeRule> page = new Page<>(request.getPageNum(), request.getPageSize());
            Page<StoreGradeRule> resultPage = this.page(page, queryWrapper);

            // 构建响应对象
            StoreGradeRulePageResp response = new StoreGradeRulePageResp();
            response.setPageNum((int) resultPage.getCurrent());
            response.setPageSize((int) resultPage.getSize());
            response.setTotal(resultPage.getTotal());
            response.setTotalPages((int) resultPage.getPages());

            // 转换数据列表
            List<StoreGradeRulePageResp.StoreGradeRuleItem> itemList = resultPage.getRecords().stream()
                    .map(this::convertToPageItem)
                    .collect(Collectors.toList());
            response.setList(itemList);

            log.info("Paging query store grade rules completed, total records: {}", resultPage.getTotal());
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("Failed to paging query store grade rules", e);
            throw e;
        }
    }

    /**
     * 将实体对象转换为分页响应项
     *
     * @param entity 实体对象
     * @return 分页响应项
     */
    private StoreGradeRulePageResp.StoreGradeRuleItem convertToPageItem(StoreGradeRule entity) {
        StoreGradeRulePageResp.StoreGradeRuleItem item = new StoreGradeRulePageResp.StoreGradeRuleItem();

        // 复制基本属性
        item.setId(entity.getId());
        item.setChannelType(entity.getChannelType());
        item.setMethod(entity.getMethod() != null ? entity.getMethod() : entity.getChannelType()); // 兼容处理
        item.setRetailerName(entity.getRetailerName());
        item.setRetailerCode(entity.getRetailerCode());
        item.setSMinCount(entity.getSMinCount() != null ? entity.getSMinCount().intValue() : null);
        item.setAMinCount(entity.getAMinCount() != null ? entity.getAMinCount().intValue() : null);
        item.setBMinCount(entity.getBMinCount() != null ? entity.getBMinCount().intValue() : null);
        item.setCMinCount(entity.getCMinCount() != null ? entity.getCMinCount().intValue() : null);
        item.setDMinCount(entity.getDMinCount() != null ? entity.getDMinCount().intValue() : null);

        // 设置规则状态
        item.setRuleStatus(entity.getRulesStatus());
        item.setRuleStatusDesc(getRuleStatusDesc(item.getRuleStatus()));

        // 设置审批状态
        item.setApprovalStatus(entity.getApproveStatus());
        item.setApprovalStatusDesc(getApprovalStatusDesc(item.getApprovalStatus()));

        // 格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        if (entity.getApplicationTime() != null) {
            item.setApplicationTime(entity.getApplicationTime().format(formatter));
        }
        if (entity.getApprovedTime() != null) {
            item.setApprovedTime(entity.getApprovedTime().format(formatter));
        }

        return item;
    }

    /**
     * 转换规则状态
     *
     * @param rulesStatus 规则状态字符串
     * @return 规则状态数字
     */
    private Integer convertRuleStatus(String rulesStatus) {
        if (StrUtil.isBlank(rulesStatus)) {
            return 0;
        }
        switch (rulesStatus.toUpperCase()) {
            case "IN EFFECT":
                return 1;
            case "NOT IN EFFECT":
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 获取规则状态描述
     *
     * @param ruleStatus 规则状态数字
     * @return 规则状态描述
     */
    private String getRuleStatusDesc(Integer ruleStatus) {
        if (ruleStatus == null) {
            return "Unknown";
        }
        switch (ruleStatus) {
            case 1:
                return "In Effect";
            case 0:
                return "Not In Effect";
            default:
                return "Unknown";
        }
    }

    /**
     * 转换审批状态
     *
     * @param approveStatus 审批状态字符串
     * @return 审批状态数字
     */
    private Integer convertApprovalStatus(String approveStatus) {
        if (StrUtil.isBlank(approveStatus)) {
            return 0;
        }
        switch (approveStatus.toUpperCase()) {
            case "APPROVED":
                return 1;
            case "IN APPROVAL":
                return 0;
            case "REJECTED":
                return 2;
            default:
                return 0;
        }
    }

    /**
     * 获取审批状态描述
     *
     * @param approvalStatus 审批状态数字
     * @return 审批状态描述
     */
    private String getApprovalStatusDesc(Integer approvalStatus) {
        if (approvalStatus == null) {
            return "Unknown";
        }
        switch (approvalStatus) {
            case 1:
                return "Approved";
            case 0:
                return "In Approval";
            case 2:
                return "Rejected";
            default:
                return "Unknown";
        }
    }
}
