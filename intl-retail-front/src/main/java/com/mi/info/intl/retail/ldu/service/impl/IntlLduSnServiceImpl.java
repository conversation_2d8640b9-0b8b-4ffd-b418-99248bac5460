package com.mi.info.intl.retail.ldu.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.ObjectId;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.enums.SerialNumberType;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.BathConReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerReqDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerResponseDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiValidationDto;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.ldu.config.LduConfig;
import com.mi.info.intl.retail.ldu.dto.JobSuccessDto;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduSnEnExcel;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduSnExcel;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduSnExcelDto;
import com.mi.info.intl.retail.ldu.enums.ExportExcelEnum;
import com.mi.info.intl.retail.ldu.enums.LanguageEnum;
import com.mi.info.intl.retail.ldu.enums.LduTypeEnum;
import com.mi.info.intl.retail.ldu.enums.RmsRetailerEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduSn;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduSnMapper;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.ldu.service.IntlLduSnImeiService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduSnService;
import com.mi.info.intl.retail.ldu.util.ConstantMessageTemplate;
import com.mi.info.intl.retail.ldu.util.NrJobTaskUtils;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.retailer.mapper.read.IntlRmsRetailerReadMapper;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import com.xiaomi.keycenter.okhttp3.OkHttpClient;
import com.xiaomi.keycenter.okhttp3.Request;
import com.xiaomi.keycenter.okhttp3.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
@Slf4j
@DubboService(group = "${center.dubbo.group:}", interfaceClass = IntlLduSnService.class)
public class IntlLduSnServiceImpl
        extends ServiceImpl<IntlLduSnMapper, IntlLduSn>
        implements IntlLduSnService {


    @Resource
    private FdsService fdsService;

    @Resource
    private IntlLduSnImeiService intlLduSnImeiService;

    @Resource
    private IntlLduSnMapper intlLduSnMapper;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Resource
    private NrJobTaskUtils nrJobTaskUtils;

    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Resource
    private IProductQueryService iProductQueryService;

    @Resource
    private LduConfig lduConfig;

    @Resource
    private IntlRmsRetailerReadMapper intlRmsRetailerReadMapper;

    private static final int DEFAULT_LIMIT = 50;

    @Override
    public CommonApiResponse<IPage<IntlLduSnDto>> pageList(IntlLduSnReq query) {
        String areaId = RpcContext.getContext().getAttachment("$area_id");
        List<String> countrysCode = new ArrayList<>();
        if (!CollectionUtils.isEmpty(query.getCountryCode())) {
            countrysCode = query.getCountryCode();
        }
        if (CollectionUtils.isEmpty(countrysCode) && !"GLOBAL".equals(areaId) && areaId != null) {
            countrysCode.add(areaId);
        }
        Page<IntlLduSn> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<IntlLduSn> queryWrapper = Wrappers.<IntlLduSn>lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getGoodsName()), IntlLduSn::getGoodsName, query.getGoodsName())
                .eq(StringUtils.isNotEmpty(query.getGoodsId()), IntlLduSn::getGoodsId, query.getGoodsId())
                .eq(StringUtils.isNotEmpty(query.getRetailerCode()), IntlLduSn::getRetailerCode, query.getRetailerCode())
                .eq(StringUtils.isNotEmpty(query.getProductLine()), IntlLduSn::getProductLine, query.getProductLine())
                .eq(StringUtils.isNotEmpty(query.getProjectCode()), IntlLduSn::getProjectCode, query.getProjectCode())
                .eq(StringUtils.isNotEmpty(query.getGoodsId()), IntlLduSn::getGoodsId, query.getGoodsId())
                .eq(StringUtils.isNotEmpty(query.getLduType()), IntlLduSn::getLduType, query.getLduType())
                .in(CollectionUtils.isNotEmpty(countrysCode), IntlLduSn::getCountryCode, countrysCode)
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getSn, query.getSnImei())
                .or()
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getImei1, query.getSnImei())
                .or()
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getImei2, query.getSnImei())
                .eq(Objects.nonNull(query.getIsReport()), IntlLduSn::getIsReport, query.getIsReport())
                .eq(Objects.nonNull(query.getStatus()), IntlLduSn::getStatus, query.getStatus())
                .orderByDesc(IntlLduSn::getPlanCreateDate);

        Page<IntlLduSn> taskConfPage = this.page(page, queryWrapper);
        Page<IntlLduSnDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        //产品线转换
        if (CollectionUtils.isEmpty(taskConfPage.getRecords())) {
            pageDTO.setCurrent(taskConfPage.getCurrent());
            pageDTO.setSize(taskConfPage.getSize());
            return new CommonApiResponse<>(pageDTO);
        }
        List<IntlLduSnDto> list = ComponentLocator.getConverter()
                .convertList(taskConfPage.getRecords(), IntlLduSnDto.class);

        List<ProductLineDto> productLineDtoList = iProductQueryService.queryProductLines();
        Map<String, ProductLineDto> productLineMap = productLineDtoList.stream().collect(
                Collectors.toMap(ProductLineDto::getProductLine, it -> it));

        List<String> skuList = list.stream().map(IntlLduSnDto::getGoodsId).distinct().collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoBySkus(skuList);
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoMap = snImeiGoodsInfoDtoList.stream().collect(
                Collectors.toMap(SnImeiGoodsInfoDto::getGoodsId, it -> it));

        list.stream().forEach(item -> {
            if (StringUtils.isNotEmpty(item.getProductLine())) {
                String language = RpcContext.getContext().getAttachment("$language");
                if (LanguageEnum.EN_US.getCode().equals(language)) {
                    if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                        item.setProductLine(productLineMap.get(item.getProductLine()).getEnName());
                    }
                    if (!Objects.isNull(snImeiGoodsInfoMap.get(item.getGoodsId()))) {
                        item.setGoodsName(snImeiGoodsInfoMap.get(item.getGoodsId()).getGoodsNameEn());
                    }
                } else {
                    if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                        item.setProductLine(productLineMap.get(item.getProductLine()).getCnName());
                    }
                }
            }
        });
        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(list.size());
        pageDTO.setRecords(list);
        return new CommonApiResponse<>(pageDTO);
    }

    @Override
    public IPage<IntlLduSnDto> pageListSn(IntlLduSnReq query) {
        String areaId = RpcContext.getContext().getAttachment("$area_id");
        List<String> countrysCode = new ArrayList<>();
        if (!CollectionUtils.isEmpty(query.getCountryCode())) {
            countrysCode = query.getCountryCode();
        }
        if (CollectionUtils.isEmpty(countrysCode) && !"GLOBAL".equals(areaId) && areaId != null) {
            countrysCode.add(areaId);
        }
        Page<IntlLduSn> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<IntlLduSn> queryWrapper = Wrappers.<IntlLduSn>lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getGoodsName()), IntlLduSn::getGoodsName, query.getGoodsName())
                .eq(StringUtils.isNotEmpty(query.getGoodsId()), IntlLduSn::getGoodsId, query.getGoodsId())
                .in(CollectionUtils.isNotEmpty(countrysCode), IntlLduSn::getCountryCode, countrysCode)
                .eq(StringUtils.isNotEmpty(query.getRetailerCode()), IntlLduSn::getRetailerCode, query.getRetailerCode())
                .eq(StringUtils.isNotEmpty(query.getProductLine()), IntlLduSn::getProductLine, query.getProductLine())
                .eq(StringUtils.isNotEmpty(query.getProjectCode()), IntlLduSn::getProjectCode, query.getProjectCode())
                .eq(StringUtils.isNotEmpty(query.getGoodsId()), IntlLduSn::getGoodsId, query.getGoodsId())
                .eq(StringUtils.isNotEmpty(query.getLduType()), IntlLduSn::getLduType, query.getLduType())
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getSn, query.getSnImei())
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getSn, query.getSnImei())
                .or()
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getImei1, query.getSnImei())
                .or()
                .eq(StringUtils.isNotEmpty(query.getSnImei()), IntlLduSn::getImei2, query.getSnImei())
                .eq(Objects.nonNull(query.getIsReport()), IntlLduSn::getIsReport, query.getIsReport())
                .eq(Objects.nonNull(query.getStatus()), IntlLduSn::getStatus, query.getStatus())
                .orderByDesc(IntlLduSn::getPlanCreateDate);

        Page<IntlLduSn> taskConfPage = this.page(page, queryWrapper);
        Page<IntlLduSnDto> pageDTO = new Page<>(query.getPageNum(), query.getPageSize());

        if (CollectionUtils.isEmpty(taskConfPage.getRecords())) {
            pageDTO.setCurrent(taskConfPage.getCurrent());
            pageDTO.setSize(taskConfPage.getSize());
            return pageDTO;
        }
        List<IntlLduSnDto> list = ComponentLocator.getConverter()
                .convertList(taskConfPage.getRecords(), IntlLduSnDto.class);

        List<ProductLineDto> productLineDtoList = iProductQueryService.queryProductLines();
        Map<String, ProductLineDto> productLineMap = productLineDtoList.stream().collect(
                Collectors.toMap(ProductLineDto::getProductLine, it -> it));

        List<String> skuList = list.stream().map(IntlLduSnDto::getGoodsId).distinct().collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoBySkus(skuList);
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoMap = snImeiGoodsInfoDtoList.stream().collect(
                Collectors.toMap(SnImeiGoodsInfoDto::getGoodsId, it -> it));

        list.stream().forEach(item -> {
            if (StringUtils.isNotEmpty(item.getProductLine())) {
                String language = RpcContext.getContext().getAttachment("$language");
                if (LanguageEnum.EN_US.getCode().equals(language)) {
                    if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                        item.setProductLine(productLineMap.get(item.getProductLine()).getEnName());
                    }
                    if (!Objects.isNull(snImeiGoodsInfoMap.get(item.getGoodsId()))) {
                        item.setGoodsName(snImeiGoodsInfoMap.get(item.getGoodsId()).getGoodsNameEn());
                    }
                } else {
                    if (!Objects.isNull(productLineMap.get(item.getProductLine()))) {
                        item.setProductLine(productLineMap.get(item.getProductLine()).getCnName());
                    }
                }
            }
        });
        pageDTO.setPages(taskConfPage.getPages());
        pageDTO.setTotal(list.size());
        pageDTO.setRecords(list);
        return pageDTO;
    }

    @Override
    public CommonApiResponse<String> create(IntlLduSnDto conf) {
        List<IntlLduSnDto> lduSnList = new ArrayList<>();
        //封装其他参数:三方服务
        String msg = conertOtherParams(conf);
        if (StringUtils.isNotEmpty(msg)) {
            return CommonApiResponse.failure(0001, msg);
        }
        lduSnList.add(conf);
        List<IntlLduSn> intlLduSnList = ComponentLocator.getConverter()
                .convertList(lduSnList, IntlLduSn.class);
        intlLduSnMapper.batchInsert(intlLduSnList);
        return new CommonApiResponse<>("success");
    }

    @Override
    public CommonResponse<String> exportPlanMaintenance(IntlLduSnReq query) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile("LDU计划维护_", ".xlsx");

            String upcAccount = RpcContext.getContext().getAttachment("$upc_account");
            String language = RpcContext.getContext().getAttachment("$language");
            if (LanguageEnum.EN_US.getCode().equals(language)) {
                excelWriter = EasyExcel.write(tempFile, IntlLduSnEnExcel.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            } else {
                excelWriter = EasyExcel.write(tempFile, IntlLduSnExcel.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            }

            WriteSheet writeSheet = EasyExcel.writerSheet("任务列表").build();

            long pageSize = 1000L;
            long currentPage = 1L;
            boolean hasNext = true;

            while (hasNext) {
                query.setPageNum(currentPage);
                query.setPageSize(pageSize);

                IPage<IntlLduSnDto> intlLduReportStatisticsDtoIPage = this.pageListSn(query);

                List<IntlLduSnDto> records = intlLduReportStatisticsDtoIPage.getRecords();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }
                List<IntlLduSnExcelDto> excelDtoList = new ArrayList<>();
                records.stream().forEach(record -> {
                    IntlLduSnExcelDto excelDto = ComponentLocator.getConverter().convert(record, IntlLduSnExcelDto.class);
                    if (LanguageEnum.EN_US.getCode().equals(language)) {
                        excelDto.setIsReport(record.getIsReport() == 1 ? "Reported" : "Not Reported");
                        excelDto.setStatus(record.getStatus() == 1 ? "Valid" : "Invalid");
                    } else {
                        excelDto.setLduType(LduTypeEnum.getMenuNameByType(record.getLduType()));
                        excelDto.setIsReport(record.getIsReport() == 1 ? "已上报" : "未上报");
                        excelDto.setStatus(record.getStatus() == 1 ? "有效" : "无效");
                    }
                    //long类型时间戳转日期格式
                    excelDto.setPlanCreateDate(new Date(record.getPlanCreateDate()));
                    excelDtoList.add(excelDto);
                });

                excelWriter.write(excelDtoList, writeSheet);

                hasNext = currentPage * pageSize < intlLduReportStatisticsDtoIPage.getTotal();
                currentPage++;
            }

            if (excelWriter != null) {
                excelWriter.finish();
            }
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);

            FdsUploadResult upload = fdsService.upload("LDU计划维护" + timestamp + ".xlsx", tempFile, true);

            return nrJobForExcelExport(upload.getUrl(), upcAccount);
        } catch (Exception e) {
            log.error("LDU上报统计列表导出异常: {}", e);
            return CommonResponse.failure(0001, "导出失败");
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private CommonResponse<String> nrJobForExcelExport(String upload, String upcAccount) {

        JobSuccessDto build = JobSuccessDto.builder().fileUrl(upload).build();

        NrJobGoIn nrJobGoIn = NrJobGoIn.builder()
                .jobKey(ExportExcelEnum.LDU_PLAN_REPORT.getJobKey())
                .owner(upcAccount)
                .taskDesc(ExportExcelEnum.LDU_PLAN_REPORT.getExcelEnName())
                .taskParam(JSONUtil.toJsonStr(build))
                // 链路追踪ID
                .traceId(ObjectId.next())
                .taskName(ExportExcelEnum.LDU_PLAN_REPORT.getExcelName())
                .build();

        return nrJobTaskUtils.triggerNrJobTask(nrJobGoIn);
    }

    @Override
    public CommonApiResponse<String> stopUse(IntlLduSnReq query) {
        if (Objects.isNull(query) || StringUtils.isBlank(query.getSn())) {
            return CommonApiResponse.failure(-1, ConstantMessageTemplate.getSnEmptyMessage());
        }
        //停用时校验是否已上报，已上报的的数据无法停用
        IntlLduSn intlLduSn = intlLduSnMapper.selectBySn(query.getSn());
        if (Objects.nonNull(intlLduSn) && !Objects.equals(intlLduSn.getIsReport(), 0)) {
            return CommonApiResponse.failure(-1, ConstantMessageTemplate.getSnNotInPlanDeactivateFailedMessage());
        }
        intlLduSnMapper.updateStatusBySn(query.getSn(), System.currentTimeMillis());
        return new CommonApiResponse<>("success");
    }

    @Override
    public CommonApiResponse<List<String>> importStopPlanMaintenance(BathConReq query) {
        List<String> errorList = new ArrayList<>();
        if (Objects.equals(query.getType(), "2")) {
            /**
             * 解析excel,修改状态
             * - 批量停用，通过模板导入时，校验模板中的SN
             *   - 是否存在于计划中，如不存在，则提示
             * Line X,当前SN不在计划中，无法直接停用(Line X,the SN doesn't exist in the LDU plan list,can not deactivate)
             *   - 状态校验，是否是已停用，如已停用，则提示
             *   Line X,当前SN的状态为已停用，请不要重复操作
             */
            try {
                List<String> snList = new ArrayList<>();
                // 创建 OkHttpClient 实例
                OkHttpClient client = new OkHttpClient();
                // 创建 HTTP 请求
                Request request = new Request.Builder().url(query.getUrl()).build();
                Response response = client.newCall(request).execute();

                if (response.isSuccessful()) {
                    // 获取响应的输入流
                    InputStream inputStream = response.body().byteStream();
                    Workbook workbook = new XSSFWorkbook(inputStream);
                    Sheet sheet = workbook.getSheetAt(0);
                    int num = 1;
                    for (Row row : sheet) {
                        if (row.getRowNum() == 0) {
                            if (!"SN/IMEI".equals(getCellValue(row.getCell(0)))) {
                                errorList.add(ConstantMessageTemplate.getTemplateError());
                                return new CommonApiResponse<>(errorList);
                            }
                            continue;
                        }
                        String sn = this.getCellValue(row.getCell(0));
                        // 校验是否可以停用
                        convertDataList(num, sn, errorList, snList);
                        num++;
                    }
                }
                // 批量停用
                if (CollUtil.isNotEmpty(snList)) {
                    intlLduSnMapper.batchUpdateBySn(snList, System.currentTimeMillis());
                }
            } catch (Exception e) {
                log.error("批量停用异常: {}", e);
            }

        }
        return new CommonApiResponse<>(errorList);
    }

    private void convertDataList(int num, String sn, List<String> errorList, List<String> snList) {
        if (StringUtils.isEmpty(sn)) {
            errorList.add(ConstantMessageTemplate.getSnEmptyMessage(num));
        } else {
            IntlLduSn intlLduSn = intlLduSnMapper.selectBySn(sn);
            if (Objects.isNull(intlLduSn)) {
                errorList.add(ConstantMessageTemplate.getSnNotInPlanDeactivateFailedMessage(num));
            } else {
                if (intlLduSn.getStatus() == 0 || intlLduSn.getIsReport() == 1) {
                    errorList.add(ConstantMessageTemplate.getSnAlreadyDeactivatedMessage(num));
                } else {
                    snList.add(intlLduSn.getSn());
                }
            }
        }
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


    @Override
    public CommonApiResponse<List<String>> importPlanMaintenance(BathConReq query) {

        List<String> errorList = new ArrayList<>();
        if (Objects.equals(query.getType(), "1")) {
            /**
             * 1、解析excel，校验数据
             * 2、根据产品ID查询产品信息，产品线，IMEI1，IMEI2，SN
             * 3、根据零售商编码查询零售商信息，零售商名称
             * 4、封装数据，插入数据库
             */
            try {
                List<IntlLduSn> confList = new ArrayList<>();
                // 创建 OkHttpClient 实例
                OkHttpClient client = new OkHttpClient();
                // 创建 HTTP 请求
                Request request = new Request.Builder().url(query.getUrl()).build();
                Response response = client.newCall(request).execute();
                List<String> retailerCodeList = null;
                if (response.isSuccessful()) {
                    Map<String, SnImeiValidationDto> snImeiValidationDtoMap = null;
                    // 获取响应的输入流
                    InputStream inputStream = response.body().byteStream();
                    Workbook workbook = new XSSFWorkbook(inputStream);
                    Sheet sheet = workbook.getSheetAt(0);
                    //1、获取所有的sn,校验是否小米产品
                    List<IntlLduSnDto> snList = getAllSn(sheet);
                    List<String> codeList = snList.stream().map(IntlLduSnDto::getSn).distinct().collect(Collectors.toList());
                    List<SnImeiValidationDto> snImeiValidationDtoList = intlLduSnImeiService.validateSnImeiInfo(convertQueryDto(codeList));
                    if (!CollUtil.isEmpty(snImeiValidationDtoList)) {
                        snImeiValidationDtoMap = snImeiValidationDtoList.stream().collect(
                                Collectors.toMap(SnImeiValidationDto::getSnImei, v -> v));
                    }
                    //获取所有的零售商编码
                    retailerCodeList = snList.stream().map(IntlLduSnDto::getRetailerCode)
                            .distinct().collect(Collectors.toList());

                    int num = 1;
                    for (Row row : sheet) {
                        if (row.getRowNum() == 0) {
                            if (!"国家代码".equals(getCellValue(row.getCell(0))) && !"零售商编码".equals(getCellValue(row.getCell(1)))) {
                                errorList.add(ConstantMessageTemplate.getTemplateError());
                                return new CommonApiResponse<>(errorList);
                            }
                            continue;
                        }
                        String countryCode = this.getCellValue(row.getCell(0));
                        String retailerCode = this.getCellValue(row.getCell(1));
                        String sn = this.getCellValue(row.getCell(2));
                        String lduType = this.getCellValue(row.getCell(3));
                        // 校验数据
                        covertPlanMaintenance(num, countryCode, retailerCode, sn,
                                lduType, confList, errorList, snImeiValidationDtoMap);
                        num++;
                    }
                }
                // 批量停用
                if (CollUtil.isNotEmpty(confList)) {
                    //填充其他参数
                    intlLduSnMapper.batchInsert(fillOtherParams(confList, retailerCodeList));
                }

            } catch (Exception e) {
                log.error("批量新增维护计划异常: {}", e);
            }
        }
        return new CommonApiResponse<>(errorList);
    }

    private List<IntlLduSnDto> getAllSn(Sheet sheet) {
        List<IntlLduSnDto> snList = new ArrayList<>();
        String msg = "";
        for (Row row : sheet) {
            if (row.getRowNum() == 0) {
                continue;
            }
            IntlLduSnDto intlLduSnDto = new IntlLduSnDto();
            intlLduSnDto.setSn(this.getCellValue(row.getCell(2)));
            intlLduSnDto.setRetailerCode(this.getCellValue(row.getCell(1)));
            if (!Objects.isNull(intlLduSnDto)) {
                snList.add(intlLduSnDto);
            }
        }
        return snList;
    }

    //封装QueryDto
    private SnImeiQueryDto convertQueryDto(List<String> snList) {
        SnImeiQueryDto snImeiQueryDto = new SnImeiQueryDto();
        snImeiQueryDto.setSnList(Lists.newArrayList());
        snImeiQueryDto.setImeiList(Lists.newArrayList());
        snImeiQueryDto.setSixNineCodeList(Lists.newArrayList());
        snList.forEach(code -> {
                    SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(code);
                    if (Objects.equals(serialNumberType, SerialNumberType.SN)) {
                        snImeiQueryDto.getSnList().add(code);
                    } else if (Objects.equals(serialNumberType, SerialNumberType.IMEI)) {
                        snImeiQueryDto.getImeiList().add(code);
                    } else if (Objects.equals(serialNumberType, SerialNumberType.CODE69)) {
                        snImeiQueryDto.getSixNineCodeList().add(code);
                    }
                }
        );
        return snImeiQueryDto;
    }

    private List<IntlLduSn> fillOtherParams(List<IntlLduSn> confList, List<String> retailerCodeList) {
        //1、获取所有的sn:填充商品，产品等数据
        List<String> snList = confList.stream().map(IntlLduSn::getSn).collect(Collectors.toList());
        List<SnImeiGoodsInfoDto> snImeiGoodsInfoDtoList = intlLduSnImeiService.queryGoodsInfoBySnImeis(convertQueryDto(snList));
        if (CollectionUtils.isEmpty(snImeiGoodsInfoDtoList)) {
            log.error("根据SN/IMEI查询商品信息异常！");
        }
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoDtoMap = snImeiGoodsInfoDtoList.stream()
                .collect(Collectors.toMap(SnImeiGoodsInfoDto::getSn, v -> v));
        Map<String, SnImeiGoodsInfoDto> snImeiGoodsInfoDtoMap2 = snImeiGoodsInfoDtoList.stream()
                .collect(Collectors.toMap(SnImeiGoodsInfoDto::getImei, v -> v));

        //区域国家，进行填充
        List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectAll();
        //零售商
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectByRetailerCode(retailerCodeList);
        Map<String, IntlRmsRetailer> retailerMap = intlRmsRetailers.stream().distinct()
                .collect(Collectors.toMap(IntlRmsRetailer::getName, v -> v));
        List<IntlLduSnDto> intlLduSnDtoList = new ArrayList<>();
        //填充其他参数
        if (CollUtil.isNotEmpty(snImeiGoodsInfoDtoList)) {
            confList.stream().forEach(conf -> {
                SnImeiGoodsInfoDto snImeiGoodsInfoDto = null;
                SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(conf.getSn());
                if (Objects.equals(serialNumberType, SerialNumberType.SN)) {
                    snImeiGoodsInfoDto = snImeiGoodsInfoDtoMap.get(conf.getSn());
                } else if (Objects.equals(serialNumberType, SerialNumberType.IMEI)) {
                    snImeiGoodsInfoDto = snImeiGoodsInfoDtoMap2.get(conf.getSn());
                }
                IntlLduSnDto intlLduSnDto = ComponentLocator.getConverter().convert(conf, IntlLduSnDto.class);
                addOtherParams(intlLduSnDto, snImeiGoodsInfoDto,
                        intlRmsCountryTimezones, retailerMap);
                intlLduSnDtoList.add(intlLduSnDto);
            });
        }
        confList = ComponentLocator.getConverter()
                .convertList(intlLduSnDtoList, IntlLduSn.class);
        return confList;
    }

    private boolean checkRetailerCodeAndCountry(String retailerCode, String countryCode) {
        SearchRetailerReqDto searchRetailerReqDto = new SearchRetailerReqDto();
        searchRetailerReqDto.setKeyword(retailerCode);
        searchRetailerReqDto.setCountryCode(Arrays.asList(countryCode));
        searchRetailerReqDto.setId(retailerCode);
        List<SearchRetailerResponseDto> queryRetailerByNameOrCode = intlRmsRetailerReadMapper.queryRetailerByNameOrCode(DEFAULT_LIMIT, searchRetailerReqDto);
        boolean flag = true;
        if (!CollectionUtils.isEmpty(queryRetailerByNameOrCode)) {
            flag = false;
        }
        return flag;
    }

    private void covertPlanMaintenance(int num, String countryCode, String retailerCode,
                                       String sn, String lduType, List<IntlLduSn> confList, List<String> errorList,
                                       Map<String, SnImeiValidationDto> snImeiValidationDtoMap) {
        //校验数据：
        //根据这些信息查询零售商信息，零售商名称等其他数据
        IntlLduSn intlLduSnDto = new IntlLduSn();
        intlLduSnDto.setRetailerCode(retailerCode);
        intlLduSnDto.setSn(sn);
        intlLduSnDto.setLduType(lduType);
        intlLduSnDto.setCountryCode(countryCode);

        boolean flag = checkRetailerCodeAndCountry(retailerCode, countryCode);

        StringBuilder msg = new StringBuilder();
        if (StringUtils.isEmpty(sn)) {
            msg.append(ConstantMessageTemplate.getSnEmptyMessage(num));
        } else {
            /**
             * 校验数据：
             *1、 LDU类型：必填，单选，选项值【大货（Mass Production Version）、专样（Customized Version）】
             * 2、通过IMEI服务接口验证IMEI的，产品校验：根据SN/IMEI/69码调imei服务接口，判断是否为小米的商品
             *3、重复性校验：根据SN/IMEI校验后台已上传的LDU数据，判断是否已有记录，如有，则校验不通过
             */
            if (Objects.isNull(snImeiValidationDtoMap.get(sn)) || !snImeiValidationDtoMap.get(sn).getIsValid()) {
                msg.append(ConstantMessageTemplate.getXiaomiSnValidationFailedMessage(num));
            }
            if (flag) {
                msg.append(ConstantMessageTemplate.matchRetailerCodeAndCountry(num));
            }
            if (StringUtils.isEmpty(countryCode)) {
                msg.append(ConstantMessageTemplate.getXiaomiCountryCodeEmptyMessage(num));
            }
            if (StringUtils.isEmpty(retailerCode)) {
                msg.append(ConstantMessageTemplate.getRetailerCodeEmptyMessage(num));
            }

            if (!LduTypeEnum.MASS_PRODUCTION_VERSION.getType().equals(intlLduSnDto.getLduType()) &&
                    !LduTypeEnum.CUSTOMIZED_VERSION.getType().equals(intlLduSnDto.getLduType())) {
                msg.append(ConstantMessageTemplate.getLduTypeErrorMessage(num));
            } else {
                IntlLduSn snEntity = intlLduSnMapper.selectBySn(sn);
                if (!Objects.isNull(snEntity)) {
                    msg.append(ConstantMessageTemplate.getSnDuplicateMessage(num));
                }
            }
        }
        if (StringUtils.isEmpty(msg.toString())) {
            confList.add(intlLduSnDto);
        } else {
            errorList.add(msg.toString());
        }
    }

    @Override
    public CommonApiResponse<String> downLoadLduTemp(BathConReq query) {
        String urlTemplate = "";
        if (Objects.equals(query.getType(), "1")) {
            //批量上传模版
            urlTemplate = lduConfig.getPlanUploadUrl();
        }
        if (Objects.equals(query.getType(), "2")) {
            //批量停用模版
            urlTemplate = lduConfig.getPlanStopUrl();
        }
        return new CommonApiResponse<>(urlTemplate);
    }

    @Override
    public List<IntlLduSnDto> selectBySnAndCountryCode(List<SnImeiGoodsInfoReq> goodsList, String countryCode) {
        List<IntlLduSn> intlLduSnList = intlLduSnMapper.selectBySnAndCountryCode(goodsList, countryCode);
        List<IntlLduSnDto> intlLduSnDtoList = ComponentLocator.getConverter().convertList(intlLduSnList, IntlLduSnDto.class);
        return intlLduSnDtoList;
    }

    @Override
    public void batchUpdateBySn(List<IntlLduSnDto> intlLduSnDtoList, String countryCode) {
        List<IntlLduSn> intlLduSns = ComponentLocator.getConverter().convertList(intlLduSnDtoList, IntlLduSn.class);
        intlLduSnMapper.batchUpdateBySnCountryCode(intlLduSns, countryCode, 1);
    }

    private String getValueById(String id) {
        if (StringUtils.isEmpty(id)) {
            return "";
        } else {
            return id;
        }
    }

    private void addOtherParams(IntlLduSnDto confSn, SnImeiGoodsInfoDto snImeiGoodsInfoDto,
                                List<IntlRmsCountryTimezone> rmsCountryTimezones,
                                Map<String, IntlRmsRetailer> retailerMap) {
        if (!Objects.isNull(snImeiGoodsInfoDto)) {
            confSn.setImei1(snImeiGoodsInfoDto.getImei());
            confSn.setImei2(snImeiGoodsInfoDto.getImei2());
            confSn.setSn(snImeiGoodsInfoDto.getSn());
            confSn.setProductLine(snImeiGoodsInfoDto.getProductLine());
            //根据产品线查询项目代码
            confSn.setProductId(getValueById(snImeiGoodsInfoDto.getProductId()));
            confSn.setProductName(getValueById(snImeiGoodsInfoDto.getProductName()));
            confSn.setProjectCode(getValueById(snImeiGoodsInfoDto.getProjectCode()));
            confSn.setGoodsId(getValueById(snImeiGoodsInfoDto.getGoodsId()));
            confSn.setGoodsName(getValueById(snImeiGoodsInfoDto.getGoodsName()));
            confSn.setRamCapacity(getValueById(snImeiGoodsInfoDto.getRam()));
            confSn.setRomCapacity(getValueById(snImeiGoodsInfoDto.getRom()));
        }

        if (!CollectionUtils.isEmpty(rmsCountryTimezones)) {
            //转换为map
            Map<String, IntlRmsCountryTimezone> intlRmsCountryTimezoneMap = rmsCountryTimezones.stream()
                    .collect(Collectors.toMap(IntlRmsCountryTimezone::getCountryCode, v -> v));
            IntlRmsCountryTimezone countryTimezone = intlRmsCountryTimezoneMap.get(confSn.getCountryCode());
            confSn.setCountry(Objects.isNull(countryTimezone) ? "" : countryTimezone.getCountryName());
            confSn.setRegionCode(Objects.isNull(countryTimezone) ? "" : countryTimezone.getAreaCode());
            confSn.setRegion(Objects.isNull(countryTimezone) ? "" : countryTimezone.getArea());
        }
        confSn.setRetailerName(Objects.isNull(retailerMap.get(confSn.getRetailerCode())) ? "" :
                retailerMap.get(confSn.getRetailerCode()).getRetailerName());

        confSn.setChannelType(Objects.isNull(retailerMap.get(confSn.getRetailerCode())) ? "" :
                RmsRetailerEnum.getEnumByCode(retailerMap.get(confSn.getRetailerCode()).getRetailerChannelType()));

        confSn.setCreateUserId(RpcContext.getContext().getAttachments().get("$upc_account"));
        confSn.setCreateUserName(RpcContext.getContext().getAttachments().get("$upc_userName"));
        confSn.setPlanCreateDate(System.currentTimeMillis());
        confSn.setIsReport(0);
        confSn.setStatus(1);
    }

    @Override
    public void batchInsert(List<IntlLduSnDto> intlLduSnList) {
        List<IntlLduSn> intlLduSns = ComponentLocator.getConverter().convertList(intlLduSnList, IntlLduSn.class);
        intlLduSnMapper.batchInsert(intlLduSns);
    }

    private String conertOtherParams(IntlLduSnDto confSn) {
        /**
         *  1、根据SN或imei查询IMEI服务接口返回sn和imei、goodsId, IMEI1、IMEI2、SN
         * 2、根据goodsId调商品主数据接口，goodsId、goodsName，
         * 3、根据goodsId查询产品线接口带出：Product Name、产品线
         * 4、项目代码，RAM,ROM
         * 5、校验零售商和国家是否匹配
         */
        String sn = confSn.getSn();
        List<SnImeiValidationDto> snImeiValidationDtoList = intlLduSnImeiService.validateSnImeiInfo(convertQueryDto(Arrays.asList(sn)));

        SnImeiGoodsInfoDto snImeiGoodsInfoDto = intlLduSnImeiService.getGoodsInfoBySnImei(sn);
        if (Objects.isNull(snImeiGoodsInfoDto)) {
            return ConstantMessageTemplate.getXiaomiSnValidationFailedMessage();
        }
        //区域国家，进行填充
        List<IntlRmsCountryTimezone> intlRmsCountryTimezones = intlRmsCountryTimezoneMapper.selectAll();
        //零售商
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectByRetailerCode(Arrays.asList(confSn.getRetailerCode()));
        //校验零售商和国家是否匹配
        boolean flag = checkRetailerCodeAndCountry(confSn.getRetailerCode(), confSn.getCountryCode());

        Map<String, IntlRmsRetailer> retailerMap = intlRmsRetailers.stream()
                .distinct().collect(Collectors.toMap(IntlRmsRetailer::getName, v -> v));
        addOtherParams(confSn, snImeiGoodsInfoDto, intlRmsCountryTimezones, retailerMap);
        String msg = "";
        /**
         * 校验数据：
         *1、 LDU类型：必填，单选，选项值【大货（Mass Production Version）、专样（Customized Version）】
         * 2、通过IMEI服务接口验证IMEI的，产品校验：根据SN/IMEI/69码调imei服务接口，判断是否为小米的商品
         *3、重复性校验：根据SN/IMEI校验后台已上传的LDU数据，判断是否已有记录，如有，则校验不通过
         */
        if (!LduTypeEnum.MASS_PRODUCTION_VERSION.getType().equals(confSn.getLduType()) &&
                !LduTypeEnum.CUSTOMIZED_VERSION.getType().equals(confSn.getLduType())) {
            msg = ConstantMessageTemplate.getLduTypeErrorMessage();
        } else if (CollectionUtils.isEmpty(snImeiValidationDtoList) || !Boolean.TRUE.equals(snImeiValidationDtoList.get(0).getIsValid())) {
            msg = ConstantMessageTemplate.getXiaomiSnValidationFailedMessage();
        } else if (flag) {
            msg = ConstantMessageTemplate.matchRetailerCodeAndCountry();
        } else {
            IntlLduSn snEntity = intlLduSnMapper.selectBySn(sn);
            if (!Objects.isNull(snEntity)) {
                msg = ConstantMessageTemplate.getSnDuplicateMessage();
            }
        }
        return msg;
    }
}
