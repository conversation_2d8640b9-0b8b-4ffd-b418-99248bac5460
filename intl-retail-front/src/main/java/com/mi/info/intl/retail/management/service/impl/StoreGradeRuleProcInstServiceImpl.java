package com.mi.info.intl.retail.management.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BusinessBpmCallBack;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.ProcessInstanceStatus;
import com.mi.info.intl.retail.management.entity.CommonApproveLog;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.CommonApproveLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.service.enums.ApprovalStatus;
import com.mi.info.intl.retail.management.service.enums.RuleStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;

@Slf4j
@Service
public class StoreGradeRuleProcInstServiceImpl implements BusinessBpmCallBack {
    
    @Resource
    private StoreGradeRuleMapper storeGradeRuleMapper;
    
    @Resource
    private CommonApproveLogMapper commonApproveLogMapper;


    @Override
    public BpmApproveBusinessCodeEnum getBpmApproveTypeEnum() {
        return BpmApproveBusinessCodeEnum.STORE_GRADE_RULE;
    }

    @Override
    public void doCallback(BpmCallBackParamDto response) {
        // 如果状态为驳回，则进行驳回处理
        if (response.getStatus().equals(ProcessInstanceStatus.REJECTED.getCode())) {
            rejected(response);
        }
        // 状态为同意，则进行同意处理
        if (response.getStatus().equals(ProcessInstanceStatus.COMPLETED.getCode())) {
            approve(response);
        }
    }
    
    // 驳回处理
    private void rejected(BpmCallBackParamDto response) {
        // 修改审批记录为驳回
        CommonApproveLog commonApproveLog = getApproveLogByBusinessKey(response.getBusinessKey());
        if (ObjectUtil.isEmpty(commonApproveLog)) {
            log.info("未找到对应的审批请求记录,businessKey:{}", response.getBusinessKey());
            return;
        }
        updateStatus(response, ApprovalStatus.REJECT.getCode());
    }
    
    /**
     * 审批通过处理
     * @param response
     */
    private void approve(BpmCallBackParamDto response) {
        CommonApproveLog commonApproveLog = getApproveLogByBusinessKey(response.getBusinessKey());
        if (ObjectUtil.isEmpty(commonApproveLog)) {
            log.info("未找到对应的审批请求记录,businessKey:{}", response.getBusinessKey());
            return;
        }
        updateStatus(response, ApprovalStatus.PASS.getCode());
        // todo 审批通过后后调用计算等级接口
        
        // todo 推送计算结果
    }
    
    /**
     * 更新审批状态和门店规则状态
     *
     * @param status
     */
    private Long updateStatus(BpmCallBackParamDto response, Integer status) {
        // 修改审批记录为驳回
        CommonApproveLog commonApproveLog = getApproveLogByBusinessKey(response.getBusinessKey());
        if (ObjectUtil.isEmpty(commonApproveLog)) {
            log.info("未找到对应的审批请求记录,businessKey:{}", response.getBusinessKey());
            return null;
        }
        // 审批时间
        Date approvalTime = new Date();
        commonApproveLog.setTargetBody(JSONObject.toJSONString(response));
        
        commonApproveLog.setApproveUserId(response.getAssignee().getUserName());
        commonApproveLog.setApprovedTime(approvalTime);
        commonApproveLog.setFlowStatus(status);
        commonApproveLogMapper.updateById(commonApproveLog);
        
        // 修改门店等级规则为驳回
        String id = commonApproveLog.getBusinessId();
        StoreGradeRule storeGradeRule = storeGradeRuleMapper.selectById(Long.valueOf(id));
        if (ObjectUtil.isEmpty(storeGradeRule)) {
            log.info("未找到对应审批请求记录的门店等级规则,id:{}", id);
            throw new RuntimeException("The store level rule for the approval request record is not found");
        }
        Integer ruleStatus = ApprovalStatus.PASS.getCode().equals(status) ? RuleStatusEnum.IN_EFFECT.getCode() : RuleStatusEnum.DRAFT.getCode();
        storeGradeRule.setRulesStatus(ruleStatus);
        storeGradeRule.setApproveStatus(status);
        storeGradeRule.setApprovedTime(LocalDateTime.now());
        storeGradeRuleMapper.updateById(storeGradeRule);
        return Long.valueOf(storeGradeRule.getId());
    }
    
    
    /**
     * 根据业务key获取审批记录
     *
     * @param businessKey
     * @return
     */
    private CommonApproveLog getApproveLogByBusinessKey(String businessKey) {
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(businessKey), CommonApproveLog::getBusinessId, businessKey);
        queryWrapper.eq(CommonApproveLog::getFlowStatus, ApprovalStatus.APPROVAL_ING.getCode());
        CommonApproveLog commonApproveLog = commonApproveLogMapper.selectOne(queryWrapper);
        return commonApproveLog;
    }
}
