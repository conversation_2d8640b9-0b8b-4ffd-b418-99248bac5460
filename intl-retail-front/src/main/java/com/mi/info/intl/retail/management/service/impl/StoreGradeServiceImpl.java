package com.mi.info.intl.retail.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerChange;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeChange;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.management.entity.CommonChangeLog;
import com.mi.info.intl.retail.management.mapper.CommonChangeLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeMapper;
import com.mi.info.intl.retail.management.service.enums.ChannelTypeEnum;
import com.mi.info.intl.retail.management.service.enums.StoreGradeEnum;
import com.mi.info.intl.retail.model.RetailerChannelGradeCount;
import com.mi.info.intl.retail.model.ChannelTypeStatistics;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import com.xiaomi.cnzone.maindataapi.api.PositionProvider;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgParamEntity;
import com.xiaomi.cnzone.maindataapi.model.req.store.EditPositionInfoRequest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.rpc.MainDataRpc;
import com.xiaomi.cnzone.commons.utils.StringUtils;
import com.xiaomi.cnzone.maindataapi.model.OrgDataDto;
import com.xiaomi.cnzone.maindataapi.model.OrgResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import java.util.Objects;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;

@DubboService(group = "${retail.dubbo.group:}", interfaceClass = StoreGradeService.class)
@Slf4j
@Service
public class StoreGradeServiceImpl implements StoreGradeService {

    @Resource
    PositionProvider positionProvider;

    @Resource
    StoreGradeMapper storeGradeMapper;

    @Resource
    private MainDataRpc mainDataRpc;

    @Resource
    private StoreGradeRuleMapper storeGradeRuleMapper;

    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Resource
    private CommonChangeLogMapper commonChangeLogMapper;

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Resource
    private StoreGradeRelationMapper storeGradeRelationMapper;

    /**
     * 门店变化触发
     *
     * @param storeCode
     */
    @Override
    public void storeChangeTrigger(String storeCode) {
        //先查询retailer、kapa、grade信息
        log.info("开始处理门店变化触发，门店编码：{}", storeCode);

        if (storeCode == null) {
            throw new RuntimeException("Store code cannot be empty");
        }

        // 1. 验证门店是否存在
        OrgResponse orgResponse = mainDataRpc.selectStoreByOrgIds(Lists.newArrayList(storeCode));
        if (Objects.isNull(orgResponse)) {
            throw new RuntimeException("Store does not exist");
        }
        OrgDataDto orgDataDto = orgResponse.getOrgList().get(0);
        // 2. 获取门店信息
        String retailerCode = orgDataDto.getOrgBuild().getRetailerCode();
        Integer capacity = orgDataDto.getOrgExtension().getCapaMonth();
        LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
        retailerQueryWrapper.eq(IntlRmsRetailer::getCrmCode, retailerCode);
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
        if (CollectionUtils.isEmpty(intlRmsRetailers)) {
            log.error("Retailer information is empty");
            throw new RuntimeException("Retailer information is empty");
        }
        Integer channelTypeKey = intlRmsRetailers.get(0).getRetailerChannelType();
        String countryCode = intlRmsRetailers.get(0).getCountryCode();
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.fromKey(channelTypeKey);
        //计算新的grade
        StoreGradeRule effectiveRule = getEffectiveRule(retailerCode, channelTypeEnum.getValue(), countryCode);
        if (effectiveRule == null) {
            log.warn("No effective rule found, retailer code: {}, channel type: {}", retailerCode, channelTypeEnum.getValue());
            return;
        }

        // 4. 重新计算门店grade
        String newGrade = getStoreGrade(capacity, effectiveRule);
        Integer storeGrading = orgDataDto.getOrgCategory().getStoreGrading() == null ?
                null : orgDataDto.getOrgCategory().getStoreGrading().intValue();
        StoreGradeEnum storeGradeEnum = StoreGradeEnum.fromKey(storeGrading);
        if (Objects.isNull(storeGradeEnum)) {
            log.warn("Invalid store grade, store code: {}, store grade: {}", storeCode, storeGrading);
            return;
        }
        String oldGrade = storeGradeEnum.getValue();

        log.info("Store grade calculation result - store code: {}, capacity: {}, old grade: {}, new grade: {}",
                storeCode, capacity, oldGrade, newGrade);

        // 5. 如果有变化则更新
        if (!Objects.equals(oldGrade, newGrade)) {
            updateStoreGrade(storeCode, newGrade, effectiveRule.getId());
            log.info("Store grade updated, store code: {}, new grade: {}", storeCode, newGrade);
            CommonChangeLog commonChangeLog = new CommonChangeLog();
            commonChangeLog.setBusinessKey("store_grade_rule");
            commonChangeLog.setBusinessId(storeCode);
            commonChangeLog.setStoreGrade(newGrade);
            commonChangeLog.setChannelType(channelTypeEnum.getValue());
            commonChangeLog.setCompleteStatus(1);
            commonChangeLog.setChangeReason("store_grade_change");
            commonChangeLog.setStoreGradeRuleId(effectiveRule.getId());

            commonChangeLogMapper.insert(commonChangeLog);
        } else {
            log.info("Store grade unchanged, store code: {}, grade: {}", storeCode, newGrade);
        }
    }

    public void storeChangeTrigger(StoreGradeChange storeGradeChange) {
        try {
            log.info("Start processing store change trigger, store code: {}", storeGradeChange.getStoreCode());

            if (storeGradeChange.getStoreCode() == null) {
                throw new RuntimeException("Store code cannot be empty");
            }

            // 1. 验证门店是否存在
            OrgResponse orgResponse = mainDataRpc.selectStoreByOrgIds(Lists.newArrayList(storeGradeChange.getStoreCode()));
            if (Objects.isNull(orgResponse)) {
                throw new RuntimeException("Store does not exist");
            }
            OrgDataDto orgDataDto = orgResponse.getOrgList().get(0);
            // 2. 获取门店信息
            String retailerCode = storeGradeChange.getRetailerCode();
            String channelType = storeGradeChange.getChannelType();
            Integer capacity = orgDataDto.getOrgExtension().getCapaMonth();

            if (StringUtils.isBlank(retailerCode) || StringUtils.isBlank(channelType) || capacity == null) {
                log.warn("Store information incomplete, skipping grade calculation. retailerCode: {}, channelType: {}, capacity: {}",
                        retailerCode, channelType, capacity);
                return;
            }

            // 3. 根据retailerCode和channelType查询相应的生效规则数据
            StoreGradeRule effectiveRule = getEffectiveRule(retailerCode, channelType, null);
            if (effectiveRule == null) {
                log.warn("No effective rule found, retailer code: {}, channel type: {}", retailerCode, channelType);
                return;
            }

            // 4. 重新计算门店grade
            String newGrade = getStoreGrade(capacity, effectiveRule);
            Byte storeGrading = orgDataDto.getOrgCategory().getStoreGrading();
            String oldGrade = "test";

            log.info("Store grade calculation result - store code: {}, capacity: {}, old grade: {}, new grade: {}",
                    storeGradeChange.getStoreCode(), capacity, oldGrade, newGrade);

            // 5. 如果有变化则更新
            if (!Objects.equals(oldGrade, newGrade)) {
                updateStoreGrade(storeGradeChange.getStoreCode(), newGrade, effectiveRule.getId());
                log.info("Store grade updated, store code: {}, new grade: {}", storeGradeChange.getStoreCode(), newGrade);
            } else {
                log.info("Store grade unchanged, store code: {}, grade: {}", storeGradeChange.getStoreCode(), newGrade);
            }

        } catch (Exception e) {
            log.error("Store change trigger processing failed, store code: {}", storeGradeChange.getStoreCode(), e);
            throw e;
        }
    }

    /**
     * 获取生效的规则数据
     *
     * @param retailerCode 零售商编码
     * @param channelType  渠道类型
     * @param countryCode  国家编码
     * @return 规则数据
     */
    private StoreGradeRule getEffectiveRule(String retailerCode, String channelType, String countryCode) {
        LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
        if (Objects.equals(ChannelTypeEnum.IR.getValue(), channelType)) {
            queryWrapper.eq(StoreGradeRule::getCountryCode, countryCode);
        } else {
            queryWrapper.eq(StoreGradeRule::getRetailerCode, retailerCode);
        }
        queryWrapper.eq(StoreGradeRule::getChannelType, channelType)
                .eq(StoreGradeRule::getRulesStatus, 1) // 生效状态
                .eq(StoreGradeRule::getApproveStatus, 1) // 审批通过状态
                .orderByDesc(StoreGradeRule::getApplicationTime)
                .last("LIMIT 1");

        return storeGradeRuleMapper.selectOne(queryWrapper);
    }

    /**
     * 更新门店等级
     *
     * @param storeCode 门店编码
     * @param newGrade  新等级
     * @param ruleId    规则ID
     */
    private void updateStoreGrade(String storeCode, String newGrade, Integer ruleId) {
        try {
            // TODO: 这里需要根据实际的业务逻辑来实现门店等级的更新
            OrgParamEntity orgParamEntity = new OrgParamEntity();
            mainDataRpc.pushEditStoreBeta(orgParamEntity);
            // 可能需要调用其他服务或更新其他表
            log.info("Update store grade - store code: {}, new grade: {}, rule ID: {}", storeCode, newGrade, ruleId);

            // 示例：调用门店服务更新等级
            // storeService.updateStoreGrade(storeCode, newGrade);


        } catch (Exception e) {
            log.error("Failed to update store grade, store code: {}, new grade: {}", storeCode, newGrade, e);
            throw new RuntimeException("Failed to update store grade: " + e.getMessage());
        }
    }

    private String getStoreGrade(Integer capa, StoreGradeRule storeGradeRule) {
        if (capa >= storeGradeRule.getSMinCount()) {
            return "S";
        }
        if (capa >= storeGradeRule.getAMinCount()) {
            return "A";
        }
        if (capa >= storeGradeRule.getBMinCount()) {
            return "B";
        }
        if (capa >= storeGradeRule.getCMinCount()) {
            return "C";
        }
        if (capa >= storeGradeRule.getDMinCount()) {
            return "D";
        }
        return null;
    }

    /**
     * 商变化触发
     *
     * @param retailerChange
     */
    @Override
    public void retailerChangeTrigger(RetailerChange retailerChange) {
        log.info("Retailer change trigger processing, retailer code: {}", retailerChange.getRetailerCode());
        if (retailerChange == null || retailerChange.getRetailerCode() == null) {
            throw new RuntimeException("Retailer code cannot be empty");
        }
        String retailerCode = retailerChange.getRetailerCode();
        LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
        retailerQueryWrapper.eq(IntlRmsRetailer::getCrmCode, retailerCode);
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
        if (CollectionUtils.isEmpty(intlRmsRetailers)) {
            log.error("Retailer information is empty");
            throw new RuntimeException("Retailer information is empty");
        }
        Integer channelTypeKey = intlRmsRetailers.get(0).getRetailerChannelType();
        String countryCode = intlRmsRetailers.get(0).getCountryCode();
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.fromKey(channelTypeKey);
        // 计算规则
        StoreGradeRule effectiveRule = getEffectiveRule(retailerCode, channelTypeEnum.getValue(), countryCode);
        if (effectiveRule == null) {
            log.warn("No effective rule found, retailer code: {}, channel type: {}", retailerCode, channelTypeEnum.getValue());
            return;
        }
        int pageNum = 1;
        int pageSize = 100;
        boolean hasMore = true;
        while (hasMore) {
            // 分页查询门店
            Page<IntlRmsStore> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<IntlRmsStore> storeQueryWrapper = Wrappers.lambdaQuery();
            storeQueryWrapper.eq(IntlRmsStore::getRetailerId, retailerCode)
                    .select(IntlRmsStore::getCode);
            Page<IntlRmsStore> storePage = intlRmsStoreMapper.selectPage(page, storeQueryWrapper);
            List<String> storeCodes = storePage.getRecords().stream()
                    .map(IntlRmsStore::getCode)
                    .collect(Collectors.toList());
            if (storeCodes == null || storeCodes.isEmpty()) {
                break;
            }
            calculationStoreGrade(effectiveRule, storeCodes, channelTypeEnum.getValue());
            hasMore = storeCodes.size() == pageSize;
            pageNum++;
        }
    }

    /**
     * 规则变化触发
     *
     * @param storeGradeRuleReq
     */
    @Override
    public void ruleChangeTrigger(StoreGradeRuleReq storeGradeRuleReq) {
        log.info("Rule change trigger processing, ruleId: {}", storeGradeRuleReq.getId());
        StoreGradeRule storeGradeRule = storeGradeRuleMapper.selectById(storeGradeRuleReq.getId());
        if (storeGradeRule == null) {
            log.error("Rule does not exist, ruleId: {}", storeGradeRuleReq.getId());
            throw new RuntimeException("Rule does not exist");
        }

        int pageNum = 1;
        int pageSize = 100;
        boolean hasMore = true;
        while (hasMore) {
            // 分页查询门店
            Page<IntlRmsStore> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<IntlRmsStore> storeQueryWrapper = Wrappers.lambdaQuery();
            if (Objects.equals(ChannelTypeEnum.IR.getValue(), storeGradeRule.getChannelType())) {
                storeQueryWrapper.eq(IntlRmsStore::getCountyCode, storeGradeRule.getCountryCode())
                        .eq(IntlRmsStore::getChannelType, ChannelTypeEnum.IR.getKey())
                        .select(IntlRmsStore::getCode);
            } else {
                storeQueryWrapper.eq(IntlRmsStore::getRetailerId, storeGradeRule.getRetailerCode())
                        .select(IntlRmsStore::getCode);
            }

            Page<IntlRmsStore> storePage = intlRmsStoreMapper.selectPage(page, storeQueryWrapper);
            List<String> storeCodes = storePage.getRecords().stream()
                    .map(IntlRmsStore::getCode)
                    .collect(Collectors.toList());
            if (storeCodes == null || storeCodes.isEmpty()) {
                break;
            }
            calculationStoreGrade(storeGradeRule, storeCodes, null);
            hasMore = storeCodes.size() == pageSize;
            pageNum++;
        }
    }

    private void calculationStoreGrade(StoreGradeRule storeGradeRule, List<String> storeCodes, String channelType) {
        for (String storeCode : storeCodes) {
            try {
                // 查询门店信息
                OrgResponse orgResponse = mainDataRpc.selectStoreByOrgIds(Lists.newArrayList(storeCode));
                if (Objects.isNull(orgResponse)) {
                    log.warn("Store does not exist, storeCode: {}", storeCode);
                    continue;
                }
                OrgDataDto orgDataDto = orgResponse.getOrgList().get(0);
                Integer capacity = orgDataDto.getOrgExtension().getCapaMonth();
                Integer storeGrading = orgDataDto.getOrgCategory().getStoreGrading() == null ?
                        null : orgDataDto.getOrgCategory().getStoreGrading().intValue();
                StoreGradeEnum storeGradeEnum = StoreGradeEnum.fromKey(storeGrading);
                if (Objects.isNull(storeGradeEnum)) {
                    log.warn("Invalid store grade, store code: {}, store grade: {}", storeCode, storeGrading);
                    continue;
                }
                String oldGrade = storeGradeEnum.getValue();
                String newGrade = getStoreGrade(capacity, storeGradeRule);
                if (!Objects.equals(oldGrade, newGrade)) {
                    updateStoreGrade(storeCode, newGrade, storeGradeRule.getId());
                    log.info("Store grade updated, store code: {}, new grade: {}", storeCode, newGrade);
                    CommonChangeLog commonChangeLog = new CommonChangeLog();
                    commonChangeLog.setBusinessKey("store_grade_rule");
                    commonChangeLog.setBusinessId(storeCode);
                    commonChangeLog.setStoreGrade(newGrade);
                    if (channelType != null) {
                        commonChangeLog.setChannelType(channelType);
                    } else {
                        commonChangeLog.setChannelType(storeGradeRule.getChannelType());
                    }
                    commonChangeLog.setCompleteStatus(1);
                    commonChangeLog.setChangeReason("store_grade_change");
                    commonChangeLog.setStoreGradeRuleId(storeGradeRule.getId());
                    commonChangeLogMapper.insert(commonChangeLog);
                } else {
                    log.info("Store grade unchanged, store code: {}, grade: {}", storeCode, newGrade);
                }
            } catch (Exception e) {
                log.error("Failed to process store change for storeCode: {}", storeCode, e);
            }
        }
    }


    /**
     * 获取每个渠道类型的统计信息
     *
     * @return 渠道类型统计列表
     */
    @Override
    public List<ChannelTypeStatistics> getChannelTypeStatistics() {
        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        List<RetailerChannelGradeCount> channelGradeCounts = storeGradeMapper.selectRetailerChannelGradeCount(countryCode);

        // 按渠道类型分组，包括null值
        Map<String, List<RetailerChannelGradeCount>> channelTypeMap = channelGradeCounts.stream()
                .collect(Collectors.groupingBy(
                        count -> count.getRetailerChannelType() != null ? count.getRetailerChannelType() : "EMPTY",
                        Collectors.toList()
                ));

        List<ChannelTypeStatistics> result = new ArrayList<>();

        for (Map.Entry<String, List<RetailerChannelGradeCount>> entry : channelTypeMap.entrySet()) {
            String channelType = entry.getKey();
            List<RetailerChannelGradeCount> counts = entry.getValue();

            // 计算完成和未完成数量，包括null值
            Integer completeCount = counts.stream()
                    .filter(count -> "1".equals(count.getGradeCalFlag()))
                    .mapToInt(count -> count.getCount().intValue())
                    .sum();

            Integer notCompleteCount = counts.stream()
                    .filter(count -> "0".equals(count.getGradeCalFlag()) || count.getGradeCalFlag() == null)
                    .mapToInt(count -> count.getCount().intValue())
                    .sum();

            Integer totalCount = completeCount + notCompleteCount;

            // 计算百分比
            Double completePercentage = totalCount > 0 ? (double) completeCount / totalCount : 0.0;
            Double notCompletePercentage = totalCount > 0 ? (double) notCompleteCount / totalCount : 0.0;

            ChannelTypeStatistics statistics = new ChannelTypeStatistics(
                    channelType, completeCount, notCompleteCount, totalCount,
                    completePercentage, notCompletePercentage
            );

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCompleteCount = result.stream()
                .mapToInt(ChannelTypeStatistics::getCompleteCount)
                .sum();
        Integer totalNotCompleteCount = result.stream()
                .mapToInt(ChannelTypeStatistics::getNotCompleteCount)
                .sum();
        Integer grandTotal = totalCompleteCount + totalNotCompleteCount;

        Double totalCompletePercentage = grandTotal > 0 ? (double) totalCompleteCount / grandTotal : 0.0;
        Double totalNotCompletePercentage = grandTotal > 0 ? (double) totalNotCompleteCount / grandTotal : 0.0;

        ChannelTypeStatistics totalStatistics = new ChannelTypeStatistics(
                "ALL", totalCompleteCount, totalNotCompleteCount, grandTotal,
                totalCompletePercentage, totalNotCompletePercentage
        );

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    /**
     * 获取门店等级统计信息
     *
     * @param ruleId 门店等级规则ID
     * @return 门店等级统计列表
     */
    @Override
    public List<StoreGradeCompleteStatistics> getStoreGradeCompleteStatistics(Integer ruleId) {
        // 通过 ruleId 查询门店等级规则
        StoreGradeRule storeGradeRule = storeGradeRuleMapper.selectById(ruleId);
        if (storeGradeRule == null) {
            log.warn("门店等级规则不存在，ruleId: {}", ruleId);
            return new ArrayList<>();
        }

        // 根据计算方法决定走不同的逻辑
        String method = storeGradeRule.getMethod();
        if ("CURRENT".equals(method)) {
            // 当前逻辑：基于现有门店等级统计
            return getCurrentGradeStatistics(storeGradeRule);
        } else if ("RELATION".equals(method)) {
            // 基于门店等级关系表统计
            return getRelationGradeStatistics(storeGradeRule);
        } else {
            // TODO: 其他计算方法的逻辑
            log.info("暂未实现的计算方法: {}, ruleId: {}", method, ruleId);
            return new ArrayList<>();
        }
    }

    /**
     * 基于现有门店等级统计的逻辑
     *
     * @param storeGradeRule 门店等级规则
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> getCurrentGradeStatistics(StoreGradeRule storeGradeRule) {
        // 从规则中获取渠道类型和渠道代码
        String retailerChannelType = storeGradeRule.getChannelType();
        String retailerChannelCode = storeGradeRule.getRetailerCode();

        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        List<StoreGradeCompleteCount> gradeCompleteCounts = storeGradeMapper.selectStoreGradeCompleteCount(countryCode,
                retailerChannelType, retailerChannelCode);

        List<StoreGradeCompleteStatistics> result = new ArrayList<>();

        // 先计算总数
        Integer totalStoreCount = gradeCompleteCounts.stream()
                .mapToInt(count -> count.getCount().intValue())
                .sum();

        for (StoreGradeCompleteCount count : gradeCompleteCounts) {
            String grade = count.getGrade() != null ? count.getGrade() : "EMPTY";
            Integer storeCount = count.getCount().intValue();

            // 计算占总数的百分比
            Double percentage = totalStoreCount > 0 ? (double) storeCount / totalStoreCount : 0.0;

            StoreGradeCompleteStatistics statistics = new StoreGradeCompleteStatistics(
                    grade, storeCount, percentage, 0L
            );

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCount = result.stream()
                .mapToInt(StoreGradeCompleteStatistics::getCount)
                .sum();

        Double totalPercentage = 1.0; // 总计占总数的100%

        StoreGradeCompleteStatistics totalStatistics = new StoreGradeCompleteStatistics(
                "ALL", totalCount, totalPercentage, 0L
        );

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    /**
     * 基于门店等级关系表统计的逻辑
     *
     * @param storeGradeRule 门店等级规则
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> getRelationGradeStatistics(StoreGradeRule storeGradeRule) {
        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        Integer ruleId = storeGradeRule.getId();

        List<StoreGradeCompleteCount> gradeCompleteCounts = storeGradeRelationMapper.selectStoreGradeCountByRuleId(ruleId, countryCode);

        List<StoreGradeCompleteStatistics> result = new ArrayList<>();

        // 先计算总数
        Integer totalStoreCount = gradeCompleteCounts.stream()
                .mapToInt(count -> count.getCount().intValue())
                .sum();

        for (StoreGradeCompleteCount count : gradeCompleteCounts) {
            String grade = count.getGrade() != null ? count.getGrade() : "EMPTY";
            Integer storeCount = count.getCount().intValue();

            // 计算占总数的百分比
            Double percentage = totalStoreCount > 0 ? (double) storeCount / totalStoreCount : 0.0;

            StoreGradeCompleteStatistics statistics = new StoreGradeCompleteStatistics(
                    grade, storeCount, percentage, 0L
            );

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCount = result.stream()
                .mapToInt(StoreGradeCompleteStatistics::getCount)
                .sum();

        Double totalPercentage = 1.0; // 总计占总数的100%

        StoreGradeCompleteStatistics totalStatistics = new StoreGradeCompleteStatistics(
                "ALL", totalCount, totalPercentage, 0L
        );

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    @Override
    public void editPositionInfo(EditPositionInfoRequest editPositionInfoRequest) {
        Result<String> result = positionProvider.editPositionInfo(editPositionInfoRequest);
    }
}