package com.mi.info.intl.retail.management.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("common_approve_log")
public class CommonApproveLog {
    @TableId(value = "id")
    private Integer id;

    @TableField("business_key")
    private String businessKey;

    @TableField("business_id")
    private String businessId;

    @TableField("flow_inst_id")
    private String flowInstId;

    @TableField("flow_status")
    private Integer flowStatus;

    @TableField("approve_user_id")
    private String approveUserId;

    @TableField("origin_body")
    private String originBody;

    @TableField("target_body")
    private String targetBody;

    @TableField("created_at")
    private Long createdAt;

    @TableField("created_by")
    private String createdBy;

    @TableField("updated_at")
    private Long updatedAt;

    @TableField("updated_by")
    private String updatedBy;

    @TableField("application_time")
    private Date applicationTime;

    @TableField("approved_time")
    private Date approvedTime;
    
    @TableField("request_approval_body")
    private String requestApprovalBody;
}
