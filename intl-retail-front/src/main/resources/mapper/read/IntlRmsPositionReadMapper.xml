<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsPositionReadMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition">
        <id property="id" column="id"/>
        <result property="positionId" column="position_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="storeId" column="store_id"/>
        <result property="storeName" column="store_name"/>
        <result property="abbreviation" column="abbreviation"/>
        <result property="state" column="state"/>
        <result property="stateName" column="state_name"/>
        <result property="distributorId" column="distributor_id"/>
        <result property="distributorName" column="distributor_name"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="retailerId" column="retailer_id"/>
        <result property="retailerName" column="retailer_name"/>
        <result property="channelType" column="channel_type"/>
        <result property="channelTypeName" column="channel_type_name"/>
        <result property="type" column="type"/>
        <result property="typeName" column="type_name"/>
        <result property="level" column="level"/>
        <result property="levelName" column="level_name"/>
        <result property="isPromotionStore" column="is_promotion_store"/>
        <result property="countryId" column="country_id"/>
        <result property="countryName" column="country_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="address" column="address"/>
        <result property="modifiedOn" column="modified_on"/>
        <result property="createdOn" column="created_on"/>
        <result property="ownerId" column="owner_id"/>
        <result property="ownerName" column="owner_name"/>
        <result property="stateCode" column="state_code"/>
        <result property="area" column="area"/>
        <result property="areaCode" column="area_code"/>
        <result property="crpsCode" column="crpscode"/>
        <result property="positionCategory" column="position_category"/>
        <result property="furnitureTtl" column="furniture_ttl"/>
        <result property="displayCapacityExpansionStatus" column="display_capacity_expansion_status"/>
        <result property="positionLocation" column="position_location"/>

    </resultMap>

    <sql id="Base_Column_List">
        id
        ,position_id,code,name,store_id,store_name,
        abbreviation,state,state_name,distributor_id,distributor_name,
        account_id,account_name,retailer_id,retailer_name,channel_type,
        channel_type_name,type,type_name,level,level_name,
        is_promotion_store,country_id,country_name,city_id,city_name,
        address,modified_on,created_on,owner_id,owner_name,
        state_code,area,area_code,crpscode,position_category,
        furniture_ttl,display_capacity_expansion_status,position_location
    </sql>
</mapper>
