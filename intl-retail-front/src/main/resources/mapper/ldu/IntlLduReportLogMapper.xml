<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="country_code" property="countryCode" jdbcType="VARCHAR"/>
        <result column="retailer_code" property="retailerCode" jdbcType="VARCHAR"/>
        <result column="retailer_name" property="retailerName" jdbcType="VARCHAR"/>
        <result column="store_code" property="storeCode" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="district" property="district" jdbcType="VARCHAR"/>
        <result column="product_line" property="productLine" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="ram_capacity" property="ramCapacity" jdbcType="VARCHAR"/>
        <result column="rom_capacity" property="romCapacity" jdbcType="VARCHAR"/>
        <result column="sn" property="sn" jdbcType="VARCHAR"/>
        <result column="69code" property="code69" jdbcType="VARCHAR"/>
        <result column="imei_1" property="imei1" jdbcType="VARCHAR"/>
        <result column="imei_2" property="imei2" jdbcType="VARCHAR"/>
        <result column="plan_status" property="planStatus" jdbcType="VARCHAR"/>
        <result column="ldu_type" property="lduType" jdbcType="VARCHAR"/>
        <result column="mishow_status" property="mishowStatus" jdbcType="TINYINT"/>
        <result column="last_mishow_fetch_time" property="lastMishowFetchTime" jdbcType="TIMESTAMP"/>
        <result column="display_status" property="displayStatus" jdbcType="TINYINT"/>
        <result column="report_distance" property="reportDistance" jdbcType="DECIMAL"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <resultMap id="reportWithFilesMap" type="com.mi.info.intl.retail.ldu.infra.entity.IntlLduReportLog">
        <id column="report_id" property="id" jdbcType="BIGINT"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="country_code" property="countryCode" jdbcType="VARCHAR"/>
        <result column="biz_region" property="bizRegion" jdbcType="VARCHAR"/>
        <result column="ops_city" property="opsCity" jdbcType="VARCHAR"/>
        <result column="grid" property="grid" jdbcType="VARCHAR"/>
        <result column="channel_type" property="channelType" jdbcType="VARCHAR"/>
        <result column="retailer_code" property="retailerCode" jdbcType="VARCHAR"/>
        <result column="retailer_name" property="retailerName" jdbcType="VARCHAR"/>
        <result column="store_code" property="storeCode" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="district" property="district" jdbcType="VARCHAR"/>
        <result column="product_line" property="productLine" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="ram_capacity" property="ramCapacity" jdbcType="VARCHAR"/>
        <result column="rom_capacity" property="romCapacity" jdbcType="VARCHAR"/>
        <result column="sn" property="sn" jdbcType="VARCHAR"/>
        <result column="69code" property="code69" jdbcType="VARCHAR"/>
        <result column="imei_1" property="imei1" jdbcType="VARCHAR"/>
        <result column="imei_2" property="imei2" jdbcType="VARCHAR"/>
        <result column="plan_status" property="planStatus" jdbcType="VARCHAR"/>
        <result column="mishow_status" property="mishowStatus" jdbcType="TINYINT"/>
        <result column="ldu_type" property="lduType" jdbcType="VARCHAR"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="last_mishow_fetch_time" property="lastMishowFetchTime" jdbcType="BIGINT"/>
        <result column="display_status" property="displayStatus" jdbcType="TINYINT"/>
        <result column="report_distance" property="reportDistance" jdbcType="DECIMAL"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="report_role" property="reportRole" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="color" property="color" jdbcType="VARCHAR"/>

        <collection property="fileUploadList" ofType="com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload">
            <id column="id" property="id" jdbcType="BIGINT"/>
            <result column="guid" property="guid" jdbcType="VARCHAR"/>
            <result column="url" property="fdsUrl" jdbcType="VARCHAR"/>
            <result column="suffix" property="suffix" jdbcType="VARCHAR"/>
            <result column="uploader" property="uploaderName" jdbcType="VARCHAR"/>
            <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
        id
        , region, region_code, country, country_code, retailer_code, retailer_name,
        store_code, store_name, province, city, district,  product_line,
        product_id, product_name, project_code, ram_capacity, rom_capacity, sn,
        69code, imei_1, imei_2, plan_status, ldu_type, mishow_status,
        last_mishow_fetch_time, display_status, reporte_distance, create_user_id,
        create_user_name, is_delete, create_time, update_user_id, update_user_name,
        update_time
    </sql>

    <insert id="batchInsertReportLogs" parameterType="java.util.List">
        INSERT INTO intl_ldu_report_log (
        report_id,
        region, region_code, country, country_code,
        retailer_code, retailer_name, store_code, store_name,
        province, city, district,
        product_line, product_id, product_name, project_code,
        ram_capacity, rom_capacity, sn, 69code,
        imei_1, imei_2, plan_status, ldu_type,
        mishow_status, last_mishow_fetch_time, display_status,
        report_distance, create_user_id, create_user_name,
        is_delete, create_time, update_user_id, update_user_name,
        update_time,report_role,quantity,channel_type,remark,color
        ) VALUES
        <foreach collection="intlLduReportLogs" item="log" separator=",">
            (
            <if test="log.reportId != null">#{log.reportId}</if>,
            <if test="log.region != null">#{log.region,jdbcType=VARCHAR}</if>
            <if test="log.region == null">''</if>,

            <if test="log.regionCode != null">#{log.regionCode,jdbcType=VARCHAR}</if>
            <if test="log.regionCode == null">''</if>,

            <if test="log.country != null">#{log.country,jdbcType=VARCHAR}</if>
            <if test="log.country == null">''</if>,

            <if test="log.countryCode != null">#{log.countryCode,jdbcType=VARCHAR}</if>
            <if test="log.countryCode == null">''</if>,

            <if test="log.retailerCode != null">#{log.retailerCode,jdbcType=VARCHAR}</if>
            <if test="log.retailerCode == null">''</if>,

            <if test="log.retailerName != null">#{log.retailerName,jdbcType=VARCHAR}</if>
            <if test="log.retailerName == null">''</if>,

            <if test="log.storeCode != null">#{log.storeCode,jdbcType=VARCHAR}</if>
            <if test="log.storeCode == null">''</if>,

            <if test="log.storeName != null">#{log.storeName,jdbcType=VARCHAR}</if>
            <if test="log.storeName == null">''</if>,

            <if test="log.province != null">#{log.province,jdbcType=VARCHAR}</if>
            <if test="log.province == null">''</if>,

            <if test="log.city != null">#{log.city,jdbcType=VARCHAR}</if>
            <if test="log.city == null">''</if>,

            <if test="log.district != null">#{log.district,jdbcType=VARCHAR}</if>
            <if test="log.district == null">''</if>,

            <if test="log.productLine != null">#{log.productLine,jdbcType=VARCHAR}</if>
            <if test="log.productLine == null">''</if>,

            <if test="log.productId != null">#{log.productId,jdbcType=VARCHAR}</if>
            <if test="log.productId == null">''</if>,

            <if test="log.productName != null">#{log.productName,jdbcType=VARCHAR}</if>
            <if test="log.productName == null">''</if>,

            <if test="log.projectCode != null">#{log.projectCode,jdbcType=VARCHAR}</if>
            <if test="log.projectCode == null">''</if>,

            <if test="log.ramCapacity != null">#{log.ramCapacity,jdbcType=VARCHAR}</if>
            <if test="log.ramCapacity == null">''</if>,

            <if test="log.romCapacity != null">#{log.romCapacity,jdbcType=VARCHAR}</if>
            <if test="log.romCapacity == null">''</if>,

            <if test="log.sn != null">#{log.sn,jdbcType=VARCHAR}</if>
            <if test="log.sn == null">''</if>,

            <if test="log.code69 != null">#{log.code69,jdbcType=VARCHAR}</if>
            <if test="log.code69 == null">''</if>,

            <if test="log.imei1 != null">#{log.imei1,jdbcType=VARCHAR}</if>
            <if test="log.imei1 == null">''</if>,

            <if test="log.imei2 != null">#{log.imei2,jdbcType=VARCHAR}</if>
            <if test="log.imei2 == null">''</if>,

            <if test="log.planStatus != null">#{log.planStatus,jdbcType=TINYINT}</if>
            <if test="log.planStatus == null">0</if>,

            <if test="log.lduType != null">#{log.lduType,jdbcType=VARCHAR}</if>
            <if test="log.lduType == null">''</if>,

            <if test="log.mishowStatus != null">#{log.mishowStatus,jdbcType=TINYINT}</if>
            <if test="log.mishowStatus == null">0</if>,

            <if test="log.lastMishowFetchTime != null">#{log.lastMishowFetchTime,jdbcType=TIMESTAMP}</if>
            <if test="log.lastMishowFetchTime == null">0</if>,

            <if test="log.displayStatus != null">#{log.displayStatus,jdbcType=TINYINT}</if>
            <if test="log.displayStatus == null">0</if>,

            <if test="log.reportDistance != null">#{log.reportDistance,jdbcType=DECIMAL}</if>
            <if test="log.reportDistance == null">0</if>,

            <if test="log.createUserId != null">#{log.createUserId,jdbcType=VARCHAR}</if>
            <if test="log.createUserId == null">''</if>,

            <if test="log.createUserName != null">#{log.createUserName,jdbcType=VARCHAR}</if>
            <if test="log.createUserName == null">''</if>,

            <if test="log.isDelete != null">#{log.isDelete,jdbcType=TINYINT}</if>
            <if test="log.isDelete == null">0</if>,

            <if test="log.createTime != null">#{log.createTime,jdbcType=TIMESTAMP}</if>
            <if test="log.createTime == null">0</if>,

            <if test="log.updateUserId != null">#{log.updateUserId,jdbcType=VARCHAR}</if>
            <if test="log.updateUserId == null">''</if>,

            <if test="log.updateUserName != null">#{log.updateUserName,jdbcType=VARCHAR}</if>
            <if test="log.updateUserName == null">''</if>,

            <if test="log.updateTime != null">#{log.updateTime,jdbcType=TIMESTAMP}</if>
            <if test="log.updateTime == null">0</if>,

            <if test="log.reportRole != null">#{log.reportRole,jdbcType=VARCHAR}</if>
            <if test="log.reportRole == null">''</if>,

            <if test="log.quantity != null">#{log.quantity}</if>
            <if test="log.quantity == null">1</if>,

            <if test="log.channelType != null">#{log.channelType}</if>
            <if test="log.channelType == null">''</if>,

            <if test="log.remark != null">#{log.remark}</if>
            <if test="log.remark == null">''</if>,

            <if test="log.color != null">#{log.color}</if>
            <if test="log.color == null">''</if>
            )
        </foreach>
    </insert>


    <select id="pageList" resultMap="reportWithFilesMap">
        SELECT
        f.id AS id,
        f.guid AS guid,
        f.fds_url AS url,
        f.suffix AS suffix,
        f.uploader_name AS uploader,
        f.create_time AS create_time,
        l.id AS report_id,
        l.region,
        l.region_code,
        l.country,
        l.country_code,
        l.biz_region,
        l.ops_city,
        l.grid,
        l.channel_type,
        l.retailer_code,
        l.retailer_name,
        l.store_code,
        l.store_name,
        l.province,
        l.city,
        l.district,
        l.product_line,
        l.product_id,
        l.product_name,
        l.project_code,
        l.ram_capacity,
        l.rom_capacity,
        l.sn,
        l.69code,
        l.imei_1,
        l.imei_2,
        l.plan_status,
        l.mishow_status,
        l.ldu_type,
        l.quantity,
        l.last_mishow_fetch_time,
        l.display_status,
        l.report_distance,
        l.create_user_id,
        l.create_user_name,
        l.report_role,
        l.is_delete,
        l.create_time,
        l.update_user_id,
        l.update_user_name,
        l.update_time,
        l.color,
        l.remark
        FROM
        intl_file_upload f
        RIGHT JOIN
        intl_ldu_report_log l ON f.related_id = l.report_id
        <where>
            <if test="query.countryCode != null and !query.countryCode.isEmpty()">
                AND l.country_code IN
                <foreach item="item" collection="query.countryCode" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.retailerCode != null and query.retailerCode != ''">
                AND l.retailer_code = #{query.retailerCode}
            </if>
            <if test="query.storeCode != null and query.storeCode != ''">
                AND l.store_code = #{query.storeCode}
            </if>
            <if test="query.productLine != null and query.productLine != ''">
                AND l.product_line = #{query.productLine}
            </if>
            <if test="query.projectCode != null and query.projectCode != ''">
                AND l.project_code = #{query.projectCode}
            </if>
            <if test="query.productId != null">
                AND l.product_id = #{query.productId}
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND l.product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.planStatus != null">
                AND l.plan_status = #{query.planStatus}
            </if>
            <if test="query.lduType != null">
                AND l.ldu_type = #{query.lduType}
            </if>
            <if test="query.mishowStatus != null">
                AND l.mishow_status = #{query.mishowStatus}
            </if>
            <if test="query.displayStatus != null">
                AND l.display_status = #{query.displayStatus}
            </if>
            <if test="query.reportStartTime != null">
                AND l.create_time &gt;= #{query.reportStartTime}
            </if>
            <if test="query.reportEndTime != null">
                AND l.create_time &lt;= #{query.reportEndTime}
            </if>
        </where>
        ORDER BY l.id DESC
        LIMIT #{query.pageSize} OFFSET #{query.offset}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        intl_ldu_report_log l
        <where>
            <if test="query.countryCode != null and !query.countryCode.isEmpty()">
                AND l.country_code IN
                <foreach item="item" collection="query.countryCode" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.retailerCode != null and query.retailerCode != ''">
                AND l.retailer_code = #{query.retailerCode}
            </if>
            <if test="query.storeCode != null and query.storeCode != ''">
                AND l.store_code = #{query.storeCode}
            </if>
            <if test="query.productLine != null and query.productLine != ''">
                AND l.product_line = #{query.productLine}
            </if>
            <if test="query.projectCode != null and query.projectCode != ''">
                AND l.project_code = #{query.projectCode}
            </if>
            <if test="query.productId != null">
                AND l.product_id = #{query.productId}
            </if>
            <if test="query.productName != null and query.productName != ''">
                AND l.product_name LIKE CONCAT('%', #{query.productName}, '%')
            </if>
            <if test="query.planStatus != null">
                AND l.plan_status = #{query.planStatus}
            </if>
            <if test="query.lduType != null">
                AND l.ldu_type = #{query.lduType}
            </if>
            <if test="query.mishowStatus != null">
                AND l.mishow_status = #{query.mishowStatus}
            </if>
            <if test="query.displayStatus != null">
                AND l.display_status = #{query.displayStatus}
            </if>
            <if test="query.reportStartTime != null">
                AND l.create_time &gt;= #{query.reportStartTime}
            </if>
            <if test="query.reportEndTime != null">
                AND l.create_time &lt;= #{query.reportEndTime}
            </if>
        </where>
    </select>


    <select id="historyList" resultMap="reportWithFilesMap">
        SELECT
        f.id AS id,
        f.guid AS guid,
        f.fds_url AS url,
        f.suffix AS suffix,
        f.uploader_name AS uploader,
        f.create_time AS create_time,
        l.id AS report_id,
        l.region,
        l.region_code,
        l.country,
        l.country_code,
        l.biz_region,
        l.ops_city,
        l.grid,
        l.channel_type,
        l.retailer_code,
        l.retailer_name,
        l.store_code,
        l.store_name,
        l.province,
        l.city,
        l.district,
        l.product_line,
        l.product_id,
        l.product_name,
        l.project_code,
        l.ram_capacity,
        l.rom_capacity,
        l.sn,
        l.69code,
        l.imei_1,
        l.imei_2,
        l.plan_status,
        l.mishow_status,
        l.ldu_type,
        l.quantity,
        l.last_mishow_fetch_time,
        l.display_status,
        l.report_distance,
        l.create_user_id,
        l.create_user_name,
        l.report_role,
        l.is_delete,
        l.create_time,
        l.update_user_id,
        l.update_user_name,
        l.update_time,
        l.remark,
        l.color
        FROM
        intl_file_upload f
        RIGHT JOIN
        intl_ldu_report_log l ON f.related_id = l.report_id
        <where>
            <if test="query.productLine != null and query.productLine != ''">
                AND product_line = #{query.productLine}
            </if>
            <if test="query.search != null and query.search != ''">
                AND (
                l.product_id = #{query.search}
                OR l.product_name LIKE CONCAT('%', #{query.search}, '%')
                OR l.sn = #{query.search}
                OR l.imei_1 = #{query.search}
                OR l.imei_2 = #{query.search}
                )
            </if>
            <if test="query.reportStartTime != null">
                AND l.create_time &gt;= #{query.reportStartTime}
            </if>
            <if test="query.reportEndTime != null">
                AND l.create_time &lt;= #{query.reportEndTime}
            </if>
        </where>
        ORDER BY l.id DESC
        LIMIT #{query.pageSize} OFFSET #{query.offset}
    </select>

    <select id="historyListCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        intl_ldu_report_log l
        <where>
            <if test="query.productLine != null and query.productLine != ''">
                AND product_line = #{query.productLine}
            </if>
            <if test="query.search != null and query.search != ''">
                AND (
                l.product_id = #{query.search}
                OR l.product_name LIKE CONCAT('%', #{query.search}, '%')
                OR l.sn = #{query.search}
                OR l.imei_1 = #{query.search}
                OR l.imei_2 = #{query.search}
                )
            </if>
            <if test="query.reportStartTime != null">
                AND l.create_time &gt;= #{query.reportStartTime}
            </if>
            <if test="query.reportEndTime != null">
                AND l.create_time &lt;= #{query.reportEndTime}
            </if>
        </where>
    </select>

    <select id="statisticReportLog" resultType="java.util.Map">
        SELECT
            'actualCoveredStores' AS metricName,
            COUNT(DISTINCT CONCAT(product_id, retailer_code, store_code)) AS metric_value
        FROM
            xm_store_be.intl_ldu_report_log
        WHERE
            is_delete = 0
          AND product_id = #{intlLduReportLog.productId}
          AND retailer_code = #{intlLduReportLog.retailerCode}
          AND store_code = #{intlLduReportLog.storeCode}

        UNION ALL

        SELECT
            'actualDisplayCount' AS metricName,
            COUNT(CONCAT(product_id, retailer_code)) AS metric_value
        FROM
            xm_store_be.intl_ldu_report_log
        WHERE
            is_delete = 0
          AND product_id = #{intlLduReportLog.productId}
          AND retailer_code = #{intlLduReportLog.retailerCode}
    </select>

</mapper>