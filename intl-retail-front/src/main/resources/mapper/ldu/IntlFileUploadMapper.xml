<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.ldu.infra.mapper.IntlFileUploadMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="guid" property="guid" jdbcType="VARCHAR"/>
        <result column="related_id" property="relatedId" jdbcType="BIGINT"/>
        <result column="is_offline_upload" property="isOfflineUpload" jdbcType="TINYINT"/>
        <result column="is_uploaded_to_blob" property="isUploadedToBlob" jdbcType="TINYINT"/>
        <result column="module_name" property="moduleName" jdbcType="VARCHAR"/>
        <result column="uploader_name" property="uploaderName" jdbcType="VARCHAR"/>
        <result column="uploader_time" property="uploaderTime" jdbcType="TIMESTAMP"/>
        <result column="fds_url" property="fdsUrl" jdbcType="VARCHAR"/>
        <result column="is_no_watermark" property="isNoWatermark" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <update id="batchByGuidFileUpload">
        <foreach item="fileUpload" collection="intlFileUploads" separator=";">
            UPDATE intl_file_upload
            <trim prefix="SET" suffixOverrides=",">
                <if test="fileUpload.fdsUrl != null">
                    fds_url = #{fileUpload.fdsUrl},
                </if>
                update_time = #{fileUpload.updateTime}
            </trim>
            WHERE guid = #{fileUpload.guid}
        </foreach>
    </update>


    <insert id="batchInsertDatas">
        INSERT INTO intl_file_upload
        (
        guid,
        related_id,
        is_offline_upload,
        is_uploaded_to_blob,
        module_name,
        uploader_name,
        uploader_time,
        fds_url,
        is_no_watermark,
        create_time,
        update_time,
        suffix
        )
        VALUES
        <foreach collection="intlFileUploads" item="fileUpload" separator=",">
            (
            #{fileUpload.guid},
            #{fileUpload.relatedId},
            #{fileUpload.isOfflineUpload},
            #{fileUpload.isUploadedToBlob},
            #{fileUpload.moduleName},
            #{fileUpload.uploaderName},
            #{fileUpload.uploaderTime},
            #{fileUpload.fdsUrl},
            #{fileUpload.isNoWatermark},
            #{fileUpload.createTime},
            #{fileUpload.updateTime},
            #{fileUpload.suffix}
            )
        </foreach>
    </insert>

    <select id="selectByBusyIdAndMoudle" resultMap="BaseResultMap">
        SELECT *
        FROM intl_file_upload
        WHERE related_id = #{relatedId}
          AND module_name = #{moduleName}
    </select>

</mapper>