<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.ldu.infra.mapper.IntlLduTargetMapper">

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        id
        , region, region_code, country, country_code,channel_type
        retailer_code, retailer_name, product_line, goods_id,
        goods_name, project_code, ram_capacity, rom_capacity,
        target_covered_stores, actual_covered_stores,product_id,product_name,
        target_sample_out, actual_sample_out, create_user_id,
        create_user_name, target_create_date, update_user_id,
        update_user_name, target_update_date
    </sql>

    <!-- 基础ResultMap -->
    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="region_code" property="regionCode" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="channel_type" property="channelType" jdbcType="VARCHAR"/>
        <result column="country_code" property="countryCode" jdbcType="VARCHAR"/>
        <result column="retailer_code" property="retailerCode" jdbcType="VARCHAR"/>
        <result column="retailer_name" property="retailerName" jdbcType="VARCHAR"/>
        <result column="product_line" property="productLine" jdbcType="VARCHAR"/>
        <result column="goods_id" property="goodsId" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="ram_capacity" property="ramCapacity" jdbcType="VARCHAR"/>
        <result column="rom_capacity" property="romCapacity" jdbcType="VARCHAR"/>
        <result column="target_covered_stores" property="targetCoveredStores" jdbcType="INTEGER"/>
        <result column="actual_covered_stores" property="actualCoveredStores" jdbcType="INTEGER"/>
        <result column="target_sample_out" property="targetSampleOut" jdbcType="INTEGER"/>
        <result column="actual_sample_out" property="actualSampleOut" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="target_create_date" property="targetCreateDate" jdbcType="BIGINT"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="target_update_date" property="targetUpdateDate" jdbcType="BIGINT"/>
    </resultMap>


    <!--    &lt;!&ndash; 根据ID查询 &ndash;&gt;-->
    <!--    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">-->
    <!--        SELECT-->
    <!--        <include refid="Base_Column_List"/>-->
    <!--        FROM intl_ldu_target-->
    <!--        WHERE id = #{id,jdbcType=BIGINT}-->
    <!--    </select>-->

    <!--    &lt;!&ndash; 根据组合条件查询（利用idx_country_retailer_product索引） &ndash;&gt;-->
    <select id="selectByCountryRetailerProduct" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_ldu_target
        WHERE country_code = #{countryCode,jdbcType=VARCHAR}
        AND retailer_code = #{retailerCode,jdbcType=VARCHAR}
        AND goods_id = #{goodsId,jdbcType=VARCHAR}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO intl_ldu_target (
        region, region_code,channel_type, country, country_code,
        retailer_code, retailer_name, product_line, goods_id,
        goods_name, project_code, product_id,product_name,ram_capacity, rom_capacity,
        target_covered_stores, actual_covered_stores,
        target_sample_out, actual_sample_out, create_user_id,
        create_user_name, target_create_date, update_user_id,
        update_user_name, target_update_date
        )
        VALUES
        <foreach collection="confList" item="conf" separator=",">
            (
            #{conf.region,jdbcType=VARCHAR},
            #{conf.regionCode,jdbcType=VARCHAR},
            #{conf.channelType,jdbcType=VARCHAR},
            #{conf.country,jdbcType=VARCHAR},
            #{conf.countryCode,jdbcType=VARCHAR},
            #{conf.retailerCode,jdbcType=VARCHAR},
            #{conf.retailerName,jdbcType=VARCHAR},
            #{conf.productLine,jdbcType=VARCHAR},
            #{conf.goodsId,jdbcType=VARCHAR},
            #{conf.goodsName,jdbcType=VARCHAR},
            #{conf.projectCode,jdbcType=VARCHAR},
            #{conf.productId,jdbcType=VARCHAR},
            #{conf.productName,jdbcType=VARCHAR},
            #{conf.ramCapacity,jdbcType=VARCHAR},
            #{conf.romCapacity,jdbcType=VARCHAR},
            #{conf.targetCoveredStores,jdbcType=INTEGER},
            #{conf.actualCoveredStores,jdbcType=INTEGER},
            #{conf.targetSampleOut,jdbcType=INTEGER},
            #{conf.actualSampleOut,jdbcType=INTEGER},
            #{conf.createUserId,jdbcType=VARCHAR},
            #{conf.createUserName,jdbcType=VARCHAR},
            #{conf.targetCreateDate,jdbcType=BIGINT},
            #{conf.updateUserId,jdbcType=VARCHAR},
            #{conf.updateUserName,jdbcType=VARCHAR},
            #{conf.targetUpdateDate,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <!--    &lt;!&ndash; 查询全部记录 &ndash;&gt;-->
    <!--    <select id="selectAll" resultMap="BaseResultMap">-->
    <!--        SELECT-->
    <!--        <include refid="Base_Column_List"/>-->
    <!--        FROM intl_ldu_target-->
    <!--    </select>-->


    <!--    &lt;!&ndash; 根据ID更新记录 &ndash;&gt;-->
    <update id="updateById">
        UPDATE intl_ldu_target
        <set>
            <if test="conf.region != null">region = #{conf.region,jdbcType=VARCHAR},</if>
            <if test="conf.regionCode != null">region_code = #{conf.regionCode,jdbcType=VARCHAR},</if>
            <if test="conf.country != null">country = #{conf.country,jdbcType=VARCHAR},</if>
            <if test="conf.countryCode != null">country_code = #{conf.countryCode,jdbcType=VARCHAR},</if>
            <if test="conf.retailerCode != null">retailer_code = #{conf.retailerCode,jdbcType=VARCHAR},</if>
            <if test="conf.retailerName != null">retailer_name = #{conf.retailerName,jdbcType=VARCHAR},</if>
            <if test="conf.productLine != null">product_line = #{conf.productLine,jdbcType=VARCHAR},</if>
            <if test="conf.goodsId != null">goods_id = #{conf.goodsId,jdbcType=VARCHAR},</if>
            <if test="conf.goodsName != null">goods_name = #{conf.goodsName,jdbcType=VARCHAR},</if>
            <if test="conf.productId != null">product_id = #{conf.productId,jdbcType=VARCHAR},</if>
            <if test="conf.productName != null">product_name = #{conf.productName,jdbcType=VARCHAR},</if>
            <if test="conf.projectCode != null">project_code = #{conf.projectCode,jdbcType=VARCHAR},</if>
            <if test="conf.ramCapacity != null">ram_capacity = #{conf.ramCapacity,jdbcType=VARCHAR},</if>
            <if test="conf.romCapacity != null">rom_capacity = #{conf.romCapacity,jdbcType=VARCHAR},</if>
            <if test="conf.targetCoveredStores != null">target_covered_stores = #{conf.targetCoveredStores,jdbcType=INTEGER},</if>
            <if test="conf.actualCoveredStores != null">actual_covered_stores = #{conf.actualCoveredStores,jdbcType=INTEGER},</if>
            <if test="conf.targetSampleOut != null">target_sample_out = #{conf.targetSampleOut,jdbcType=INTEGER},</if>
            <if test="conf.actualSampleOut != null">actual_sample_out = #{conf.actualSampleOut,jdbcType=INTEGER},</if>
            <if test="conf.updateUserId != null">update_user_id = #{conf.updateUserId,jdbcType=VARCHAR},</if>
            <if test="conf.updateUserName != null">update_user_name = #{conf.updateUserName,jdbcType=VARCHAR},</if>
            <if test="conf.targetUpdateDate != null">target_update_date = #{conf.targetUpdateDate,jdbcType=BIGINT},</if>
        </set>
        WHERE id = #{conf.id,jdbcType=BIGINT}

    </update>

    <!-- 批量更新多个字段 -->
    <update id="batchUpdate">
        <foreach collection="intlLduTargetList" item="item" separator=";">
            UPDATE intl_ldu_target
            SET
            actual_covered_stores = #{item.actualCoveredStores},
            actual_sample_out = #{item.actualSampleOut},
            update_user_id = #{item.updateUserId},
            update_user_name = #{item.updateUserName},
            target_update_date = #{item.targetUpdateDate}
            WHERE product_id = #{item.productId}  and  retailer_code = #{item.retailerCode}  and  country_code = #{item.countryCode}
        </foreach>
    </update>


</mapper>