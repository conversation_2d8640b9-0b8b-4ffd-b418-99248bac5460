# 测试环境配置文件
spring:
  datasource:
    # 测试数据库配置
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
    # 或者使用 MySQL 测试数据库（如果有的话）
    # url: *******************************************************************************************************************
    # driver-class-name: com.mysql.cj.jdbc.Driver
    # username: test_user
    # password: test_password
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        
  # MyBatis 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.mi.info.intl.retail.ldu.infra.entity

# 日志配置
logging:
  level:
    com.mi.info.intl.retail.ldu.infra.mapper: DEBUG
    com.mi.info.intl.retail.ldu.service: DEBUG
    org.springframework.jdbc: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 测试特定配置
test:
  database:
    # 是否使用内存数据库进行测试
    use-in-memory: true
    # 是否初始化测试数据
    init-test-data: true 