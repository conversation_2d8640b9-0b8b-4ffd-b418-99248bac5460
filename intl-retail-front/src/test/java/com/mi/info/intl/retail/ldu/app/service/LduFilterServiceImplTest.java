package com.mi.info.intl.retail.ldu.app.service;

import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.RetailerQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.IGateWayChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto.GateWayChannelInfoResponse;
import com.mi.info.intl.retail.ldu.app.service.impl.LduFilterServiceImpl;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025/7/9 11:50
 */
@ExtendWith(MockitoExtension.class)
public class LduFilterServiceImplTest {

    @InjectMocks
    private LduFilterServiceImpl lduFilterServiceImpl;

    @Mock
    private IProductQueryService productQueryService;

    @Mock
    private IGateWayChannelInfoService gateWayChannelInfoService;

    @DisplayName("测试查询产品线列表-响应为空")
    @Test
    public void testQueryProductLines_return_empty() {
        when(productQueryService.queryProductLines()).thenReturn(Lists.newArrayList());
        CommonResponse<List<ProductLineDto>> listCommonResponse = lduFilterServiceImpl.queryProductLines();
        Assertions.assertTrue(CollectionUtils.isEmpty(listCommonResponse.getData()));
    }

    @DisplayName("测试查询产品线列表-响应不为空")
    @Test
    public void testQueryProductLines_return_not_empty() {
        ProductLineDto productLineDto = new ProductLineDto();
        productLineDto.setProductLine("1001");
        productLineDto.setCnName("手机");
        productLineDto.setEnName("Mobile");
        List<ProductLineDto> expectList = Lists.newArrayList(productLineDto);
        when(productQueryService.queryProductLines()).thenReturn(Lists.newArrayList(productLineDto));
        CommonResponse<List<ProductLineDto>> listCommonResponse = lduFilterServiceImpl.queryProductLines();
        Assertions.assertEquals(expectList, listCommonResponse.getData());
    }

    @DisplayName("测试查询零售商列表-请求参数为空，响应为空")
    @Test
    public void testQueryRetailers_param_empty() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> lduFilterServiceImpl.queryRetailers(null));
    }

    @DisplayName("测试查询零售商列表-请求参数region为空，响应为空")
    @Test
    public void testQueryRetailers_param_region_is_empty() {
        // 创建测试数据
        RetailerQueryDto request = new RetailerQueryDto();
        request.setKeyword("小米");
        CommonResponse<List<RetailerInfoResponse>> listCommonResponse = lduFilterServiceImpl.queryRetailers(request);
        Assertions.assertTrue(CollectionUtils.isEmpty(listCommonResponse.getData()));
    }

    @DisplayName("测试查询零售商列表-请求参数keyword为空，响应为空")
    @Test
    public void testQueryRetailers_param_keyword_is_empty() {
        // 创建测试数据
        RetailerQueryDto request = new RetailerQueryDto();
        request.setRegion("HK");
        CommonResponse<List<RetailerInfoResponse>> listCommonResponse = lduFilterServiceImpl.queryRetailers(request);
        Assertions.assertTrue(CollectionUtils.isEmpty(listCommonResponse.getData()));
    }

    @DisplayName("测试查询零售商列表-请求参数codes为空，响应为空")
    @Test
    public void testQueryRetailers_param_codes_is_empty() {
        // 创建测试数据
        RetailerQueryDto request = new RetailerQueryDto();
        request.setRegion("HK");
        CommonResponse<List<RetailerInfoResponse>> listCommonResponse = lduFilterServiceImpl.queryRetailers(request);
        Assertions.assertTrue(CollectionUtils.isEmpty(listCommonResponse.getData()));
    }

    @DisplayName("测试查询零售商列表-请求参数不为空，响应不为空")
    @Test
    public void testQueryRetailers_param_not_empty() {
        // 创建测试数据
        RetailerQueryDto request = new RetailerQueryDto();
        request.setRegion("HK");
        request.setKeyword("小米");

        ChannelInfoRequest channelInfoRequest = new ChannelInfoRequest();
        channelInfoRequest.setType(20);
        channelInfoRequest.setAreaId("HK");
        channelInfoRequest.setCountryCode("HK");
        channelInfoRequest.setSearch("小米");

        ChannelInfoResponse channelInfoResponse = new ChannelInfoResponse();
        ChannelInfoResponse.BasicDTO basic = new ChannelInfoResponse.BasicDTO();
        basic.setCode("1001");
        basic.setName("小米旗舰店");
        channelInfoResponse.setBasic(basic);

        GateWayChannelInfoResponse gateWayChannelInfoResponse = new GateWayChannelInfoResponse();
        gateWayChannelInfoResponse.setCode(200);
        gateWayChannelInfoResponse.setMessage("成功");
        gateWayChannelInfoResponse.setData(Lists.newArrayList(channelInfoResponse));

        // 模拟依赖服务返回值
        when(gateWayChannelInfoService.queryChannelInfo(any(ChannelInfoRequest.class))).thenAnswer(invocation -> {
            ChannelInfoRequest arg = invocation.getArgument(0);
            Assertions.assertTrue(Objects.nonNull(arg));
            Assertions.assertEquals(channelInfoRequest.getType(), arg.getType());
            Assertions.assertEquals(channelInfoRequest.getAreaId(), arg.getAreaId());
            Assertions.assertEquals(channelInfoRequest.getCountryCode(), arg.getCountryCode());
            Assertions.assertEquals(channelInfoRequest.getSearch(), arg.getSearch());
            return gateWayChannelInfoResponse;
        });

        // 调用被测试方法
        CommonResponse<List<RetailerInfoResponse>> listCommonResponse = lduFilterServiceImpl.queryRetailers(request);

        // 验证返回结果
        Assertions.assertTrue(CollectionUtils.isNotEmpty(listCommonResponse.getData()));
        Assertions.assertEquals(1, listCommonResponse.getData().size());
        RetailerInfoResponse retailerInfoResponse = listCommonResponse.getData().get(0);
        Assertions.assertEquals("1001", retailerInfoResponse.getCode());
        Assertions.assertEquals("小米旗舰店", retailerInfoResponse.getName());
    }
}
