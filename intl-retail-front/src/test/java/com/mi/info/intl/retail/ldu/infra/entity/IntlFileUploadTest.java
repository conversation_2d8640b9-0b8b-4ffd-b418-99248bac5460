package com.mi.info.intl.retail.ldu.infra.entity;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * IntlFileUpload实体类单元测试
 * 测试@Data注解生成的getter、setter、equals、hashCode、toString方法
 */
@DisplayName("IntlFileUpload实体类测试")
class IntlFileUploadTest {

    @Test
    @DisplayName("测试getter和setter方法")
    void testGetterAndSetter() {
        // 准备数据
        IntlFileUpload fileUpload = new IntlFileUpload();
        
        // 设置测试数据
        Long id = 1L;
        String guid = "test-guid-123";
        Long relatedId = 100L;
        Integer isOfflineUpload = 1;
        Integer isUploadedToBlob = 0;
        String moduleName = "ldu_upload";
        String uploaderName = "test_user";
        Long uploaderTime = System.currentTimeMillis();
        String fdsUrl = "https://fds.example.com/file.pdf";
        Integer isNoWatermark = 1;
        Long createTime = System.currentTimeMillis();
        Long updateTime = System.currentTimeMillis();
        String suffix = "pdf";
        
        // 执行测试 - 设置值
        fileUpload.setId(id);
        fileUpload.setGuid(guid);
        fileUpload.setRelatedId(relatedId);
        fileUpload.setIsOfflineUpload(isOfflineUpload);
        fileUpload.setIsUploadedToBlob(isUploadedToBlob);
        fileUpload.setModuleName(moduleName);
        fileUpload.setUploaderName(uploaderName);
        fileUpload.setUploaderTime(uploaderTime);
        fileUpload.setFdsUrl(fdsUrl);
        fileUpload.setIsNoWatermark(isNoWatermark);
        fileUpload.setCreateTime(createTime);
        fileUpload.setUpdateTime(updateTime);
        fileUpload.setSuffix(suffix);
        
        // 验证结果 - 获取值
        assertEquals(id, fileUpload.getId());
        assertEquals(guid, fileUpload.getGuid());
        assertEquals(relatedId, fileUpload.getRelatedId());
        assertEquals(isOfflineUpload, fileUpload.getIsOfflineUpload());
        assertEquals(isUploadedToBlob, fileUpload.getIsUploadedToBlob());
        assertEquals(moduleName, fileUpload.getModuleName());
        assertEquals(uploaderName, fileUpload.getUploaderName());
        assertEquals(uploaderTime, fileUpload.getUploaderTime());
        assertEquals(fdsUrl, fileUpload.getFdsUrl());
        assertEquals(isNoWatermark, fileUpload.getIsNoWatermark());
        assertEquals(createTime, fileUpload.getCreateTime());
        assertEquals(updateTime, fileUpload.getUpdateTime());
        assertEquals(suffix, fileUpload.getSuffix());
    }

    @Test
    @DisplayName("测试equals方法 - 相同对象")
    void testEquals_SameObject() {
        // 准备数据
        IntlFileUpload fileUpload1 = new IntlFileUpload();
        fileUpload1.setId(1L);
        fileUpload1.setGuid("test-guid");
        
        IntlFileUpload fileUpload2 = new IntlFileUpload();
        fileUpload2.setId(1L);
        fileUpload2.setGuid("test-guid");
        
        // 执行测试
        boolean result = fileUpload1.equals(fileUpload2);
        
        // 验证结果
        assertTrue(result);
    }

    @Test
    @DisplayName("测试equals方法 - 不同对象")
    void testEquals_DifferentObjects() {
        // 准备数据
        IntlFileUpload fileUpload1 = new IntlFileUpload();
        fileUpload1.setId(1L);
        fileUpload1.setGuid("test-guid-1");
        
        IntlFileUpload fileUpload2 = new IntlFileUpload();
        fileUpload2.setId(2L);
        fileUpload2.setGuid("test-guid-2");
        
        // 执行测试
        boolean result = fileUpload1.equals(fileUpload2);
        
        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试equals方法 - 与null比较")
    void testEquals_WithNull() {
        // 准备数据
        IntlFileUpload fileUpload = new IntlFileUpload();
        fileUpload.setId(1L);
        
        // 执行测试
        boolean result = fileUpload.equals(null);
        
        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试equals方法 - 与自身比较")
    void testEquals_WithSelf() {
        // 准备数据
        IntlFileUpload fileUpload = new IntlFileUpload();
        fileUpload.setId(1L);
        
        // 执行测试
        boolean result = fileUpload.equals(fileUpload);
        
        // 验证结果
        assertTrue(result);
    }

    @Test
    @DisplayName("测试equals方法 - 与不同类型对象比较")
    void testEquals_WithDifferentType() {
        // 准备数据
        IntlFileUpload fileUpload = new IntlFileUpload();
        fileUpload.setId(1L);
        
        String differentObject = "test string";
        
        // 执行测试
        boolean result = fileUpload.equals(differentObject);
        
        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试hashCode方法")
    void testHashCode() {
        // 准备数据
        IntlFileUpload fileUpload1 = new IntlFileUpload();
        fileUpload1.setId(1L);
        fileUpload1.setGuid("test-guid");
        
        IntlFileUpload fileUpload2 = new IntlFileUpload();
        fileUpload2.setId(1L);
        fileUpload2.setGuid("test-guid");
        
        // 执行测试
        int hashCode1 = fileUpload1.hashCode();
        int hashCode2 = fileUpload2.hashCode();
        
        // 验证结果 - 相同对象应该有相同的hashCode
        assertEquals(hashCode1, hashCode2);
        
        // 验证hashCode不为0
        assertNotEquals(0, hashCode1);
    }

    @Test
    @DisplayName("测试toString方法")
    void testToString() {
        // 准备数据
        IntlFileUpload fileUpload = new IntlFileUpload();
        fileUpload.setId(1L);
        fileUpload.setGuid("test-guid");
        fileUpload.setModuleName("ldu_upload");
        fileUpload.setUploaderName("test_user");
        
        // 执行测试
        String result = fileUpload.toString();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("IntlFileUpload"));
        assertTrue(result.contains("id=1"));
        assertTrue(result.contains("guid=test-guid"));
        assertTrue(result.contains("moduleName=ldu_upload"));
        assertTrue(result.contains("uploaderName=test_user"));
    }

    @Test
    @DisplayName("测试toString方法 - 空对象")
    void testToString_EmptyObject() {
        // 准备数据
        IntlFileUpload fileUpload = new IntlFileUpload();
        
        // 执行测试
        String result = fileUpload.toString();
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("IntlFileUpload"));
    }

    @Test
    @DisplayName("测试边界值 - null值处理")
    void testNullValues() {
        // 准备数据
        IntlFileUpload fileUpload = new IntlFileUpload();
        
        // 执行测试 - 设置null值
        fileUpload.setId(null);
        fileUpload.setGuid(null);
        fileUpload.setModuleName(null);
        fileUpload.setUploaderName(null);
        
        // 验证结果
        assertNull(fileUpload.getId());
        assertNull(fileUpload.getGuid());
        assertNull(fileUpload.getModuleName());
        assertNull(fileUpload.getUploaderName());
    }

    @Test
    @DisplayName("测试边界值 - 特殊字符")
    void testSpecialCharacters() {
        // 准备数据
        IntlFileUpload fileUpload = new IntlFileUpload();
        
        String specialGuid = "guid-with-special-chars!@#$%^&*()";
        String specialModuleName = "module_name_with_underscores";
        String specialUploaderName = "<EMAIL>";
        
        // 执行测试
        fileUpload.setGuid(specialGuid);
        fileUpload.setModuleName(specialModuleName);
        fileUpload.setUploaderName(specialUploaderName);
        
        // 验证结果
        assertEquals(specialGuid, fileUpload.getGuid());
        assertEquals(specialModuleName, fileUpload.getModuleName());
        assertEquals(specialUploaderName, fileUpload.getUploaderName());
    }

    @Test
    @DisplayName("测试equals和hashCode一致性")
    void testEqualsAndHashCodeConsistency() {
        // 准备数据
        IntlFileUpload fileUpload1 = new IntlFileUpload();
        fileUpload1.setId(1L);
        fileUpload1.setGuid("test-guid");
        fileUpload1.setRelatedId(100L);
        
        IntlFileUpload fileUpload2 = new IntlFileUpload();
        fileUpload2.setId(1L);
        fileUpload2.setGuid("test-guid");
        fileUpload2.setRelatedId(100L);
        
        // 执行测试
        boolean equalsResult = fileUpload1.equals(fileUpload2);
        int hashCode1 = fileUpload1.hashCode();
        int hashCode2 = fileUpload2.hashCode();
        
        // 验证结果 - equals为true时，hashCode应该相同
        assertTrue(equalsResult);
        assertEquals(hashCode1, hashCode2);
    }
} 