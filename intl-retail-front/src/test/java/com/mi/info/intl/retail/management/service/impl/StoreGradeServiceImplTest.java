package com.mi.info.intl.retail.management.service.impl;

import com.mi.info.intl.retail.management.mapper.StoreGradeMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.model.ChannelTypeStatistics;
import com.mi.info.intl.retail.model.RetailerChannelGradeCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * StoreGradeServiceImpl 单元测试
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@ExtendWith(MockitoExtension.class)
class StoreGradeServiceImplTest {

    @Mock
    private StoreGradeMapper storeGradeMapper;

    @Mock
    private StoreGradeRuleMapper storeGradeRuleMapper;

    @Mock
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @InjectMocks
    private StoreGradeServiceImpl storeGradeService;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    void testGetChannelTypeStatistics_WithValidData() {
        // 准备测试数据
        List<RetailerChannelGradeCount> mockData = Arrays.asList(
            new RetailerChannelGradeCount("ONLINE", "1", 10L),
            new RetailerChannelGradeCount("ONLINE", "0", 5L),
            new RetailerChannelGradeCount("OFFLINE", "1", 8L),
            new RetailerChannelGradeCount("OFFLINE", "0", 2L)
        );

        when(storeGradeMapper.selectRetailerChannelGradeCount(any())).thenReturn(mockData);

        // 执行测试
        List<ChannelTypeStatistics> result = storeGradeService.getChannelTypeStatistics();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + ONLINE + OFFLINE

        // 验证总计统计
        ChannelTypeStatistics totalStats = result.get(0);
        assertEquals("ALL", totalStats.getChannelType());
        assertEquals(18, totalStats.getCompleteCount()); // 10 + 8
        assertEquals(7, totalStats.getNotCompleteCount()); // 5 + 2

        verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(any());
    }

    @Test
    void testGetChannelTypeStatistics_WithEmptyData() {
        when(storeGradeMapper.selectRetailerChannelGradeCount(any())).thenReturn(Arrays.asList());

        List<ChannelTypeStatistics> result = storeGradeService.getChannelTypeStatistics();

        assertNotNull(result);
        assertEquals(1, result.size()); // 只有 ALL
        assertEquals(0, result.get(0).getCompleteCount());
        assertEquals(0, result.get(0).getNotCompleteCount());

        verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithCurrentMethod() {
        // 准备测试数据
        Integer ruleId = 1;
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType("ONLINE")
            .retailerCode("RETAILER001")
            .method("CURRENT")
            .build();

        List<StoreGradeCompleteCount> mockData = Arrays.asList(
            new StoreGradeCompleteCount("S", 10L),
            new StoreGradeCompleteCount("A", 20L)
        );

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);
        when(storeGradeMapper.selectStoreGradeCompleteCount(any(), eq("ONLINE"), eq("RETAILER001"))).thenReturn(mockData);

        // 执行测试
        List<StoreGradeCompleteStatistics> result = storeGradeService.getStoreGradeCompleteStatistics(ruleId);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + S + A
        assertEquals(30, result.get(0).getCount()); // 总计

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, times(1)).selectStoreGradeCompleteCount(any(), eq("ONLINE"), eq("RETAILER001"));
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithRelationMethod() {
        // 准备测试数据
        Integer ruleId = 1;
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType("ONLINE")
            .retailerCode("RETAILER001")
            .method("RELATION")
            .build();

        List<StoreGradeCompleteCount> mockData = Arrays.asList(
            new StoreGradeCompleteCount("S", 15L),
            new StoreGradeCompleteCount("A", 25L)
        );

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);
        when(storeGradeRelationMapper.selectStoreGradeCountByRuleId(any(), any())).thenReturn(mockData);

        // 执行测试
        List<StoreGradeCompleteStatistics> result = storeGradeService.getStoreGradeCompleteStatistics(ruleId);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + S + A
        assertEquals(40, result.get(0).getCount()); // 总计

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeRelationMapper, times(1)).selectStoreGradeCountByRuleId(any(), any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithInvalidRuleId() {
        Integer ruleId = 999;
        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(null);

        List<StoreGradeCompleteStatistics> result = storeGradeService.getStoreGradeCompleteStatistics(ruleId);

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, never()).selectStoreGradeCompleteCount(any(), any(), any());
        verify(storeGradeRelationMapper, never()).selectStoreGradeCountByRuleId(any(), any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithUnsupportedMethod() {
        Integer ruleId = 1;
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType("ONLINE")
            .retailerCode("RETAILER001")
            .method("FUTURE")
            .build();

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);

        List<StoreGradeCompleteStatistics> result = storeGradeService.getStoreGradeCompleteStatistics(ruleId);

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, never()).selectStoreGradeCompleteCount(any(), any(), any());
        verify(storeGradeRelationMapper, never()).selectStoreGradeCountByRuleId(any(), any());
    }
} 