package com.mi.info.intl.retail.ldu.infra.mapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * IntlLduReportLogMapper 测试类
 * 主要测试 selectDistinctProjectCodes 方法
 * 使用 Mock 进行单元测试，避免依赖真实数据库
 */
@DisplayName("IntlLduReportLogMapper 测试")
class IntlLduReportLogMapperTest {

    @Mock
    private IntlLduReportLogMapper intlLduReportLogMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试查询所有不重复的 project_code - 正常情况")
    void testSelectDistinctProjectCodes_NormalCase() {
        // 准备测试数据
        List<String> expectedProjectCodes = Arrays.asList("PROJECT_A", "PROJECT_B", "PROJECT_C");
        
        // Mock mapper 方法
        when(intlLduReportLogMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行查询
        List<String> projectCodes = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(projectCodes, "返回的列表不应为空");
        assertEquals(expectedProjectCodes, projectCodes, "返回的数据应该与期望值一致");
        assertEquals(3, projectCodes.size(), "应该返回3个 project_code");
        
        // 验证方法被调用
        verify(intlLduReportLogMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogMapper);
    }

    @Test
    @DisplayName("测试查询所有不重复的 project_code - 空结果")
    void testSelectDistinctProjectCodes_EmptyResult() {
        // 准备测试数据 - 空列表
        List<String> expectedProjectCodes = Arrays.asList();
        
        // Mock mapper 方法
        when(intlLduReportLogMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行查询
        List<String> projectCodes = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(projectCodes, "返回的列表不应为空");
        assertEquals(expectedProjectCodes, projectCodes, "返回的数据应该与期望值一致");
        assertEquals(0, projectCodes.size(), "应该返回0个 project_code");
        assertTrue(projectCodes.isEmpty(), "列表应该为空");
        
        // 验证方法被调用
        verify(intlLduReportLogMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogMapper);
    }

    @Test
    @DisplayName("测试查询所有不重复的 project_code - 大量数据")
    void testSelectDistinctProjectCodes_LargeDataSet() {
        // 准备测试数据 - 大量 project_code
        List<String> expectedProjectCodes = Arrays.asList(
            "PROJECT_001", "PROJECT_002", "PROJECT_003", "PROJECT_004", "PROJECT_005",
            "PROJECT_006", "PROJECT_007", "PROJECT_008", "PROJECT_009", "PROJECT_010"
        );
        
        // Mock mapper 方法
        when(intlLduReportLogMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行查询
        List<String> projectCodes = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(projectCodes, "返回的列表不应为空");
        assertEquals(expectedProjectCodes, projectCodes, "返回的数据应该与期望值一致");
        assertEquals(10, projectCodes.size(), "应该返回10个 project_code");
        
        // 验证数据排序（按字母顺序）
        List<String> sortedData = projectCodes;
        for (int i = 0; i < sortedData.size() - 1; i++) {
            assertTrue(sortedData.get(i).compareTo(sortedData.get(i + 1)) <= 0, 
                "数据应该按字母顺序排序");
        }
        
        // 验证方法被调用
        verify(intlLduReportLogMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogMapper);
    }

    @Test
    @DisplayName("测试查询所有不重复的 project_code - 单个结果")
    void testSelectDistinctProjectCodes_SingleResult() {
        // 准备测试数据 - 单个 project_code
        List<String> expectedProjectCodes = Arrays.asList("SINGLE_PROJECT");
        
        // Mock mapper 方法
        when(intlLduReportLogMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行查询
        List<String> projectCodes = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(projectCodes, "返回的列表不应为空");
        assertEquals(expectedProjectCodes, projectCodes, "返回的数据应该与期望值一致");
        assertEquals(1, projectCodes.size(), "应该返回1个 project_code");
        assertEquals("SINGLE_PROJECT", projectCodes.get(0), "第一个元素应该正确");
        
        // 验证方法被调用
        verify(intlLduReportLogMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogMapper);
    }

    @Test
    @DisplayName("测试查询所有不重复的 project_code - 重复数据验证")
    void testSelectDistinctProjectCodes_DuplicateData() {
        // 准备测试数据 - 包含重复的 project_code（模拟数据库返回的数据）
        List<String> rawData = Arrays.asList("PROJECT_A", "PROJECT_B", "PROJECT_A", "PROJECT_C", "PROJECT_B");
        List<String> expectedProjectCodes = Arrays.asList("PROJECT_A", "PROJECT_B", "PROJECT_C"); // 去重后的结果
        
        // Mock mapper 方法
        when(intlLduReportLogMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行查询
        List<String> projectCodes = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(projectCodes, "返回的列表不应为空");
        assertEquals(expectedProjectCodes, projectCodes, "返回的数据应该与期望值一致");
        assertEquals(3, projectCodes.size(), "应该返回3个不重复的 project_code");
        
        // 验证去重功能
        long distinctCount = projectCodes.stream().distinct().count();
        assertEquals(projectCodes.size(), distinctCount, "结果应该已经去重");
        
        // 验证方法被调用
        verify(intlLduReportLogMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogMapper);
    }

    @Test
    @DisplayName("测试查询所有不重复的 project_code - 数据验证")
    void testSelectDistinctProjectCodes_DataValidation() {
        // 准备测试数据
        List<String> expectedProjectCodes = Arrays.asList("PROJECT_A", "PROJECT_B", "PROJECT_C");
        
        // Mock mapper 方法
        when(intlLduReportLogMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行查询
        List<String> projectCodes = intlLduReportLogMapper.selectDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(projectCodes, "返回的列表不应为空");
        
        // 验证每个元素都是字符串类型且不为空
        for (String projectCode : projectCodes) {
            assertNotNull(projectCode, "每个 project_code 不应为 null");
            assertTrue(projectCode instanceof String, "每个 project_code 应该是字符串类型");
            assertFalse(projectCode.trim().isEmpty(), "每个 project_code 不应为空字符串");
        }
        
        // 验证过滤空值功能
        boolean hasNull = projectCodes.stream().anyMatch(code -> code == null);
        assertFalse(hasNull, "结果不应包含 null 值");
        
        boolean hasEmpty = projectCodes.stream().anyMatch(code -> code.isEmpty());
        assertFalse(hasEmpty, "结果不应包含空字符串");
        
        // 验证方法被调用
        verify(intlLduReportLogMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(intlLduReportLogMapper);
    }
} 