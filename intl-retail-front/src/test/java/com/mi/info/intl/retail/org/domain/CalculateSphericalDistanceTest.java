package com.mi.info.intl.retail.org.domain;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import static org.junit.jupiter.api.Assertions.*;

/**
 * CalculateSphericalDistance类的单元测试
 * 
 * <AUTHOR> Assistant
 * @Date 2025/1/27
 */
@DisplayName("球面距离计算工具类测试")
class CalculateSphericalDistanceTest {

    @Nested
    @DisplayName("getDistance方法测试")
    class GetDistanceTest {

        @Test
        @DisplayName("测试相同坐标点的距离应为0")
        void getDistance_SameCoordinates_ShouldReturnZero() {
            // 准备数据
            double lat1 = 39.9042;
            double lng1 = 116.4074;
            double lat2 = 39.9042;
            double lng2 = 116.4074;

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(lat1, lng1, lat2, lng2);

            // 验证结果
            assertEquals(0.0, result, 0.1, "相同坐标点的距离应该为0");
        }

        @Test
        @DisplayName("测试北京到上海的直线距离")
        void getDistance_BeijingToShanghai_ShouldReturnCorrectDistance() {
            // 准备数据 - 北京坐标
            double beijingLat = 39.9042;
            double beijingLng = 116.4074;
            // 上海坐标
            double shanghaiLat = 31.2304;
            double shanghaiLng = 121.4737;

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(beijingLat, beijingLng, shanghaiLat, shanghaiLng);

            // 验证结果 - 北京到上海的实际距离约为1068公里
            assertTrue(result > 1060000 && result < 1080000, 
                "北京到上海的距离应该在1060-1080公里之间，实际结果: " + result + " 米");
        }

        @Test
        @DisplayName("测试经纬度边界值")
        void getDistance_BoundaryValues_ShouldHandleCorrectly() {
            // 准备数据 - 测试边界值
            double lat1 = 90.0;  // 北极
            double lng1 = 180.0; // 最东
            double lat2 = -90.0; // 南极
            double lng2 = -180.0; // 最西

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(lat1, lng1, lat2, lng2);

            // 验证结果 - 从北极到南极的距离应该约为20000公里
            assertTrue(result > 19900000 && result < 20100000, 
                "从北极到南极的距离应该在19900-20100公里之间，实际结果: " + result + " 米");
        }

        @Test
        @DisplayName("测试经度跨越180度的情况")
        void getDistance_Crossing180Degree_ShouldHandleCorrectly() {
            // 准备数据 - 跨越180度经线
            double lat1 = 0.0;
            double lng1 = 179.0; // 接近180度
            double lat2 = 0.0;
            double lng2 = -179.0; // 接近-180度

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(lat1, lng1, lat2, lng2);

            // 验证结果 - 在赤道上跨越180度经线的距离应该约为222公里
            assertTrue(result > 220000 && result < 224000, 
                "跨越180度经线的距离应该在220-224公里之间，实际结果: " + result + " 米");
        }

        @Test
        @DisplayName("测试短距离计算")
        void getDistance_ShortDistance_ShouldReturnAccurateResult() {
            // 准备数据 - 两个很近的点
            double lat1 = 39.9042;
            double lng1 = 116.4074;
            double lat2 = 39.9043; // 纬度增加0.0001度
            double lng2 = 116.4075; // 经度增加0.0001度

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(lat1, lng1, lat2, lng2);

            // 验证结果 - 短距离应该大于0且合理
            assertTrue(result > 0 && result < 100, 
                "短距离应该在0-100米之间，实际结果: " + result + " 米");
        }

        @Test
        @DisplayName("测试不同半球的距离计算")
        void getDistance_DifferentHemispheres_ShouldReturnCorrectDistance() {
            // 准备数据 - 北半球和南半球
            double lat1 = 40.7128; // 纽约纬度
            double lng1 = -74.0060; // 纽约经度
            double lat2 = -33.8688; // 悉尼纬度
            double lng2 = 151.2093; // 悉尼经度

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(lat1, lng1, lat2, lng2);

            // 验证结果 - 纽约到悉尼的距离应该约为16000公里
            assertTrue(result > 15900000 && result < 16100000, 
                "纽约到悉尼的距离应该在15900-16100公里之间，实际结果: " + result + " 米");
        }

        @Test
        @DisplayName("测试零度坐标")
        void getDistance_ZeroCoordinates_ShouldHandleCorrectly() {
            // 准备数据 - 零度坐标
            double lat1 = 0.0;
            double lng1 = 0.0;
            double lat2 = 0.0;
            double lng2 = 1.0; // 经度差1度

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(lat1, lng1, lat2, lng2);

            // 验证结果 - 在赤道上经度差1度约为111公里
            assertTrue(result > 110000 && result < 112000, 
                "赤道上经度差1度应该在110-112公里之间，实际结果: " + result + " 米");
        }

        @Test
        @DisplayName("测试极大距离")
        void getDistance_VeryLargeDistance_ShouldHandleCorrectly() {
            // 准备数据 - 地球直径距离
            double lat1 = 0.0;
            double lng1 = 0.0;
            double lat2 = 0.0;
            double lng2 = 180.0; // 对跖点

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(lat1, lng1, lat2, lng2);

            // 验证结果 - 地球半周长约为20000公里
            assertTrue(result > 19900000 && result < 20100000, 
                "地球半周长应该在19900-20100公里之间，实际结果: " + result + " 米");
        }

        @Test
        @DisplayName("测试精度验证")
        void getDistance_PrecisionTest_ShouldBeAccurate() {
            // 准备数据 - 使用已知精确距离的坐标
            double lat1 = 40.7128; // 纽约
            double lng1 = -74.0060;
            double lat2 = 40.7128; // 同纬度，经度差1度
            double lng2 = -73.0060;

            // 执行测试
            double result = CalculateSphericalDistance.getDistance(lat1, lng1, lat2, lng2);

            // 验证结果 - 在纽约纬度上经度差1度约为85公里
            assertTrue(result > 84000 && result < 86000, 
                "在纽约纬度上经度差1度应该在84-86公里之间，实际结果: " + result + " 米");
        }
    }

    @Nested
    @DisplayName("rad方法测试")
    class RadTest {

        @Test
        @DisplayName("测试角度转弧度的基本转换")
        void rad_BasicConversion_ShouldReturnCorrectRadian() {
            // 准备数据
            double degree = 180.0;

            // 执行测试 - 通过反射调用私有方法
            try {
                java.lang.reflect.Method radMethod = CalculateSphericalDistance.class.getDeclaredMethod("rad", double.class);
                radMethod.setAccessible(true);
                double result = (double) radMethod.invoke(null, degree);

                // 验证结果 - 180度应该等于π弧度
                assertEquals(Math.PI, result, 0.0001, "180度应该等于π弧度");
            } catch (Exception e) {
                fail("无法调用rad方法: " + e.getMessage());
            }
        }

        @Test
        @DisplayName("测试零度转弧度")
        void rad_ZeroDegree_ShouldReturnZero() {
            // 准备数据
            double degree = 0.0;

            // 执行测试
            try {
                java.lang.reflect.Method radMethod = CalculateSphericalDistance.class.getDeclaredMethod("rad", double.class);
                radMethod.setAccessible(true);
                double result = (double) radMethod.invoke(null, degree);

                // 验证结果
                assertEquals(0.0, result, 0.0001, "0度应该等于0弧度");
            } catch (Exception e) {
                fail("无法调用rad方法: " + e.getMessage());
            }
        }

        @Test
        @DisplayName("测试90度转弧度")
        void rad_NinetyDegree_ShouldReturnHalfPi() {
            // 准备数据
            double degree = 90.0;

            // 执行测试
            try {
                java.lang.reflect.Method radMethod = CalculateSphericalDistance.class.getDeclaredMethod("rad", double.class);
                radMethod.setAccessible(true);
                double result = (double) radMethod.invoke(null, degree);

                // 验证结果 - 90度应该等于π/2弧度
                assertEquals(Math.PI / 2, result, 0.0001, "90度应该等于π/2弧度");
            } catch (Exception e) {
                fail("无法调用rad方法: " + e.getMessage());
            }
        }

        @Test
        @DisplayName("测试360度转弧度")
        void rad_ThreeSixtyDegree_ShouldReturnTwoPi() {
            // 准备数据
            double degree = 360.0;

            // 执行测试
            try {
                java.lang.reflect.Method radMethod = CalculateSphericalDistance.class.getDeclaredMethod("rad", double.class);
                radMethod.setAccessible(true);
                double result = (double) radMethod.invoke(null, degree);

                // 验证结果 - 360度应该等于2π弧度
                assertEquals(2 * Math.PI, result, 0.0001, "360度应该等于2π弧度");
            } catch (Exception e) {
                fail("无法调用rad方法: " + e.getMessage());
            }
        }
    }

    @Nested
    @DisplayName("常量测试")
    class ConstantsTest {

        @Test
        @DisplayName("测试地球半径常量")
        void earthRadius_ShouldBeCorrect() {
            // 通过反射获取私有常量
            try {
                java.lang.reflect.Field field = CalculateSphericalDistance.class.getDeclaredField("EARTH_RADIUS");
                field.setAccessible(true);
                double earthRadius = (double) field.get(null);

                // 验证结果 - 地球半径应该约为6378137米
                assertEquals(6378137.0, earthRadius, 0.1, "地球半径应该为6378137.0米");
            } catch (Exception e) {
                fail("无法获取EARTH_RADIUS常量: " + e.getMessage());
            }
        }
    }
} 