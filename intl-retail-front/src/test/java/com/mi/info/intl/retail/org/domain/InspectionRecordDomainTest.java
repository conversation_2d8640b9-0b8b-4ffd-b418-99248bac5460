package com.mi.info.intl.retail.org.domain;

import com.mi.info.intl.retail.intlretail.service.api.position.enums.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * InspectionRecordDomain 单元测试
 * <AUTHOR>
 */
@DisplayName("InspectionRecordDomain领域对象测试")
class InspectionRecordDomainTest {

    @Nested
    @DisplayName("approve方法测试")
    class ApproveTest {
        @Test
        @DisplayName("调用approve后应更新modifiedOn")
        void approve_ShouldUpdateModifiedOn() throws InterruptedException {
            InspectionRecordDomain domain = new InspectionRecordDomain();
            domain.setModifiedOn(1L);
            Thread.sleep(2); // 保证时间变化
            domain.approve();
            assertTrue(domain.getModifiedOn() > 1L, "approve应更新时间戳");
        }
    }

    @Nested
    @DisplayName("disapprove方法测试")
    class DisapproveTest {
        @Test
        @DisplayName("disapprove应设置状态、remark和原因")
        void disapprove_ShouldSetFields() throws InterruptedException {
            InspectionRecordDomain domain = new InspectionRecordDomain();
            domain.setModifiedOn(1L);
            String remark = "照片模糊";
            DisapproveReasonEnum reason = DisapproveReasonEnum.PHOTO_BLURRY;
            Thread.sleep(2);
            domain.disapprove(remark, reason);
            assertEquals(TaskStatusEnum.NOT_COMPLETED, domain.getTaskStatus());
            assertEquals(remark, domain.getRemark());
            assertEquals(reason, domain.getDisapproveReason());
            assertTrue(domain.getModifiedOn() > 1L, "disapprove应更新时间戳");
        }
    }

    @Nested
    @DisplayName("markAsNoNeedToComplete方法测试")
    class MarkAsNoNeedToCompleteTest {
        @Test
        @DisplayName("markAsNoNeedToComplete应设置相关字段")
        void markAsNoNeedToComplete_ShouldSetFields() {
            InspectionRecordDomain domain = new InspectionRecordDomain();
            String modifiedBy = "user123";
            domain.markAsNoNeedToComplete(modifiedBy);
            assertEquals(TaskStatusEnum.NO_NEED_TO_DO, domain.getTaskStatus());
            assertEquals(InspectionStatusEnum.TO_BE_VERIFIED, domain.getInspectionStatus());
            assertEquals(modifiedBy, domain.getModifiedBy());
            assertEquals("{}", domain.getUploadData());
            assertNotNull(domain.getTaskCompletionTime());
            assertEquals(0L, domain.getVerificationTime());
        }
    }

    @Test
    @DisplayName("边界场景：disapprove传null reason")
    void disapprove_NullReason_ShouldHandleGracefully() {
        InspectionRecordDomain domain = new InspectionRecordDomain();
        String remark = "无原因";
        domain.disapprove(remark, null);
        assertEquals(TaskStatusEnum.NOT_COMPLETED, domain.getTaskStatus());
        assertEquals(remark, domain.getRemark());
        assertNull(domain.getDisapproveReason());
    }
}