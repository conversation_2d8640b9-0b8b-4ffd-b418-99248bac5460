# Project Code 查询功能测试指南

## 测试类型说明

### 1. 单元测试（推荐）
- **文件**: `IntlLduReportLogMapperTest.java`
- **特点**: 使用 Mock 进行测试，不依赖真实数据库
- **优势**: 运行快速，不依赖外部环境
- **适用场景**: 开发阶段快速验证逻辑

### 2. 服务层测试
- **文件**: `IntlLduReportLogServiceImplTest.java`
- **特点**: 测试服务层业务逻辑
- **优势**: 验证异常处理和业务逻辑

### 3. 控制器层测试
- **文件**: `IntlLduReportLogControllerTest.java`
- **特点**: 测试 REST API 接口
- **优势**: 验证接口响应格式

### 4. 集成测试（可选）
- **文件**: `IntlLduReportLogMapperIntegrationTest.java`
- **特点**: 使用真实数据库连接
- **优势**: 验证 SQL 语句和数据库交互
- **注意**: 需要配置测试数据库环境

## 运行测试

### 运行所有单元测试
```bash
# 在项目根目录执行
mvn test -Dtest="*Test" -Dexcludes="*IntegrationTest"
```

### 运行特定测试类
```bash
# 运行 Mapper 单元测试
mvn test -Dtest=IntlLduReportLogMapperTest

# 运行服务层测试
mvn test -Dtest=IntlLduReportLogServiceImplTest

# 运行控制器测试
mvn test -Dtest=IntlLduReportLogControllerTest
```

### 运行集成测试（需要数据库环境）
```bash
# 运行集成测试
mvn test -Dtest=IntlLduReportLogMapperIntegrationTest
```

## 解决 Autowire 问题

### 问题原因
IDEA 提示 "Could not autowire. No beans of 'IntlLduReportLogMapper' type found" 的原因：

1. **测试环境配置不完整**
2. **MyBatis Mapper 没有被正确扫描**
3. **缺少必要的依赖**

### 解决方案

#### 方案一：使用单元测试（推荐）
```java
@Mock
private IntlLduReportLogMapper intlLduReportLogMapper;

@BeforeEach
void setUp() {
    MockitoAnnotations.openMocks(this);
}
```

#### 方案二：配置集成测试环境
1. 确保 `application-test.yml` 配置正确
2. 添加必要的依赖到 `pom.xml`：

```xml
<!-- 测试依赖 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>

<!-- H2 内存数据库（用于测试） -->
<dependency>
    <groupId>com.h2database</groupId>
    <artifactId>h2</artifactId>
    <scope>test</scope>
</dependency>
```

#### 方案三：使用测试配置类
```java
@TestConfiguration
@MapperScan("com.mi.info.intl.retail.ldu.infra.mapper")
public class TestConfig {
    // 测试配置
}
```

## 测试最佳实践

### 1. 优先使用单元测试
- 运行速度快
- 不依赖外部环境
- 便于调试和维护

### 2. 合理使用集成测试
- 验证数据库交互
- 测试 SQL 语句正确性
- 性能测试

### 3. 测试覆盖率
- 正常流程测试
- 异常情况测试
- 边界条件测试

### 4. 测试数据管理
- 使用测试专用的数据
- 测试后清理数据
- 避免影响生产环境

## 常见问题解决

### Q: 为什么 IDEA 显示 Autowire 错误？
A: 这是正常的，因为单元测试使用 Mock，不需要真实的 Bean。可以忽略这个警告。

### Q: 如何验证 SQL 语句正确性？
A: 使用集成测试，或者查看 MyBatis 的 SQL 日志输出。

### Q: 测试运行很慢怎么办？
A: 使用单元测试替代集成测试，或者配置更快的测试数据库。

### Q: 如何调试测试？
A: 在 IDEA 中右键测试方法，选择 "Debug" 进行调试。

## 测试结果验证

### 成功标准
1. 所有测试用例通过
2. 代码覆盖率 > 80%
3. 无编译错误和警告

### 失败排查
1. 检查测试环境配置
2. 验证依赖是否正确
3. 查看测试日志输出 