package com.mi.info.intl.retail.org.app.event.listener;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.VerifyActionEnum;
import com.mi.info.intl.retail.org.app.event.UpdateApprovalInfo;
import com.mi.info.intl.retail.org.app.event.UpdateApprovalStatusEvent;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.enums.OperationType;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.infra.http.MaindataHttp;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UpdateApprovalStatusEventListener单元测试
 */
@ExtendWith(MockitoExtension.class)
class UpdateApprovalStatusEventListenerTest {

    @InjectMocks
    private UpdateApprovalStatusEventListener listener;

    @Mock
    private InspectionRecordRepository inspectionRecordRepository;

    @Mock
    private InspectionHistoryRepository inspectionHistoryRepository;

    @Mock
    private MaindataHttp maindataHttp;

    @Mock
    private RuleConfigRepository ruleConfigRepository;

    @Mock
    private TaskCenterServiceRpc taskCenterServiceRpc;

    @Mock
    private PositionRepository positionRepository;

    private UpdateApprovalStatusEvent event;
    private UpdateApprovalInfo updateApprovalInfo;
    private InspectionRecordDomain inspectionRecord;
    private Long positionInspectionId = 12345L;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        updateApprovalInfo = new UpdateApprovalInfo();
        updateApprovalInfo.setPositionInspectionId(positionInspectionId);
        updateApprovalInfo.setVerifier("testUser");
        updateApprovalInfo.setVerifierMiid(123456L);

        event = new UpdateApprovalStatusEvent(this, updateApprovalInfo);

        inspectionRecord = new InspectionRecordDomain();
        inspectionRecord.setId(positionInspectionId);
        inspectionRecord.setPositionCode("POS001");
        inspectionRecord.setRuleCode("RULE001");
        inspectionRecord.setInspectionOwnerMiId(789L);
        inspectionRecord.setTaskStatus(TaskStatusEnum.COMPLETED);
        inspectionRecord.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
        inspectionRecord.setUploadData("{\"storeGate\":{\"images\":[\"image1.jpg\"]}}");
    }

    @Test
    @DisplayName("审批通过操作 - 成功场景")
    void handleUpdateApprovalStatusEvent_Approve_Success() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(VerifyActionEnum.APPROVE.getCode());
        when(inspectionRecordRepository.getById(positionInspectionId)).thenReturn(inspectionRecord);
        when(inspectionRecordRepository.update(any(InspectionRecordDomain.class))).thenReturn(true);
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(true);
        when(maindataHttp.editPositionInfo(anyList())).thenReturn(new PositionResponse());

        // 执行测试
        listener.handleUpdateApprovalStatusEvent(event);

        // 验证结果
        verify(inspectionRecordRepository).getById(positionInspectionId);
        verify(inspectionRecordRepository).update(argThat(record -> 
            record.getInspectionStatus() == InspectionStatusEnum.VERIFICATION_PASSED &&
            record.getVerifier().equals("testUser") &&
            record.getVerifierMiid().equals(123456L)
        ));
        verify(inspectionHistoryRepository).save(argThat(history -> 
            history.getOperationType() == OperationType.VERIFICATION_SUCCESS
        ));
        // editPositionInfo在update成功后被调用
        verify(maindataHttp).editPositionInfo(anyList());
    }

    @Test
    @DisplayName("审批拒绝操作 - 成功场景")
    void handleUpdateApprovalStatusEvent_Disapprove_Success() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(VerifyActionEnum.DISAPPROVE.getCode());
        updateApprovalInfo.setRemark("测试拒绝原因");
        updateApprovalInfo.setReason(DisapproveReasonEnum.OTHER.getCode());

        when(inspectionRecordRepository.getById(positionInspectionId)).thenReturn(inspectionRecord);
        when(inspectionRecordRepository.update(any(InspectionRecordDomain.class))).thenReturn(true);
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(true);
        when(ruleConfigRepository.getByRuleCode("RULE001")).thenReturn(createRuleConfig());
        when(positionRepository.getByPositionCode("POS001")).thenReturn(createPositionDomain());
        doNothing().when(taskCenterServiceRpc).reloadTaskStatus(any(TaskCenterTaskReq.class));

        // 执行测试
        listener.handleUpdateApprovalStatusEvent(event);

        // 验证结果
        verify(inspectionRecordRepository).update(argThat(record -> 
            record.getInspectionStatus() == InspectionStatusEnum.VERIFICATION_FAILED &&
            record.getTaskStatus() == TaskStatusEnum.NOT_COMPLETED &&
            record.getRemark().equals("测试拒绝原因") &&
            record.getDisapproveReason() == DisapproveReasonEnum.OTHER
        ));
        verify(inspectionHistoryRepository).save(argThat(history -> 
            history.getOperationType() == OperationType.VERIFICATION_FAILED
        ));
        verify(taskCenterServiceRpc).reloadTaskStatus(any(TaskCenterTaskReq.class));
    }

    @Test
    @DisplayName("阵地巡检记录不存在 - 异常场景")
    void handleUpdateApprovalStatusEvent_RecordNotFound() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(VerifyActionEnum.APPROVE.getCode());
        when(inspectionRecordRepository.getById(positionInspectionId)).thenReturn(null);

        // 执行测试
        listener.handleUpdateApprovalStatusEvent(event);

        // 验证结果
        verify(inspectionRecordRepository).getById(positionInspectionId);
        verify(inspectionRecordRepository, never()).update(any());
        verify(inspectionHistoryRepository, never()).save(any());
    }

    @Test
    @DisplayName("无效的验证操作 - 异常场景")
    void handleUpdateApprovalStatusEvent_InvalidVerifyAction() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(999); // 无效的验证操作
        when(inspectionRecordRepository.getById(positionInspectionId)).thenReturn(inspectionRecord);

        // 执行测试
        listener.handleUpdateApprovalStatusEvent(event);

        // 验证结果
        verify(inspectionRecordRepository).getById(positionInspectionId);
        verify(inspectionRecordRepository, never()).update(any());
        verify(inspectionHistoryRepository, never()).save(any());
    }

    @Test
    @DisplayName("保存历史记录失败 - 异常场景")
    void handleUpdateApprovalStatusEvent_SaveHistoryFailed() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(VerifyActionEnum.APPROVE.getCode());
        when(inspectionRecordRepository.getById(positionInspectionId)).thenReturn(inspectionRecord);
        when(inspectionRecordRepository.update(any(InspectionRecordDomain.class))).thenReturn(true);
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(false);
        when(maindataHttp.editPositionInfo(anyList())).thenReturn(new PositionResponse());

        // 执行测试
        listener.handleUpdateApprovalStatusEvent(event);

        // 验证结果
        verify(inspectionRecordRepository).update(any());
        verify(inspectionHistoryRepository).save(any());
        // 即使历史记录保存失败，editPositionInfo仍会被调用（因为update成功了）
        verify(maindataHttp).editPositionInfo(anyList());
    }

    @Test
    @DisplayName("审批通过但无上传数据 - 边界场景")
    void handleUpdateApprovalStatusEvent_ApproveWithEmptyUploadData() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(VerifyActionEnum.APPROVE.getCode());
        inspectionRecord.setUploadData(null);
        when(inspectionRecordRepository.getById(positionInspectionId)).thenReturn(inspectionRecord);
        when(inspectionRecordRepository.update(any(InspectionRecordDomain.class))).thenReturn(true);
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(true);

        // 执行测试
        listener.handleUpdateApprovalStatusEvent(event);

        // 验证结果
        verify(inspectionRecordRepository).update(any());
        verify(inspectionHistoryRepository).save(any());
        verify(maindataHttp, never()).editPositionInfo(anyList());
    }

    @Test
    @DisplayName("拒绝操作但规则配置缺失 - 异常场景")
    void handleUpdateApprovalStatusEvent_DisapproveWithMissingRuleConfig() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(VerifyActionEnum.DISAPPROVE.getCode());
        updateApprovalInfo.setRemark("测试拒绝原因");
        updateApprovalInfo.setReason(DisapproveReasonEnum.OTHER.getCode());

        when(inspectionRecordRepository.getById(positionInspectionId)).thenReturn(inspectionRecord);
        when(inspectionRecordRepository.update(any(InspectionRecordDomain.class))).thenReturn(true);
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(true);
        when(ruleConfigRepository.getByRuleCode("RULE001")).thenReturn(null);

        // 执行测试
        listener.handleUpdateApprovalStatusEvent(event);

        // 验证结果
        verify(inspectionRecordRepository).update(any());
        verify(inspectionHistoryRepository).save(any());
        verify(taskCenterServiceRpc, never()).reloadTaskStatus(any(com.xiaomi.cnzone.brain.platform.api.model.req.TaskReq.class));
    }

    @Test
    @DisplayName("拒绝操作但阵地信息缺失 - 异常场景")
    void handleUpdateApprovalStatusEvent_DisapproveWithMissingPosition() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(VerifyActionEnum.DISAPPROVE.getCode());
        updateApprovalInfo.setRemark("测试拒绝原因");
        updateApprovalInfo.setReason(DisapproveReasonEnum.OTHER.getCode());

        when(inspectionRecordRepository.getById(positionInspectionId)).thenReturn(inspectionRecord);
        when(inspectionRecordRepository.update(any(InspectionRecordDomain.class))).thenReturn(true);
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(true);
        when(ruleConfigRepository.getByRuleCode("RULE001")).thenReturn(createRuleConfig());
        when(positionRepository.getByPositionCode("POS001")).thenReturn(null);

        // 执行测试
        listener.handleUpdateApprovalStatusEvent(event);

        // 验证结果
        verify(inspectionRecordRepository).update(any());
        verify(inspectionHistoryRepository).save(any());
        verify(taskCenterServiceRpc, never()).reloadTaskStatus(any(com.xiaomi.cnzone.brain.platform.api.model.req.TaskReq.class));
    }

    @Test
    @DisplayName("数据库异常处理 - 异常场景")
    void handleUpdateApprovalStatusEvent_ExceptionHandling() {
        // 准备数据
        updateApprovalInfo.setVerifyAction(VerifyActionEnum.APPROVE.getCode());
        when(inspectionRecordRepository.getById(positionInspectionId)).thenThrow(new RuntimeException("数据库异常"));

        // 执行测试 - 不应该抛出异常
        assertDoesNotThrow(() -> listener.handleUpdateApprovalStatusEvent(event));

        // 验证结果
        verify(inspectionRecordRepository).getById(positionInspectionId);
        verify(inspectionRecordRepository, never()).update(any());
    }

    private RuleConfigDomain createRuleConfig() {
        RuleConfigDomain ruleConfig = new RuleConfigDomain();
        ruleConfig.setTaskBatchId(12345L);
        return ruleConfig;
    }

    private PositionDomain createPositionDomain() {
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("POS001");
        return positionDomain;
    }
} 