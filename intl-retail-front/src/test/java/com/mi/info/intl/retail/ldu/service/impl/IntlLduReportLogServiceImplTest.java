package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduReportLogMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * IntlLduReportLogServiceImpl 测试类
 * 主要测试 getDistinctProjectCodes 方法
 */
@DisplayName("IntlLduReportLogServiceImpl 测试")
class IntlLduReportLogServiceImplTest {

    @Mock
    private IntlLduReportLogMapper statisticsMapper;

    @InjectMocks
    private IntlLduReportLogServiceImpl intlLduReportLogService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("成功获取不重复的 project_code 列表")
    void testGetDistinctProjectCodes_Success() {
        // 准备测试数据
        List<String> expectedProjectCodes = Arrays.asList("PROJECT_A", "PROJECT_B", "PROJECT_C", "PROJECT_D");
        
        // Mock mapper 方法
        when(statisticsMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogService.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertNotNull(response.getData(), "数据不应为空");
        assertEquals(expectedProjectCodes, response.getData(), "返回的数据应该与期望值一致");
        assertEquals(4, response.getData().size(), "应该返回4个 project_code");
        assertEquals("ok", response.getMessage(), "成功时消息应该为 'ok'");
        
        // 验证 mapper 方法被调用
        verify(statisticsMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(statisticsMapper);
    }

    @Test
    @DisplayName("获取空列表 - 没有 project_code 数据")
    void testGetDistinctProjectCodes_EmptyResult() {
        // 准备测试数据 - 空列表
        List<String> expectedProjectCodes = Collections.emptyList();
        
        // Mock mapper 方法
        when(statisticsMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogService.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertNotNull(response.getData(), "数据不应为空");
        assertEquals(expectedProjectCodes, response.getData(), "返回的数据应该与期望值一致");
        assertEquals(0, response.getData().size(), "应该返回0个 project_code");
        assertTrue(response.getData().isEmpty(), "列表应该为空");
        
        // 验证 mapper 方法被调用
        verify(statisticsMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(statisticsMapper);
    }

    @Test
    @DisplayName("数据库异常处理")
    void testGetDistinctProjectCodes_DatabaseException() {
        // Mock mapper 方法抛出异常
        when(statisticsMapper.selectDistinctProjectCodes()).thenThrow(new RuntimeException("Database connection failed"));
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogService.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "响应不应为空");
        assertNotEquals(0, response.getCode(), "失败时 code 不应为 0");
        assertNull(response.getData(), "失败时数据应该为空");
        assertNotNull(response.getMessage(), "错误消息不应为空");
        assertTrue(response.getMessage().contains("查询 project_code 失败"), "错误消息应该包含预期内容");
        assertTrue(response.getMessage().contains("Database connection failed"), "错误消息应该包含具体异常信息");
        
        // 验证 mapper 方法被调用
        verify(statisticsMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(statisticsMapper);
    }

    @Test
    @DisplayName("SQL 异常处理")
    void testGetDistinctProjectCodes_SqlException() {
        // Mock mapper 方法抛出 SQL 异常
        when(statisticsMapper.selectDistinctProjectCodes()).thenThrow(new org.springframework.jdbc.UncategorizedSQLException("SQL Error", "SELECT", new java.sql.SQLException("Table not found")));
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogService.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "响应不应为空");
        assertNotEquals(0, response.getCode(), "失败时 code 不应为 0");
        assertNull(response.getData(), "失败时数据应该为空");
        assertNotNull(response.getMessage(), "错误消息不应为空");
        assertTrue(response.getMessage().contains("查询 project_code 失败"), "错误消息应该包含预期内容");
        
        // 验证 mapper 方法被调用
        verify(statisticsMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(statisticsMapper);
    }

    @Test
    @DisplayName("获取单个 project_code")
    void testGetDistinctProjectCodes_SingleResult() {
        // 准备测试数据 - 只有一个 project_code
        List<String> expectedProjectCodes = Arrays.asList("SINGLE_PROJECT");
        
        // Mock mapper 方法
        when(statisticsMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogService.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertNotNull(response.getData(), "数据不应为空");
        assertEquals(expectedProjectCodes, response.getData(), "返回的数据应该与期望值一致");
        assertEquals(1, response.getData().size(), "应该返回1个 project_code");
        assertEquals("SINGLE_PROJECT", response.getData().get(0), "第一个元素应该正确");
        
        // 验证 mapper 方法被调用
        verify(statisticsMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(statisticsMapper);
    }

    @Test
    @DisplayName("获取大量 project_code")
    void testGetDistinctProjectCodes_LargeResult() {
        // 准备测试数据 - 大量 project_code
        List<String> expectedProjectCodes = Arrays.asList(
            "PROJECT_001", "PROJECT_002", "PROJECT_003", "PROJECT_004", "PROJECT_005",
            "PROJECT_006", "PROJECT_007", "PROJECT_008", "PROJECT_009", "PROJECT_010"
        );
        
        // Mock mapper 方法
        when(statisticsMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogService.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertNotNull(response.getData(), "数据不应为空");
        assertEquals(expectedProjectCodes, response.getData(), "返回的数据应该与期望值一致");
        assertEquals(10, response.getData().size(), "应该返回10个 project_code");
        
        // 验证数据排序（按字母顺序）
        List<String> sortedData = response.getData();
        for (int i = 0; i < sortedData.size() - 1; i++) {
            assertTrue(sortedData.get(i).compareTo(sortedData.get(i + 1)) <= 0, 
                "数据应该按字母顺序排序");
        }
        
        // 验证 mapper 方法被调用
        verify(statisticsMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(statisticsMapper);
    }

    @Test
    @DisplayName("NullPointerException 异常处理")
    void testGetDistinctProjectCodes_NullPointerException() {
        // Mock mapper 方法抛出 NullPointerException
        when(statisticsMapper.selectDistinctProjectCodes()).thenThrow(new NullPointerException("Mapper is null"));
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogService.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "响应不应为空");
        assertNotEquals(0, response.getCode(), "失败时 code 不应为 0");
        assertNull(response.getData(), "失败时数据应该为空");
        assertNotNull(response.getMessage(), "错误消息不应为空");
        assertTrue(response.getMessage().contains("查询 project_code 失败"), "错误消息应该包含预期内容");
        
        // 验证 mapper 方法被调用
        verify(statisticsMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(statisticsMapper);
    }

    @Test
    @DisplayName("验证返回数据的不可变性")
    void testGetDistinctProjectCodes_DataImmutability() {
        // 准备测试数据
        List<String> originalData = Arrays.asList("PROJECT_A", "PROJECT_B", "PROJECT_C");
        List<String> expectedProjectCodes = new java.util.ArrayList<>(originalData);
        
        // Mock mapper 方法
        when(statisticsMapper.selectDistinctProjectCodes()).thenReturn(expectedProjectCodes);
        
        // 执行测试
        CommonResponse<List<String>> response = intlLduReportLogService.getDistinctProjectCodes();
        
        // 验证结果
        assertNotNull(response, "响应不应为空");
        assertEquals(0, response.getCode(), "成功时 code 应该为 0");
        assertNotNull(response.getData(), "数据不应为空");
        
        // 尝试修改返回的数据，验证不可变性
        List<String> returnedData = response.getData();
        assertThrows(UnsupportedOperationException.class, () -> {
            returnedData.add("NEW_PROJECT");
        }, "返回的列表应该是不可变的");
        
        // 验证 mapper 方法被调用
        verify(statisticsMapper, times(1)).selectDistinctProjectCodes();
        verifyNoMoreInteractions(statisticsMapper);
    }
} 