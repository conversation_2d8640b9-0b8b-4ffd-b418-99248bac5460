package com.mi.info.intl.retail.org.domain;

import com.mi.info.intl.retail.intlretail.service.api.position.enums.PositionStageEnum;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class RuleConfigDomainTest {

    private RuleConfigDomain newRuleConfigWithWindow(Long startMillis, Long endMillis) {
        RuleConfigDomain domain = new RuleConfigDomain();
        domain.setRuleCode("RULE-001");
        domain.setStartTime(startMillis);
        domain.setEndTime(endMillis);
        return domain;
        }

    @Test
    @DisplayName("validatePositionStage_传入null_返回false")
    void validatePositionStage_NullStage_ReturnsFalse() {
        RuleConfigDomain domain = newRuleConfigWithWindow(null, null);
        assertFalse(domain.validatePositionStage(null));
    }

    @Test
    @DisplayName("validatePositionStage_当前时间早于开始时间_返回false")
    void validatePositionStage_BeforeStartTime_ReturnsFalse() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(now + 60_000, now + 120_000);
        assertFalse(domain.validatePositionStage(PositionStageEnum.ACCEPTANCE_APPROVED));
    }

    @Test
    @DisplayName("validatePositionStage_当前时间晚于结束时间_返回false")
    void validatePositionStage_AfterEndTime_ReturnsFalse() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(now - 120_000, now - 60_000);
        assertFalse(domain.validatePositionStage(PositionStageEnum.ACCEPTANCE_APPROVED));
    }

    @Test
    @DisplayName("validatePositionStage_当前时间在有效期内_返回true")
    void validatePositionStage_WithinRange_ReturnsTrue() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(now - 60_000, now + 60_000);
        assertTrue(domain.validatePositionStage(PositionStageEnum.ACCEPTANCE_APPROVED));
    }

    @Test
    @DisplayName("validatePositionStage_无开始时间仅有未来结束时间_返回true")
    void validatePositionStage_NullStart_EndInFuture_ReturnsTrue() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(null, now + 60_000);
        assertTrue(domain.validatePositionStage(PositionStageEnum.UPGRADE_ACCEPTANCE_APPROVED));
    }

    @Test
    @DisplayName("validatePositionStage_开始时间已过无结束时间_返回true")
    void validatePositionStage_StartInPast_NullEnd_ReturnsTrue() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(now - 60_000, null);
        assertTrue(domain.validatePositionStage(PositionStageEnum.UPGRADE_ACCEPTANCE_APPROVED));
    }

    @Test
    @DisplayName("validateInspectionRecord_传入null_返回false")
    void validateInspectionRecord_NullRecord_ReturnsFalse() {
        RuleConfigDomain domain = newRuleConfigWithWindow(null, null);
        assertFalse(domain.validateInspectionRecord(null));
    }

    @Test
    @DisplayName("validateInspectionRecord_巡检记录创建时间早于开始时间_返回false")
    void validateInspectionRecord_BeforeStartTime_ReturnsFalse() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(now - 60_000, now + 60_000);
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setRuleCode("RULE-001");
        record.setPositionCreationTime(now - 120_000);
        assertFalse(domain.validateInspectionRecord(record));
    }

    @Test
    @DisplayName("validateInspectionRecord_巡检记录创建时间晚于结束时间_返回false")
    void validateInspectionRecord_AfterEndTime_ReturnsFalse() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(now - 120_000, now - 60_000);
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setRuleCode("RULE-001");
        record.setPositionCreationTime(now);
        assertFalse(domain.validateInspectionRecord(record));
    }

    @Test
    @DisplayName("validateInspectionRecord_巡检记录创建时间在有效期内_返回true")
    void validateInspectionRecord_WithinRange_ReturnsTrue() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(now - 120_000, now + 120_000);
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setRuleCode("RULE-001");
        record.setPositionCreationTime(now);
        assertTrue(domain.validateInspectionRecord(record));
    }

    @Test
    @DisplayName("validateInspectionRecord_创建时间等于开始与结束边界_返回true")
    void validateInspectionRecord_OnBoundary_ReturnsTrue() {
        long now = System.currentTimeMillis();
        RuleConfigDomain domain = newRuleConfigWithWindow(now, now);
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setRuleCode("RULE-001");
        record.setPositionCreationTime(now);
        assertTrue(domain.validateInspectionRecord(record));
    }
} 