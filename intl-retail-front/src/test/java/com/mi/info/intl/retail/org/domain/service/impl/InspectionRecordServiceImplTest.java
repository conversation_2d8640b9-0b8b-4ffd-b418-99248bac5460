package com.mi.info.intl.retail.org.domain.service.impl;

import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.dto.InspectionRecordDTO;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * InspectionRecordServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
class InspectionRecordServiceImplTest {

    @InjectMocks
    private InspectionRecordServiceImpl inspectionRecordService;

    @Mock
    private InspectionRecordRepository inspectionRecordRepository;

    private InspectionRecordDomain testInspectionRecordDomain;
    private InspectionRecordDTO testInspectionRecordDTO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testInspectionRecordDomain = new InspectionRecordDomain();
        testInspectionRecordDomain.setId(1L);
        testInspectionRecordDomain.setRuleCode("RULE_001");
        testInspectionRecordDomain.setPositionCode("POS_001");
        testInspectionRecordDomain.setCountry("CN");
        testInspectionRecordDomain.setRegion("Asia");

        testInspectionRecordDTO = new InspectionRecordDTO();
        testInspectionRecordDTO.setId(1L);
        testInspectionRecordDTO.setRuleCode("RULE_001");
        testInspectionRecordDTO.setBusinessCode("BUS_001");
        testInspectionRecordDTO.setCountry("CN");
    }

    @Test
    @DisplayName("保存巡检记录 - 成功")
    void saveInspectionRecord_Success() {
        // 准备数据
        when(inspectionRecordRepository.save(any(InspectionRecordDomain.class))).thenReturn(true);

        // 执行测试
        boolean result = inspectionRecordService.saveInspectionRecord(testInspectionRecordDomain);

        // 验证结果
        assertTrue(result);
        verify(inspectionRecordRepository).save(testInspectionRecordDomain);
    }

    @Test
    @DisplayName("保存巡检记录 - 失败")
    void saveInspectionRecord_Failure() {
        // 准备数据
        when(inspectionRecordRepository.save(any(InspectionRecordDomain.class))).thenReturn(false);

        // 执行测试
        boolean result = inspectionRecordService.saveInspectionRecord(testInspectionRecordDomain);

        // 验证结果
        assertFalse(result);
        verify(inspectionRecordRepository).save(testInspectionRecordDomain);
    }

    @Test
    @DisplayName("更新巡检记录 - 成功")
    void updateInspectionRecord_Success() {
        // 准备数据
        when(inspectionRecordRepository.update(any(InspectionRecordDomain.class))).thenReturn(true);

        // 执行测试
        boolean result = inspectionRecordService.updateInspectionRecord(testInspectionRecordDomain);

        // 验证结果
        assertTrue(result);
        verify(inspectionRecordRepository).update(testInspectionRecordDomain);
    }

    @Test
    @DisplayName("更新巡检记录 - 失败")
    void updateInspectionRecord_Failure() {
        // 准备数据
        when(inspectionRecordRepository.update(any(InspectionRecordDomain.class))).thenReturn(false);

        // 执行测试
        boolean result = inspectionRecordService.updateInspectionRecord(testInspectionRecordDomain);

        // 验证结果
        assertFalse(result);
        verify(inspectionRecordRepository).update(testInspectionRecordDomain);
    }

    @Test
    @DisplayName("根据ID获取巡检记录 - 成功")
    void getInspectionRecordById_Success() {
        // 准备数据
        Long id = 1L;
        when(inspectionRecordRepository.getById(id)).thenReturn(testInspectionRecordDomain);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordService.getInspectionRecordById(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(id, result.getId());
        verify(inspectionRecordRepository).getById(id);
    }

    @Test
    @DisplayName("根据ID获取巡检记录 - 返回null")
    void getInspectionRecordById_ReturnsNull() {
        // 准备数据
        Long id = 999L;
        when(inspectionRecordRepository.getById(id)).thenReturn(null);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordService.getInspectionRecordById(id);

        // 验证结果
        assertNull(result);
        verify(inspectionRecordRepository).getById(id);
    }

    @Test
    @DisplayName("通过阵地代码获取巡检记录列表 - 成功")
    void getInspectionRecordsByBusinessCode_Success() {
        // 准备数据
        String businessCode = "BUS_001";
        List<InspectionRecordDomain> expectedList = Arrays.asList(testInspectionRecordDomain);
        when(inspectionRecordRepository.getByBusinessCode(businessCode)).thenReturn(expectedList);

        // 执行测试
        List<InspectionRecordDomain> result = inspectionRecordService.getInspectionRecordsByBusinessCode(businessCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testInspectionRecordDomain, result.get(0));
        verify(inspectionRecordRepository).getByBusinessCode(businessCode);
    }

    @Test
    @DisplayName("通过阵地代码获取巡检记录列表 - 返回空列表")
    void getInspectionRecordsByBusinessCode_ReturnsEmptyList() {
        // 准备数据
        String businessCode = "BUS_001";
        when(inspectionRecordRepository.getByBusinessCode(businessCode)).thenReturn(Collections.emptyList());

        // 执行测试
        List<InspectionRecordDomain> result = inspectionRecordService.getInspectionRecordsByBusinessCode(businessCode);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(inspectionRecordRepository).getByBusinessCode(businessCode);
    }

    @Test
    @DisplayName("通过规则编码和阵地代码获取巡检记录 - 成功")
    void getInspectionRecordByRuleCodeAndBusinessCode_Success() {
        // 准备数据
        String ruleCode = "RULE_001";
        String businessCode = "BUS_001";
        when(inspectionRecordRepository.getByRuleCodeAndBusinessCode(ruleCode, businessCode))
                .thenReturn(testInspectionRecordDomain);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordService.getInspectionRecordByRuleCodeAndBusinessCode(ruleCode, businessCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(testInspectionRecordDomain, result);
        verify(inspectionRecordRepository).getByRuleCodeAndBusinessCode(ruleCode, businessCode);
    }

    @Test
    @DisplayName("通过规则编码和阵地代码获取巡检记录 - 返回null")
    void getInspectionRecordByRuleCodeAndBusinessCode_ReturnsNull() {
        // 准备数据
        String ruleCode = "RULE_001";
        String businessCode = "BUS_001";
        when(inspectionRecordRepository.getByRuleCodeAndBusinessCode(ruleCode, businessCode))
                .thenReturn(null);

        // 执行测试
        InspectionRecordDomain result = inspectionRecordService.getInspectionRecordByRuleCodeAndBusinessCode(ruleCode, businessCode);

        // 验证结果
        assertNull(result);
        verify(inspectionRecordRepository).getByRuleCodeAndBusinessCode(ruleCode, businessCode);
    }

    @Test
    @DisplayName("通过国家列表查询未下发的巡检记录 - 成功")
    void getPendingInspectionsByCountries_Success() {
        // 准备数据
        List<String> countries = Arrays.asList("CN", "US");
        List<InspectionRecordDTO> expectedList = Arrays.asList(testInspectionRecordDTO);
        when(inspectionRecordRepository.getPendingInspectionsByCountries(countries)).thenReturn(expectedList);

        // 执行测试
        List<InspectionRecordDTO> result = inspectionRecordService.getPendingInspectionsByCountries(countries);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testInspectionRecordDTO, result.get(0));
        verify(inspectionRecordRepository).getPendingInspectionsByCountries(countries);
    }

    @Test
    @DisplayName("通过国家列表查询未下发的巡检记录 - 返回空列表")
    void getPendingInspectionsByCountries_ReturnsEmptyList() {
        // 准备数据
        List<String> countries = Arrays.asList("CN", "US");
        when(inspectionRecordRepository.getPendingInspectionsByCountries(countries)).thenReturn(Collections.emptyList());

        // 执行测试
        List<InspectionRecordDTO> result = inspectionRecordService.getPendingInspectionsByCountries(countries);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(inspectionRecordRepository).getPendingInspectionsByCountries(countries);
    }

    @Test
    @DisplayName("通过国家列表查询未下发的巡检记录 - 国家列表为null")
    void getPendingInspectionsByCountries_NullCountries() {
        // 执行测试
        List<InspectionRecordDTO> result = inspectionRecordService.getPendingInspectionsByCountries(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(inspectionRecordRepository, never()).getPendingInspectionsByCountries(any());
    }

    @Test
    @DisplayName("通过国家列表查询未下发的巡检记录 - 国家列表为空")
    void getPendingInspectionsByCountries_EmptyCountries() {
        // 执行测试
        List<InspectionRecordDTO> result = inspectionRecordService.getPendingInspectionsByCountries(Collections.emptyList());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(inspectionRecordRepository, never()).getPendingInspectionsByCountries(any());
    }
} 