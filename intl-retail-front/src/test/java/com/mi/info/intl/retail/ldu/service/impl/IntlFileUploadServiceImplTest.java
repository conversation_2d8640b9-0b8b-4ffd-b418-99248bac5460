package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.core.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.upload.FileUploadInfo;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlFileUploadMapper;
import com.mi.info.intl.retail.model.CommonApiResponse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ExtendWith(MockitoExtension.class)
class IntlFileUploadServiceImplTest {
    @Mock
    private IntlFileUploadMapper intlFileUploadMapper;

    @InjectMocks
    private IntlFileUploadServiceImpl intlFileUploadService;

    private FileUploadInfo buildFileUploadInfo() {
        FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData("guid-123", Collections.singletonList("http://fds.com/file1.jpg"));
        FileUploadInfo info = new FileUploadInfo();
        info.setMetaDataList(Collections.singletonList(metaData));
        info.setModuleName(FileUploadEnum.LDU_INSPECTION.getCode());
        info.setUploaderName("tester");
        info.setOfflineUpload(false);
        info.setUploadedToBlob(false);
        info.setNoWatermark(false);
        info.setRelatedId(100L);
        return info;
    }

    @Test
    public void testSave_parameter_missing(){
        FileUploadInfo fileUploadInfo1 = buildFileUploadInfo();
        FileUploadInfo fileUploadInfo = new FileUploadInfo();
        RetailRunTimeException exp =
                assertThrows(RetailRunTimeException.class, () -> intlFileUploadService.save(fileUploadInfo));
        assertEquals("metaDataList can not be null",exp.getMessage());
        fileUploadInfo.setMetaDataList(fileUploadInfo1.getMetaDataList());
        exp = assertThrows(RetailRunTimeException.class, () -> intlFileUploadService.save(fileUploadInfo));
        assertEquals("uploaderName can not be null",exp.getMessage());
        fileUploadInfo.setUploaderName("tester");
        exp = assertThrows(RetailRunTimeException.class, () -> intlFileUploadService.save(fileUploadInfo));
        assertEquals("unsupported module name",exp.getMessage());
    }

    @Test
    void testSave() {
        doNothing().when(intlFileUploadMapper).batchInsertDatas(any());
        FileUploadInfo info = buildFileUploadInfo();
        CommonApiResponse<Object> response = intlFileUploadService.save(info);
        assertEquals(0, response.getCode());
        verify(intlFileUploadMapper, times(1)).batchInsertDatas(any());
    }

    @Test
    void testSaveSimple() {
        doNothing().when(intlFileUploadMapper).batchInsertDatas(any());
        FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData("guid-456", Collections.singletonList("http://fds.com/file2.jpg"));
        CommonApiResponse<Object> response = intlFileUploadService.saveSimple(Collections.singletonList(metaData), FileUploadEnum.LDU_INSPECTION, "tester");
        assertEquals(0, response.getCode());
        verify(intlFileUploadMapper, times(1)).batchInsertDatas(any());
    }

    @Test
    void testSaveSimple_multi_images() {
        doNothing().when(intlFileUploadMapper).batchInsertDatas(any());
        FileUploadInfo.MetaData metaData = new FileUploadInfo.MetaData("guid-456",
                Stream.generate(() -> "http://fds.com/file2" +
                        ".jpg").limit(300).collect(Collectors.toList()));
        CommonApiResponse<Object> response = intlFileUploadService.saveSimple(Collections.singletonList(metaData), FileUploadEnum.LDU_INSPECTION, "tester");
        assertEquals(0, response.getCode());
        verify(intlFileUploadMapper, times(2)).batchInsertDatas(any());
    }
} 