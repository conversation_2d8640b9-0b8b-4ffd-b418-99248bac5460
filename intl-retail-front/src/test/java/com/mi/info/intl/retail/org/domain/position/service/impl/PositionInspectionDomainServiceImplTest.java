package com.mi.info.intl.retail.org.domain.position.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionFurnitureRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionAllDetailDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionHistoryItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionItemListRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionSelectorItemList;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.ConstructionType;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.StoreLimitedRangeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.PositionCommonItemList;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.enums.OperationType;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.infra.rpc.StoreRelateRpc;
import com.xiaomi.cnzone.maindataapi.api.PositionProvider;
import com.xiaomi.cnzone.maindataapi.model.dto.store.PositionListResponse;
import com.xiaomi.cnzone.maindataapi.model.req.store.PositionFurniture;
import com.xiaomi.cnzone.storeapi.api.channelbuild.common.ChannelFurnitureProvider;
import com.xiaomi.cnzone.storeapi.api.channelbuild.position.BuildChannelPositionProvider;
import com.xiaomi.cnzone.storeapi.model.channelbuild.business.resp.PositionImageCenterResp;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.resp.ConfigFurnitureResp;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Arrays;
import java.util.HashMap;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
public class PositionInspectionDomainServiceImplTest {
    @InjectMocks
    private PositionInspectionDomainServiceImpl service;
    @Mock
    private InspectionRecordRepository inspectionRecordRepository;
    @Mock
    private PositionRepository positionRepository;
    @Mock
    private StoreRelateRpc storeRelateRpc;
    @Mock
    private BuildChannelPositionProvider buildChannelPositionProvider;
    @Mock
    private InspectionHistoryRepository inspectionHistoryRepository;
    @Mock
    private PositionProvider positionProvider;
    @Mock
    private ChannelFurnitureProvider channelFurnitureProvider;
    @Mock
    private IntlFileUploadService fileUploadService;

    private MockedStatic<RequestContextInfo> mockedStaticRequestContextInfo;

    @BeforeEach
    public void setUp() {
        mockedStaticRequestContextInfo = Mockito.mockStatic(RequestContextInfo.class);
    }

    @AfterEach
    public void tearDown() {
        mockedStaticRequestContextInfo.close();
    }

    @Test
    public void testGetPositionInspectionAllDetail_success_with_completed_task() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(305L);

        // 构造巡检记录 - 已完成任务
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(305L);
        record.setPositionCode("CID001971");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
        record.setActionCode("ACTION001");
        record.setUploadData("{\"storeGate\": {\"guid\": \"guid1\", \"name\": \"\", \"images\": [\"url1\", \"url2\"]}, \"positionDisplay\": {\"guid\": \"guid2\", \"name\": \"\", \"images\": [\"url3\", \"url4\"]}}");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("CID001971");
        positionDomain.setPositionName("Test Position");
        positionDomain.setCountryName("India");
        positionDomain.setPositionTypeName("Retail");
        positionDomain.setPositionCategory(Arrays.asList(1, 2));
        positionDomain.setPositionLocation(1);
        positionDomain.setDisplayCapacityExpansionStatus(1);

        // Mock 方法调用
        when(inspectionRecordRepository.getById(305L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001971")).thenReturn(positionDomain);
        when(buildChannelPositionProvider.imageCenter(any())).thenReturn(Result.success(Collections.emptyList()));
        when(storeRelateRpc.get3CCommonPositionItemList(any(), any(), any())).thenReturn(new PositionCommonItemList());

        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse posResponse = new PositionListResponse();
        posResponse.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(posResponse);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        ConfigFurnitureResp resp = new ConfigFurnitureResp();
        resp.setFurnitureId("F001");
        resp.setFurnitureName("FName001");
        resp.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any())).thenReturn(
                Result.success(Collections.singletonList(resp)));
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position", response.getData().getPositionBasicInfo().getPositionName());
        assertEquals("India", response.getData().getPositionBasicInfo().getCountry());
        assertNotNull(response.getData().getPhotoGalleries());
    }

    @Test
    public void testGetPositionInspectionAllDetail_success_with_incomplete_task() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(306L);

        // 构造巡检记录 - 未完成任务
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(306L);
        record.setPositionCode("CID001972");
        record.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
        record.setInspectionStatus(InspectionStatusEnum.NOT_COMPLETED);

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("CID001972");
        positionDomain.setPositionName("Test Position 2");

        // Mock 方法调用
        when(inspectionRecordRepository.getById(306L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001972")).thenReturn(positionDomain);
        when(storeRelateRpc.get3CCommonPositionItemList(any(), any(), any())).thenReturn(new PositionCommonItemList());

        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse posResponse = new PositionListResponse();
        posResponse.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(posResponse);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        ConfigFurnitureResp resp = new ConfigFurnitureResp();
        resp.setFurnitureId("F001");
        resp.setFurnitureName("FName001");
        resp.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any())).thenReturn(
                Result.success(Collections.singletonList(resp)));
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position 2", response.getData().getPositionBasicInfo().getPositionName());
        // 未完成任务不应该有图片数据
        assertNotNull(response.getData().getPhotoGalleries());
    }

    @Test
    public void testGetPositionInspectionAllDetail_invalid_request() {
        // 测试空ID
        PositionInspectionDetailRequest request1 = new PositionInspectionDetailRequest();
        request1.setPositionInspectionId(null);
        CommonApiResponse<PositionInspectionAllDetailDTO> response1 = service.getPositionInspectionAllDetail(request1);
        assertEquals(500, response1.getCode());
        assertEquals("阵地巡检ID不能为空", response1.getMessage());

        // 测试无效ID
        PositionInspectionDetailRequest request2 = new PositionInspectionDetailRequest();
        request2.setPositionInspectionId(0L);
        CommonApiResponse<PositionInspectionAllDetailDTO> response2 = service.getPositionInspectionAllDetail(request2);
        assertEquals(500, response2.getCode());
        assertEquals("阵地巡检ID不能为空", response2.getMessage());
    }

    @Test
    public void testGetPositionInspectionAllDetail_record_not_found() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(999L);

        // Mock 巡检记录不存在
        when(inspectionRecordRepository.getById(999L)).thenReturn(null);

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(500, response.getCode());
        assertEquals("阵地巡检记录不存在", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionAllDetail_position_not_found() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(307L);

        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(307L);
        record.setPositionCode("CID001973");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);

        // Mock 巡检记录存在但阵地信息不存在
        when(inspectionRecordRepository.getById(307L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001973")).thenReturn(null);

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(500, response.getCode());
        assertEquals("阵地信息不存在", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionAllDetail_with_exception() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(308L);

        // Mock 抛出异常
        when(inspectionRecordRepository.getById(308L)).thenThrow(new RuntimeException("Database error"));

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("系统异常"));
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionAllDetail_with_invalid_upload_data() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(309L);

        // 构造巡检记录 - 包含无效的JSON数据
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(309L);
        record.setPositionCode("CID001974");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setUploadData("invalid json data");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("CID001974");
        positionDomain.setPositionName("Test Position 3");

        // Mock 方法调用
        when(inspectionRecordRepository.getById(309L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001974")).thenReturn(positionDomain);
        when(buildChannelPositionProvider.imageCenter(any())).thenReturn(Result.success(Collections.emptyList()));
        when(storeRelateRpc.get3CCommonPositionItemList(any(), any(), any())).thenReturn(new PositionCommonItemList());

        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse posResponse = new PositionListResponse();
        posResponse.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(posResponse);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        ConfigFurnitureResp resp = new ConfigFurnitureResp();
        resp.setFurnitureId("F001");
        resp.setFurnitureName("FName001");
        resp.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any())).thenReturn(
                Result.success(Collections.singletonList(resp)));
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");

        // 执行测试 - 应该能正常处理，不会因为JSON解析失败而抛出异常
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position 3", response.getData().getPositionBasicInfo().getPositionName());
    }

    @Test
    public void testGetPositionInspectionAllDetail_with_build_channel_images() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(310L);

        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(310L);
        record.setPositionCode("CID001975");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setActionCode("ACTION002");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("CID001975");
        positionDomain.setPositionName("Test Position 4");

        // 构造建店图片数据
        List<PositionImageCenterResp> imageCenterRespList = new ArrayList<>();
        PositionImageCenterResp resp = new PositionImageCenterResp();
        resp.setStoreGate(Arrays.asList("url1", "url2"));
        resp.setPositionDisplay(Arrays.asList("url3", "url4"));
        imageCenterRespList.add(resp);

        // Mock 方法调用
        when(inspectionRecordRepository.getById(310L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001975")).thenReturn(positionDomain);
        when(buildChannelPositionProvider.imageCenter(any())).thenReturn(Result.success(imageCenterRespList));
        when(storeRelateRpc.get3CCommonPositionItemList(any(), any(), any())).thenReturn(new PositionCommonItemList());

        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse posResponse = new PositionListResponse();
        posResponse.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(posResponse);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        ConfigFurnitureResp resps = new ConfigFurnitureResp();
        resps.setFurnitureId("F001");
        resps.setFurnitureName("FName001");
        resps.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any())).thenReturn(
                Result.success(Collections.singletonList(resps)));
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position 4", response.getData().getPositionBasicInfo().getPositionName());
        assertNotNull(response.getData().getPhotoGalleries());
        assertNotNull(response.getData().getPhotoGalleries().getPositionCreation());
    }

    @Test
    public void testGetSelectorList_success() {
        // 构造请求
        PositionItemListRequest request = new PositionItemListRequest();
        request.setInternationalAreaId("area1");
        request.setBusinessScene("scene1");
        // 构造mock返回
        PositionCommonItemList commonItemList = new PositionCommonItemList();
        commonItemList.setPositionCategory(Collections.singletonList(new OptionalItem<>("cat1", "品类1")));
        commonItemList.setPositionType(Collections.singletonList(new OptionalItem<>("type1", "类型1")));
        commonItemList.setPositionLocation(Collections.singletonList(new OptionalItem<>("loc1", "位置1")));
        commonItemList.setDisplayStandardization(Collections.singletonList(new OptionalItem<>("std1", "标准1")));
        when(storeRelateRpc.get3CCommonPositionItemList(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString()))
                .thenReturn(commonItemList);
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");
        PositionSelectorItemList result = service.getSelectorList(request);
        assertNotNull(result);
        assertEquals(1, result.getPositionCategory().size());
        assertEquals("cat1", result.getPositionCategory().get(0).getKey());
        assertEquals("品类1", result.getPositionCategory().get(0).getValue());
        assertEquals(1, result.getPositionType().size());
        assertEquals("type1", result.getPositionType().get(0).getKey());
        assertEquals("类型1", result.getPositionType().get(0).getValue());
        assertEquals(1, result.getPositionLocation().size());
        assertEquals("loc1", result.getPositionLocation().get(0).getKey());
        assertEquals("位置1", result.getPositionLocation().get(0).getValue());
        assertEquals(1, result.getDisplayStandardization().size());
        assertEquals("std1", result.getDisplayStandardization().get(0).getKey());
        assertEquals("标准1", result.getDisplayStandardization().get(0).getValue());
        // 校验枚举下拉项不为空
        assertEquals(TaskStatusEnum.values().length - 1, result.getTaskStatus().size());
        assertEquals(InspectionStatusEnum.values().length, result.getInspectionStatus().size());
        assertEquals(ConstructionType.values().length, result.getPositionConstructionType().size());
        assertEquals(DisapproveReasonEnum.values().length, result.getDisapproveReason().size());
        assertEquals(StoreLimitedRangeEnum.values().length, result.getStoreLimitedRange().size());
    }

    @Test
    public void testGetPositionFurnitureList_old_furniture_success() {
        // 构造请求
        PositionFurnitureRequest request = new PositionFurnitureRequest();
        request.setPositionCode("P001");
        // 构造mock返回
        PositionListResponse.FurnitureDisplay furnitureDisplay = new PositionListResponse.FurnitureDisplay();
        furnitureDisplay.setSlotEndCapTable1(1); // 有库存
        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        extension.setFurnitureDisplay(furnitureDisplay);
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse response = new PositionListResponse();
        response.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(response);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);
        // 调用
        List<OptionalItem<Integer>> result = service.getPositionFurnitureList(request);
        // 断言
        assertNotNull(result);
        assertEquals("slotEndCapTable1", result.get(0).getValue());
    }

    @Test
    public void testGetPositionFurnitureList_new_furniture_success() {
        // 构造请求
        PositionFurnitureRequest request = new PositionFurnitureRequest();
        request.setPositionCode("P001");
        // 构造mock返回
        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse response = new PositionListResponse();
        response.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(response);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        // 必须mock channelFurnitureProvider.queryFurnitureList
        ConfigFurnitureResp resp = new ConfigFurnitureResp();
        resp.setFurnitureId("F001");
        resp.setFurnitureName("FName001");
        resp.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any()))
                .thenReturn(Result.success(Collections.singletonList(resp)));

        // 调用
        List<OptionalItem<Integer>> result = service.getPositionFurnitureList(request);
        // 断言
        assertEquals(3, result.size());
        assertEquals("uniqueFName", result.get(0).getValue());
    }

    @Test
    public void testOperationHistory_success() {
        // 构造巡检记录
        Long inspectionId = 123L;
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(inspectionId);
        record.setPositionCode("P001");
        // 构造历史记录
        InspectionHistoryDomain history = new InspectionHistoryDomain();
        history.setId(1L);
        history.setInspectionRecordId(inspectionId);
        history.setOperationType(OperationType.SUBMIT);
        history.setRemark("remark");
        history.setDisapproveReason(2);
        history.setOperator("tester");
        history.setOperationTime(123456789L);
        history.setCreateTime(123456789L);
        List<InspectionHistoryDomain> historyList = Collections.singletonList(history);
        // mock依赖
        when(inspectionRecordRepository.getById(inspectionId)).thenReturn(record);
        when(inspectionHistoryRepository.getByInspectionRecordId(inspectionId)).thenReturn(historyList);
        // 调用
        List<PositionInspectionHistoryItem> result = service.operationHistory(inspectionId);
        // 断言
        assertNotNull(result);
        assertEquals(1, result.size());
        PositionInspectionHistoryItem item = result.get(0);
        assertEquals("tester", item.getOperator());
    }

    @Test
    public void testOperationHistory_not_found() {
        List<PositionInspectionHistoryItem> historyItems = service.operationHistory(1L);
        assertEquals(Collections.emptyList(), historyItems);
    }

    @Test
    public void testGetPositionInspectionDetail_success_with_completed_task() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(1001L);

        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(1001L);
        record.setPositionCode("P001");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
        record.setUploadData("{\"storeGate\": {\"guid\": \"guid1\", \"name\": \"\", \"images\": [\"url1\"]}}");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("P001");
        positionDomain.setPositionName("Test Position");

        // Mock
        when(inspectionRecordRepository.getById(1001L)).thenReturn(record);
        when(positionRepository.getByPositionCode("P001")).thenReturn(positionDomain);

        // 执行
        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);

        // 验证
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position", response.getData().getStoreInfo().getFrontName());
    }

    @Test
    public void testGetPositionInspectionDetail_invalid_request() {
        // 空ID
        PositionInspectionDetailRequest request1 = new PositionInspectionDetailRequest();
        request1.setPositionInspectionId(null);
        CommonApiResponse<PositionInspectionDetailResponse> response1 = service.getPositionInspectionDetail(request1);
        assertEquals(500, response1.getCode());
        assertEquals("阵地巡检ID不能为空", response1.getMessage());

        // 非法ID
        PositionInspectionDetailRequest request2 = new PositionInspectionDetailRequest();
        request2.setPositionInspectionId(0L);
        CommonApiResponse<PositionInspectionDetailResponse> response2 = service.getPositionInspectionDetail(request2);
        assertEquals(500, response2.getCode());
        assertEquals("阵地巡检ID不能为空", response2.getMessage());
    }

    @Test
    public void testGetPositionInspectionDetail_record_not_found() {
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(2001L);
        when(inspectionRecordRepository.getById(2001L)).thenReturn(null);

        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);
        assertEquals(500, response.getCode());
        assertEquals("阵地巡检记录不存在", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionDetail_position_not_found() {
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(2002L);

        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(2002L);
        record.setPositionCode("P002");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);

        when(inspectionRecordRepository.getById(2002L)).thenReturn(record);
        when(positionRepository.getByPositionCode("P002")).thenReturn(null);

        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);
        assertEquals(500, response.getCode());
        assertEquals("阵地信息不存在", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionDetail_with_invalid_upload_data() {
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(2003L);

        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(2003L);
        record.setPositionCode("P003");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setUploadData("invalid json");

        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("P003");
        positionDomain.setPositionName("Test Position 3");

        when(inspectionRecordRepository.getById(2003L)).thenReturn(record);
        when(positionRepository.getByPositionCode("P003")).thenReturn(positionDomain);

        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position 3", response.getData().getStoreInfo().getFrontName());
    }

    @Test
    public void testGetPositionInspectionDetail_with_exception() {
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(2004L);

        when(inspectionRecordRepository.getById(2004L)).thenThrow(new RuntimeException("DB error"));

        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("系统异常"));
        assertNull(response.getData());
    }
} 