package com.mi.info.intl.retail.org.app.event.listener;

import cn.mioffice.guard.mail.MiMailSender;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.org.app.event.PositionImageBatchUploadEvent;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.position.service.PositionInspectionDomainService;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.infra.entity.ImageLocal;
import com.mi.info.intl.retail.org.infra.entity.PositionImageInfo;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.anyList;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ExtendWith(MockitoExtension.class)
class PositionImageBatchUploadEventListenerTest {

    @Mock
    private InspectionRecordRepository inspectionRecordRepository;
    @Mock
    private FdsService fdsService;
    @Mock
    private RuleConfigRepository ruleConfigRepository;
    @Mock
    private IntlRmsUserService userService;
    @Mock
    private PositionInspectionDomainService positionInspectionDomainService;
    @Mock
    private PositionRepository positionRepository;
    @Mock
    private IntlCountryTimezoneService countryTimezoneService;
    @Mock
    private MiMailSender miMailSender;

    @InjectMocks
    private PositionImageBatchUploadEventListener listener;

    @Test
    void testHandlePositionImageBatchUploadEvent() {
        try (MockedStatic<RequestContextInfo> mockedStatic = mockStatic(RequestContextInfo.class)) {
            mockedStatic.when(RequestContextInfo::getAreaId).thenReturn("ID");
            PositionDomain positionDomain = new PositionDomain();
            positionDomain.setCountry("MID111");
            positionDomain.setCrpsCode("P001");
            when(positionRepository.getByPositionCodeList(any())).thenReturn(Collections.singletonList(positionDomain));
            Map<String, String> map = new HashMap<>();
            map.put("MID111", "ID");
            when(countryTimezoneService.getCountryCodeByCountryIds(any())).thenReturn(map);
            when(positionInspectionDomainService.getPositionFurnitureList(any())).thenReturn(
                    Collections.singletonList(new OptionalItem<>(1, "furniture")));
            PositionImageInfo info = new PositionImageInfo("P001");
            info.getStoreGate().add(new ImageLocal("storeGate", new File("test.jpg"), null));
            info.getPositionLandingPhoto().add(new ImageLocal("landing", new File("test2.jpg"), null));
            info.getPositionDisplay().add(new ImageLocal("display", new File("test3.jpg"), null));
            info.getFurniturePicture().add(new ImageLocal("furniture", new File("test4.jpg"), null));
            info.getFurniturePicture().add(new ImageLocal("furniture", new File("test5.jpg"), null));
            InspectionRecordDomain record = new InspectionRecordDomain();
            record.setRuleCode("rule001");
            record.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
            record.setPositionCode("P001");
            when(inspectionRecordRepository.getByBusinessCodeList(anyList())).thenReturn(
                    Collections.singletonList(record));
            when(fdsService.upload(anyString(), any(File.class), eq(true))).thenReturn(
                    new FdsUploadResult("url", "url"));
            RuleConfigDomain ruleConfigDomain = new RuleConfigDomain();
            ruleConfigDomain.setRuleCode("rule001");
            when(ruleConfigRepository.getByRuleCodeList(anyList())).thenReturn(
                    Collections.singletonList(ruleConfigDomain));
            when(userService.getIntlRmsUserByMiId(any())).thenReturn(Optional.empty());
            doNothing().when(positionInspectionDomainService)
                    .updateInspectionRecordAndFinishTask(any(PositionImageInfo.class));
            PositionImageBatchUploadEvent event =
                    new PositionImageBatchUploadEvent(100L, "<EMAIL>", Collections.singletonList(info),
                            new File("/tmp"));
            listener.handlePositionImageBatchUploadEvent(event);
            // 验证主流程被调用
            verify(positionInspectionDomainService, atLeastOnce()).updateInspectionRecordAndFinishTask(
                    any(PositionImageInfo.class));
        }
    }

    @Test
    void testUpdateAndFinishTask() {
        PositionImageInfo info = new PositionImageInfo("P002");
        doThrow(new RuntimeException("fail")).when(positionInspectionDomainService)
                .updateInspectionRecordAndFinishTask(any(PositionImageInfo.class));
        listener.updateAndFinishTask(info);
        assertFalse(info.isSuccess());
        assertNotNull(info.getRemark());
    }

    @Test
    void testGenerateSimpleTaskTableHtml() {
        PositionImageInfo info1 = new PositionImageInfo("P003");
        info1.markFailed("失败原因");
        PositionImageInfo info2 = new PositionImageInfo("P004");
        List<PositionImageInfo> list = Arrays.asList(info1, info2);
        String html = listener.generateSimpleTaskTableHtml(list);
        assertTrue(html.contains("P003"));
        assertTrue(html.contains("P004"));
    }

} 