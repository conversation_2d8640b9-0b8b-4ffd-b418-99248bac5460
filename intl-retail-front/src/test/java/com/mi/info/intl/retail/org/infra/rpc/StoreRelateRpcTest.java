package com.mi.info.intl.retail.org.infra.rpc;

import com.mi.info.intl.retail.org.domain.PositionCommonItemList;
import com.mi.info.intl.retail.org.domain.util.BeanConverter;
import com.mi.info.intl.retail.org.domain.util.RpcUtil;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import com.xiaomi.cnzone.storems.api.model.req.store.CommonConfigReq;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StoreRelateRpc单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("StoreRelateRpc测试")
class StoreRelateRpcTest {

    @InjectMocks
    private StoreRelateRpc storeRelateRpc;

    @Mock
    private StoreRelateProvider storeRelateProvider;

    private CommonConfigDTO2 mockCommonConfigDTO2;
    private Result<CommonConfigDTO2> mockResult;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockCommonConfigDTO2 = new CommonConfigDTO2();
        mockCommonConfigDTO2.setPositionCategory(new ArrayList<>());
        mockCommonConfigDTO2.setPositionType(new ArrayList<>());
        mockCommonConfigDTO2.setPositionLocation(new ArrayList<>());
        mockCommonConfigDTO2.setDisplayCapacityExpansionStatus(new ArrayList<>());

        // 创建成功的Result对象
        mockResult = Result.success(mockCommonConfigDTO2);
    }

    @Test
    @DisplayName("正常场景：所有参数都有值")
    void get3CCommonPositionItemList_AllParametersProvided_ReturnsPositionCommonItemList() {
        // 准备数据
        String areaId = "SG";
        String businessScene = "retail";
        String language = "zh-CN";

        // Mock RPC调用
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class)))
                .thenReturn(mockResult);

        // 执行测试
        PositionCommonItemList result = storeRelateRpc.get3CCommonPositionItemList(areaId, businessScene, language);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getPositionCategory());
        assertNotNull(result.getPositionType());
        assertNotNull(result.getPositionLocation());
        assertNotNull(result.getDisplayStandardization());

        // 验证RPC调用
        verify(storeRelateProvider).get3CCommonConfig(argThat(req -> 
            req.getLanguage().equals(language) &&
            req.getInternationalAreaId().equals(areaId) &&
            req.getBusinessScene().equals(businessScene)
        ));
    }

    @Test
    @DisplayName("正常场景：areaId为空")
    void get3CCommonPositionItemList_AreaIdEmpty_ReturnsPositionCommonItemList() {
        // 准备数据
        String areaId = "";
        String businessScene = "retail";
        String language = "en-US";

        // Mock RPC调用
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class)))
                .thenReturn(mockResult);

        // 执行测试
        PositionCommonItemList result = storeRelateRpc.get3CCommonPositionItemList(areaId, businessScene, language);

        // 验证结果
        assertNotNull(result);

        // 验证RPC调用 - areaId为空时不应设置
        verify(storeRelateProvider).get3CCommonConfig(argThat(req -> 
            req.getLanguage().equals(language) &&
            req.getInternationalAreaId() == null &&
            req.getBusinessScene().equals(businessScene)
        ));
    }

    // @Test
    // @DisplayName("正常场景：businessScene为空")
    // void get3CCommonPositionItemList_BusinessSceneEmpty_ReturnsPositionCommonItemList() {
    //     // 准备数据
    //     String areaId = "CN";
    //     String businessScene = "";
    //     String language = "zh-CN";

    //     // Mock RPC调用
    //     when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class)))
    //             .thenReturn(mockResult);

    //     // 执行测试
    //     PositionCommonItemList result = storeRelateRpc.get3CCommonPositionItemList(areaId, businessScene, language);

    //     // 验证结果
    //     assertNotNull(result);

    //     // 验证RPC调用 - businessScene为空时不应设置
    //     verify(storeRelateProvider).get3CCommonConfig(argThat(req -> 
    //         req.getLanguage().equals(language) &&
    //         req.getInternationalAreaId().equals(areaId) &&
    //         req.getBusinessScene() == null
    //     ));
    // }

    // @Test
    // @DisplayName("正常场景：所有参数都为空")
    // void get3CCommonPositionItemList_AllParametersEmpty_ReturnsPositionCommonItemList() {
    //     // 准备数据
    //     String areaId = null;
    //     String businessScene = null;
    //     String language = "en-US";

    //     // Mock RPC调用
    //     when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class)))
    //             .thenReturn(mockResult);

    //     // 执行测试
    //     PositionCommonItemList result = storeRelateRpc.get3CCommonPositionItemList(areaId, businessScene, language);

    //     // 验证结果
    //     assertNotNull(result);

    //     // 验证RPC调用 - 只有language被设置
    //     verify(storeRelateProvider).get3CCommonConfig(argThat(req -> 
    //         req.getLanguage().equals(language) &&
    //         req.getInternationalAreaId() == null &&
    //         req.getBusinessScene() == null
    //     ));
    // }

    @Test
    @DisplayName("异常场景：RPC调用失败")
    void get3CCommonPositionItemList_RpcCallFails_ThrowsException() {
        // 准备数据
        String areaId = "SG";
        String businessScene = "retail";
        String language = "zh-CN";

        // Mock RPC调用失败 - 返回非0状态码的Result
        Result<CommonConfigDTO2> failedResult = mock(Result.class);
        when(failedResult.getCode()).thenReturn(1);
        when(failedResult.getMessage()).thenReturn("RPC call failed");

        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class)))
                .thenReturn(failedResult);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> storeRelateRpc.get3CCommonPositionItemList(areaId, businessScene, language));

        // 验证异常信息
        assertEquals("RPC call failed", exception.getMessage());

        // 验证RPC调用
        verify(storeRelateProvider).get3CCommonConfig(any(CommonConfigReq.class));
    }

    @Test
    @DisplayName("异常场景：RPC返回null")
    void get3CCommonPositionItemList_RpcReturnsNull_ThrowsException() {
        // 准备数据
        String areaId = "SG";
        String businessScene = "retail";
        String language = "zh-CN";

        // Mock RPC调用返回null
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class)))
                .thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> storeRelateRpc.get3CCommonPositionItemList(areaId, businessScene, language));

        // 验证异常信息
        assertEquals("result is null", exception.getMessage());

        // 验证RPC调用
        verify(storeRelateProvider).get3CCommonConfig(any(CommonConfigReq.class));
    }

    @Test
    @DisplayName("正常场景：验证BeanConverter转换逻辑")
    void get3CCommonPositionItemList_ValidatesBeanConverterMapping() {
        // 准备数据
        String areaId = "SG";
        String businessScene = "retail";
        String language = "zh-CN";

        // Mock RPC调用
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class)))
                .thenReturn(mockResult);

        // 执行测试
        PositionCommonItemList result = storeRelateRpc.get3CCommonPositionItemList(areaId, businessScene, language);

        // 验证结果
        assertNotNull(result);
        // 验证BeanConverter.INSTANCE.map被正确调用
        // 由于BeanConverter是静态方法，这里主要验证返回结果的结构
        assertNotNull(result.getPositionCategory());
        assertNotNull(result.getPositionType());
        assertNotNull(result.getPositionLocation());
        assertNotNull(result.getDisplayStandardization());
    }

    @Test
    @DisplayName("边界场景：language为null")
    void get3CCommonPositionItemList_LanguageNull_HandlesGracefully() {
        // 准备数据
        String areaId = "SG";
        String businessScene = "retail";
        String language = null;

        // Mock RPC调用
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class)))
                .thenReturn(mockResult);

        // 执行测试
        PositionCommonItemList result = storeRelateRpc.get3CCommonPositionItemList(areaId, businessScene, language);

        // 验证结果
        assertNotNull(result);

        // 验证RPC调用 - language为null时仍能正常处理
        verify(storeRelateProvider).get3CCommonConfig(argThat(req -> 
            req.getLanguage() == null &&
            req.getInternationalAreaId().equals(areaId) &&
            req.getBusinessScene().equals(businessScene)
        ));
    }
} 