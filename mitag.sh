#!/bin/sh

# Usage: mitag.sh [ENV [TAG]]
#   ENV: environment name. accept values: 'test', 'sg-pre', 'sg-prod'.
#        default value:
#          * 'sg-prod' if given git commit is in master branch and associates with sg-pre tag;
#          * 'sg-pre'  if given git commit is in master branch and associates with test tag;
#          * 'test' if given git commit has no test tag.
#   TAG: existing git tag name. for example 'test-20220310-00'.
#        default value: current branch HEAD.

### START_MIT ###
git archive --remote=********************:mit/infra/ci-templates.git master mitag.sh | tar -xOf - | sh -s "$@"


# 打包命令示例
#git pull --tags
#git tag test-20241107
#git push origin test-20241107