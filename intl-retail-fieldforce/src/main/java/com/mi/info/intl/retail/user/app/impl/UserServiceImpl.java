package com.mi.info.intl.retail.user.app.impl;

import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.user.app.UserService;
import com.mi.info.intl.retail.user.domain.UserInfoManager;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper;
import lombok.extern.slf4j.Slf4j;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = UserService.class)
public class UserServiceImpl implements UserService, UserApiService {
    @Autowired
    private IntlRmsUserMapper intlRmsUserMapper;
    @Autowired
    private UserInfoManager userInfoManager;

    @Override
    public IntlRmsUser getUserInfo(String email) {
        if (StringUtils.isEmpty(email)) {
            return new IntlRmsUser();
        }
        return intlRmsUserMapper.selectByEmail(email);
    }

    @Override
    public IntlRmsUser getUserInfoDomainName(String domainName) {
        return intlRmsUserMapper.selectByDomainName(domainName);
    }

    @Override
    public List<BusinessDataResponse> getUserPositions(BusinessDataInputRequest userInfoRequest) {

        return userInfoManager.getUserPositions(userInfoRequest);
    }

    @Override
    public Optional<IntlRmsUserNewDto> getUserByMiId(Long miId) {
        if (ObjectUtils.isEmpty(miId)) {
            log.error("getUserByMiId param:mid is null");
            return Optional.empty();
        }
        //todo 优化，从redis查先
        LambdaQueryWrapper<IntlRmsUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(IntlRmsUser::getMiId, miId);
        IntlRmsUser intlRmsUser = intlRmsUserMapper.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(intlRmsUser)) {
            log.error("getUserByMiId can not find user by miid:{}", miId);
            return Optional.empty();
        }
        IntlRmsUserNewDto intlRmsUserNewDto = new IntlRmsUserNewDto();
        ComponentLocator.getConverter().convert(intlRmsUser, intlRmsUserNewDto);
        return Optional.of(intlRmsUserNewDto);
    }

    /**
     * @param miIds
     * @return
     */
    @Override
    public Optional<List<IntlRmsUserNewDto>> getUserListByMiIds(List<Long> miIds) {
        if (CollectionUtils.isEmpty(miIds)) {
            log.error("getUserListByMiIds param:miIds is empty");
            throw new BusinessException("miId is empty");
        }
        if (miIds.size() > 500) {
            throw new BusinessException("miIds size is more than 500");
        }

        LambdaQueryWrapper<IntlRmsUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(IntlRmsUser::getMiId, miIds);
        List<IntlRmsUser> intlRmsUsers = intlRmsUserMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(intlRmsUsers)) {
            return Optional.empty();
        }
        List<IntlRmsUserNewDto> intlRmsUserNewDtos = JacksonUtil.parseArray(JacksonUtil.toStr(intlRmsUsers), IntlRmsUserNewDto.class);
        return Optional.of(intlRmsUserNewDtos);
    }

    @Override
    public Optional<UserInfoDTO> queryUserByMiId(Long miId) {
        log.info("查询用户信息, miId: {}", miId);

        try {
            IntlRmsUser user = intlRmsUserMapper.selectByMiId(miId);

            if (user != null) {
                log.info("查询用户信息成功, miId: {}, domainName: {}", miId, user.getDomainName());
                return Optional.of(convertToUserInfo(user));
            } else {
                log.warn("未找到用户信息, miId: {}", miId);
                return Optional.empty();
            }

        } catch (Exception e) {
            log.error("查询用户信息失败, miId: {}", miId, e);
            return Optional.empty();
        }
    }

    /**
     * 转换IntlRmsUser为UserInfoDTO
     */
    private UserInfoDTO convertToUserInfo(IntlRmsUser user) {
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setMiId(user.getMiId());
        userInfo.setDomainName(user.getDomainName());
        userInfo.setJobId(user.getJobId());
        userInfo.setEnglishName(user.getEnglishName());
        return userInfo;
    }
}
