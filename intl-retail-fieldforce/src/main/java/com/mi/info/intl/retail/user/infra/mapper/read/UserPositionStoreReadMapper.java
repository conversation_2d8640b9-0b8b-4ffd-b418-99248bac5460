package com.mi.info.intl.retail.user.infra.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.user.infra.entity.UserPositionStore;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserPositionStoreReadMapper extends BaseMapper<UserPositionStore> {

    List<UserPositionStore> getUserPositionStore(@Param("country") String country,
            @Param("titles") List<Integer> titles, @Param("storeGrades") List<String> storeGrades, @Param("midList") List<Long> midList);

}
