package com.mi.info.intl.retail.intlretail.app.exception;

import com.xiaomi.mit.api.error.ErrorCode;
import com.xiaomi.mit.api.error.ErrorCodes;

/**
 * <AUTHOR>
 * 模拟多个模块配置自定义错误码(通常是不需要多个)
 */
public class AnotherCustomErrorCodes extends ErrorCodes {

    private static final String RESOURCE_ERROR_MESSAGE_NAME = "another_custom_error_message";

    static {
        addErrorMessageResource(RESOURCE_ERROR_MESSAGE_NAME);
    }

    /**
     * 自定义错误1
     */
    public static final ErrorCode ANOTHER_CUSTOM_ERROR1 = register(40010120, "ANOTHER_CUSTOM_ERROR1");

}

