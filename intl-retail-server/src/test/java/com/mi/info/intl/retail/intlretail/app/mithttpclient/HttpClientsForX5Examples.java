package com.mi.info.intl.retail.intlretail.app.mithttpclient;

import com.xiaomi.core.auth.x5.X5AppInfo;
import com.xiaomi.core.auth.x5.X5Response;
import com.xiaomi.core.http.MitHttpClients;
import com.xiaomi.mit.api.error.Exceptions;
import com.xiaomi.mit.common.http.Body;
import com.xiaomi.mit.common.http.MitHttpException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class HttpClientsForX5Examples {

    final String x5AppId = "xm_test";
    final String x5AppSecret = "18f34bde4bb73c10f8ba1b02e3cf3efd";
    final String method = "findUserList";
    final String url = "http://localhost:8080/x5/user/findUserList";
    final X5UserQueryDTO userBody = new X5UserQueryDTO(1);

    final X5AppInfo appInfo = new X5AppInfo(x5AppId, x5AppSecret, method);

    /**
     * X5不支持get
     * 错误的method示例1
     */
    @Test
    void wrongHttpMethod() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> MitHttpClients.x5(appInfo).url(url).get().as(X5UserInfo.class));
    }

    /**
     * X5AppInfo中缺少method参数
     * 错误的示例2
     */
    @Test
    void withoutX5AppMethod() {
        Assertions.assertThrows(MitHttpException.class, () -> {
            // X5AppInfo缺少method参数
            final X5AppInfo wrongAppInfo = new X5AppInfo(x5AppId, x5AppSecret);
            MitHttpClients.x5(wrongAppInfo).url(url).post(Body.json(userBody)).as(X5UserInfo.class);
        });
    }

    @Test
    void findUserListFromX5Server() {
        final X5Response<List<X5UserInfo>> listX5Response = MitHttpClients.x5(appInfo).url(url).post(Body.json(userBody)).asList(X5UserInfo.class);
        final List<X5UserInfo> users = getFromX5Response(listX5Response);
        Assertions.assertFalse(users.isEmpty());
    }

    <T> T getFromX5Response(X5Response<T> response) {
        if (response.getHeader().getIntCode() == 200) {
            return response.getBody();
        }
        throw Exceptions.internalServerError("request failed with bad code: " + response.getHeader().getIntCode() + response);
    }

    static class X5UserQueryDTO {
        private int id;

        public X5UserQueryDTO(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

    static class X5UserInfo {
        private Integer id;

        private String name;

        public X5UserInfo() {
        }

        public X5UserInfo(Integer id, String name) {
            this.id = id;
            this.name = name;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

    }

}

