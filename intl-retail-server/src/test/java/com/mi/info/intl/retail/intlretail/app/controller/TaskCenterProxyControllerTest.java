package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.proxy.TaskCenterProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.PopWindowDTO;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.ConfirmPopWindowsReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.HeadCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCommonReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterDetailReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterTaskNumReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
class TaskCenterProxyControllerTest {

    @Mock
    private TaskCenterProxyService taskCenterProxyService;

    @InjectMocks
    private TaskCenterProxyController taskCenterProxyController;

    private AutoCloseable closeable;

    @BeforeEach
    void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void tearDown() throws Exception {
        closeable.close();
    }

    @Test
    void getCalendarByType_ShouldReturnCalendarDetail() {
        // 准备测试数据
        TaskCenterCalendarReq req = new TaskCenterCalendarReq();
        Object expectedResult = new Object();
        
        // 配置mock行为
        when(taskCenterProxyService.getCalendarByType(any(TaskCenterCalendarReq.class))).thenReturn(expectedResult);
        
        // 执行测试
        CommonApiResponse<Object> response = taskCenterProxyController.getCalendarByType(req);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResult, response.getData());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).getCalendarByType(req);
    }

    @Test
    void noNeedCompleteTask_ShouldReturnSuccessResponse() {
        // 准备测试数据
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        
        // 配置mock行为
        doNothing().when(taskCenterProxyService).noNeedCompleteTask(any(TaskCenterFinishTaskReq.class));
        
        // 执行测试
        CommonApiResponse<Void> response = taskCenterProxyController.noNeedCompleteTask(req);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNull(response.getData());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).noNeedCompleteTask(req);
    }

    @Test
    void finishOuterTask_ShouldReturnSuccessResponse() {
        // 准备测试数据
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        
        // 配置mock行为
        when(taskCenterProxyService.finishOuterTask(any(TaskCenterFinishTaskReq.class))).thenReturn("success");
        
        // 执行测试
        CommonApiResponse<Void> response = taskCenterProxyController.finishOuterTask(req);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNull(response.getData());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).finishOuterTask(req);
    }

    @Test
    void finishOuterTask_ShouldThrowRuntimeException_WhenServiceThrowsException() {
        // 准备测试数据
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        
        // 配置mock行为
        doThrow(new RuntimeException("Service error")).when(taskCenterProxyService).finishOuterTask(any(TaskCenterFinishTaskReq.class));
        
        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            taskCenterProxyController.finishOuterTask(req);
        });
        
        // 验证异常消息
        assertEquals("Error finish outer task: Service error", exception.getMessage());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).finishOuterTask(req);
    }

    @Test
    void queryTaskNum_ShouldReturnTaskNums() {
        // 准备测试数据
        TaskCenterTaskNumReq req = new TaskCenterTaskNumReq();
        Object expectedResult = new Object();
        
        // 配置mock行为
        when(taskCenterProxyService.queryTaskNum(any(TaskCenterTaskNumReq.class))).thenReturn(expectedResult);
        
        // 执行测试
        CommonApiResponse<Object> response = taskCenterProxyController.queryTaskNum(req);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResult, response.getData());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).queryTaskNum(req);
    }

    @Test
    void getDetailTaskInfo_ShouldReturnDetailTaskInfo() {
        // 准备测试数据
        TaskCenterDetailReq req = new TaskCenterDetailReq();
        Object expectedResult = new Object();
        
        // 配置mock行为
        when(taskCenterProxyService.getDetailTaskInfo(any(TaskCenterDetailReq.class))).thenReturn(expectedResult);
        
        // 执行测试
        CommonApiResponse<Object> response = taskCenterProxyController.getDetailTaskInfo(req);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResult, response.getData());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).getDetailTaskInfo(req);
    }

/*    @Test
    void getPopWindowContent_ShouldReturnPopWindowContent() {
        // 准备测试数据
        TaskCenterCommonReq req = new TaskCenterCommonReq();
        Object expectedResult = new Object();
        
        // 配置mock行为
        when(taskCenterProxyService.getPopWindowContent(any(TaskCenterCommonReq.class))).thenReturn((List<PopWindowDTO>) expectedResult);
        
        // 执行测试
        CommonApiResponse<Object> response = taskCenterProxyController.getPopWindowContent(req);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResult, response.getData());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).getPopWindowContent(req);
    }*/

    @Test
    void confirmPopWindow_ShouldReturnSuccessResponse() {
        // 准备测试数据
        ConfirmPopWindowsReq req = new ConfirmPopWindowsReq();
        
        // 配置mock行为
        doNothing().when(taskCenterProxyService).confirmPopWindow(any(ConfirmPopWindowsReq.class));
        
        // 执行测试
        CommonApiResponse<Void> response = taskCenterProxyController.confirmPopWindow(req);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertNull(response.getData());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).confirmPopWindow(req);
    }

    @Test
    void getCalendarForHead_ShouldReturnCalendarForHead() {
        // 准备测试数据
        HeadCalendarReq req = new HeadCalendarReq();
        Object expectedResult = new Object();
        
        // 配置mock行为
        when(taskCenterProxyService.getCalendarForHead(any(HeadCalendarReq.class))).thenReturn(expectedResult);
        
        // 执行测试
        CommonApiResponse<Object> response = taskCenterProxyController.getCalendarForHead(req);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(expectedResult, response.getData());
        
        // 验证交互
        verify(taskCenterProxyService, times(1)).getCalendarForHead(req);
    }

    @Test
    public void testMessage(){
        TaskCenterCommonReq req = new TaskCenterCommonReq();
        req.setMiId(1L);
        req.setOrgId("orgId");
        req.setCountry("country");
        req.setJobValue("500900002");

        taskCenterProxyController.getPopWindowContent(req);
    }
}