package com.mi.info.intl.retail.intlretail.app.samples.converter;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * created by MIT
 *
 * <AUTHOR>
 * @date 2022/1/10
 */
@Mapper
public interface Converter {

    Converter INSTANCE = Mappers.getMapper(Converter.class);

    @Mapping(source = "numberOfSeats", target = "seatCount")
    CarDto carToCarDto(Car car);

    @Mapping(source = "seatCount", target = "numberOfSeats")
    Car carDtoToCar(CarDto carDto);

}

