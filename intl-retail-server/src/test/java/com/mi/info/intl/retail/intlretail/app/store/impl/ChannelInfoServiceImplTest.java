package com.mi.info.intl.retail.intlretail.app.store.impl;

import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.intlretail.app.TestApplication;

import com.mi.info.intl.retail.intlretail.service.api.store.IChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = TestApplication.class)
public class ChannelInfoServiceImplTest {

    @Value("${spring.cache.enabled:false}")
    private boolean cacheEnabled;

    @Autowired
    private IChannelInfoService iChannelInfoService;

    @Test
    public void test() {
        System.out.println("cacheEnabled = " + cacheEnabled);
        ChannelInfoRequest channelInfoRequest = new ChannelInfoRequest();
        channelInfoRequest.setType(20);
        channelInfoRequest.setAreaId("ID");
        List<String> codes = new ArrayList<>();
        codes.add("RMSUAT20230427127428");
        channelInfoRequest.setCodes(codes);

        List<ChannelInfoResponse> result = iChannelInfoService.queryChannelInfo(channelInfoRequest);
        Assertions.assertNotNull(result);
    }

    @Test
    public void test2() {
        // 存入缓存
        CacheUtils.put("userCache", "123", "value");
        // 获取缓存
        String string = CacheUtils.get("userCache", "123", String.class);
        // 删除缓存
        CacheUtils.evict("userCache", "123");
    }

    @Test
    public void test3() {
        test2();
        Map<String, Object> map = new HashMap<>();
        map.put("key1", "value1");
        map.put("key2", "value2");
        // 存入缓存
        CacheUtils.hputAll("map1", map);
        // 更新缓存
        CacheUtils.hput("map1", "key1", "value11");
        // 删除缓存
        CacheUtils.hdel("map1", "key2");
        // 查询缓存
        System.out.println(CacheUtils.hget("map1", "key1", String.class));
    }
}