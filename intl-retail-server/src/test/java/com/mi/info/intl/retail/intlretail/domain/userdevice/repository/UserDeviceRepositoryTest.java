package com.mi.info.intl.retail.intlretail.domain.userdevice.repository;

import com.mi.info.intl.retail.cooperation.task.app.mq.RmsApiRequestConsumer;
import com.mi.info.intl.retail.intlretail.app.StartApplication;
import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.domain.userdevice.entity.UserDevice;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest(classes = StartApplication.class)
public class UserDeviceRepositoryTest {
    
    @Autowired
    private UserDeviceRepository userDeviceRepository;
    
    @Autowired
    private RmsApiRequestConsumer rmsApiRequestConsumer;
    
    
    @Test
    public void testQueryByUserAccount() {
        String userAccount = "<EMAIL>";
        UserDevice result = userDeviceRepository.queryByUserAccount(userAccount);
        assertNotNull(result);
    }
    
    @Test
    public void testRmsApiRequestConsumer() {
        String message =
                "{\"path\":\"new_SubmitData\"," + "\"type\":\"SalesUpload_Qty\"," + "\"account\":\"<EMAIL>\","
                        + "\"mid\":********,\"positionId\":" + "\"d3aa0e50-5447-f011-877a-6045bd22249c\","
                        + "\"countryCode\":\"ID\",\"positionCode\":\"MIIDS00992\"}";
        rmsApiRequestConsumer.onMessage(message);
    }
    
    
}