package com.mi.info.intl.retail.intlretail.app.position;

import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.xiaomi.cnzone.brain.platform.api.model.req.PushTaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.TaskExecutorReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.TaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.ProretailOuterEventReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.NoNeedCompleteTaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.CreateInstanceResp;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformAppProvider;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformOuterProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class TaskCenterServiceRpcTest {
    @Mock
    private BrainPlatformAppProvider brainPlatformAppProvider;
    @Mock
    private BrainPlatformOuterProvider brainPlatformOuterProvider;
    @InjectMocks
    private TaskCenterServiceRpc taskCenterServiceRpc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testOuterTaskFinish() {
        when(brainPlatformOuterProvider.outerTaskFinish(any(ProretailOuterEventReq.class)))
                .thenReturn(Result.success(""));
        ProretailOuterEventReq req = new ProretailOuterEventReq();
        assertDoesNotThrow(() -> taskCenterServiceRpc.outerTaskFinish(req));
        verify(brainPlatformOuterProvider, times(1)).outerTaskFinish(any(ProretailOuterEventReq.class));
    }

    @Test
    void testNoNeedCompleteTask() {
        when(brainPlatformAppProvider.noNeedCompleteTask(any(NoNeedCompleteTaskReq.class)))
                .thenReturn(Result.success(null));
        NoNeedCompleteTaskReq req = new NoNeedCompleteTaskReq();
        assertDoesNotThrow(() -> taskCenterServiceRpc.noNeedCompleteTask(req));
        verify(brainPlatformAppProvider, times(1)).noNeedCompleteTask(any(NoNeedCompleteTaskReq.class));
    }

    @Test
    void testPushTask() {
        when(brainPlatformOuterProvider.pushTask(any(PushTaskReq.class)))
                .thenReturn(Result.success(null));
        PushTaskReq req = new PushTaskReq();
        req.setList(Collections.emptyList());
        assertDoesNotThrow(() -> taskCenterServiceRpc.pushTask(req));
        verify(brainPlatformOuterProvider, times(1)).pushTask(any(PushTaskReq.class));
    }

    @Test
    void testReloadTaskStatus() {
        when(brainPlatformOuterProvider.reloadTaskStatus(any(TaskReq.class)))
                .thenReturn(Result.success(null));
        TaskReq req = new TaskReq();
        assertDoesNotThrow(() -> taskCenterServiceRpc.reloadTaskStatus(req));
        verify(brainPlatformOuterProvider, times(1)).reloadTaskStatus(any(TaskReq.class));
    }

    @Test
    void testChangeExecutor() {
        when(brainPlatformAppProvider.changeExecutor(any(TaskExecutorReq.class)))
                .thenReturn(Result.success(null));
        TaskExecutorReq req = new TaskExecutorReq();
        assertDoesNotThrow(() -> taskCenterServiceRpc.changeExecutor(req));
        verify(brainPlatformAppProvider, times(1)).changeExecutor(any(TaskExecutorReq.class));
    }
}
