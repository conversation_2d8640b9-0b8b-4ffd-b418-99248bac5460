package com.mi.info.intl.retail.intlretail.app.position;

import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionApproveRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.VerifyActionEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.app.service.position.PositionInspectionServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

// @SpringBootTest(classes = TestApplication.class)
public class PositionInspectionServiceImplTest {

    @Autowired
    private PositionInspectionServiceImpl positionInspectionService;

    // @Test
    public void testtaskReminder(){
        // 调用被测方法
        CommonApiResponse<String> result = positionInspectionService.taskReminder();

//        // 验证结果
//        assertNotNull(result);
//        assertEquals(200, result.getCode());
//        assertNotNull(result.getData());
    }

    // @Test
    public void testApprovePositionInspection() {
        // 构造请求参数
        PositionInspectionApproveRequest request = new PositionInspectionApproveRequest();
        request.setPositionInspectionId(1L);
        request.setVerifyAction(VerifyActionEnum.APPROVE.getCode()); // 通过
        request.setRemark("测试审批通过");
        
        // 调用被测方法
        CommonApiResponse<String> result = positionInspectionService.approvePositionInspection(request);
        
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(200, result.getCode());
//        assertNotNull(result.getData());
    }

    // @Test
    public void testApprovePositionInspection_Disapprove() {
        // 构造拒绝请求参数
        PositionInspectionApproveRequest request = new PositionInspectionApproveRequest();
        request.setPositionInspectionId(129L);
        request.setVerifyAction(VerifyActionEnum.DISAPPROVE.getCode()); // 不通过
        request.setRemark("测试审批不通过");
        request.setReason(DisapproveReasonEnum.POSITION_TYPE_INCORRECT.getCode()); // 拒绝原因类型
        
        // 调用被测方法
        CommonApiResponse<String> result = positionInspectionService.approvePositionInspection(request);
        
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(200, result.getCode());
//        assertNotNull(result.getData());
    }

    // @Test
    public void testNoNeedCompleteTask() {
        // 构造请求参数
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(54776L);
        req.setMiId(1040L);
        req.setOrgId("MIIDX00068");
        req.setOperatorMid(1040L);
        
        // 调用被测方法
        CommonApiResponse<String> result = positionInspectionService.noNeedCompleteTask(req);
        
//        // 验证结果
//        assertNotNull(result);
//        assertEquals(200, result.getCode());
//        assertNotNull(result.getData());
    }

    // @Test
    public void testdispatch() {
        // 构造请求参数·


        // 调用被测方法
        CommonApiResponse<String> result = positionInspectionService.dispatchInspectionTasks(18);

//        // 验证结果
//        assertNotNull(result);
//        assertEquals(200, result.getCode());
//        assertNotNull(result.getData());
    }



}
