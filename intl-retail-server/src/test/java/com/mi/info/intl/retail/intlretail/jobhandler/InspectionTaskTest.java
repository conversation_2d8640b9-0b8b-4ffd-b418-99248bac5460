package com.mi.info.intl.retail.intlretail.jobhandler;

import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.service.api.job.InspectionTaskJobProvider;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/18
 **/
@SpringBootTest(classes = TestApplication.class)
public class InspectionTaskTest {

    @Resource
    private InspectionTaskJobProvider taskJobProvider;



   @Test
    public void queryTaskInspectionPush(){
       taskJobProvider.queryTaskInspectionPush("");
   }

}
