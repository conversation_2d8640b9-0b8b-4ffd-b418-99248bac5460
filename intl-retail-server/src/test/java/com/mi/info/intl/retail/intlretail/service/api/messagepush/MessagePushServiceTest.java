package com.mi.info.intl.retail.intlretail.service.api.messagepush;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.service.api.inspection.InspectionService;
import com.mi.info.intl.retail.intlretail.service.api.job.InspectionTaskJobProvider;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.MasterDataService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextUserRequest;
import com.mi.info.intl.retail.intlretail.service.api.messagepush.dto.MessagePushRequest;
import com.mi.info.intl.retail.intlretail.service.api.messagepush.dto.MessagePushResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.TaskCenterProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.ConfirmPopWindowsReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCommonReq;
import com.mi.info.intl.retail.intlretail.service.api.request.IntlInspectionTaskRequest;
import com.mi.info.intl.retail.intlretail.service.api.request.StopInspectionTaskRequest;
import com.mi.info.intl.retail.intlretail.service.api.result.InspectionTaskConfDTO;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@Disabled
@SpringBootTest(classes = TestApplication.class)
public class MessagePushServiceTest {

    @Autowired
    private MessagePushService messagePushService;

    @Resource
    private InspectionService inspectionService;

    @Resource
    private TaskCenterProxyService taskCenterProxyService;

    @Resource
    private InspectionTaskJobProvider inspectionTaskJobProvider;

    @Resource
    private MasterDataService masterDataService;

    @Test
    public void test(){
        for (int i = 0; i < 20; i++) {
            MessagePushRequest request = new MessagePushRequest();
            // Set up the request object with necessary data
            List<String> userAccountList = Arrays.asList(
                    "<EMAIL>"
            );
            request.setTitle("test1234567890");
            request.setContent("ios test");
            request.setPageUrl("https://m.xiaomiyoupin.com/manpower/IncentivePolicyDetail?year=2025&month=3&day=11");
            request.setUserAccountList(userAccountList);

            MessagePushResponse response = messagePushService.sendPushMessage(request);
            assertNotNull(response);
            System.out.println(i);
        }

    }

    @Test
    public void test1(){
        IntlInspectionTaskRequest request = new IntlInspectionTaskRequest();
        request.setCountryList(Arrays.asList("ID"));
        CommonApiResponse<IPage<InspectionTaskConfDTO>> iPageCommonApiResponse = inspectionService.pageList(request);
        IPage<InspectionTaskConfDTO> data = iPageCommonApiResponse.getData();
        System.out.println(data.getRecords().size());
    }

    @Test
    public void test2(){
        IntlInspectionTaskRequest request = new IntlInspectionTaskRequest();
//        request.setCountryList(Arrays.asList("ID"));
        CommonApiResponse<String> export = inspectionService.export(request);
        System.out.println("========>>>>>>>>>>>>>>>>>>>\n");
        System.out.println(export.getData());
    }

    @Test
    public void queryTask(){
        inspectionTaskJobProvider.pushMessage("{\"isPush\": true}");
    }

    @Test
    public void test3(){
        TaskCenterCommonReq req = new TaskCenterCommonReq();
        req.setMiId(20250011L);
        req.setOrgId("orgId");
        req.setCountry("country");
        req.setJobValue("500900002");
        taskCenterProxyService.getPopWindowContent(req);
    }

    @Test
    public void test4(){
        ConfirmPopWindowsReq req = new ConfirmPopWindowsReq();
        req.setType("INSPECTION");
        req.setMiId(1L);
        req.setJobValue("500900002");
        ConfirmPopWindowsReq.ConfirmPopWindowDetail confirmPopWindowDetail = new ConfirmPopWindowsReq.ConfirmPopWindowDetail();
        confirmPopWindowDetail.setRedFlag(true);
        confirmPopWindowDetail.setTaskInstanceId("1");
        req.setList(Arrays.asList(confirmPopWindowDetail));
        taskCenterProxyService.confirmPopWindow(req);
    }

    @Test
    public void stopTask(){
        StopInspectionTaskRequest stopInspectionTaskRequest = new StopInspectionTaskRequest();
        inspectionService.stopTask(stopInspectionTaskRequest);
    }
    @Test
    public void test5(){
        CommonApiResponse<IPage<PlainTextImeiUserDTO>> iPageCommonApiResponse = masterDataService.pageQueryPlainTextUser(new PlainTextUserRequest());
        System.out.println(iPageCommonApiResponse.getData());
    }
}