package com.mi.info.intl.retail.intlretail.app.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.intlretail.app.controller.BaseController;
import com.mi.info.intl.retail.intlretail.app.dto.menustatus.ProceedingResultDto;
import com.mi.info.intl.retail.intlretail.service.app.proxy.dto.MenuStatusCacheDto;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CacheAspectTest {

    private static final String TEST_KEY = "20260612";
    private static final String TEST_HASH_KEY = "MenuStatus:<EMAIL>";
    private static final String TEST_MENU_NAME = "Receiving";
    @InjectMocks
    private CacheAspect cacheAspect;
    @Mock
    private ProceedingJoinPoint joinPoint;
    @Mock
    private BaseController baseController;
    @Spy
    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        when(joinPoint.getTarget()).thenReturn(baseController);
    }

    @Test
    void rmsTokenAndTypeQueryHandleCache_WhenMenuNameMatches_ShouldUpdateMenuStatus() throws Throwable {
        // Arrange
        List<MenuStatusCacheDto.MenuStatusList> menuList = createMenuStatusList();
        MenuStatusCacheDto cacheDto = createMenuStatusCacheDto(0, "Success", menuList);
        ProceedingResultDto responseDto = new ProceedingResultDto();
        responseDto.setCode(0);
        responseDto.setMessage("Success");

        when(joinPoint.proceed()).thenReturn(responseDto);
        when(joinPoint.getArgs()).thenReturn(new Object[]{null, "new_SubmitData", "Receiving"});
        when(baseController.getCurrentDay()).thenReturn(TEST_KEY);
        when(baseController.getAccount()).thenReturn("<EMAIL>");

        try (MockedStatic<CacheUtils> mockedCacheUtils = Mockito.mockStatic(CacheUtils.class)) {
            mockedCacheUtils.when(() -> CacheUtils.hget(TEST_KEY, TEST_HASH_KEY, Object.class))
                    .thenReturn(cacheDto);

            // Act
            Object result = cacheAspect.rmsTokenAndTypeQueryHandleCache(joinPoint);

            // Assert
            assertEquals(responseDto, result);
            mockedCacheUtils.verify(() -> CacheUtils.hputAllWithExpire(
                    eq(TEST_KEY),
                    any(),
                    eq(48L),
                    eq(TimeUnit.HOURS)
            ));
        }
    }

    @Test
    void rmsTokenAndTypeQueryHandleCache_WhenNotTaskSubmit_ShouldReturnOriginalResult() throws Throwable {
        // Arrange
        ProceedingResultDto originalResult = new ProceedingResultDto();
        originalResult.setCode(1);
        originalResult.setMessage("Error");
        when(joinPoint.proceed()).thenReturn(originalResult);
        when(joinPoint.getArgs()).thenReturn(new Object[]{null, "other", "path"});

        // Act
        Object result = cacheAspect.rmsTokenAndTypeQueryHandleCache(joinPoint);

        // Assert
        assertEquals(originalResult, result);
    }

    @Test
    void rmsTokenAndTypeQueryHandleCache_WhenUserSubmit_ShouldHandleUserBaseData() throws Throwable {
        // Arrange
        ProceedingResultDto originalResult = new ProceedingResultDto();
        originalResult.setCode(0);
        originalResult.setMessage("Success");
        when(joinPoint.proceed()).thenReturn(originalResult);
        when(joinPoint.getArgs()).thenReturn(new Object[]{null, "new_SubmitData", "SignIn"});
        when(baseController.getCurrentDay()).thenReturn(TEST_KEY);
        when(baseController.getAccount()).thenReturn("<EMAIL>");

        try (MockedStatic<CacheUtils> mockedCacheUtils = Mockito.mockStatic(CacheUtils.class)) {
            mockedCacheUtils.when(() -> CacheUtils.hget(TEST_KEY, TEST_HASH_KEY, Object.class))
                    .thenReturn(new Object());

            // Act
            Object result = cacheAspect.rmsTokenAndTypeQueryHandleCache(joinPoint);

            // Assert
            assertEquals(originalResult, result);
            mockedCacheUtils.verify(() -> CacheUtils.hdel(TEST_KEY, TEST_HASH_KEY));
        }
    }

    private MenuStatusCacheDto createMenuStatusCacheDto(int code, String message, List<MenuStatusCacheDto.MenuStatusList> data) {
        MenuStatusCacheDto dto = new MenuStatusCacheDto();
        dto.setCode(code);
        dto.setMessage(message);
        dto.setData(data);
        return dto;
    }

    private List<MenuStatusCacheDto.MenuStatusList> createMenuStatusList() {
        List<MenuStatusCacheDto.MenuStatusList> list = new ArrayList<>();
        MenuStatusCacheDto.MenuStatusList menuStatus = new MenuStatusCacheDto.MenuStatusList();
        menuStatus.setScreenName(TEST_MENU_NAME);
        menuStatus.setMenuStoreJobFinish(false);
        menuStatus.setMenuStoreJobVisible(true);
        menuStatus.setLabel("Test Label");
        menuStatus.setStatusCount(1);
        menuStatus.setType(100000002);
        menuStatus.setExecutor(100000002);
        menuStatus.setInStoreJob(true);
        list.add(menuStatus);
        return list;
    }
}
