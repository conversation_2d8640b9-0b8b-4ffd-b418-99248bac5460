package com.mi.info.intl.retail.intlretail.app.samples.keycenter;

import com.xiaomi.core.security.SimpleKeycenter;
import com.xiaomi.mit.common.codec.Coder;
import com.xiaomi.mit.common.security.CryptoCoder;
import com.xiaomi.mit.common.security.Cryptos;
import org.apache.commons.lang3.RandomStringUtils;

/**********************************************************************
 * 此类演示使用 架构组-基础库-keycenter加解密的用法
 * keycenter Api 使用文档：http://docs.mit.mi.com/mit-commons-java/mit-core/keycenter
 * keycenter接入文档：https://wiki.n.miui.com/pages/viewpage.action?pageId=61682778
 * *********************************************************************
 */
public class Keycenters {

    /**
     * 此方法演示 SimpleKeycenter.bytesToBytes(sid)
     * Bytes <-> Bytes
     */
    public void bytesToBytes() {
        String sid = "sid";
        CryptoCoder<byte[]> crypto = SimpleKeycenter.bytesToBytes(sid);
        byte[] input = Cryptos.randomBytes(16);
        byte[] cipher = crypto.encrypt(input);
    }

    /**
     * 此方法演示 SimpleKeycenter.stringToString(sid)
     * String <-> String
     */
    public void stringToString() {
        String sid = "sid";
        CryptoCoder<String> crypto = SimpleKeycenter.stringToString(sid);
        String input = RandomStringUtils.randomAlphabetic(100);
        String cipher = crypto.encrypt(input);
        String result = crypto.decrypt(cipher);
    }

    /**
     * 此方法演示 SimpleKeycenter.bytesToString(sid)
     * Bytes <-> String
     */
    public void bytesToString() {
        String sid = "sid";
        Coder<byte[], String> crypto = SimpleKeycenter.bytesToString(sid);
        byte[] input = Cryptos.randomBytes(16);
        String cipher = crypto.encode(input);
        byte[] result = crypto.decode(cipher);
    }

}

