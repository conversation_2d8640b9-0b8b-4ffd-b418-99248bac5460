package com.mi.info.intl.retail.intlretail.app.store.impl.gateway;

import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.IGateWayChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto.GateWayChannelInfoResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = TestApplication.class)
public class GateWayChannelInfoServiceImplTest {
    @DubboReference(group = "sg_staging")
    private IGateWayChannelInfoService iGateWayChannelInfoService;

    @Test
    public void test() {
        //RpcContext rpcContext = RpcContext.getContext();
        //rpcContext.setAttachment("heracontext", "mone-retail-area-for-global:GLOBal");
        ChannelInfoRequest channelInfoRequest = new ChannelInfoRequest();
        channelInfoRequest.setType(20);
        channelInfoRequest.setAreaId("ID");
        List<String> codes = new ArrayList<>();
        codes.add("RMS");
        channelInfoRequest.setCodes(codes);
        //channelInfoRequest.setSearch("RMS");
        GateWayChannelInfoResponse result = iGateWayChannelInfoService.queryChannelInfo(channelInfoRequest);
        Assertions.assertNotNull(result);
    }
}
