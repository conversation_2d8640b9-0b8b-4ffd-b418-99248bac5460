package com.mi.info.intl.retail.intlretail.app.store.impl;

import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.service.api.store.IChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.IChannelStoreService;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.UniversalProxyRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.UniversalProxyResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(classes = TestApplication.class)
public class ChannelStoreServiceImplTest {

    @DubboReference(group = "sg_staging")
    private IChannelStoreService iChannelStoreService;

    @Test
    public void testRPC(){
        RpcContext rpcContext = RpcContext.getContext();
        rpcContext.setAttachment("heracontext", "mone-retail-area-for-global:MY");

        UniversalProxyRequest requestBody  = new UniversalProxyRequest();
        requestBody.setAreaId("ID");
        requestBody.setPath("new_QueryStoreForChannelRetail");
        requestBody.setInput("{\"orgIds\":[\"1232\",\"32432432\"]}");
        UniversalProxyResponse result = iChannelStoreService.universalProxyApi(requestBody);
        Assertions.assertNotNull(result);
    }

    @Test
    public void testParam(){
        UniversalProxyRequest requestBody  = new UniversalProxyRequest();
        requestBody.setAreaId("ID");
        requestBody.setPath("new_QueryStoreForChannelRetail");
        requestBody.setInput("{\"orgIds\":[\"1232\",\"32432432\"]}");
        UniversalProxyResponse result = iChannelStoreService.universalProxyApi(requestBody);
        Assertions.assertNotNull(result);
    }
}