package com.mi.info.intl.retail.intlretail.app.exception;

import com.xiaomi.mit.api.Result;
import com.xiaomi.mit.api.error.ErrorCodes;
import com.xiaomi.mit.api.error.Exceptions;
import org.springframework.web.bind.annotation.*;

/**
 * created by MIT
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@RestController
@RequestMapping("exception")
public class ExceptionController {

    @GetMapping("/0")
    public Object throwErrorCodeException() {
        throw Exceptions.internalServerError("error code exception from Exceptions.xxx(xxx)");
    }

    @GetMapping("/1")
    public Object throwErrorCodeExceptionWithOnlyErrorCode() {
        throw Exceptions.of(ErrorCodes.ACCESS_TOKEN_EXPIRED);
    }

    @GetMapping("/2")
    public Object throwErrorCodeExceptionWithErrorCodeAndCustomMessage() {
        throw Exceptions.of(ErrorCodes.ACCESS_TOKEN_EXPIRED, "error code exception from Exceptions.of(xxErrorCode, msg)");
    }

    @GetMapping("/3")
    public Object throwIllargumentException() {
        throw new IllegalArgumentException("some illegal exception");
    }

    @GetMapping("/4")
    public Object throwNotCatchedException() {
        throw new NotCaughtException("not caught exception");
    }

    @RequestMapping (value = "/5", method = RequestMethod.POST)
    public Object throwHttpRequestMethodNotSupportedException() {
        return Result.success();
    }

    @GetMapping("/6")
    public Object throwMissingPathVariableException(@PathVariable("user") String user) {
        return Result.success();
    }

    @GetMapping("/7")
    public Object throwMissingServletRequestParameterException(@RequestParam(value = "user") String user) {
        return Result.success();
    }

    @GetMapping("/8")
    public Object throwMethodArgumentTypeMismatchException(@RequestParam(value = "age") int age) {
        return Result.success();
    }

    @GetMapping("/9")
    public Object throwLegacyExceptionAndWillBeCaughtByCustomGlobalExceptionHandler() {
        throw  new LegacyException(408, "message from the legacy exception and be caught by CustomGlobalExceptionHandler");
    }

    @GetMapping("/10")
    public Object throwLegacyExceptionAndWillNotBeCaughtByCustomGlobalExceptionHandler() {
        throw  new LegacyExceptionWithoutHandler(408, "message from the legacy exception and not be caught by CustomGlobalExceptionHandler");
    }

    static class NotCaughtException extends RuntimeException {
        public NotCaughtException(String message) {
            super(message);
        }
    }

}

