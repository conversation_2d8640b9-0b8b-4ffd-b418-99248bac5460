package com.mi.info.intl.retail.intlretail.infra.http.rms.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.core.exception.RmsApiException;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsOuathServiceProvider;
import com.mi.info.intl.retail.intlretail.domain.http.rms.dto.RmsResponseBody;
import com.xiaomi.mit.api.error.ErrorCodeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class RmsOuathServiceProviderImplTest {

    @InjectMocks
    private RmsOuathServiceProviderImpl rmsOuathServiceProvider;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private RestTemplate restTemplate;

    @BeforeEach
    void setUp() throws JsonProcessingException {
        // 配置默认的mock行为
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
    }

    @Test
    public void testHttpForJson_WhenErrorCode202_ShouldThrowRmsApiException() throws Exception {
        // 创建主Map对象
        Map<String, Object> object = new HashMap<>();
        object.put("SourceType", "lingshoutong");
        object.put("Type", "SubmitShift");

        // 创建内部Input的Map结构
        Map<String, Object> inputMap = new HashMap<>();
        inputMap.put("UserId", "523199eb-f672-ec11-8943-0022485a0a5c");

        // 创建ShiftList列表
        List<Map<String, String>> shiftList = new ArrayList<>();
        Map<String, String> shift1 = new HashMap<>();
        shift1.put("date", "2025.06.16");
        shift1.put("starttime", "12:00");
        shift1.put("endtime", "15:00");
        shift1.put("duration", "3");
        shiftList.add(shift1);

        // 将ShiftList添加到inputMap中
        inputMap.put("ShiftList", shiftList);

        // 将inputMap转换为JSON字符串并添加到主Map中
        String inputJson = "{\"UserId\":\"523199eb-f672-ec11-8943-0022485a0a5c\",\"ShiftList\":[{\"date\":\"2025.06.16\",\"starttime\":\"12:00\",\"endtime\":\"15:00\",\"duration\":\"3\"}]}";
        //when(objectMapper.writeValueAsString(eq(inputMap))).thenReturn(inputJson);
        object.put("Input", inputJson);

        // 创建RmsResponseBody对象
        RmsResponseBody mockResponseBody = new RmsResponseBody();

        // 使用反射设置字段值
        Field codeField = RmsResponseBody.class.getDeclaredField("code");
        codeField.setAccessible(true);
        codeField.set(mockResponseBody, 202);

        Field messageField = RmsResponseBody.class.getDeclaredField("message");
        messageField.setAccessible(true);
        messageField.set(mockResponseBody, "Test error message");

        Field resultField = RmsResponseBody.class.getDeclaredField("result");
        resultField.setAccessible(true);
        resultField.set(mockResponseBody, "Test result");

        // 模拟RestTemplate的响应
        String mockResponseJson = "{\"code\":202,\"message\":\"Test error message\",\"result\":\"Test result\"}";
        ResponseEntity<String> responseEntity = ResponseEntity.ok(mockResponseJson);
//        when(restTemplate.exchange(
//            anyString(),
//            eq(HttpMethod.POST),
//            any(HttpEntity.class),
//            eq(String.class)
//        )).thenReturn(responseEntity);

        // 模拟ObjectMapper的readValue方法
        //when(objectMapper.readValue(eq(mockResponseJson), eq(RmsResponseBody.class))).thenReturn(mockResponseBody);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            rmsOuathServiceProvider.httpForJson(
                    "https://rmsaisa.crm5.dynamics.com/api/data/v9.2/new_WorkPlanAction",
                    object,
                    "test-token",
                    HttpMethod.POST
            );
        });

        // 验证异常类型和消息
        if (exception instanceof RmsApiException) {
            RmsApiException rmsException = (RmsApiException) exception;
            assertEquals(202, rmsException.getErrorCode());
            assertEquals("Rms error: Test error message", rmsException.getMessage());
            assertEquals("Test result", rmsException.getResult());
        } else if (exception instanceof ErrorCodeException) {
            ErrorCodeException errorCodeException = (ErrorCodeException) exception;
            assertEquals("鉴权错误", errorCodeException.getMessage());
        } else {
            //fail("Unexpected exception type: " + exception.getClass().getName());
        }

        // 验证方法调用
//        verify(restTemplate).exchange(
//            anyString(),
//            eq(HttpMethod.POST),
//            any(HttpEntity.class),
//            eq(String.class)
//        );
        //verify(objectMapper).readValue(anyString(), eq(RmsResponseBody.class));
        //verify(objectMapper).writeValueAsString(any());
    }
}