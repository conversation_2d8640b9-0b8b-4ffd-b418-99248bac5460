package com.mi.info.intl.retail.intlretail.app.exception;

import com.xiaomi.mit.api.error.ErrorCode;
import com.xiaomi.mit.api.error.ErrorCodes;
import java.util.Locale;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * created by MIT
 *
 * <AUTHOR>
 * @date 2021/8/27
 */
public class ErrorCodesTest {

//    @Test
//    void errorCodeTest() {
//        ErrorCode ok = ErrorCodes.OK;
//        Assertions.assertEquals(200, ok.getHttpCode());
//
//        // normal test
//        Assertions.assertEquals("OK", ok.getMessage(Locale.ENGLISH));
//        Assertions.assertEquals("成功", ok.getMessage(Locale.CHINESE));
//
//        // 用其他中文调用
//        Assertions.assertEquals("成功", ok.getMessage(Locale.TAIWAN));
//
//        // 直接调用， 默认中文
//        Assertions.assertEquals("成功", ok.getMessage());
//
//        // 传入既非中文也非英文的 locale， 默认返回英文
//        Assertions.assertEquals("OK", ok.getMessage(Locale.FRANCE));
//
//
//        // custom error
//        ErrorCode customError1 = CustomErrorCodes.CUSTOM_ERROR1;
//        Assertions.assertEquals("自定义错误1", customError1.getMessage(Locale.CHINESE));
//
//        // another custom error. mock multiple module error resource
//        ErrorCode anotherCustomError1 = AnotherCustomErrorCodes.ANOTHER_CUSTOM_ERROR1;
//        Assertions.assertEquals("自定义错误1", anotherCustomError1.getMessage(Locale.CHINESE));
//
//        // custom error and not set i18n message. should default return the name
//        ErrorCode customError2 = CustomErrorCodes.CUSTOM_ERROR2;
//        Assertions.assertEquals("CUSTOM_ERROR2", customError2.getMessage());
//
//        // 定义相同的 code, 初始化报错
//        Assertions.assertThrows(ExceptionInInitializerError.class, () -> CustomErrorDuplicateCodes.DUPLICATE_ERROR.getMessage());
//
//    }

}

