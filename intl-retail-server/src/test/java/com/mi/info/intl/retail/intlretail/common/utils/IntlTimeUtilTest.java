package com.mi.info.intl.retail.intlretail.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
public class IntlTimeUtilTest {
    
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    @Test
    public void testGetStartAndEndOfDayMillis() {
        ZoneId zoneId = ZoneId.of("Europe/Madrid");
        
        // 当前时刻的 ZonedDateTime（带时区信息）
        ZonedDateTime nowZoned = ZonedDateTime.now(zoneId);
        
        // 获取当天 00:00:00.000 的 ZonedDateTime
        ZonedDateTime startOfDay = nowZoned.toLocalDate().atStartOfDay(zoneId);
        
        // 获取当天 23:59:59.999 的 ZonedDateTime
        ZonedDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1_000_000); // 减去 1 毫秒
        
        // 转换为 Instant 再转为时间戳（毫秒）
        long startTimestamp = startOfDay.toInstant().toEpochMilli();
        long endTimestamp = endOfDay.toInstant().toEpochMilli();
        log.info("Now beijin timestamp date str: {}", SIMPLE_DATE_FORMAT.format(new Date()));
        log.info("Europe/Madrid now-date str: {}", nowZoned.format(FORMATTER));
        log.info("Europe/Madrid start-date timestamp: {}", startTimestamp);
        log.info("Beijin start-date timestamp date str: {}", SIMPLE_DATE_FORMAT.format(new Date(startTimestamp)));
        log.info("Europe/Madrid start-date str: {}", startOfDay.format(FORMATTER));
        log.info("Europe/Madrid end-date timestamp: {}", endTimestamp);
        log.info("Beijin end-date timestamp date str: {}", SIMPLE_DATE_FORMAT.format(new Date(endTimestamp)));
        log.info("Europe/Madrid end-date timestamp date str: {}", endOfDay.format(FORMATTER));
        log.info("endTimestamp - startTimestamp: {}", endTimestamp - startTimestamp);
    }
}
