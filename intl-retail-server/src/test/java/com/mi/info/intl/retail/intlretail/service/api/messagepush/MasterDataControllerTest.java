package com.mi.info.intl.retail.intlretail.service.api.messagepush;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.MasterDataService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextUserRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.Test;
import org.junit.jupiter.api.Disabled;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/1
 **/
@Disabled
@SpringBootTest(classes = TestApplication.class)
public class MasterDataControllerTest {

    @Resource
    private MasterDataService masterDataService;

    @Test
    public void aa(){
        CommonApiResponse<IPage<PlainTextImeiUserDTO>> iPageCommonApiResponse = masterDataService.pageQueryPlainTextUser(new PlainTextUserRequest());
        System.out.println(iPageCommonApiResponse);
    }
}
