package com.mi.info.intl.retail.intlretail.app.interceptor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class ApiProxyInterceptorTest {

    private HandlerInterceptor interceptor;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        interceptor = new ApiProxyInterceptor();
    }

    @Test
    public void testPreHandle_AlreadyForwarded() throws Exception {
        // 模拟请求已被转发
        when(request.getAttribute("API_PROXY_FORWARDED")).thenReturn(true);

        boolean result = interceptor.preHandle(request, response, null);

        assertTrue(result);
        verify(request, never()).getRequestDispatcher(anyString());
    }

    /*@Test
    public void testPreHandle_ValidRegion() throws Exception {
        // 模拟请求未被转发
        when(request.getAttribute("API_PROXY_FORWARDED")).thenReturn(null);
        // 模拟请求 URI
        when(request.getRequestURI()).thenReturn("/asia/api/proxy");

        boolean result = interceptor.preHandle(request, response, null);

        assertFalse(result);
        verify(request).setAttribute("API_PROXY_FORWARDED", true);
        verify(request).getRequestDispatcher("/api/proxy");
        verify(request.getRequestDispatcher("/api/proxy")).forward(request, response);
    }*/

    @Test
    public void testPreHandle_InvalidRegion() throws Exception {
        // 模拟请求未被转发
        when(request.getAttribute("API_PROXY_FORWARDED")).thenReturn(null);
        // 模拟无效的请求 URI
        when(request.getRequestURI()).thenReturn("/invalid/api/test");

        boolean result = interceptor.preHandle(request, response, null);

        assertTrue(result);
        verify(request, never()).setAttribute("API_PROXY_FORWARDED", true);
        verify(request, never()).getRequestDispatcher(anyString());
    }

    @Test
    public void testPreHandle_NoRegion() throws Exception {
        // 模拟请求未被转发
        when(request.getAttribute("API_PROXY_FORWARDED")).thenReturn(null);
        // 模拟没有区域前缀的请求 URI
        when(request.getRequestURI()).thenReturn("/api/test");

        boolean result = interceptor.preHandle(request, response, null);

        assertTrue(result);
        verify(request, never()).setAttribute("API_PROXY_FORWARDED", true);
        verify(request, never()).getRequestDispatcher(anyString());
    }
}