package com.mi.info.intl.retail.intlretail.app.exception;

import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.xiaomi.mit.starter.exception.handler.GlobalExceptionHandlerConfiguration;
import com.xiaomi.mit.starter.springweb.ResultHandlerConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.ResultMatcher;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

/**
 * created by MIT
 *
 * <AUTHOR>
 * @date 2021/12/29
 */
@WebMvcTest(value = ExceptionController.class)
@ContextConfiguration(classes = TestApplication.class)
@Import(value = { GlobalExceptionHandlerConfiguration.class, ResultHandlerConfiguration.class})
public class GlobalExceptionHandlerTest {

    @Autowired
    MockMvc mvc;

    private ResultMatcher jsonExpect(String path, Object expectValue) {
        return jsonPath(path).value(expectValue);
    }

    private void check(int urlIndex, int code, String msg, ResultMatcher statusMatcher) throws Exception {
        ResultActions actions = mvc.perform(get("/exception/" + urlIndex));
        if (statusMatcher != null) {
            actions.andExpect(statusMatcher);
        }
        actions.andExpect(jsonExpect("code", code));
        if (StringUtils.isNotEmpty(msg)) {
            actions.andExpect(jsonExpect("message", msg));
        }
    }

    private void check(int urlIndex, int code) throws Exception {
        check(urlIndex, code, null, null);
    }

    private void check(int urlIndex, int code, String msg) throws Exception {
        check(urlIndex, code, msg, null);
    }

//    @Test
//    void throwErrorCodeExceptionTest() throws  Exception {
//        check(0, 50000000, "error code exception from Exceptions.xxx(xxx)", status().is5xxServerError());
//    }
//
//    @Test
//    void throwErrorCodeExceptionWithOnlyErrorCodeTest() throws Exception {
//        check(1, 40100005, "access token 过期", status().is4xxClientError());
//    }
//
//    @Test
//    void throwErrorCodeExceptionWithErrorCodeAndCustomMessageTest() throws Exception {
//        check(2, 40100005, "error code exception from Exceptions.of(xxErrorCode, msg)", status().is4xxClientError());
//    }
//
//    @Test
//    void throwIllargumentExceptionTest() throws  Exception {
//        check(3, 40000001, "exception from CustomGlobalExceptionHandler and override GlobalExceptionHandler in mit-starter", status().is4xxClientError());
//    }
//
//    @Test
//    void throwNotCatchedExceptionTest() throws  Exception {
//        check(4, 50000000, "not caught exception", status().is5xxServerError());
//    }
//
//    @Test
//    void throwHttpRequestMethodNotSupportedExceptionTest() throws  Exception {
//        check(5, 40500000, "接口不支持 GET 类型方法", status().is4xxClientError());
//    }
//
//    @Test
//    void throwMissingPathVariableExceptionTest() throws Exception {
//        check(6, 40000001, "参数 user 未设置", status().is4xxClientError());
//    }
//
//    @Test
//    void throwMissingServletRequestParameterExceptionTest() throws Exception {
//        check(7, 40000001, "参数 user 未设置", status().is4xxClientError());
//    }
//
//    @Test
//    void throwMethodArgumentTypeMismatchExceptionTest() throws Exception {
//        check(8, 40000001, "参数 age 未设置", status().is4xxClientError());
//    }
//
//    @Test
//    void throwLegacyExceptionAndWillBeCaughtByCustomGlobalExceptionHandlerTest() throws Exception {
//        check(9, 408, "message from the legacy exception and be caught by CustomGlobalExceptionHandler", status().is4xxClientError());
//    }
//
//    @Test
//    void throwLegacyExceptionAndWillNotBeCaughtByCustomGlobalExceptionHandlerTest() throws Exception {
//        check(10, 50000000, "message from the legacy exception and not be caught by CustomGlobalExceptionHandler", status().is5xxServerError());
//    }

}

