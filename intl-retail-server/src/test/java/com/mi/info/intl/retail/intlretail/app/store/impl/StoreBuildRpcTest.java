package com.mi.info.intl.retail.intlretail.app.store.impl;

import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.intlretail.service.app.rpc.StoreBuildRpc;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = TestApplication.class)
public class StoreBuildRpcTest {

    @Autowired
    private StoreBuildRpc storeBuildRpc;


    @Test
    public void testQuery() {
    }
}