package com.mi.info.intl.retail.intlretail.app.samples.converter;

import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * created by MIT
 *
 * <AUTHOR>
 * @date 2022/1/10
 */
@Slf4j
public class MapstructTest {

    /** easiest converter */
    // @Test
    void testMapstructConverter() {

        final int diameter = 50;
        List<Wheel> wheels = Lists.newArrayList(
            Wheel.builder().wheelPosition(WheelPosition.LEFT_FRONT).diameter(diameter).build(),
            Wheel.builder().wheelPosition(WheelPosition.LEFT_REAR).diameter(diameter).build(),
            Wheel.builder().wheelPosition(WheelPosition.RIGHT_FRONT).diameter(diameter).build(),
            Wheel.builder().wheelPosition(WheelPosition.RIGHT_REAR).diameter(diameter).build());

        // given  Car{make='A', numberOfSeats=5, type=SEDAN}
        Car car = Car.builder().make("A").numberOfSeats(5).wheels(wheels).build();

        // convert  CarDto{make='A', seatCount=5, type='SEDAN'
        CarDto carDto = Converter.INSTANCE.carToCarDto(car);

        // convert back
        Car back = Converter.INSTANCE.carDtoToCar(carDto);

        log.info("\ncar: {},\ncarDto: {},\nback: {}", car, carDto, back);
    }

}

