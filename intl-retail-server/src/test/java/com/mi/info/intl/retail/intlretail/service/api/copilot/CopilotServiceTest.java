package com.mi.info.intl.retail.intlretail.service.api.copilot;

import com.mi.info.intl.retail.intlretail.service.app.copilot.CopilotServiceImpl;
import com.xiaomi.nr.copilot.api.request.AIAssistantComparableProductRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantContrastProductParamsRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantFeedbackTagsRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantProductParamsRequest;
import com.xiaomi.nr.copilot.api.request.BreakAIAnswerRequest;
import com.xiaomi.nr.copilot.api.request.CopilotComparisonHistoryRequest;
import com.xiaomi.nr.copilot.api.response.AIAssistantComparableProductResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantContrastProductParamsResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantFeedbackTagsResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantProductComparisonHistoryResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantProductParamsResponse;
import com.xiaomi.nr.copilot.api.service.ChatAiAssistantService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import com.xiaomi.youpin.infra.rpc.errors.Scopes;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;


/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-05-26 9:59
 */
@ExtendWith(MockitoExtension.class)
public class CopilotServiceTest {

    @InjectMocks
    private CopilotServiceImpl copilotService;

    @Mock
    private ChatAiAssistantService chatAiAssistantService;

    @Test
    void testGetProductParams_Success() throws BizError {
        // 准备测试数据
        AIAssistantProductParamsRequest  request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        AIAssistantProductParamsResponse response = new AIAssistantProductParamsResponse();
        Result<AIAssistantProductParamsResponse> result = Result.success(response);

        // 模拟chatAiAssistantService的返回值
        when(chatAiAssistantService.getProductParams(request)).thenReturn(result);

        // 调用被测试方法
        AIAssistantProductParamsResponse actualResponse = copilotService.getProductParams(request);
        // 验证结果
        assertEquals(response, actualResponse);
    }

    @Test
    void testBreakeAIAnswer_Success() throws BizError {
        // 准备测试数据
        BreakAIAnswerRequest request = new BreakAIAnswerRequest();
        request.setRequestId("1");


        // 模拟chatAiAssistantService的返回值
        when(chatAiAssistantService.breakAiGenerate(request)).thenReturn(Result.success(true));

        // 调用被测试方法
        Boolean aBoolean = copilotService.breakAIAnswer(request);
        // 验证结果
        assertEquals(true, aBoolean);
    }

    @Test
    void testGetProductParams_BizError() throws BizError {
        // 准备测试数据
        AIAssistantProductParamsRequest  request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        // 模拟chatAiAssistantService抛出BizError异常
        when(chatAiAssistantService.getProductParams(request)).thenThrow(new RuntimeException("Biz error"));

        // 验证是否抛出RuntimeException
        assertThrows(RuntimeException.class, () -> {
            copilotService.getProductParams(request);
        });
    }

    @Test
    void testGetProductParams_NullResult() throws BizError {
        // 准备测试数据
        AIAssistantProductParamsRequest  request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        // 模拟chatAiAssistantService返回null
        when(chatAiAssistantService.getProductParams(request)).thenReturn(null);

        // 验证是否抛出RuntimeException
        assertThrows(RuntimeException.class, () -> {
            copilotService.getProductParams(request);
        });
    }

    @Test
    void testBreakAiAnswer_NullResult() throws BizError {
        // 准备测试数据
        BreakAIAnswerRequest request = new BreakAIAnswerRequest();
        request.setRequestId("1");

        // 模拟chatAiAssistantService返回null
        when(chatAiAssistantService.breakAiGenerate(request)).thenReturn(null);

        // 验证是否抛出RuntimeException
        assertThrows(RuntimeException.class, () -> {
            copilotService.breakAIAnswer(request);
        });
    }

    @Test
    void testGetProductParams_NonZeroCode() throws BizError {
        // 准备测试数据
        AIAssistantProductParamsRequest  request = new AIAssistantProductParamsRequest();
        request.setItemCode("123456");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        Result<AIAssistantProductParamsResponse> result = Result.fail(ErrorCode.createOnce(Scopes.SCOPE_TAGGING, 1), "Error message");

        // 模拟chatAiAssistantService返回非零code的结果
        when(chatAiAssistantService.getProductParams(request)).thenReturn(result);

        // 验证是否抛出RuntimeException
        assertThrows(RuntimeException.class, () -> {
            copilotService.getProductParams(request);
        });
    }

    @Test
    public void getComparableProductsTest() throws BizError {
        AIAssistantComparableProductRequest request = new AIAssistantComparableProductRequest();
        request.setProductId("123456");
        request.setIsXiaomiProduct(true);
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        AIAssistantComparableProductResponse response = new AIAssistantComparableProductResponse();
        response.setItems(null);

        when(chatAiAssistantService.getComparableProducts(request)).thenReturn(Result.success(response));

        AIAssistantComparableProductResponse resp = copilotService.getComparableProducts(request);
        assertNull(resp.getItems());
    }

    @Test
    public void contrastProductParamsTest() throws BizError {
        AIAssistantContrastProductParamsRequest request = new AIAssistantContrastProductParamsRequest();
        request.setItemCode("123456");
        request.setTargetItemCode("456789");
        request.setOrgId("JIM123");
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        Result<AIAssistantContrastProductParamsResponse> result = Result.success(null);
        when(chatAiAssistantService.contrastProductParams(request)).thenReturn(result);

        AIAssistantContrastProductParamsResponse resp = copilotService.contrastProductParams(request);
        assertNull(resp);
    }

    @Test
    public void getUserComparisonHistoryTest() throws BizError {
        CopilotComparisonHistoryRequest request = new CopilotComparisonHistoryRequest();
        request.setChannelId(1);
        request.setAreaId("ID");
        request.setUserId("123456");
        request.setLanguage("en-US");

        Result<AIAssistantProductComparisonHistoryResponse> result = Result.success(null);
        when(chatAiAssistantService.getUserComparisonHistory(request)).thenReturn(result);

        AIAssistantProductComparisonHistoryResponse resp = copilotService.getUserComparisonHistory(request);
        assertNull(resp);
    }

    @Test
    void testGetFeedbackTags_Success() throws BizError {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        AIAssistantFeedbackTagsResponse response = new AIAssistantFeedbackTagsResponse();

        Result<AIAssistantFeedbackTagsResponse> result = Result.success(response);

        when(chatAiAssistantService.getFeedbackTags(request)).thenReturn(result);

        AIAssistantFeedbackTagsResponse actualResponse = copilotService.getFeedbackTags(request);
        assertEquals(response, actualResponse);
    }

    @Test
    void testGetFeedbackTags_Exception() throws BizError {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        when(chatAiAssistantService.getFeedbackTags(request)).thenThrow(new RuntimeException("Service error"));

        assertThrows(RuntimeException.class, () -> {
            copilotService.getFeedbackTags(request);
        });
    }

    @Test
    void testGetFeedbackTags_NullResult() throws BizError {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        when(chatAiAssistantService.getFeedbackTags(request)).thenReturn(null);

        assertThrows(RuntimeException.class, () -> {
            copilotService.getFeedbackTags(request);
        });
    }

    @Test
    void testGetFeedbackTags_NonZeroCode() throws BizError {
        AIAssistantFeedbackTagsRequest request = new AIAssistantFeedbackTagsRequest();
        request.setChannelId(1);

        Result<AIAssistantFeedbackTagsResponse> result = Result.fail(ErrorCode.createOnce(Scopes.SCOPE_TAGGING, 2), "Error message");

        when(chatAiAssistantService.getFeedbackTags(request)).thenReturn(result);

        assertThrows(RuntimeException.class, () -> {
            copilotService.getFeedbackTags(request);
        });
    }
}
