package com.mi.info.intl.retail.intlretail.app.controller.i8n;

import com.mi.info.intl.retail.intlretail.app.controller.i18n.GetAreaDetailResp;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR> 董鑫儒
 * @Description
 * @Date 创建于 2025/8/6 21:23
 */
public class GetAreaDetailRespTest {
    @Test
    void testAreaDetailRespGetterSetter() {
        GetAreaDetailResp.AreaItem.Extend extend = new GetAreaDetailResp.AreaItem.Extend();
        extend.setTaxRate("10%");
        extend.setEmailMatch(".*@test.com");
        extend.setTelMatch("\\d{11}");
        extend.setDateFormat("yyyy-MM-dd");
        extend.setDateFormatJava("yyyy-MM-dd");
        extend.setDateTimeMinFormat("yyyy-MM-dd HH:mm");
        extend.setDateTimeMinFormatJava("yyyy-MM-dd HH:mm");
        extend.setDateTimeSecFormat("yyyy-MM-dd HH:mm:ss");
        extend.setDateTimeSecFormatJava("yyyy-MM-dd HH:mm:ss");
        extend.setCurrencyMinorUnit("2");
        extend.setUnicodeList("U+4E00-U+9FA5");

        assertEquals("10%", extend.getTaxRate());
        assertEquals(".*@test.com", extend.getEmailMatch());
        assertEquals("\\d{11}", extend.getTelMatch());
        assertEquals("yyyy-MM-dd", extend.getDateFormat());
        assertEquals("yyyy-MM-dd", extend.getDateFormatJava());
        assertEquals("yyyy-MM-dd HH:mm", extend.getDateTimeMinFormat());
        assertEquals("yyyy-MM-dd HH:mm", extend.getDateTimeMinFormatJava());
        assertEquals("yyyy-MM-dd HH:mm:ss", extend.getDateTimeSecFormat());
        assertEquals("yyyy-MM-dd HH:mm:ss", extend.getDateTimeSecFormatJava());
        assertEquals("2", extend.getCurrencyMinorUnit());
        assertEquals("U+4E00-U+9FA5", extend.getUnicodeList());

        GetAreaDetailResp.AreaItem areaItem = new GetAreaDetailResp.AreaItem();
        areaItem.setAreaId("CN");
        areaItem.setShortNameCn("中国");
        areaItem.setShortNameEn("China");
        areaItem.setFullNameEn("People's Republic of China");
        areaItem.setTimezone("Asia/Shanghai");
        areaItem.setThreeLetterId("CHN");
        areaItem.setDigitalCode(156);
        areaItem.setWmsCountryId(1L);
        areaItem.setCurrency("人民币");
        areaItem.setCurrencyCode("CNY");
        areaItem.setCurrencyDigits(2);
        areaItem.setCurrencyThousandSymbol(",");
        areaItem.setCurrencyDigitsSymbol(".");
        areaItem.setLocaleCode("zh_CN");
        areaItem.setPhonePrefix("+86");
        areaItem.setIdc("IDC1");
        areaItem.setRegion("华东");
        areaItem.setContinent("亚洲");
        areaItem.setStatus(1);
        areaItem.setExtend(extend);

        assertEquals("CN", areaItem.getAreaId());
        assertEquals("中国", areaItem.getShortNameCn());
        assertEquals("China", areaItem.getShortNameEn());
        assertEquals("People's Republic of China", areaItem.getFullNameEn());
        assertEquals("Asia/Shanghai", areaItem.getTimezone());
        assertEquals("CHN", areaItem.getThreeLetterId());
        assertEquals(156, areaItem.getDigitalCode());
        assertEquals(1L, areaItem.getWmsCountryId());
        assertEquals("人民币", areaItem.getCurrency());
        assertEquals("CNY", areaItem.getCurrencyCode());
        assertEquals(2, areaItem.getCurrencyDigits());
        assertEquals(",", areaItem.getCurrencyThousandSymbol());
        assertEquals(".", areaItem.getCurrencyDigitsSymbol());
        assertEquals("zh_CN", areaItem.getLocaleCode());
        assertEquals("+86", areaItem.getPhonePrefix());
        assertEquals("IDC1", areaItem.getIdc());
        assertEquals("华东", areaItem.getRegion());
        assertEquals("亚洲", areaItem.getContinent());
        assertEquals(1, areaItem.getStatus());
        assertEquals(extend, areaItem.getExtend());

        GetAreaDetailResp resp = new GetAreaDetailResp();
        resp.setAreaItemList(Arrays.asList(areaItem));
        assertNotNull(resp.getAreaItemList());
        assertEquals(1, resp.getAreaItemList().size());
        assertEquals(areaItem, resp.getAreaItemList().get(0));
    }

    @Test
    void testAllArgsConstructorAndBuilder() {
        GetAreaDetailResp.AreaItem.Extend extend = new GetAreaDetailResp.AreaItem.Extend(
                "10%", ".*@test.com", "\\d{11}", "yyyy-MM-dd", "yyyy-MM-dd",
                "yyyy-MM-dd HH:mm", "yyyy-MM-dd HH:mm", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss",
                "2", "U+4E00-U+9FA5"
        );

        GetAreaDetailResp.AreaItem areaItem = GetAreaDetailResp.AreaItem.builder()
                .areaId("US")
                .shortNameCn("美国")
                .shortNameEn("USA")
                .fullNameEn("United States of America")
                .timezone("America/New_York")
                .threeLetterId("USA")
                .digitalCode(840)
                .wmsCountryId(2L)
                .currency("美元")
                .currencyCode("USD")
                .currencyDigits(2)
                .currencyThousandSymbol(",")
                .currencyDigitsSymbol(".")
                .localeCode("en_US")
                .phonePrefix("+1")
                .idc("IDC2")
                .region("北美")
                .continent("美洲")
                .status(1)
                .extend(extend)
                .build();

        assertEquals("US", areaItem.getAreaId());
        assertEquals("美国", areaItem.getShortNameCn());
        assertEquals("USA", areaItem.getShortNameEn());
        assertEquals("United States of America", areaItem.getFullNameEn());
        assertEquals("America/New_York", areaItem.getTimezone());
        assertEquals("USA", areaItem.getThreeLetterId());
        assertEquals(840, areaItem.getDigitalCode());
        assertEquals(2L, areaItem.getWmsCountryId());
        assertEquals("美元", areaItem.getCurrency());
        assertEquals("USD", areaItem.getCurrencyCode());
        assertEquals(2, areaItem.getCurrencyDigits());
        assertEquals(",", areaItem.getCurrencyThousandSymbol());
        assertEquals(".", areaItem.getCurrencyDigitsSymbol());
        assertEquals("en_US", areaItem.getLocaleCode());
        assertEquals("+1", areaItem.getPhonePrefix());
        assertEquals("IDC2", areaItem.getIdc());
        assertEquals("北美", areaItem.getRegion());
        assertEquals("美洲", areaItem.getContinent());
        assertEquals(1, areaItem.getStatus());
        assertEquals(extend, areaItem.getExtend());

        GetAreaDetailResp resp = GetAreaDetailResp.builder()
                .areaItemList(Arrays.asList(areaItem))
                .build();

        assertNotNull(resp.getAreaItemList());
        assertEquals(1, resp.getAreaItemList().size());
        assertEquals(areaItem, resp.getAreaItemList().get(0));
    }
}
