package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.proxy.TaskCenterProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.QueryCentralBrainCardsRequest;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.QueryCentralBrainListRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LstProxyControllerTest {

    @Mock
    private TaskCenterProxyService taskCenterProxyService;

    @InjectMocks
    private LstProxyController lstProxyController;

    private AutoCloseable closeable;

    @BeforeEach
    void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void tearDown() throws Exception {
        closeable.close();
    }
    
    @Test
    void queryCentralBrainList_ShouldReturnSuccessResponse() {
        // 准备测试数据
        QueryCentralBrainListRequest request = new QueryCentralBrainListRequest();
        Object mockResult = new Object();
        
        // 配置mock行为
        when(taskCenterProxyService.queryCentralBrainList(any(QueryCentralBrainListRequest.class)))
            .thenReturn(mockResult);

        // 执行测试
        CommonApiResponse<Object> response = lstProxyController.queryCentralBrainList(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(mockResult, response.getData());
        
        // 验证交互
        verify(taskCenterProxyService).queryCentralBrainList(request);
    }

    @Test
    void queryCentralBrainCards_ShouldReturnSuccessResponse() {
        // 准备测试数据
        QueryCentralBrainCardsRequest request = new QueryCentralBrainCardsRequest();
        Object mockResult = new Object();
        
        // 配置mock行为
        when(taskCenterProxyService.queryCentralBrainCards(any(QueryCentralBrainCardsRequest.class)))
            .thenReturn(mockResult);

        // 执行测试
        CommonApiResponse<Object> response = lstProxyController.queryCentralBrainCards(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("ok", response.getMessage());
        assertEquals(mockResult, response.getData());
        
        // 验证交互
        verify(taskCenterProxyService).queryCentralBrainCards(request);
    }
}