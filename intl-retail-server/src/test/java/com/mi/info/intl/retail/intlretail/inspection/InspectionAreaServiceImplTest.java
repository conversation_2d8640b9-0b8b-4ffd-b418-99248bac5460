package com.mi.info.intl.retail.intlretail.inspection;

import com.mi.info.intl.retail.intlretail.app.StartApplication;
import com.mi.info.intl.retail.intlretail.service.api.request.PersonAreaRequest;
import com.mi.info.intl.retail.intlretail.service.app.inspection.impl.InspectionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;

@ActiveProfiles("dev")
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {StartApplication.class})
@Slf4j
public class InspectionAreaServiceImplTest {

    @Resource
    private InspectionServiceImpl inspectionServiceImpl;
    @Test
    public void testGetPersonAreaInfo_Success(){

        PersonAreaRequest request = new PersonAreaRequest();

        request.setMiIdList(Arrays.asList(3150462080L));

        inspectionServiceImpl.getPersonAreaInfo(request);

    }
}
