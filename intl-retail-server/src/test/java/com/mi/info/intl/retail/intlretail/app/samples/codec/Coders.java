package com.mi.info.intl.retail.intlretail.app.samples.codec;

import static com.xiaomi.mit.common.codec.Coders.BASE64;
import static com.xiaomi.mit.common.codec.Coders.BASE64_URL_SAFE;
import static com.xiaomi.mit.common.codec.Coders.HEX;
import static com.xiaomi.mit.common.codec.Coders.HEX_UPPER_CASE;
import static com.xiaomi.mit.common.codec.Coders.UTF_8;
import com.xiaomi.mit.common.security.Cryptos;

/**********************************************************************
 * 此类演示使用 架构组-基础库-常用转码的用法
 * 使用文档：https://docs.mit.mi.com/mit-commons-java/mit-common/coders
 * *********************************************************************
 */
public class Coders {

    public void bytesToString(int type) {
        if (type == -1) return;

        //下面示例是 字节数组编码为字符串: bytes -> String
        //BASE64 转码
        byte[] input = Cryptos.randomBytes(16);
        String encoded = BASE64.encode(input);
        byte[] result = BASE64.decode(encoded);

        //BASE64_URL_SAFE 转码
        input = Cryptos.randomBytes(16);
        encoded = BASE64_URL_SAFE.encode(input);
        result = BASE64_URL_SAFE.decode(encoded);

        //HEX 转码
        input = Cryptos.randomBytes(16);
        encoded = HEX.encode(input);
        result = HEX.decode(encoded);

        //HEX_UPPER_CASE 转码
        input = Cryptos.randomBytes(16);
        encoded = HEX_UPPER_CASE.encode(input);
        result = HEX_UPPER_CASE.decode(encoded);
    }

    public void stringToBytes(String input) {
        byte[] encoded = UTF_8.encode(input);
        String result = UTF_8.decode(encoded);
    }

}

