package com.mi.info.intl.retail.intlretail.app.position;

import com.mi.info.intl.retail.intlretail.app.TestApplication;
import com.mi.info.intl.retail.org.infra.http.MaindataHttp;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionImage;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionExtension;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionResponse;

// @SpringBootTest(classes = TestApplication.class)
public class MaindataHttpTest {
    @Autowired
    private MaindataHttp maindataHttp;

    // @Test
    public void testeditPositionInfo() throws Exception {
        // 构造PositionImage
        PositionImage positionImage = new PositionImage();
        positionImage.setStoreGate(java.util.Collections.singletonList("https://smc-india.staging-cnbj2.mi-fds.com/smc-india/1752736369_5885090f-98a2-46d9-a1b4-fd1c43e4b3f8_A874F53A-6D46-41BA-A165-06636189E9A9.jpg"));
        positionImage.setPositionLandingPicture(java.util.Collections.singletonList("https://smc-india.staging-cnbj2.mi-fds.com/smc-india/1752736373_4da56235-177f-4eae-a356-be40564ea33a_63E7BA78-1CE0-405C-8196-7A6B24E0F452.jpg"));
        positionImage.setPositionDisplay(java.util.Collections.singletonList("https://smc-india.staging-cnbj2.mi-fds.com/smc-india/1752736417_af2311a0-5f2e-471e-8caa-c8aea2de5645_12836CD5-0BA6-441C-B8C4-************.jpg"));
        positionImage.setFurniturePicture(java.util.Collections.singletonList("https://smc-india.staging-cnbj2.mi-fds.com/smc-india/1752736417_af2311a0-5f2e-471e-8caa-c8aea2de5645_12836CD5-0BA6-441C-B8C4-************.jpg"));

        // 构造PositionExtension
        PositionExtension positionExtension = new PositionExtension();
        positionExtension.setPositionImage(positionImage);

        // 构造PositionRequest
        PositionRequest positionRequest = new PositionRequest();
        positionRequest.setPositionCode("CID001915");
        positionRequest.setOperator("2105040167");
        positionRequest.setInspectionStatus(1);

        java.util.List<PositionRequest> reqList = java.util.Collections.singletonList(positionRequest);
        PositionResponse response = maindataHttp.editPositionInfo(reqList);
        System.out.println("PositionResponse: code=" + response.getCode() + ", message=" + response.getMessage());
        org.junit.jupiter.api.Assertions.assertNotNull(response, "response 不能为空");
        org.junit.jupiter.api.Assertions.assertEquals(0, response.getCode(), "code 应该为0");
        // 可根据实际业务断言
        // Assertions.assertNotNull(response);
    }
}
