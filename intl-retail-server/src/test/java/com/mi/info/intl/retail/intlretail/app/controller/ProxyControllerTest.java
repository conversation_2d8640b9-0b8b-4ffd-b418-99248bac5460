package com.mi.info.intl.retail.intlretail.app.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ProxyController的单元测试，覆盖正常流程、异常分支等。
 */
@ExtendWith(MockitoExtension.class)
class ProxyControllerTest {

    @InjectMocks
    private ProxyController proxyController;

    @Mock
    private RmsProxyService rmsProxyService;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        // 每个测试开始前重置SecurityContextHolder
        SecurityContextHolder.clearContext();
    }

    @Test
    void testRmsTokenQuery_Success() throws Exception {
        // 准备测试数据
        String path = "new_GetUserBaseData";
        String requestBody = "{\"UserAccount\":\"<EMAIL>\",\"Longitude\":\"116.********\",\"Latitude\":\"40.********\",\"StoreId\":null}";
        String expectedResponse = "{\"UserId\":\"523199eb-f672-ec11-8943-0022485a0a5c\",\"JobValue\":*********,\"JobTitle\":\"Supervisor\",\"EmployeeCode\":\"MIE2022110130876\",\"EnglishName\":\"zhulin\",\"UserAccount\":\"<EMAIL>\",\"IsAgreedPrivacy\":false,\"IsAgreedNotification\":false,\"UserCountryId\":\"d7d10c0c-c215-eb11-a813-000d3aa3eed3\",\"UserCountryCode\":\"3812\",\"UserCountryShortCode\":\"MY\",\"CurrencyCode\":\"EUR\",\"PrivacyId\":null,\"EnvironmentFlag\":null,\"IsOfflineIMEI\":false,\"IsOffline\":false,\"UserLanguageId\":\"9f2ce2b5-a416-eb11-a813-000d3aa3e835\",\"UserLanguageCode\":\"zh-CN\",\"StoreRetailerId\":null,\"DailyHour\":0,\"StoreId\":\"faeb92f2-d2a9-ec11-9840-000d3a0850b9\",\"StoreName\":\"BANANA (MUEANG LAMPANG BR:1547)\",\"StoreCode\":\"MITHP07768\",\"Longitude\":\"116.3109\",\"Latitude\":\"40.046896\",\"StoreCount\":38,\"StoreAddress\":\"938 HUAWIANG, MUEANG, LAMPANG\",\"StoreGrade\":*********,\"StoreType\":*********,\"ChannelType\":0,\"IsPromotionStore\":false,\"StoreAccountId\":\"dca2c9a4-d415-eb11-a813-000d3aa3e917\",\"StoreCountryId\":\"d7d10c0c-c215-eb11-a813-000d3aa3eed3\",\"IsBrand\":false,\"SignRuleId\":\"46178d1f-eb6f-ef11-a670-0022485a5afb\",\"IsNormal\":true,\"NormalPhotoRequired\":false,\"NormalNoteRequired\":false,\"Effectiveduration\":-1.0,\"Effectiverange\":200.**********,\"IsAbnormalPhoto\":true,\"AbNormalPhotoRequired\":false,\"IsAbnormalNote\":false,\"AbNormalNoteRequired\":false,\"SignRecordId\":\"eac519ff-9ef2-4bf2-8257-b6bfb6ed60d2\",\"SigninTime\":\"2025/05/21 14:04:32\",\"SignoutTime\":\"2025/05/21 14:17:50\",\"Menu\":null,\"MiIDVirtual\":\"**********\"}";

        // 设置认证token
        Jwt jwt = mock(Jwt.class);
        when(jwt.getTokenValue()).thenReturn("mock-token-value");
        SecurityContextHolder.getContext().setAuthentication(new JwtAuthenticationToken(jwt));

        // Mock ObjectMapper的行为
        when(objectMapper.writeValueAsString(any())).thenReturn(requestBody);

        // Mock RmsProxyService的行为
        when(rmsProxyService.requestByUserToken(
                argThat(arg -> arg != null && arg.equals("/api/data/v9.2/" + path)),
                argThat(arg -> arg != null && arg.equals(requestBody)),
                argThat(arg -> arg != null && arg.equals("mock-token-value")),
                argThat(arg -> arg != null && arg.equals("POST"))
        )).thenReturn(expectedResponse);

        // 执行测试
        CommonApiResponse<Object> response = proxyController.rmsTokenQuery(requestBody, path);

        // 验证结果
        assertNotNull(response);
        verify(rmsProxyService).requestByUserToken(
                eq("/api/data/v9.2/" + path),
                eq(requestBody),
                eq("mock-token-value"),
                eq("POST")
        );
    }

    @Test
    void testRmsTokenQuery_TokenNull() {
        // SecurityContextHolder已在setUp中清除，无需额外设置

        // 执行测试，期望抛出IllegalArgumentException
        assertThrows(IllegalArgumentException.class, () ->
                proxyController.rmsTokenQuery("{}", "test/path")
        );
    }

    @Test
    void testRmsTokenAndTypeQuery_TokenNull() {
        // SecurityContextHolder已在setUp中清除，无需额外设置

        // 执行测试，期望抛出IllegalArgumentException
        assertThrows(IllegalArgumentException.class, () ->
                proxyController.rmsTokenAndTypeQuery("{}", "test/path", "test_type")
        );
    }


    @Test
    void testRmsTokenUserDataQuery() throws JsonProcessingException {
        //准备测试数据

        // Mock request body
        String requestBody = "{\"UserAccount\":\"<EMAIL>\",\"Longitude\":\"116.********\",\"Latitude\":\"40.********\",\"StoreId\":null}";
        // Mock RmsProxyService response
        String expectedResponse = "{\"UserId\":\"523199eb-f672-ec11-8943-0022485a0a5c\",\"JobValue\":*********,\"JobTitle\":\"Supervisor\",\"EmployeeCode\":\"MIE2022110130876\",\"EnglishName\":\"zhulin\",\"UserAccount\":\"<EMAIL>\",\"IsAgreedPrivacy\":false,\"IsAgreedNotification\":false,\"UserCountryId\":\"d7d10c0c-c215-eb11-a813-000d3aa3eed3\",\"UserCountryCode\":\"3812\",\"UserCountryShortCode\":\"MY\",\"CurrencyCode\":\"EUR\",\"PrivacyId\":null,\"EnvironmentFlag\":null,\"IsOfflineIMEI\":false,\"IsOffline\":false,\"UserLanguageId\":\"9f2ce2b5-a416-eb11-a813-000d3aa3e835\",\"UserLanguageCode\":\"zh-CN\",\"StoreRetailerId\":null,\"DailyHour\":0,\"StoreId\":\"faeb92f2-d2a9-ec11-9840-000d3a0850b9\",\"StoreName\":\"BANANA (MUEANG LAMPANG BR:1547)\",\"StoreCode\":\"MITHP07768\",\"Longitude\":\"116.3109\",\"Latitude\":\"40.046896\",\"StoreCount\":38,\"StoreAddress\":\"938 HUAWIANG, MUEANG, LAMPANG\",\"StoreGrade\":*********,\"StoreType\":*********,\"ChannelType\":0,\"IsPromotionStore\":false,\"StoreAccountId\":\"dca2c9a4-d415-eb11-a813-000d3aa3e917\",\"StoreCountryId\":\"d7d10c0c-c215-eb11-a813-000d3aa3eed3\",\"IsBrand\":false,\"SignRuleId\":\"46178d1f-eb6f-ef11-a670-0022485a5afb\",\"IsNormal\":true,\"NormalPhotoRequired\":false,\"NormalNoteRequired\":false,\"Effectiveduration\":-1.0,\"Effectiverange\":200.**********,\"IsAbnormalPhoto\":true,\"AbNormalPhotoRequired\":false,\"IsAbnormalNote\":false,\"AbNormalNoteRequired\":false,\"SignRecordId\":\"eac519ff-9ef2-4bf2-8257-b6bfb6ed60d2\",\"SigninTime\":\"2025/05/21 14:04:32\",\"SignoutTime\":\"2025/05/21 14:17:50\",\"Menu\":null,\"MiIDVirtual\":\"**********\"}";
        // 设置认证token
        Jwt jwt = mock(Jwt.class);
        when(jwt.getTokenValue()).thenReturn("mock-token-value");
        SecurityContextHolder.getContext().setAuthentication(new JwtAuthenticationToken(jwt));
        // Mock ObjectMapper的行为
        when(objectMapper.writeValueAsString(any())).thenReturn(requestBody);
        // Mock RmsProxyService的行为
        when(rmsProxyService.requestByUserToken(
                argThat(arg -> arg != null && arg.equals("/api/data/v9.2/new_GetUserBaseData")),
                argThat(arg -> arg != null && arg.equals(requestBody)),
                argThat(arg -> arg != null && arg.equals("mock-token-value")),
                argThat(arg -> arg != null && arg.equals("POST"))
        )).thenReturn(expectedResponse);

        // 执行测试
        CommonApiResponse<Object> response = proxyController.rmsTokenUserDataQuery(requestBody);

        // 验证结果
        assertNotNull(response);
        verify(rmsProxyService).requestByUserToken(
                eq("/api/data/v9.2/new_GetUserBaseData"),
                eq(requestBody),
                eq("mock-token-value"),
                eq("POST")
        );

    }


}
