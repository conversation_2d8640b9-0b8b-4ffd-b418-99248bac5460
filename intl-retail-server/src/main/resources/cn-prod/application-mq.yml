rocketmq:
  name-server: alsgp0-rocketmq.namesrv.api.xiaomi.net:9876
  producer:
    group: overseas_retail_rms_consumer_group
#    access-key@kc-sid: india-sales.g
#    access-key: GCArVgNN6cw9UuoNO9MpQsCjpKAJ8WJkQdklbXp9JVhMgxgS4F3gwzYNT8+mSCyOseGW33cBGBCDcHZGp/FG+JEgyxDKA8RtGBRSf7iFrTco/wiMQDxHuyu0PpvxwQA=
#    secret-key@kc-sid: india-sales.g  # sk使用key center 加密处理，原文需要到融合云团队管理中查看。
#    secret-key: GDCDegNPMDFj8TZFFA/E7vJGQjHW5T73r4VfoYRb1lpcN+d1OalPYjEmsiXTkpM1qKQYEmVmhBs/hE7diJk0oXhv0Q1VARgQVMWshVlHSBiDh0nRyPLRlhgUy+a8m6+4nCNN9c4o/keHARd9s3kA
  push-consumer:
    group: overseas_retail_rms_consumer_group
#    access-key@kc-sid: india-sales.g
#    access-key: GCArVgNN6cw9UuoNO9MpQsCjpKAJ8WJkQdklbXp9JVhMgxgS4F3gwzYNT8+mSCyOseGW33cBGBCDcHZGp/FG+JEgyxDKA8RtGBRSf7iFrTco/wiMQDxHuyu0PpvxwQA=
#    secret-key@kc-sid: india-sales.g  # sk使用key center 加密处理，原文需要到融合云团队管理中查看。
#    secret-key: GDCDegNPMDFj8TZFFA/E7vJGQjHW5T73r4VfoYRb1lpcN+d1OalPYjEmsiXTkpM1qKQYEmVmhBs/hE7diJk0oXhv0Q1VARgQVMWshVlHSBiDh0nRyPLRlhgUy+a8m6+4nCNN9c4o/keHARd9s3kA
demo:
  rocketmq:
    topic: india_sales_smc_retail_universe_topic

intl-retail:
  rocketmq:
    store:
      group: overseas_retail_rms_consumer_group
      topic: store_digitization_pro
    position:
      group: overseas_retail_rms_position_consumer_group
      topic: xmmaindata_position_area_message_online
    demo:
      group: overseas_retail_rms_demo_consumer_group
      topic: india_sales_smc_retail_universe_topic
    enabled: false