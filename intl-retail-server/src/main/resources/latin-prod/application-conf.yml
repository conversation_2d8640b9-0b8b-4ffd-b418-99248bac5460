env: prod
miwork:
  alarm:
    groupId:
      p0: oc_5447f12341241c7bc363a7e85112ccdb
      p1: oc_bd49254d614d7fb6af3b43b9fb1118f0
      p2: oc_2ac1eb9138eb642cf45dc27ea9d956f8
# 加密配置
itsm:
  key@kc-sid: india-sales.g
  key: GCDXn2luoETagCTfAZPMTrMlOhEQCPeKDugTc-Zrx60SKRgSyaaFHeV0QkOBMmp9EOlpJ70BGBBKc0w93WtDjZKhNy23JP-tGBTlw3NIgfu_svIfs0czxr_hud_N5QA
feishu:
  appId: cli_a60d6cea8478d063
  appSecret@kc-sid: india-sales.g
  appSecret: GDDNlEYkzhoTik1dVyPqlcWPXVuogQbNfUiP2XLy9yAcGh5KLYgoMC1c6EzmjJeTYzAYEqJPvtpFFkOqmbN025peIFY0/xgQcfNLEUowQH+GXGG8jS8HjBgUOfevm5PGoheSWZsXTgWMPCbIiUUA
  getGroupListUrl: https://open.f.mioffice.cn/open-apis/im/v1/chats?page_token=
  getUnionidsUrl: https://open.f.mioffice.cn/open-apis/contact/v3/users/batch_get_id
  sendMessageGroupUrl: https://open.f.mioffice.cn/open-apis/message/v4/send/
  batchSendMessageUrl: https://open.f.mioffice.cn/open-apis/message/v4/batch_send/
  tokenUrl: https://open.f.mioffice.cn/open-apis/auth/v3/tenant_access_token/internal
  messageUrl: https://open.f.mioffice.cn/open-apis/im/v1/messages