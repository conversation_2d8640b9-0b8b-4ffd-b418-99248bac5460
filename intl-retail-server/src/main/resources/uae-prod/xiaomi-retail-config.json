[{"short_name_cn": "阿富汗", "short_name_en": "Afghanistan", "full_name_en": "the Islamic Republic of Afghanistan", "area_id": "AF", "timezone": "", "three_letter_id": "AFG", "digital_code": 4, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "93", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "奥兰群岛", "short_name_en": "Aland Islands", "full_name_en": "", "area_id": "AX", "timezone": "", "three_letter_id": "ALA", "digital_code": 248, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "358 18", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "阿尔巴尼亚", "short_name_en": "Albania", "full_name_en": "the Republic of Albania", "area_id": "AL", "timezone": "Europe/Tirane", "three_letter_id": "ALB", "digital_code": 8, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "355", "idc": "sg", "region": "eu", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "阿尔及利亚", "short_name_en": "Algeria", "full_name_en": "the People's Democratic Republic of Algeria", "area_id": "DZ", "timezone": "Africa/Algiers", "three_letter_id": "DZA", "digital_code": 12, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "213", "idc": "", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "美属萨摩亚", "short_name_en": "American Samoa", "full_name_en": "", "area_id": "AS", "timezone": "", "three_letter_id": "ASM", "digital_code": 16, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-684", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "安道尔", "short_name_en": "Andorra", "full_name_en": "the Principality of Andorra", "area_id": "AD", "timezone": "", "three_letter_id": "AND", "digital_code": 20, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "376", "idc": "ams", "region": "eu", "continent": "Europe", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "安哥拉", "short_name_en": "Angola", "full_name_en": "the Republic of Angola", "area_id": "AO", "timezone": "Africa/Luanda", "three_letter_id": "AGO", "digital_code": 24, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "244", "idc": "", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "安圭拉", "short_name_en": "<PERSON><PERSON><PERSON>", "full_name_en": "", "area_id": "AI", "timezone": "", "three_letter_id": "AIA", "digital_code": 660, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-264", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "南极洲", "short_name_en": "Antarctica ", "full_name_en": "", "area_id": "AQ", "timezone": "", "three_letter_id": "ATA", "digital_code": 10, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "672", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "安提瓜和巴布达", "short_name_en": "Antigua and Barbuda", "full_name_en": "", "area_id": "AG", "timezone": "", "three_letter_id": "ATG", "digital_code": 28, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-268", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "阿根廷", "short_name_en": "Argentina", "full_name_en": "the Argentine Republic", "area_id": "AR", "timezone": "America/Argentina/Buenos_Aires", "three_letter_id": "ARG", "digital_code": 32, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "es-AR", "phone_prefix": "54", "idc": "sg", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "亚美尼亚", "short_name_en": "Armenia", "full_name_en": "the Republic of Armenia", "area_id": "AM", "timezone": "Asia/Yerevan", "three_letter_id": "ARM", "digital_code": 51, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "374", "idc": "", "region": "sg", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "阿鲁巴", "short_name_en": "Aruba", "full_name_en": "", "area_id": "AW", "timezone": "", "three_letter_id": "ABW", "digital_code": 533, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "297", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "澳大利亚", "short_name_en": "Australia", "full_name_en": "The Commonwealth of Australia", "area_id": "AU", "timezone": "Australia/Sydney", "three_letter_id": "AUS", "digital_code": 36, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-AU", "phone_prefix": "61", "idc": "sg", "region": "sg", "continent": "Oceania", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "奥地利", "short_name_en": "Austria", "full_name_en": "the Republic of Austria", "area_id": "AT", "timezone": "Europe/Vienna", "three_letter_id": "AUT", "digital_code": 40, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "de-AT", "phone_prefix": "43", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "阿塞拜疆", "short_name_en": "Azerbaijan", "full_name_en": "the Republic of Azerbaijan", "area_id": "AZ", "timezone": "Asia/Baku", "three_letter_id": "AZE", "digital_code": 31, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "994", "idc": "sg", "region": "eu", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "巴哈马", "short_name_en": "Bahamas (The)", "full_name_en": "the Commonwealth of The Bahamas", "area_id": "BS", "timezone": "", "three_letter_id": "BHS", "digital_code": 44, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-242", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "巴林", "short_name_en": "Bahrain", "full_name_en": "the Kingdom of Bahrain", "area_id": "BH", "timezone": "Asia/Bahrain", "three_letter_id": "BHR", "digital_code": 48, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "973", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "孟加拉国", "short_name_en": "Bangladesh", "full_name_en": "the People's Republic of Bangladesh", "area_id": "BD", "timezone": "Asia/Dhaka", "three_letter_id": "BGD", "digital_code": 50, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-BD", "phone_prefix": "880", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "巴巴多斯", "short_name_en": "Barbados", "full_name_en": "", "area_id": "BB", "timezone": "", "three_letter_id": "BRB", "digital_code": 52, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-246", "idc": "", "continent": "South America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "白俄罗斯", "short_name_en": "Belarus", "full_name_en": "the Republic of Belarus", "area_id": "BY", "timezone": "Europe/Minsk", "three_letter_id": "BLR", "digital_code": 112, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "ru-BY", "phone_prefix": "375", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "比利时", "short_name_en": "Belgium", "full_name_en": "the Kingdom of Belgium", "area_id": "BE", "timezone": "Europe/Brussels", "three_letter_id": "BEL", "digital_code": 56, "wms_country_id": *********, "currency": "€%s", "currency_code": "EUR", "currency_digits": 2, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "fr-Be", "phone_prefix": "32", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "伯利兹", "short_name_en": "Belize", "full_name_en": "", "area_id": "BZ", "timezone": "", "three_letter_id": "BLZ", "digital_code": 84, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "501", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "贝宁", "short_name_en": "Benin", "full_name_en": "the Republic of Benin", "area_id": "BJ", "timezone": "", "three_letter_id": "BEN", "digital_code": 204, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "229", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "百慕大", "short_name_en": "Bermuda", "full_name_en": "", "area_id": "BM", "timezone": "", "three_letter_id": "BMU", "digital_code": 60, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-441", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "不丹", "short_name_en": "Bhutan", "full_name_en": "the Kingdom of Bhutan", "area_id": "BT", "timezone": "", "three_letter_id": "BTN", "digital_code": 64, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "975", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "玻利维亚", "short_name_en": "Bolivia (Plurinational State of)", "full_name_en": "the Republic of Bolivia", "area_id": "BO", "timezone": "America/La_Paz", "three_letter_id": "BOL", "digital_code": 68, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "591", "idc": "", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "波黑", "short_name_en": "Bosnia and Herzegovina", "full_name_en": "", "area_id": "BA", "timezone": "Europe/Sarajevo", "three_letter_id": "BIH", "digital_code": 70, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "387", "idc": "", "region": "eu", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "博茨瓦纳", "short_name_en": "Botswana", "full_name_en": "the Republic of Botswana", "area_id": "BW", "timezone": "", "three_letter_id": "BWA", "digital_code": 72, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "267", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "布维岛", "short_name_en": "Bouvet Island", "full_name_en": "", "area_id": "BV", "timezone": "", "three_letter_id": "BVT", "digital_code": 74, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "47", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "巴西", "short_name_en": "Brazil", "full_name_en": "the Federative Republic of Brazil", "area_id": "BR", "timezone": "America/Sao_Paulo", "three_letter_id": "BRA", "digital_code": 76, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "pt-BR", "phone_prefix": "55", "idc": "sg", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "英属印度洋领地", "short_name_en": "British Indian Ocean Territory (the)", "full_name_en": "", "area_id": "IO", "timezone": "", "three_letter_id": "IOT", "digital_code": 86, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "246", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "文莱", "short_name_en": "Brunei Darussalam", "full_name_en": "", "area_id": "BN", "timezone": "Asia/Brunei", "three_letter_id": "BRN", "digital_code": 96, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "673", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "保加利亚", "short_name_en": "Bulgaria", "full_name_en": "the Republic of Bulgaria", "area_id": "BG", "timezone": "Europe/Sofia", "three_letter_id": "BGR", "digital_code": 100, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "bg-BG", "phone_prefix": "359", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "布基纳法索", "short_name_en": "Burkina Faso", "full_name_en": "Burkina Faso", "area_id": "BF", "timezone": "", "three_letter_id": "BFA", "digital_code": 854, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "226", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "布隆迪", "short_name_en": "Burundi", "full_name_en": "the Republic of Burundi", "area_id": "BI", "timezone": "", "three_letter_id": "BDI", "digital_code": 108, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "257", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "柬埔寨", "short_name_en": "Cambodia", "full_name_en": "the Kingdom of Cambodia", "area_id": "KH", "timezone": "Asia/Phnom_Penh", "three_letter_id": "KHM", "digital_code": 116, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "km-KH", "phone_prefix": "855", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "喀麦隆", "short_name_en": "Cameroon", "full_name_en": "the Republic of Cameroon", "area_id": "CM", "timezone": "Africa/Douala", "three_letter_id": "CMR", "digital_code": 120, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "237", "idc": "", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "加拿大", "short_name_en": "Canada", "full_name_en": "Canada", "area_id": "CA", "timezone": "America/Toronto", "three_letter_id": "CAN", "digital_code": 124, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-CA", "phone_prefix": "1", "idc": "sg", "region": "sg", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "佛得角", "short_name_en": "Cape Verde", "full_name_en": "the Republic of Cape Verde", "area_id": "CV", "timezone": "", "three_letter_id": "CPV", "digital_code": 132, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "238", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "开曼群岛", "short_name_en": "Cayman Islands (the)", "full_name_en": "", "area_id": "KY", "timezone": "", "three_letter_id": "CYM", "digital_code": 136, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-345", "idc": "", "continent": "South America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "中非", "short_name_en": "Central African Republic (the)", "full_name_en": "the Central African Republic", "area_id": "CF", "timezone": "", "three_letter_id": "CAF", "digital_code": 140, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "236", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "乍得", "short_name_en": "Chad", "full_name_en": "the Republic of Chad", "area_id": "TD", "timezone": "", "three_letter_id": "TCD", "digital_code": 148, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "235", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "智利", "short_name_en": "Chile", "full_name_en": "the Republic of Chile", "area_id": "CL", "timezone": "America/Santiago", "three_letter_id": "CHL", "digital_code": 152, "wms_country_id": *********, "currency": "$%s", "currency_code": "CLP", "currency_digits": 2, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "es-CL", "phone_prefix": "56", "idc": "sg", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "中国", "short_name_en": "China", "full_name_en": "the People's Republic of China", "area_id": "CN", "timezone": "Asia/Shanghai", "three_letter_id": "CHN", "digital_code": 156, "wms_country_id": 0, "currency": "￥%s", "currency_code": "CNY", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "zh-CN", "phone_prefix": "86", "idc": "cn", "region": "cn", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Han\", \"Latin\"]", "tax_rate": "0", "tel_match": "^1[3456789][0-9]{9}$", "date_format": "yyyy-MM-dd", "date_time_min_format": "yyyy-MM-dd HH:mm", "date_time_sec_format": "yyyy-MM-dd HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "圣诞岛", "short_name_en": "Christmas Island", "full_name_en": "", "area_id": "CX", "timezone": "", "three_letter_id": "CXR", "digital_code": 162, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "61 8914", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "科科斯（基林）群岛", "short_name_en": "Cocos (Keeling) Islands (the)", "full_name_en": "", "area_id": "CC", "timezone": "", "three_letter_id": "CCK", "digital_code": 166, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "61 891", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "哥伦比亚", "short_name_en": "Colombia", "full_name_en": "the Republic of Colombia", "area_id": "CO", "timezone": "America/Bogota", "three_letter_id": "COL", "digital_code": 170, "wms_country_id": *********, "currency": "$%s", "currency_code": "COP", "currency_digits": 0, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "es-CO", "phone_prefix": "57", "idc": "sg", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "1"}}, {"short_name_cn": "科摩罗", "short_name_en": "Comoros", "full_name_en": "the Union of the Comoros", "area_id": "KM", "timezone": "", "three_letter_id": "COM", "digital_code": 174, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "269", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "刚果（布）", "short_name_en": "Congo", "full_name_en": "the Republic of the Congo", "area_id": "CG", "timezone": "", "three_letter_id": "COG", "digital_code": 178, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "242", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "刚果（金）", "short_name_en": "Congo (the Democratic Republic of the)", "full_name_en": "the Democratic Republic of the Congo", "area_id": "CD", "timezone": "", "three_letter_id": "COD", "digital_code": 180, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "243", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "库克群岛", "short_name_en": "Cook Islands (the)", "full_name_en": "", "area_id": "CK", "timezone": "", "three_letter_id": "COK", "digital_code": 184, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "682", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "哥斯达黎加", "short_name_en": "Costa Rica", "full_name_en": "the Republic of Costa Rica", "area_id": "CR", "timezone": "America/Costa_Rica", "three_letter_id": "CRI", "digital_code": 188, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "506", "idc": "", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "科特迪瓦", "short_name_en": "Republic of Ivory Coast", "full_name_en": "the Republic of Côte d'Ivoire", "area_id": "CI", "timezone": "Africa/Abidjan", "three_letter_id": "CIV", "digital_code": 384, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "fr-CI", "phone_prefix": "225", "idc": "sg", "region": "eu", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "克罗地亚", "short_name_en": "Croatia", "full_name_en": "the Republic of Croatia", "area_id": "HR", "timezone": "Europe/Zagreb", "three_letter_id": "HRV", "digital_code": 191, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "hr-HR", "phone_prefix": "385", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "古巴", "short_name_en": "Cuba", "full_name_en": "the Republic of Cuba", "area_id": "CU", "timezone": "", "three_letter_id": "CUB", "digital_code": 192, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "53", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "塞浦路斯", "short_name_en": "Cyprus", "full_name_en": "the Republic of Cyprus", "area_id": "CY", "timezone": "Asia/Nicosia", "three_letter_id": "CYP", "digital_code": 196, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "357", "idc": "sg", "region": "eu", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "捷克", "short_name_en": "Czech Republic", "full_name_en": "the Czech Republic", "area_id": "CZ", "timezone": "Europe/Prague", "three_letter_id": "CZE", "digital_code": 203, "wms_country_id": *********, "currency": "Kč%s", "currency_code": "CZK", "currency_digits": 0, "currency_thousand_symbol": "%20", "currency_digits_symbol": ",", "locale_code": "cs-CZ", "phone_prefix": "420", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9}$", "date_format": "dd.<PERSON><PERSON><PERSON>yyyy", "date_time_min_format": "dd.MM.yyyy HH:mm", "date_time_sec_format": "dd.MM.yyyy HH:mm:ss", "currency_minor_unit": "1"}}, {"short_name_cn": "丹麦", "short_name_en": "Denmark", "full_name_en": "the Kingdom of Denmark", "area_id": "DK", "timezone": "Europe/Copenhagen", "three_letter_id": "DNK", "digital_code": 208, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "da-DK", "phone_prefix": "45", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "吉布提", "short_name_en": "Djibouti", "full_name_en": "the Republic of Djibouti", "area_id": "DJ", "timezone": "", "three_letter_id": "DJI", "digital_code": 262, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "253", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "多米尼克", "short_name_en": "Dominica", "full_name_en": "the Commonwealth of Dominica", "area_id": "DM", "timezone": "", "three_letter_id": "DMA", "digital_code": 212, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-767", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "多米尼加", "short_name_en": "Dominican Republic", "full_name_en": "the Dominican Republic", "area_id": "DO", "timezone": "America/Santo_Domingo", "three_letter_id": "DOM", "digital_code": 214, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-809", "idc": "", "region": "sg", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "厄瓜多尔", "short_name_en": "Ecuador", "full_name_en": "the Republic of Ecuador", "area_id": "EC", "timezone": "America/Guayaquil", "three_letter_id": "ECU", "digital_code": 218, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "es-EC", "phone_prefix": "593", "idc": "sg", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "埃及", "short_name_en": "Egypt", "full_name_en": "the Arab Republic of Egypt", "area_id": "EG", "timezone": "Africa/Cairo", "three_letter_id": "EGY", "digital_code": 818, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "ar-EG", "phone_prefix": "20", "idc": "sg", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "萨尔瓦多", "short_name_en": "El Salvador", "full_name_en": "the Republic of El Salvador", "area_id": "SV", "timezone": "America/El_Salvador", "three_letter_id": "SLV", "digital_code": 222, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "503", "idc": "", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "赤道几内亚", "short_name_en": "Equatorial Guinea", "full_name_en": "the Republic of Equatorial Guinea", "area_id": "GQ", "timezone": "", "three_letter_id": "GNQ", "digital_code": 226, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "240", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "厄立特里亚", "short_name_en": "Eritrea", "full_name_en": "", "area_id": "ER", "timezone": "", "three_letter_id": "ERI", "digital_code": 232, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "291", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "爱沙尼亚", "short_name_en": "Estonia", "full_name_en": "the Republic of Estonia", "area_id": "EE", "timezone": "Europe/Tallinn", "three_letter_id": "EST", "digital_code": 233, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "372", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "埃塞俄比亚", "short_name_en": "Ethiopia", "full_name_en": "the Federal Democratic Republic of Ethiopia", "area_id": "ET", "timezone": "", "three_letter_id": "ETH", "digital_code": 231, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "251", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "福克兰群岛（马尔维纳斯）", "short_name_en": "Falkland Islands (the) [Malvinas]", "full_name_en": "", "area_id": "FK", "timezone": "", "three_letter_id": "FLK", "digital_code": 238, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "500", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "法罗群岛", "short_name_en": "Faroe Islands (the)", "full_name_en": "", "area_id": "FO", "timezone": "", "three_letter_id": "FRO", "digital_code": 234, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "298", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "斐济", "short_name_en": "Fiji", "full_name_en": "the Republic of the Fiji Islands", "area_id": "FJ", "timezone": "", "three_letter_id": "FJI", "digital_code": 242, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "679", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "芬兰", "short_name_en": "Finland", "full_name_en": "the Republic of Finland", "area_id": "FI", "timezone": "Europe/Helsinki", "three_letter_id": "FIN", "digital_code": 246, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "fi-FI", "phone_prefix": "358", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "法国", "short_name_en": "France", "full_name_en": "the French Republic", "area_id": "FR", "timezone": "Europe/Paris", "three_letter_id": "FRA", "digital_code": 250, "wms_country_id": 449841, "currency": "%s€", "currency_code": "EUR", "currency_digits": 2, "currency_thousand_symbol": "%20", "currency_digits_symbol": ",", "locale_code": "fr-FR", "phone_prefix": "33", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "20", "tel_match": "^\\d{9,10}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "法属圭亚那", "short_name_en": "French Guiana", "full_name_en": "", "area_id": "GF", "timezone": "", "three_letter_id": "GUF", "digital_code": 254, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "594", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "法属波利尼西亚", "short_name_en": "French Polynesia", "full_name_en": "", "area_id": "PF", "timezone": "", "three_letter_id": "PYF", "digital_code": 258, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "689", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "法属南部领地", "short_name_en": "French Southern Territories (the)", "full_name_en": "", "area_id": "TF", "timezone": "", "three_letter_id": "ATF", "digital_code": 260, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "262", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "加蓬", "short_name_en": "Gabon", "full_name_en": "the Gabonese Republic", "area_id": "GA", "timezone": "", "three_letter_id": "GAB", "digital_code": 266, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "241", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "冈比亚", "short_name_en": "Gambia (The)", "full_name_en": "the Republic of The Gambia", "area_id": "GM", "timezone": "", "three_letter_id": "GMB", "digital_code": 270, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "220", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "格鲁吉亚", "short_name_en": "Georgia", "full_name_en": "", "area_id": "GE", "timezone": "", "three_letter_id": "GEO", "digital_code": 268, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "995", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "德国", "short_name_en": "Germany", "full_name_en": "he Federal Republic of Germany", "area_id": "DE", "timezone": "Europe/Berlin", "three_letter_id": "DEU", "digital_code": 276, "wms_country_id": 239721, "currency": "%s€", "currency_code": "EUR", "currency_digits": 2, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "de-DE", "phone_prefix": "49", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "19", "tel_match": "^\\d{10,12}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "加纳", "short_name_en": "Ghana", "full_name_en": "the Republic of Ghana", "area_id": "GH", "timezone": "Africa/Accra", "three_letter_id": "GHA", "digital_code": 288, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "233", "idc": "", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "直布罗陀", "short_name_en": "Gibraltar", "full_name_en": "", "area_id": "GI", "timezone": "", "three_letter_id": "GIB", "digital_code": 292, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "350", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "希腊", "short_name_en": "Greece", "full_name_en": "the Hellenic Republic", "area_id": "GR", "timezone": "Europe/Athens", "three_letter_id": "GRC", "digital_code": 300, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "el-GR", "phone_prefix": "30", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "格陵兰", "short_name_en": "Greenland", "full_name_en": "", "area_id": "GL", "timezone": "", "three_letter_id": "GRL", "digital_code": 304, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "299", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "格林纳达", "short_name_en": "Grenada", "full_name_en": "", "area_id": "GD", "timezone": "", "three_letter_id": "GRD", "digital_code": 308, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-473", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "瓜德罗普", "short_name_en": "Guadeloupe", "full_name_en": "", "area_id": "GP", "timezone": "", "three_letter_id": "GLP", "digital_code": 312, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "590", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "关岛", "short_name_en": "Guam", "full_name_en": "", "area_id": "GU", "timezone": "", "three_letter_id": "GUM", "digital_code": 316, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-671", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "危地马拉", "short_name_en": "Guatemala", "full_name_en": "the Republic of Guatemala", "area_id": "GT", "timezone": "America/Guatemala", "three_letter_id": "GTM", "digital_code": 320, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "es-GT", "phone_prefix": "502", "idc": "sg", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "格恩西岛", "short_name_en": "Guernsey", "full_name_en": "", "area_id": "GG", "timezone": "", "three_letter_id": "GGY", "digital_code": 831, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "44 1481", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "几内亚", "short_name_en": "Guinea", "full_name_en": "the Republic of Guinea", "area_id": "GN", "timezone": "", "three_letter_id": "GIN", "digital_code": 324, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "224", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "几内亚比绍", "short_name_en": "Guinea-Bissau", "full_name_en": "the Republic of Guinea-Bissau", "area_id": "GW", "timezone": "", "three_letter_id": "GNB", "digital_code": 624, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "245", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "圭亚那", "short_name_en": "Guyana", "full_name_en": "the Republic of Guyana", "area_id": "GY", "timezone": "", "three_letter_id": "GUY", "digital_code": 328, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "592", "idc": "", "continent": "South America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "海地", "short_name_en": "Haiti", "full_name_en": "the Republic of Haiti", "area_id": "HT", "timezone": "America/Port-au-Prince", "three_letter_id": "HTI", "digital_code": 332, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "509", "idc": "", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "赫德岛和麦克唐纳岛", "short_name_en": "Heard Island and McDonald Islands", "full_name_en": "", "area_id": "HM", "timezone": "", "three_letter_id": "HMD", "digital_code": 334, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "672", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "梵蒂冈", "short_name_en": "Holy See (the) [Vatican City State]", "full_name_en": "", "area_id": "VA", "timezone": "", "three_letter_id": "VAT", "digital_code": 336, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "39 06", "idc": "ams", "region": "eu", "continent": "Europe", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "洪都拉斯", "short_name_en": "Honduras", "full_name_en": "the Republic of Honduras", "area_id": "HN", "timezone": "America/Tegucigalpa", "three_letter_id": "HND", "digital_code": 340, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "504", "idc": "", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "中国香港", "short_name_en": "Hong Kong (Province of China)", "full_name_en": "Hong Kong (Province of China)", "area_id": "HK", "timezone": "Asia/Hong_Kong", "three_letter_id": "HKG", "digital_code": 344, "wms_country_id": 3385, "currency": "HK$%s", "currency_code": "HKD", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "zh-HK", "phone_prefix": "852", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Han\", \"Latin\"]", "tax_rate": "0", "tel_match": "^[2,3,6,7,8,9]\\d{7}$", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "匈牙利", "short_name_en": "Hungary", "full_name_en": "the Republic of Hungary", "area_id": "HU", "timezone": "Europe/Budapest", "three_letter_id": "HUN", "digital_code": 348, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "hu-HU", "phone_prefix": "36", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "冰岛", "short_name_en": "Iceland", "full_name_en": "the Republic of Iceland", "area_id": "IS", "timezone": "", "three_letter_id": "ISL", "digital_code": 352, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "354", "idc": "ams", "region": "eu", "continent": "Europe", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "印度", "short_name_en": "India", "full_name_en": "the Republic of India", "area_id": "IN", "timezone": "Asia/Calcutta", "three_letter_id": "IND", "digital_code": 356, "wms_country_id": 28587, "currency": "₹%s", "currency_code": "INR", "currency_digits": 0, "currency_thousand_symbol": ",", "currency_digits_symbol": "", "locale_code": "en-IN", "phone_prefix": "91", "idc": "in", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "印度尼西亚", "short_name_en": "Indonesia", "full_name_en": "the Republic of Indonesia", "area_id": "ID", "timezone": "Asia/Jakarta", "three_letter_id": "IDN", "digital_code": 360, "wms_country_id": 254391, "currency": "Rp%s", "currency_code": "IDR", "currency_digits": 0, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "id-ID", "phone_prefix": "62", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{8,15}$", "date_format": "dd-MM-yyyy", "date_time_min_format": "dd-MM-yyyy HH:mm", "date_time_sec_format": "dd-MM-yyyy HH:mm:ss", "currency_minor_unit": "100"}}, {"short_name_cn": "伊朗", "short_name_en": "Iran (the Islamic Republic of)", "full_name_en": "the Islamic Republic of Iran", "area_id": "IR", "timezone": "", "three_letter_id": "IRN", "digital_code": 364, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "98", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "伊拉克", "short_name_en": "Iraq", "full_name_en": "the Republic of Iraq", "area_id": "IQ", "timezone": "Asia/Baghdad", "three_letter_id": "IRQ", "digital_code": 368, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "ar-IQ", "phone_prefix": "964", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "爱尔兰", "short_name_en": "Ireland", "full_name_en": "", "area_id": "IE", "timezone": "Europe/Dublin", "three_letter_id": "IRL", "digital_code": 372, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "353", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "英国属地曼岛", "short_name_en": "Isle of Man", "full_name_en": "", "area_id": "IM", "timezone": "", "three_letter_id": "IMN", "digital_code": 833, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "44 1624", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "以色列", "short_name_en": "Israel", "full_name_en": "the State of Israel", "area_id": "IL", "timezone": "Asia/Jerusalem", "three_letter_id": "ISR", "digital_code": 376, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "972", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "意大利", "short_name_en": "Italy", "full_name_en": "the Republic of Italy", "area_id": "IT", "timezone": "Europe/Rome", "three_letter_id": "ITA", "digital_code": 380, "wms_country_id": *********, "currency": "%s€", "currency_code": "EUR", "currency_digits": 2, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "it-IT", "phone_prefix": "39", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "22", "tel_match": "^\\d{10}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "牙买加", "short_name_en": "Jamaica", "full_name_en": "", "area_id": "JM", "timezone": "", "three_letter_id": "JAM", "digital_code": 388, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-876", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "日本", "short_name_en": "Japan", "full_name_en": "Japan", "area_id": "JP", "timezone": "Asia/Tokyo", "three_letter_id": "JPN", "digital_code": 392, "wms_country_id": *********, "currency": "¥%s", "currency_code": "JPY", "currency_digits": 0, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "ja-<PERSON>", "phone_prefix": "81", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Han\", \"Hiragana\", \"Katakana\", \"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{10,11}$", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss", "currency_minor_unit": "1"}}, {"short_name_cn": "泽西岛", "short_name_en": "Jersey", "full_name_en": "", "area_id": "JE", "timezone": "", "three_letter_id": "JEY", "digital_code": 832, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "44 1534", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "约旦", "short_name_en": "Jordan", "full_name_en": "the Hashemite Kingdom of Jordan", "area_id": "JO", "timezone": "Asia/Amman", "three_letter_id": "JOR", "digital_code": 400, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "962", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "哈萨克斯坦", "short_name_en": "Kazakhstan", "full_name_en": "the Republic of Kazakhstan", "area_id": "KZ", "timezone": "Asia/Almaty", "three_letter_id": "KAZ", "digital_code": 398, "wms_country_id": *********, "currency": "%s₸", "currency_code": "KZT", "currency_digits": 2, "currency_thousand_symbol": "%20", "currency_digits_symbol": ",", "locale_code": "ru-RU", "phone_prefix": "7", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Cyrillic\", \"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9,11}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "肯尼亚", "short_name_en": "Kenya", "full_name_en": "the Republic of Kenya", "area_id": "KE", "timezone": "Africa/Nairobi", "three_letter_id": "KEN", "digital_code": 404, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-KE", "phone_prefix": "254", "idc": "sg", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "基里巴斯", "short_name_en": "Kiribati", "full_name_en": "the Republic of Kiribati", "area_id": "KI", "timezone": "", "three_letter_id": "KIR", "digital_code": 296, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "686", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "朝鲜", "short_name_en": "Korea (the Democratic People's Republic of)", "full_name_en": "the Democratic People's Republic of Korea", "area_id": "KP", "timezone": "", "three_letter_id": "PRK", "digital_code": 408, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "850", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "韩国", "short_name_en": "South Korea", "full_name_en": "the Republic of Korea", "area_id": "KR", "timezone": "Asia/Seoul", "three_letter_id": "KOR", "digital_code": 410, "wms_country_id": *********, "currency": "%s원", "currency_code": "KRW", "currency_digits": 0, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "ko-KR", "phone_prefix": "82", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Han\", \"Hangul\", \"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9,11}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "1"}}, {"short_name_cn": "科威特", "short_name_en": "Kuwait", "full_name_en": "he State of Kuwait", "area_id": "KW", "timezone": "Asia/Kuwait", "three_letter_id": "KWT", "digital_code": 414, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "965", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "吉尔吉斯斯坦", "short_name_en": "Kyrgyzstan", "full_name_en": "the Kyrgyz Republic", "area_id": "KG", "timezone": "", "three_letter_id": "KGZ", "digital_code": 417, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "996", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "老挝", "short_name_en": "Lao People's Democratic Republic", "full_name_en": "the Lao People's Democratic Republic", "area_id": "LA", "timezone": "Asia/Vientiane", "three_letter_id": "LAO", "digital_code": 418, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "856", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "拉脱维亚", "short_name_en": "Latvia", "full_name_en": "the Republic of Latvia", "area_id": "LV", "timezone": "Europe/Riga", "three_letter_id": "LVA", "digital_code": 428, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "371", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "黎巴嫩", "short_name_en": "Lebanon", "full_name_en": "the Lebanese Republic", "area_id": "LB", "timezone": "", "three_letter_id": "LBN", "digital_code": 422, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "961", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "莱索托", "short_name_en": "Lesotho", "full_name_en": "the Kingdom of Lesotho", "area_id": "LS", "timezone": "", "three_letter_id": "LSO", "digital_code": 426, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "266", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "利比里亚", "short_name_en": "Liberia", "full_name_en": "the Republic of Liberia", "area_id": "LR", "timezone": "", "three_letter_id": "LBR", "digital_code": 430, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "231", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "利比亚", "short_name_en": "Libya", "full_name_en": "the Socialist People's Libyan Arab Jam<PERSON>riya", "area_id": "LY", "timezone": "Africa/Tripoli", "three_letter_id": "LBY", "digital_code": 434, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "218", "idc": "", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "列支敦士登", "short_name_en": "Liechtenstein", "full_name_en": "the Principality of Liechtenstein", "area_id": "LI", "timezone": "", "three_letter_id": "LIE", "digital_code": 438, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "423", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "立陶宛", "short_name_en": "Lithuania", "full_name_en": "the Republic of Lithuania", "area_id": "LT", "timezone": "Europe/Vilnius", "three_letter_id": "LTU", "digital_code": 440, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "370", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "卢森堡", "short_name_en": "Luxembourg", "full_name_en": "the Grand Duchy of Luxembourg", "area_id": "LU", "timezone": "Europe/Luxembourg", "three_letter_id": "LUX", "digital_code": 442, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "352", "idc": "sg", "region": "eu", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "澳门", "short_name_en": "Macao", "full_name_en": "Macao Special Administrative Region of China", "area_id": "MO", "timezone": "Asia/Macau", "three_letter_id": "MAC", "digital_code": 446, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "853", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "前南马其顿", "short_name_en": "North Macedonia", "full_name_en": "the former Yugoslav Republic of Macedonia", "area_id": "MK", "timezone": "Europe/Skopje", "three_letter_id": "MKD", "digital_code": 807, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "389", "idc": "", "region": "eu", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "马达加斯加", "short_name_en": "Madagascar", "full_name_en": "the Republic of Madagascar", "area_id": "MG", "timezone": "", "three_letter_id": "MDG", "digital_code": 450, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "261", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "马拉维", "short_name_en": "Malawi", "full_name_en": "the Republic of Malawi", "area_id": "MW", "timezone": "", "three_letter_id": "MWI", "digital_code": 454, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "265", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "马来西亚", "short_name_en": "Malaysia", "full_name_en": "Malaysia", "area_id": "MY", "timezone": "Asia/Kuching", "three_letter_id": "MYS", "digital_code": 458, "wms_country_id": 3812, "currency": "RM%s", "currency_code": "MYR", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "en-MY", "phone_prefix": "60", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9,11}$", "date_format": "dd-MM-yyyy", "date_time_min_format": "dd-MM-yyyy HH:mm", "date_time_sec_format": "dd-MM-yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "马尔代夫", "short_name_en": "Maldives", "full_name_en": "the Republic of Maldives", "area_id": "MV", "timezone": "", "three_letter_id": "MDV", "digital_code": 462, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "960", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "马里", "short_name_en": "Mali", "full_name_en": "the Republic of Mali", "area_id": "ML", "timezone": "", "three_letter_id": "MLI", "digital_code": 466, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "223", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "马耳他", "short_name_en": "Malta", "full_name_en": "the Republic of Malta", "area_id": "MT", "timezone": "", "three_letter_id": "MLT", "digital_code": 470, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "356", "idc": "ams", "region": "eu", "continent": "Europe", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "马绍尔群岛", "short_name_en": "Marshall Islands (the)", "full_name_en": "the Republic of the Marshall Islands", "area_id": "MH", "timezone": "", "three_letter_id": "MHL", "digital_code": 584, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "692", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "马提尼克", "short_name_en": "Martinique", "full_name_en": "", "area_id": "MQ", "timezone": "", "three_letter_id": "MTQ", "digital_code": 474, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "596", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "毛利塔尼亚", "short_name_en": "Mauritania", "full_name_en": "the Islamic Republic of Mauritania", "area_id": "MR", "timezone": "", "three_letter_id": "MRT", "digital_code": 478, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "222", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "毛里求斯", "short_name_en": "Mauritius", "full_name_en": "the Republic of Mauritius", "area_id": "MU", "timezone": "", "three_letter_id": "MUS", "digital_code": 480, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "230", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "马约特", "short_name_en": "Mayotte", "full_name_en": "", "area_id": "YT", "timezone": "", "three_letter_id": "MYT", "digital_code": 175, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "262", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "墨西哥", "short_name_en": "Mexico", "full_name_en": "the United Mexican States", "area_id": "MX", "timezone": "America/Mexico_City", "three_letter_id": "MEX", "digital_code": 484, "wms_country_id": *********, "currency": "$%s", "currency_code": "MXN", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "es-MX", "phone_prefix": "52", "idc": "sg", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^[1-9]\\d{9}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "密克罗尼西亚联邦", "short_name_en": "Micronesia (the Federated States of)", "full_name_en": "the Federated States of Micronesia", "area_id": "FM", "timezone": "", "three_letter_id": "FSM", "digital_code": 583, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "691", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "摩尔多瓦", "short_name_en": "Moldova (Republic of)", "full_name_en": "the Republic of Moldova", "area_id": "MD", "timezone": "Europe/Chisinau", "three_letter_id": "MDA", "digital_code": 498, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "373", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "摩纳哥", "short_name_en": "Monaco", "full_name_en": "the Principality of Monaco", "area_id": "MC", "timezone": "", "three_letter_id": "MCO", "digital_code": 492, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "377", "idc": "ams", "region": "eu", "continent": "Europe", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "蒙古", "short_name_en": "Mongolia", "full_name_en": "", "area_id": "MN", "timezone": "", "three_letter_id": "MNG", "digital_code": 496, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "976", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "黑山", "short_name_en": "Montenegro", "full_name_en": "he Republic of Montenegro", "area_id": "ME", "timezone": "Europe/Podgorica", "three_letter_id": "MNE", "digital_code": 499, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "382", "idc": "", "region": "eu", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "蒙特塞拉特", "short_name_en": "Montserrat", "full_name_en": "", "area_id": "MS", "timezone": "", "three_letter_id": "MSR", "digital_code": 500, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-664", "idc": "", "continent": "South America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "摩洛哥", "short_name_en": "Morocco", "full_name_en": "The Kingdom of Morocco", "area_id": "MA", "timezone": "Africa/Casablanca", "three_letter_id": "MAR", "digital_code": 504, "wms_country_id": *********, "currency": "", "currency_code": "MAD", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "ar-<PERSON>", "phone_prefix": "212", "idc": "sg", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "莫桑比克", "short_name_en": "Mozambique", "full_name_en": "the Republic of Mozambique", "area_id": "MZ", "timezone": "", "three_letter_id": "MOZ", "digital_code": 508, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "258", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "缅甸", "short_name_en": "Myanmar", "full_name_en": "the Union of Myanmar", "area_id": "MM", "timezone": "Asia/Yangon", "three_letter_id": "MMR", "digital_code": 104, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "my-MM", "phone_prefix": "95", "idc": "sg", "region": "sg", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "纳米比亚", "short_name_en": "Namibia", "full_name_en": "the Republic of Namibia", "area_id": "NA", "timezone": "", "three_letter_id": "NAM", "digital_code": 516, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "264", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "瑙鲁", "short_name_en": "Nauru", "full_name_en": "the Republic of Nauru", "area_id": "NR", "timezone": "", "three_letter_id": "NRU", "digital_code": 520, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "674", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "尼泊尔", "short_name_en": "Nepal", "full_name_en": "Nepal", "area_id": "NP", "timezone": "Asia/Kathmandu", "three_letter_id": "NPL", "digital_code": 524, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-NP", "phone_prefix": "977", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "荷兰", "short_name_en": "Netherlands", "full_name_en": "the Kingdom of the Netherlands", "area_id": "NL", "timezone": "Europe/Amsterdam", "three_letter_id": "NLD", "digital_code": 528, "wms_country_id": *********, "currency": "€%s", "currency_code": "EUR", "currency_digits": 2, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "nl-NL", "phone_prefix": "31", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "荷属安的列斯", "short_name_en": "Netherlands Antilles (the)", "full_name_en": "", "area_id": "AN", "timezone": "", "three_letter_id": "ANT", "digital_code": 530, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "599", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "新喀里多尼亚", "short_name_en": "New Caledonia", "full_name_en": "", "area_id": "NC", "timezone": "", "three_letter_id": "NCL", "digital_code": 540, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "687", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "新西兰", "short_name_en": "New Zealand", "full_name_en": "", "area_id": "NZ", "timezone": "Pacific/Auckland", "three_letter_id": "NZL", "digital_code": 554, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "64", "idc": "", "region": "sg", "continent": "Oceania", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "尼加拉瓜", "short_name_en": "Nicaragua", "full_name_en": "the Republic of Nicaragua", "area_id": "NI", "timezone": "America/Managua", "three_letter_id": "NIC", "digital_code": 558, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "505", "idc": "", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "尼日尔", "short_name_en": "Niger (the)", "full_name_en": "the Republic of the Niger", "area_id": "NE", "timezone": "", "three_letter_id": "NER", "digital_code": 562, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "227", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "尼日利亚", "short_name_en": "Nigeria", "full_name_en": "the Federal Republic of Nigeria", "area_id": "NG", "timezone": "Africa/Lagos", "three_letter_id": "NGA", "digital_code": 566, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-NG", "phone_prefix": "234", "idc": "sg", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "纽埃", "short_name_en": "Niue", "full_name_en": "the Republic of Niue", "area_id": "NU", "timezone": "", "three_letter_id": "NIU", "digital_code": 570, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "683", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "诺福克岛", "short_name_en": "Norfolk Island", "full_name_en": "", "area_id": "NF", "timezone": "", "three_letter_id": "NFK", "digital_code": 574, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "672", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "北马里亚纳", "short_name_en": "Northern Mariana Islands (the)", "full_name_en": "the Commonwealth of the Northern Mariana Islands", "area_id": "MP", "timezone": "", "three_letter_id": "MNP", "digital_code": 580, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-670", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "挪威", "short_name_en": "Norway", "full_name_en": "the Kingdom of Norway", "area_id": "NO", "timezone": "Europe/Oslo", "three_letter_id": "NOR", "digital_code": 578, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "47", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "阿曼", "short_name_en": "Oman", "full_name_en": "the Sultanate of Oman", "area_id": "OM", "timezone": "Asia/Muscat", "three_letter_id": "OMN", "digital_code": 512, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "968", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "巴基斯坦", "short_name_en": "Pakistan", "full_name_en": "the Islamic Republic of Pakistan", "area_id": "PK", "timezone": "Asia/Karachi", "three_letter_id": "PAK", "digital_code": 586, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-PK", "phone_prefix": "92", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "帕劳", "short_name_en": "<PERSON><PERSON>", "full_name_en": "the Republic of Palau", "area_id": "PW", "timezone": "", "three_letter_id": "PLW", "digital_code": 585, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "680", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "巴勒斯坦", "short_name_en": "Palestinian Territory (the Occupied)", "full_name_en": "the Occupied Palestinian Territory", "area_id": "PS", "timezone": "", "three_letter_id": "PSE", "digital_code": 275, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "970", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "巴拿马", "short_name_en": "Panama", "full_name_en": "the Republic of Panama", "area_id": "PA", "timezone": "America/Panama", "three_letter_id": "PAN", "digital_code": 591, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "es-PA", "phone_prefix": "507", "idc": "sg", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "巴布亚新几内亚", "short_name_en": "Papua New Guinea", "full_name_en": "", "area_id": "PG", "timezone": "Pacific/Port_Moresby", "three_letter_id": "PNG", "digital_code": 598, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "675", "idc": "", "region": "sg", "continent": "Oceania", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "巴拉圭", "short_name_en": "Paraguay", "full_name_en": "the Republic of Paraguay", "area_id": "PY", "timezone": "America/Asuncion", "three_letter_id": "PRY", "digital_code": 600, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "595", "idc": "", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "秘鲁", "short_name_en": "Peru", "full_name_en": "the Republic of Peru", "area_id": "PE", "timezone": "America/Lima", "three_letter_id": "PER", "digital_code": 604, "wms_country_id": *********, "currency": "S/%s", "currency_code": "PEN", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "es-419", "phone_prefix": "51", "idc": "sg", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{8,9}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "菲律宾", "short_name_en": "Philippines", "full_name_en": "the Republic of the Philippines", "area_id": "PH", "timezone": "Asia/Manila", "three_letter_id": "PHL", "digital_code": 608, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-PH", "phone_prefix": "63", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "皮特凯恩", "short_name_en": "Pitcairn", "full_name_en": "", "area_id": "PN", "timezone": "", "three_letter_id": "PCN", "digital_code": 612, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "64", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "波兰", "short_name_en": "Poland", "full_name_en": "the Republic of Poland", "area_id": "PL", "timezone": "Europe/Warsaw", "three_letter_id": "POL", "digital_code": 616, "wms_country_id": *********, "currency": "%s zł", "currency_code": "PLN", "currency_digits": 2, "currency_thousand_symbol": "%20", "currency_digits_symbol": ",", "locale_code": "pl-PL", "phone_prefix": "48", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "currency_minor_unit": "0.01", "date_format": "dd.<PERSON><PERSON><PERSON>yyyy", "date_time_min_format": "dd.MM.yyyy HH:mm", "date_time_sec_format": "dd.MM.yyyy HH:mm:ss", "email_match": "", "tax_rate": "0", "tel_match": "^(0{1}\\d{9,20}|[1-9]\\d{8,19})$"}}, {"short_name_cn": "葡萄牙", "short_name_en": "Portugal", "full_name_en": "the Portuguese Republic", "area_id": "PT", "timezone": "Europe/Lisbon", "three_letter_id": "PRT", "digital_code": 620, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "pt-PT", "phone_prefix": "351", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "波多黎各", "short_name_en": "Puerto Rico", "full_name_en": "", "area_id": "PR", "timezone": "", "three_letter_id": "PRI", "digital_code": 630, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-787", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "卡塔尔", "short_name_en": "Qatar", "full_name_en": "the State of Qatar", "area_id": "QA", "timezone": "Asia/Riyadh", "three_letter_id": "QAT", "digital_code": 634, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "974", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "留尼汪", "short_name_en": "Réunion", "full_name_en": "", "area_id": "RE", "timezone": "", "three_letter_id": "REU", "digital_code": 638, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "262", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "罗马尼亚", "short_name_en": "Romania", "full_name_en": "Romania", "area_id": "RO", "timezone": "Europe/Bucharest", "three_letter_id": "ROU", "digital_code": 642, "wms_country_id": *********, "currency": "%s RON", "currency_code": "RON", "currency_digits": 2, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "ro-RO", "phone_prefix": "40", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{9}$", "date_format": "dd.<PERSON><PERSON><PERSON>yyyy", "date_time_min_format": "dd.MM.yyyy HH:mm", "date_time_sec_format": "dd.MM.yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "俄罗斯联邦", "short_name_en": "Russian Federation", "full_name_en": "the Russian Federation", "area_id": "RU", "timezone": "Europe/Moscow", "three_letter_id": "RUS", "digital_code": 643, "wms_country_id": *********, "currency": "%s₽", "currency_code": "RUB", "currency_digits": 2, "currency_thousand_symbol": "%20", "currency_digits_symbol": ",", "locale_code": "ru-RU", "phone_prefix": "7", "idc": "ru", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Cyrillic\", \"Latin\"]", "tax_rate": "0", "tel_match": "^[1-9]\\d{9,10}$", "date_format": "dd.<PERSON><PERSON><PERSON>yyyy", "date_time_min_format": "dd.MM.yyyy HH:mm", "date_time_sec_format": "dd.MM.yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "卢旺达", "short_name_en": "Rwanda", "full_name_en": "the Republic of Rwanda", "area_id": "RW", "timezone": "", "three_letter_id": "RWA", "digital_code": 646, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "250", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "圣赫勒拿", "short_name_en": "Saint Helena", "full_name_en": "", "area_id": "SH", "timezone": "", "three_letter_id": "SHN", "digital_code": 654, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "290", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "圣基茨和尼维斯", "short_name_en": "Saint Kitts and Nevis", "full_name_en": "", "area_id": "KN", "timezone": "", "three_letter_id": "KNA", "digital_code": 659, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-869", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "圣卢西亚", "short_name_en": "Saint Lucia", "full_name_en": "", "area_id": "LC", "timezone": "", "three_letter_id": "LCA", "digital_code": 662, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-758", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "圣皮埃尔和密克隆", "short_name_en": "Saint Pierre and Miquelon", "full_name_en": "", "area_id": "PM", "timezone": "", "three_letter_id": "SPM", "digital_code": 666, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "508", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "圣文森特和格林纳丁斯", "short_name_en": "Saint Vincent and the Grenadines", "full_name_en": "", "area_id": "VC", "timezone": "", "three_letter_id": "VCT", "digital_code": 670, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-784", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "萨摩亚", "short_name_en": "Samoa", "full_name_en": "the Independent State of Samoa", "area_id": "WS", "timezone": "", "three_letter_id": "WSM", "digital_code": 882, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "685", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "圣马力诺", "short_name_en": "San Marino", "full_name_en": "the Republic of San Marino", "area_id": "SM", "timezone": "", "three_letter_id": "SMR", "digital_code": 674, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "378", "idc": "ams", "region": "eu", "continent": "Europe", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "圣多美和普林西比", "short_name_en": "Sao Tome and Principe", "full_name_en": "the Democratic Republic of Sao Tome and Principe", "area_id": "ST", "timezone": "", "three_letter_id": "STP", "digital_code": 678, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "239", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "沙特阿拉伯", "short_name_en": "Saudi Arabia", "full_name_en": "the Kingdom of Saudi Arabia", "area_id": "SA", "timezone": "Africa/Cairo", "three_letter_id": "SAU", "digital_code": 682, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "ar-SA", "phone_prefix": "966", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "塞内加尔", "short_name_en": "Senegal", "full_name_en": "the Republic of Senegal", "area_id": "SN", "timezone": "Africa/Dakar", "three_letter_id": "SEN", "digital_code": 686, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "221", "idc": "", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "塞尔维亚", "short_name_en": "Serbia", "full_name_en": "the Republic of Serbia", "area_id": "RS", "timezone": "Europe/Belgrade", "three_letter_id": "SRB", "digital_code": 688, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "sr-RS", "phone_prefix": "381", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "塞舌尔", "short_name_en": "Seychelles", "full_name_en": "the Republic of Seychelles", "area_id": "SC", "timezone": "", "three_letter_id": "SYC", "digital_code": 690, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "248", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "塞拉利昂", "short_name_en": "Sierra Leone", "full_name_en": "the Republic of Sierra Leone", "area_id": "SL", "timezone": "", "three_letter_id": "SLE", "digital_code": 694, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "232", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "新加坡", "short_name_en": "Singapore", "full_name_en": "the Republic of Singapore", "area_id": "SG", "timezone": "Asia/Singapore", "three_letter_id": "SGP", "digital_code": 702, "wms_country_id": 3778, "currency": "S$%s", "currency_code": "SGD", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "en-SG", "phone_prefix": "65", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^[89]\\d{7}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "斯洛伐克", "short_name_en": "Slovakia", "full_name_en": "the Slovak Republic", "area_id": "SK", "timezone": "Europe/Bratislava", "three_letter_id": "SVK", "digital_code": 703, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "421", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "斯洛文尼亚", "short_name_en": "Slovenia", "full_name_en": "the Republic of Slovenia", "area_id": "SI", "timezone": "Europe/Ljubljana", "three_letter_id": "SVN", "digital_code": 705, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "386", "idc": "", "region": "eu", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "所罗门群岛", "short_name_en": "Solomon Islands (the)", "full_name_en": "", "area_id": "SB", "timezone": "", "three_letter_id": "SLB", "digital_code": 90, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "677", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "索马里", "short_name_en": "Somalia", "full_name_en": "the Somali Republic", "area_id": "SO", "timezone": "", "three_letter_id": "SOM", "digital_code": 706, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "252", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "南非", "short_name_en": "South Africa", "full_name_en": "The Republic of South Africa", "area_id": "ZA", "timezone": "Africa/Johannesburg", "three_letter_id": "ZAF", "digital_code": 710, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-ZA", "phone_prefix": "27", "idc": "sg", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "南乔治亚岛和南桑德韦奇岛", "short_name_en": "South Georgia and the South Sandwich Islands", "full_name_en": "", "area_id": "GS", "timezone": "", "three_letter_id": "SGS", "digital_code": 239, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "500", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "西班牙", "short_name_en": "Spain", "full_name_en": "the Kingdom of Spain", "area_id": "ES", "timezone": "Europe/Madrid", "three_letter_id": "ESP", "digital_code": 724, "wms_country_id": 717808, "currency": "%s€", "currency_code": "EUR", "currency_digits": 2, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "es-ES", "phone_prefix": "34", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "21", "tel_match": "^[1-9]\\d{8}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "斯里兰卡", "short_name_en": "Sri Lanka", "full_name_en": "the Democratic Socialist Republic of Sri Lanka", "area_id": "LK", "timezone": "Asia/Calcutta", "three_letter_id": "LKA", "digital_code": 144, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-LK", "phone_prefix": "94", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "苏丹", "short_name_en": "Sudan (the)", "full_name_en": "the Republic of the Sudan", "area_id": "SD", "timezone": "", "three_letter_id": "SDN", "digital_code": 736, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "249", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "苏里南", "short_name_en": "Suriname", "full_name_en": "the Republic of Suriname", "area_id": "SR", "timezone": "", "three_letter_id": "SUR", "digital_code": 740, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "597", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "斯瓦尔巴岛和扬马延岛", "short_name_en": "Svalbard and <PERSON>", "full_name_en": "", "area_id": "SJ", "timezone": "", "three_letter_id": "SJM", "digital_code": 744, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "47", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "斯威士兰", "short_name_en": "Swaziland", "full_name_en": "the Kingdom of Swaziland", "area_id": "SZ", "timezone": "", "three_letter_id": "SWZ", "digital_code": 748, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "268", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "瑞典", "short_name_en": "Sweden", "full_name_en": "the Kingdom of Sweden", "area_id": "SE", "timezone": "Europe/Stockholm", "three_letter_id": "SWE", "digital_code": 752, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "sv-SE", "phone_prefix": "46", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "瑞士", "short_name_en": "Switzerland", "full_name_en": "the Swiss Confederation", "area_id": "CH", "timezone": "Europe/Zurich", "three_letter_id": "CHE", "digital_code": 756, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "41", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "叙利亚", "short_name_en": "Syrian Arab Republic (the)", "full_name_en": "the Syrian Arab Republic", "area_id": "SY", "timezone": "", "three_letter_id": "SYR", "digital_code": 760, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "963", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "中国台湾", "short_name_en": "Taiwan (Province of China)", "full_name_en": "Taiwan (Province of China)", "area_id": "TW", "timezone": "Asia/Taipei", "three_letter_id": "TWN", "digital_code": 158, "wms_country_id": 3386, "currency": "NT$%s", "currency_code": "TWD", "currency_digits": 0, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "zh-TW", "phone_prefix": "886", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Han\", \"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{7,10}$", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss", "currency_minor_unit": "1"}}, {"short_name_cn": "塔吉克斯坦", "short_name_en": "Tajikistan", "full_name_en": "the Republic of Tajikistan", "area_id": "TJ", "timezone": "", "three_letter_id": "TJK", "digital_code": 762, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "992", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "坦桑尼亚", "short_name_en": "Tanzania, United Republic of", "full_name_en": "the United Republic of Tanzania", "area_id": "TZ", "timezone": "Africa/Dar_es_Salaam", "three_letter_id": "TZA", "digital_code": 834, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "255", "idc": "", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "泰国", "short_name_en": "Thailand", "full_name_en": "the Kingdom of Thailand", "area_id": "TH", "timezone": "Asia/Bangkok", "three_letter_id": "THA", "digital_code": 764, "wms_country_id": *********, "currency": "฿%s", "currency_code": "THB", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "th-TH", "phone_prefix": "66", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Thai\", \"Latin\"]", "tax_rate": "7", "tel_match": "^[6,8,9]\\d{8}$", "date_format": "dd-MM-yyyy", "date_time_min_format": "dd-MM-yyyy HH:mm", "date_time_sec_format": "dd-MM-yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "东帝汶", "short_name_en": "Timor-Leste", "full_name_en": "the Democratic Republic of Timor-Leste", "area_id": "TL", "timezone": "", "three_letter_id": "TLS", "digital_code": 626, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "670", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "多哥", "short_name_en": "Togo", "full_name_en": "the Togolese Republic", "area_id": "TG", "timezone": "", "three_letter_id": "TGO", "digital_code": 768, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "228", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "托克劳", "short_name_en": "Tokelau", "full_name_en": "", "area_id": "TK", "timezone": "", "three_letter_id": "TKL", "digital_code": 772, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "690", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "汤加", "short_name_en": "Tonga", "full_name_en": "the Kingdom of Tonga", "area_id": "TO", "timezone": "", "three_letter_id": "TON", "digital_code": 776, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "676", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "特立尼达和多巴哥", "short_name_en": "Trinidad and Tobago", "full_name_en": "the Republic of Trinidad and Tobago", "area_id": "TT", "timezone": "", "three_letter_id": "TTO", "digital_code": 780, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-868", "idc": "", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "突尼斯", "short_name_en": "Tunisia", "full_name_en": "the Republic of Tunisia", "area_id": "TN", "timezone": "Africa/Tunis", "three_letter_id": "TUN", "digital_code": 788, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "fr-TN", "phone_prefix": "216", "idc": "sg", "region": "eu", "continent": "Africa", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "土耳其", "short_name_en": "Turkey", "full_name_en": "the Republic of Turkey", "area_id": "TR", "timezone": "Europe/Istanbul", "three_letter_id": "TUR", "digital_code": 792, "wms_country_id": *********, "currency": "%sTL", "currency_code": "TRY", "currency_digits": 2, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "tr-TR", "phone_prefix": "90", "idc": "sg", "region": "sg", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^5\\d{9}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "土库曼斯坦", "short_name_en": "Turkmenistan", "full_name_en": "", "area_id": "TM", "timezone": "", "three_letter_id": "TKM", "digital_code": 795, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "993", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "特克斯和凯科斯群岛", "short_name_en": "Turks and Caicos Islands (the)", "full_name_en": "", "area_id": "TC", "timezone": "", "three_letter_id": "TCA", "digital_code": 796, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-649", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "图瓦卢", "short_name_en": "Tuvalu", "full_name_en": "", "area_id": "TV", "timezone": "", "three_letter_id": "TUV", "digital_code": 798, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "688", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "乌干达", "short_name_en": "Uganda", "full_name_en": "the Republic of Uganda", "area_id": "UG", "timezone": "", "three_letter_id": "UGA", "digital_code": 800, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "256", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "乌克兰", "short_name_en": "Ukraine", "full_name_en": "Ukraine", "area_id": "UA", "timezone": "Europe/Kiev", "three_letter_id": "UKR", "digital_code": 804, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "uk-UA", "phone_prefix": "380", "idc": "sg", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "阿联酋", "short_name_en": "United Arab Emirates", "full_name_en": "the United Arab Emirates", "area_id": "AE", "timezone": "Asia/Dubai", "three_letter_id": "ARE", "digital_code": 784, "wms_country_id": *********, "currency": "", "currency_code": "AED", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-AE", "phone_prefix": "971", "idc": "sg", "region": "sg", "continent": "", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "英国", "short_name_en": "United Kingdom", "full_name_en": "United Kingdom (the)", "area_id": "UK", "timezone": "Europe/London", "three_letter_id": "GBR", "digital_code": 826, "wms_country_id": 201098, "currency": "￡%s", "currency_code": "GBP", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "en-GB", "phone_prefix": "44", "idc": "ams", "region": "eu", "continent": "Europe", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "20", "tel_match": "^\\d{10,11}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "美国", "short_name_en": "United States", "full_name_en": "the United States of America", "area_id": "US", "timezone": "America/New_York", "three_letter_id": "USA", "digital_code": 840, "wms_country_id": 130513, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-US", "phone_prefix": "1", "idc": "sg", "region": "sg", "continent": "North America", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "美国本土外小岛屿", "short_name_en": "United States Minor Outlying Islands (the)", "full_name_en": "", "area_id": "UM", "timezone": "", "three_letter_id": "UMI", "digital_code": 581, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "乌拉圭", "short_name_en": "Uruguay", "full_name_en": "the Eastern Republic of Uruguay", "area_id": "UY", "timezone": "America/Montevideo", "three_letter_id": "URY", "digital_code": 858, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "598", "idc": "", "region": "sg", "continent": "South America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "乌兹别克斯坦", "short_name_en": "Uzbekistan", "full_name_en": "the Republic of Uzbekistan", "area_id": "UZ", "timezone": "Asia/Tashkent", "three_letter_id": "UZB", "digital_code": 860, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "ru-UZ", "phone_prefix": "998", "idc": "sg", "region": "eu", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "瓦努阿图", "short_name_en": "Vanuatu", "full_name_en": "the Republic of Vanuatu", "area_id": "VU", "timezone": "", "three_letter_id": "VUT", "digital_code": 548, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "678", "idc": "", "continent": "Oceania", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "委内瑞拉", "short_name_en": "Venezuela (Bolivarian Republic of)", "full_name_en": "the Bolivarian Republic of Venezuela", "area_id": "VE", "timezone": "America/Caracas", "three_letter_id": "VEN", "digital_code": 862, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "58", "idc": "", "region": "sg", "continent": "North America", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}, {"short_name_cn": "越南", "short_name_en": "Vietnam", "full_name_en": "the Socialist Republic of Viet Nam", "area_id": "VN", "timezone": "Asia/Ho_Chi_Minh", "three_letter_id": "VNM", "digital_code": 704, "wms_country_id": *********, "currency": "₫%s", "currency_code": "VND", "currency_digits": 0, "currency_thousand_symbol": ".", "currency_digits_symbol": ",", "locale_code": "vi-VN", "phone_prefix": "84", "idc": "sg", "region": "sg", "continent": "Asia", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^\\d{10,11}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "1"}}, {"short_name_cn": "英属维尔京群岛", "short_name_en": "Virgin Islands (British)", "full_name_en": "British Virgin Islands (the)", "area_id": "VG", "timezone": "", "three_letter_id": "VGB", "digital_code": 92, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-284", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "美属维尔京群岛", "short_name_en": "Virgin Islands (U.S.)", "full_name_en": "the Virgin Islands of the United States", "area_id": "VI", "timezone": "", "three_letter_id": "VIR", "digital_code": 850, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "1-340", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "瓦利斯和富图纳", "short_name_en": "Wallis and Futuna", "full_name_en": "Wallis and Futuna Islands", "area_id": "WF", "timezone": "", "three_letter_id": "WLF", "digital_code": 876, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "681", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "西撒哈拉", "short_name_en": "Western Sahara", "full_name_en": "", "area_id": "EH", "timezone": "", "three_letter_id": "ESH", "digital_code": 732, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "212", "idc": "", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "也门", "short_name_en": "Yemen", "full_name_en": "the Republic of Yemen", "area_id": "YE", "timezone": "", "three_letter_id": "YEM", "digital_code": 887, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "967", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "赞比亚", "short_name_en": "Zambia", "full_name_en": "the Republic of Zambia", "area_id": "ZM", "timezone": "", "three_letter_id": "ZMB", "digital_code": 894, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "260", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "津巴布韦", "short_name_en": "Zimbabwe", "full_name_en": "the Republic of Zimbabwe", "area_id": "ZW", "timezone": "", "three_letter_id": "ZWE", "digital_code": 716, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "263", "idc": "", "continent": "Africa", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "海湾阿拉伯国家合作委员会", "short_name_en": "Gulf Cooperation Council", "full_name_en": "Gulf Cooperation Council", "area_id": "GCC", "timezone": "Asia/Dubai", "three_letter_id": "GCC", "digital_code": 0, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "ar-GCC", "phone_prefix": "0", "idc": "sg", "region": "sg", "continent": "", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "全球", "short_name_en": "Global", "full_name_en": "全球", "area_id": "GLOBAL", "timezone": "Asia/Shanghai", "three_letter_id": "GLO", "digital_code": 156, "wms_country_id": 0, "currency": "￥%s", "currency_code": "CNY", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "en-001", "phone_prefix": "0", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0", "tel_match": "^1[3456789][0-9]{9}$", "date_format": "yyyy-MM-dd", "date_time_min_format": "yyyy-MM-dd HH:mm", "date_time_sec_format": "yyyy-MM-dd HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "阿拉伯", "short_name_en": "Arab", "full_name_en": "Arab", "area_id": "ARAB", "timezone": "Asia/Dubai", "three_letter_id": "ARA", "digital_code": 0, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-GB", "phone_prefix": "0", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "海外社区(历史数据兼容)", "short_name_en": "Oversea Community", "full_name_en": "Oversea Community", "area_id": "OC", "timezone": "Asia/Shanghai", "three_letter_id": "OC", "digital_code": 0, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "en-GB", "phone_prefix": "0", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "中东", "short_name_en": "Middle East", "full_name_en": "Middle East", "area_id": "MEI", "timezone": "Asia/Riyadh", "three_letter_id": "MIE", "digital_code": 0, "wms_country_id": 0, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "ar-EG", "phone_prefix": "0", "idc": "sg", "region": "sg", "continent": "Asia", "status": 0, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "0"}}, {"short_name_cn": "英国", "short_name_en": "United Kingdom", "full_name_en": "United Kingdom (the)", "area_id": "GB", "timezone": "Europe/London", "three_letter_id": "GBR", "digital_code": 826, "wms_country_id": 201098, "currency": "￡%s", "currency_code": "GBP", "currency_digits": 2, "currency_thousand_symbol": ",", "currency_digits_symbol": ".", "locale_code": "en-GB", "phone_prefix": "44", "idc": "ams", "region": "eu", "continent": "Europe", "status": 1, "extend": {"unicode_list": "[\"Latin\"]", "tax_rate": "20", "tel_match": "^\\d{10,11}$", "date_format": "dd/MM/yyyy", "date_time_min_format": "dd/MM/yyyy HH:mm", "date_time_sec_format": "dd/MM/yyyy HH:mm:ss", "currency_minor_unit": "0.01"}}, {"short_name_cn": "科索沃", "short_name_en": "Republic of Kosovo", "full_name_en": "Republic of Kosovo", "area_id": "XK", "timezone": "Europe/Pristina", "three_letter_id": "XKX", "digital_code": 926, "wms_country_id": *********, "currency": "", "currency_code": "", "currency_digits": 0, "currency_thousand_symbol": "", "currency_digits_symbol": "", "locale_code": "", "phone_prefix": "", "idc": "", "region": "eu", "continent": "Europe", "status": 1, "extend": {"date_format": "yyyy/MM/dd", "date_time_min_format": "yyyy/MM/dd HH:mm", "date_time_sec_format": "yyyy/MM/dd HH:mm:ss"}}]