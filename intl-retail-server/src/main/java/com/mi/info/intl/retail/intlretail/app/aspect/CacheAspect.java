package com.mi.info.intl.retail.intlretail.app.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.intlretail.app.controller.BaseController;
import com.mi.info.intl.retail.intlretail.app.dto.menustatus.ProceedingResultDto;
import com.mi.info.intl.retail.intlretail.app.enums.MenuTypeEnum;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.service.app.proxy.dto.MenuStatusCacheDto;
import com.mi.info.intl.retail.model.CommonApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
@Slf4j
public class CacheAspect {

    static final String USER_BASE_DATA_HASH_KEY_PREFIX = "UserBaseData";
    static final String MENU_HASH_KEY_PREFIX = "Menu";
    static final String MENU_STATUS_HASH_KEY_PREFIX = "MenuStatus";
    static final String SUBMITDATA = "new_SubmitData";
    static final List<String> CACHE_PATHS =
            Arrays.asList("new_GetUserBaseData", "new_QueryMenuStatus", "new_CreationIMEIInfo");
    static final List<String> CACHE_FOR_MENUSTATUS =
            Arrays.asList("new_QueryMenuStatus");
    static final List<String> TASK_SUBMIT =
            Arrays.asList("new_SubmitData/Receiving", "new_CreationIMEIInfo", "new_SubmitData/CreateStoreIncident",
                    "new_WorkPlanAction/SubmitLeave", "new_WorkPlanAction/SubmitOverTime", "new_WorkPlanAction/SubmitShift",
                    "new_SubmitData/StoreVisit");
    static final List<String> USER_SUBMIT =
            Arrays.asList("new_SubmitData/SignIn", "new_SubmitData/SignOut");
    static final List<String> EXCLUDE_COUNTRY = Arrays.asList("AO", "AE");

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 定义精准切入点（根据实际类路径修改）
    @Around("execution(* com.mi.info.intl.retail.intlretail.app.controller.ProxyController.rmsTokenQuery(..))")
    public Object rmsTokenQueryHandleCache(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        String path = (String) args[1];    // 第1个参数是path

        BaseController baseController = (BaseController) joinPoint.getTarget();
        String countryCode = baseController.getCountryCode();
        // 非指定path的请求和指定国家不应用缓存
        if (!CACHE_PATHS.contains(path) || EXCLUDE_COUNTRY.contains(countryCode)) {
            return joinPoint.proceed();
        }
        log.info("path is {}", path);
        Object requestBody = args[0];
        log.info("requestBody is {}", JsonUtil.bean2json(requestBody));

        String key = baseController.getCurrentDay();  // 第5个参数是key
        String hashKey = USER_BASE_DATA_HASH_KEY_PREFIX + ":" + baseController.getAccount();  // 第6个参数是hashKey
        // 需要根据门店id缓存的请求
        if (CACHE_FOR_MENUSTATUS.contains(path)) {
            //Object requestBody = args[0];
            //MenuStatusRequestDto menuStatusRequest = objectMapper.convertValue(requestBody, MenuStatusRequestDto.class);
            //hashKey = MENU_STATUS_HASH_KEY_PREFIX + ":" + baseController.getAccount() + ":" + menuStatusRequest.getStoreId();
            //菜单状态缓存key
            hashKey = MENU_STATUS_HASH_KEY_PREFIX + ":" + baseController.getAccount();
        } else if (TASK_SUBMIT.contains(path)) {
            String menuName = MenuTypeEnum.getMenuNameByType(path);
            if (menuName != null) {
                hashKey = MENU_STATUS_HASH_KEY_PREFIX + ":" + baseController.getAccount();
                return updateCache(joinPoint, key, hashKey, menuName);
            }
        }
        return handleCache(joinPoint, key, hashKey);
    }

    @Around("execution(* com.mi.info.intl.retail.intlretail.app.controller.ProxyController.rmsTokenUserDataQuery(..))")
    public Object rmsTokenUserDataQueryCache(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object requestBody = args[0];
        log.info("requestBody is {}", JsonUtil.bean2json(requestBody));
        BaseController baseController = (BaseController) joinPoint.getTarget();
        String countryCode = baseController.getCountryCode();
        // 指定国家不应用缓存
        if (EXCLUDE_COUNTRY.contains(countryCode)) {
            return joinPoint.proceed();
        }
        String key = baseController.getCurrentDay();  // 第5个参数是key
        String hashKey = USER_BASE_DATA_HASH_KEY_PREFIX + ":" + baseController.getAccount();  // 第6个参数是hashKey
        return handleCache(joinPoint, key, hashKey);
    }

    @Around("execution(* com.mi.info.intl.retail.intlretail.app.controller.RmsProxyConfigController.homeFunctions(..))")
    public Object homeFunctionsHandleCache(ProceedingJoinPoint joinPoint) throws Throwable {
        BaseController baseController = (BaseController) joinPoint.getTarget();
        String countryCode = baseController.getCountryCode();
        // 指定国家不应用缓存
        if (EXCLUDE_COUNTRY.contains(countryCode)) {
            return joinPoint.proceed();
        }
        String key = baseController.getCurrentDay();  // 第5个参数是key
        String hashKey = MENU_HASH_KEY_PREFIX + ":" + baseController.getAccount();  // 第6个参数是hashKey
        return handleCache(joinPoint, key, hashKey);
    }

    // 定义精准切入点（根据实际类路径修改）
    @Around("execution(* com.mi.info.intl.retail.intlretail.app.controller.ProxyController.rmsTokenAndTypeQuery(..))")
    public Object rmsTokenAndTypeQueryHandleCache(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        // 第2个参数是path
        String path = (String) args[1];
        // 第3个参数是type
        String type = (String) args[2];
        // 接口名
        String fullPath = path + "/" + type;

        return submitDataHandle(joinPoint, fullPath);
    }

    // 定义精准切入点（根据实际类路径修改）
    @Around("execution(* com.mi.info.intl.retail.intlretail.app.controller.ProxyController.rmsNewSubmitData(..))")
    public Object rmsNewSubmitDataHandleCache(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        // 第1个参数是入参
        Object requestBody = args[0];
        log.info("requestBody is {}", JsonUtil.bean2json(requestBody));
        // 第2个参数是type
        String type = (String) args[1];

        String fullPath = SUBMITDATA + "/" + type;

        return submitDataHandle(joinPoint, fullPath);
    }

    Object handleCache(ProceedingJoinPoint joinPoint, String key, String hashKey) throws Throwable {
        Object cachedValue = CacheUtils.hget(key, hashKey, Object.class);
        if (cachedValue != null) {
            log.info("{} use cache, is {}", key + hashKey, JsonUtil.bean2json(cachedValue));
            return cachedValue;
        }

        // 执行原方法
        Object result = joinPoint.proceed();
        log.info("{} do not use cache, is {}", key + hashKey, JsonUtil.bean2json(result));

        // 结果不为空时更新缓存
        if (result != null) {
            CacheUtils.hputAllWithExpire(
                    key,
                    Collections.singletonMap(hashKey, result),
                    24,
                    TimeUnit.HOURS
            );
        }
        return result;
    }

    Object updateCache(ProceedingJoinPoint joinPoint, String key, String hashKey, String menuName) throws Throwable {
        // 执行原方法
        Object result = joinPoint.proceed();

        //缓存结果为空时直接返回
        Object cachedValue = CacheUtils.hget(key, hashKey, Object.class);
        if (cachedValue == null) {
            log.info("{} do not update cache, is {}", key + hashKey, JsonUtil.bean2json(result));
            return result;
        }

        // 结果为成功时更新缓存
        if (result != null) {
            // 将结果转换为DTO
            ProceedingResultDto requestDto = objectMapper.convertValue(result, ProceedingResultDto.class);
            // 将缓存数据转换为DTO
            String cachedJson = objectMapper.writeValueAsString(cachedValue);
            MenuStatusCacheDto cacheDto = objectMapper.readValue(cachedJson, MenuStatusCacheDto.class);

            // 检查code是否为0
            if (requestDto.getCode() == 0 && cacheDto.getData() != null) {
                // 更新对应菜单的状态
                for (MenuStatusCacheDto.MenuStatusList menuStatus : cacheDto.getData()) {
                    if (menuName.equals(menuStatus.getScreenName())) {
                        menuStatus.setMenuStoreJobFinish(true);
                        break;
                    }
                }

                // 更新缓存
                CacheUtils.hputAllWithExpire(
                        key,
                        Collections.singletonMap(hashKey, new CommonApiResponse<>(cacheDto.getData())),
                        24,
                        TimeUnit.HOURS
                );
                log.info("{} update cache, is {}", key + hashKey, JsonUtil.bean2json(cacheDto));
            }
        }
        return result;
    }

    Object deleteCache(ProceedingJoinPoint joinPoint, String key, String hashKey) throws Throwable {
        // 执行原方法
        Object result = joinPoint.proceed();

        //缓存结果为空时直接返回
        Object cachedValue = CacheUtils.hget(key, hashKey, Object.class);
        if (cachedValue == null) {
            log.info("{} do not have cache, is {}", key + hashKey, JsonUtil.bean2json(result));
            return result;
        }

        // 结果为成功时更新缓存
        if (result != null) {
            // 将结果转换为DTO
            ProceedingResultDto requestDto = objectMapper.convertValue(result, ProceedingResultDto.class);

            // 检查code是否为0
            if (requestDto.getCode() == 0) {
                CacheUtils.hdel(key, hashKey);
                log.info("{} delete cache, is {}", key + hashKey, JsonUtil.bean2json(requestDto));
            }
        }
        return result;
    }

    Object submitDataHandle(ProceedingJoinPoint joinPoint, String fullPath) throws Throwable {

        BaseController baseController = (BaseController) joinPoint.getTarget();
        String countryCode = baseController.getCountryCode();
        // 非指定path的请求和指定国家不应用缓存
        if ((!TASK_SUBMIT.contains(fullPath) && !USER_SUBMIT.contains(fullPath)) || EXCLUDE_COUNTRY.contains(countryCode)) {
            return joinPoint.proceed();
        }
        log.info("path is {}", fullPath);

        String key = baseController.getCurrentDay();
        //删除UserBaseData缓存
        if (USER_SUBMIT.contains(fullPath)) {
            String hashKey = USER_BASE_DATA_HASH_KEY_PREFIX + ":" + baseController.getAccount();
            return deleteCache(joinPoint, key, hashKey);
        }
        //更新MenuStatus缓存
        else if (TASK_SUBMIT.contains(fullPath)) {
            String menuName = MenuTypeEnum.getMenuNameByType(fullPath);
            if (menuName != null) {
                String hashKey = MENU_STATUS_HASH_KEY_PREFIX + ":" + baseController.getAccount();
                return updateCache(joinPoint, key, hashKey, menuName);
            }
        }

        return joinPoint.proceed();
    }
}
