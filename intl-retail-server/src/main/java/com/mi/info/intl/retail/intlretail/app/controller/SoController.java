package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.so.ImeiReportVerifyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiReportVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.QtyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetFilterListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyStatisticsRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyStatisticsResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetQtyBoardDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetQtyBoardDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetStoreListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetStoreListResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;

import com.mi.info.intl.retail.model.CommonApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/so")
public class SoController {
    @Resource
    private ImeiUploadService imeiUploadService;
    @Resource
    private QtyService qtyService;
    @Resource
    private ImeiReportVerifyService imeiReportVerifyService;




    @PostMapping("/submitImei")
    @ResponseBody
    public CommonApiResponse<Object> submitImei(@RequestBody SubmitImeiReq request) {
        log.info("submitImei request: {}", request);
        return imeiUploadService.submitImei(request);
    }

    @PostMapping("/imeiReportVerify")
    @ResponseBody
    public CommonApiResponse<Object> imeiReportVerify(@RequestBody ImeiReportVerifyRequest request) {
        return imeiReportVerifyService.imeiReportVerify(request);
    }

    @PostMapping("/getSkuList")
    @ResponseBody
    public CommonResponse<GetSkuListResponse> getSkuList(@RequestBody GetSkuListRequest request) {
        log.info("getSkuList request: {}", request);
        return qtyService.getSkuList(request);
    }

    @PostMapping("/submitQty")
    @ResponseBody
    public CommonResponse<Object> submitQty(@RequestBody SubmitQtyReq request) {
        log.info("submitQty request: {}", request);
        return qtyService.submitQty(request);
    }

    @PostMapping("/getFilterList")
    @ResponseBody
    public CommonResponse<GetFilterListResponse> getFilterList() {
        log.info("getFilterList request");
        return qtyService.getFilterList();
    }

    @PostMapping("/queryQtyStatistics")
    @ResponseBody
    public CommonResponse<QueryQtyStatisticsResponse> queryQtyStatistics(@RequestBody QueryQtyStatisticsRequest request) {
        log.info("queryQtyStatistics request: {}", request);
        return qtyService.queryQtyStatistics(request);
    }

    @PostMapping("/queryQtyList")
    @ResponseBody
    public CommonResponse<QueryQtyListResponse> queryQtyList(@RequestBody QueryQtyListRequest request) {
        log.info("queryQtyList request: {}", request);
        return qtyService.queryQtyList(request);
    }

    @PostMapping("/getQtyBoardData")
    @ResponseBody
    public CommonResponse<GetQtyBoardDataResponse> getQtyBoardData(@RequestBody GetQtyBoardDataRequest request) {
        log.info("getQtyBoardData request: {}", request);
        return qtyService.getQtyBoardData(request);
    }

    @PostMapping("/queryQtyDetail")
    @ResponseBody
    public CommonResponse<QueryQtyDetailResponse> queryQtyDetail(@RequestBody QueryQtyDetailRequest request) {
        log.info("queryQtyDetail request: {}", request);
        return qtyService.queryQtyDetail(request);
    }

    @PostMapping("/getStoreList")
    @ResponseBody
    public CommonResponse<GetStoreListResponse> getStoreList(@RequestBody GetStoreListRequest request) {
        log.info("getStoreList request: {}", request);
        return qtyService.getStoreList(request);
    }

    @PostMapping("/queryImeiListByPage")
    @ResponseBody
    public CommonApiResponse<ImeiListQueryResp> queryImeiListByPage(@RequestBody ImeiListQueryReq request) {
        log.info("queryImeiListByPage request: {}", request);
        return imeiUploadService.queryImeiListByPage(request);
    }

    @PostMapping("/queryImeiDetail")
    @ResponseBody
    public CommonApiResponse<ImeiDetailQueryResp> queryImeiDetail(@RequestBody ImeiDetailQueryReq request) {
        log.info("queryImeiDetail request: {}", request);
        return imeiUploadService.queryImeiDetail(request);
    }

    @PostMapping("/queryImeiSummary")
    @ResponseBody
    public CommonApiResponse<ImeiSummaryQueryResp> queryImeiSummary(@RequestBody ImeiSummaryQueryReq request) {
        log.info("queryImeiSummary request: {}", request);
        return imeiUploadService.queryImeiSummary(request);
    }

    @PostMapping("/getImportTemplate")
    @ResponseBody
    public CommonResponse<GetImportTemplateResponse> getImportTemplate(@RequestBody GetImportTemplateRequest request) {
        log.info("getImportTemplate request: {}", request);
        return qtyService.getImportTemplate(request);
    }
}
