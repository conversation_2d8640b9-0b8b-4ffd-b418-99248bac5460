package com.mi.info.intl.retail.intlretail.app.controller;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mi.info.intl.retail.core.crypto.AESUtil;
import com.mi.info.intl.retail.core.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.app.dto.ProxyApiResponse;
import com.mi.info.intl.retail.intlretail.app.dto.ProxyAppRequest;
import com.mi.info.intl.retail.intlretail.app.event.RmsApiRequestEvent;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsAipRequestInfo;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsProxyService;
import com.mi.info.intl.retail.ldu.enums.ResultCodeEnum;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;

/*
 * RequestMapping多路径兼容，不要删除
 */
@RestController
@RequestMapping({"/api/proxy", "/*/api/proxy"})
@Slf4j
public class ProxyController extends BaseController {


    static final String TOKEN_NULL_MESSAGE = "token is null";

    @Autowired
    private RmsProxyService rmsProxyService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private IntlFileUploadService intlFileUploadService;

    @Value("${itsm.key}")
    private String itsmKey; // 16 位密钥

    @PostMapping("/rmsUserToken2/{path}")
    @ResponseBody
    public CommonApiResponse<Object> rmsTokenQuery(@RequestBody(required = false) Object requestBody,
                                                   @PathVariable("path") String path) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        Assert.notNull(token, TOKEN_NULL_MESSAGE);
        String result = rmsProxyService.requestByUserToken("/api/data/v9.2/" + path, objectToString(requestBody),
                token.getToken().getTokenValue(), "POST");
        String storeId = getStoreId(requestBody);
        eventPublisher.publishEvent(
                new RmsApiRequestEvent(this, new RmsAipRequestInfo(path, null, getAccount(), storeId), token));
        return generateByResult(result);
    }

    @PostMapping("/rmsUserToken2/new_GetUserBaseData")
    @ResponseBody
    public CommonApiResponse<Object> rmsTokenUserDataQuery(@RequestBody(required = false) Object requestBody) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        Assert.notNull(token, TOKEN_NULL_MESSAGE);
        String result = rmsProxyService.requestByUserToken("/api/data/v9.2/new_GetUserBaseData",
                objectToString(requestBody), token.getToken().getTokenValue(), "POST");
        if (StringUtils.isEmpty(result)) {
            return new CommonApiResponse<>(Optional.ofNullable(result).orElse(""));
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(result); // 解析一次 JSON
            // 加密 UserAccount 逻辑
            if (rootNode.isObject() && rootNode.has("UserAccount")) {
                String userAccount = rootNode.get("UserAccount").asText();
                // 使用 AES 加密 userAccount
                String encryptedUserId = AESUtil.encrypt(itsmKey, userAccount);
                // 添加 UserIdEncrypted，加密的是User Account的信息
                ((ObjectNode) rootNode).put("UserIdEncrypted", encryptedUserId);
            }

            // 重新序列化为字符串
            String modifiedJson = mapper.writeValueAsString(rootNode);
            return new ProxyApiResponse(modifiedJson); // 返回加密后的 JSON 字符串
        } catch (JsonProcessingException e) {
            return new CommonApiResponse<>(result);
        }
    }

    @PostMapping("/rmsUserToken2/new_SubmitData/{type}")
    @ResponseBody
    public CommonApiResponse<Object> rmsNewSubmitData(@RequestBody(required = false) Object requestBody,
                                                      @PathVariable("type") String type) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        Assert.notNull(token, TOKEN_NULL_MESSAGE);
        String result = rmsProxyService.requestByUserToken("/api/data/v9.2/new_SubmitData", type,
                objectToString(requestBody), token.getToken().getTokenValue(), "POST");
        String storeId = getStoreId(requestBody);
        String path = "new_SubmitData";
        eventPublisher.publishEvent(
                new RmsApiRequestEvent(this, new RmsAipRequestInfo(path, type, getAccount(), storeId), token));
        return generateByResult(result);
    }

    @PostMapping("/rmsUserToken2/new_SubmitData/OfflineUpload")
    @ResponseBody
    public CommonApiResponse<Object> offlineUpload(@RequestBody(required = false) Object requestBody) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(JSONUtil.toJsonStr(requestBody));
            return intlFileUploadService.updateByGuid(requestBody, rootNode);
        } catch (JsonProcessingException e) {
            log.error("Error parsing JSON: ", e);
            return new CommonApiResponse<>(ResultCodeEnum.SYSTEM_ERROR.getCode(), ResultCodeEnum.SYSTEM_ERROR.getEnMsg(), "");
        }

    }

    @PostMapping("/rmsUserToken2/{path}/{type}")
    @ResponseBody
    public CommonApiResponse<Object> rmsTokenAndTypeQuery(@RequestBody(required = false) Object requestBody,
                                                          @PathVariable("path") String path, @PathVariable("type") String type) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        Assert.notNull(token, TOKEN_NULL_MESSAGE);
        String result = rmsProxyService.requestByUserToken("/api/data/v9.2/" + path, type, objectToString(requestBody),
                token.getToken().getTokenValue(), "POST");
        String storeId = getStoreId(requestBody);
        eventPublisher.publishEvent(
                new RmsApiRequestEvent(this, new RmsAipRequestInfo(path, type, getAccount(), storeId), token));
        return generateByResult(result);
    }

    private String getStoreId(Object requestBody) {
        Map<String, Object> requestBodyMap = JsonUtil.json2map(objectToString(requestBody));
        return (String) requestBodyMap.get("storeId");
    }

    /**
     * 请求参数序列化
     *
     * @param jsonObject
     * @return
     */
    private String objectToString(Object jsonObject) {
        String result;
        if (jsonObject == null || "".equals(jsonObject)) {
            return "{}";
        }
        try {
            result = objectMapper.writeValueAsString(jsonObject);
            log.info("rmsUserToken2 api请求体" + result);
        } catch (JsonProcessingException e) {
            throw new RetailRunTimeException("json serialize error:" + e);
        }
        return result;
    }

    /**
     * result适配 预处理
     *
     * @param result
     * @return
     */
    private CommonApiResponse<Object> generateByResult(String result) {
        if (StringUtils.isEmpty(result)) {
            return new CommonApiResponse<>(Optional.ofNullable(result).orElse(""));
        }
        try {
            new ObjectMapper().readTree(result);  // 尝试解析
            return new ProxyApiResponse(result);  // 成功则赋值
        } catch (JsonProcessingException e) {
            return new CommonApiResponse<>(result);
        }
    }

    @PostMapping("/file/download")
    public ResponseEntity<ByteArrayResource> getFileUser(@RequestBody ProxyAppRequest request) {
        JwtAuthenticationToken token = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        Assert.notNull(token, TOKEN_NULL_MESSAGE);
        return rmsProxyService.requestByUserToken(request.getPath(), token.getToken().getTokenValue(), "GET");
    }
}
