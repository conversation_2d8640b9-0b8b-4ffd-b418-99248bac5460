package com.mi.info.intl.retail.intlretail.app.controller.ldu;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.app.controller.BaseController;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RetailerAppConfigService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduReportLogService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.utils.DateUtils;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.mone.docs.annotations.http.MiApiRequestMethod;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-03
 */
@ApiModule(value = "International New Retail Platform", apiInterface = IntlLduReportLogController.class)
@RestController
@RequestMapping("/api/ldureport")
public class IntlLduReportLogController extends BaseController {

    @Resource
    private IntlLduReportLogService intlLduReportLogServiceService;

    @Resource
    RetailerAppConfigService retailerAppConfigService;

    @Resource
    private IProductQueryService iProductQueryService;

    /**
     * scan
     *
     * @param scanCodeReq
     * @return
     */
    @ApiDoc(name = "scan", value = "/api/ldureport/scan", method = MiApiRequestMethod.POST)
    @PostMapping("/scan")
    public CommonResponse<SnImeiGoodsInfoDto> scan(@RequestBody ScanCodeReq scanCodeReq) {
        return intlLduReportLogServiceService.scan(scanCodeReq);
    }

    /**
     * Get product history list
     *
     * @param intlLduReportReq
     * @return
     */
    @ApiDoc(name = "Get product history list", value = "/api/ldureport/historyList", method = MiApiRequestMethod.POST)
    @PostMapping("/historyList")
    public CommonApiResponse<Page<IntlLduReportLogDto>> historyList(@RequestBody IntlLduReportReq intlLduReportReq) {

        String startDate = intlLduReportReq.getStartDate();
        String endDate = intlLduReportReq.getEndDate();

        if (!ObjectUtil.isEmpty(startDate) && ObjectUtil.isEmpty(endDate)) {
            intlLduReportReq.setReportStartTime(DateUtils.getStartOfMonth(startDate).getTime());
            intlLduReportReq.setReportEndTime(DateUtils.getEndOfMonth(startDate).getTime());
        } else if (!ObjectUtil.isEmpty(endDate) && !ObjectUtil.isEmpty(startDate)) {
            intlLduReportReq.setReportStartTime(DateUtils.getStartOfDay(startDate).getTime());
            intlLduReportReq.setReportEndTime(DateUtils.getEndOfDay(endDate).getTime());
        }
        return intlLduReportLogServiceService.historyList(intlLduReportReq);
    }

    /**
     * Get product details
     *
     * @param intlLduReportReq
     * @return
     */
    @ApiDoc(name = "Get product details", value = "/api/ldureport/detailInfo", method = MiApiRequestMethod.POST)
    @PostMapping("/detailInfo")
    public CommonApiResponse<IntlLduReportLogDto> detailInfo(@RequestBody IntlLduReportReq intlLduReportReq) {
        CommonResponse<IntlLduReportLogDto> commonResponse = intlLduReportLogServiceService.detail(intlLduReportReq);
        return new CommonApiResponse<>(commonResponse.getData());
    }

    @ApiDoc(name = "submit", value = "/api/ldureport/submit", method = MiApiRequestMethod.POST)
    @PostMapping("/submit")
    public CommonResponse<Integer> submit(@RequestBody IntlLduReportSubmitReq intlLduReportDataReq) {
        String tokenValue = this.getTokenValue();
        String account = this.getAccount();
        RmsUserBaseDataResponse userBaseInfo = retailerAppConfigService.requestRmsGetUserInfo(account, tokenValue);
        return intlLduReportLogServiceService.submit(intlLduReportDataReq, userBaseInfo);
    }

    @PostMapping("/scanBarcode")
    public CommonResponse<SnImeiGoodsInfoDto> scanBarcode(@RequestBody BarcodeScanReq request) {
        return intlLduReportLogServiceService.scanBarcode(request);
    }

    @PostMapping("/getStoreInfo")
    public CommonResponse<StroeInfoDto> getStoreInfo(@RequestBody RmsPositionReq request) {
        return intlLduReportLogServiceService.getStoreInfo(request);
    }

    @PostMapping("/queryProductLines")
    public CommonApiResponse<List<ProductLineDto>> queryProductLines() {
        List<ProductLineDto> productLineDtos = iProductQueryService.queryProductLines();
        return new CommonApiResponse<>(productLineDtos);
    }

    @PostMapping("/export")
    public void export(@RequestBody IntlLduReportReq intlLduReportDataReq) {
        intlLduReportLogServiceService.excelExport(intlLduReportDataReq);
    }

    @PostMapping("/update")
    public CommonResponse<String> update(@RequestBody IntlLduReportReq intlLduReportDataReq) {
        return intlLduReportLogServiceService.update(intlLduReportDataReq);
    }

    @PostMapping("/detail")
    public CommonResponse<IntlLduReportLogDto> detail(@RequestBody IntlLduReportReq intlLduReportDataReq) {
        return intlLduReportLogServiceService.detail(intlLduReportDataReq);
    }
}
