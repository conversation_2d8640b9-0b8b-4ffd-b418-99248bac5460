package com.mi.info.intl.retail.intlretail.app.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.advice.excel.annotation.ExportExcel;
import com.mi.info.intl.retail.cooperation.task.dto.IntlInspectionTaskQuery;
import com.mi.info.intl.retail.cooperation.task.dto.dto.InspectionTaskConfDTO;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionTaskConfService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5
 **/
@RestController
@RequestMapping("/api/inspectionTask")
public class InspectionTaskController {

    @Resource
    private IntlInspectionTaskConfService inspectionTaskConfService;

    @PostMapping("/pageList")
    public CommonApiResponse<IPage<InspectionTaskConfDTO>> pageList(@RequestBody IntlInspectionTaskQuery query) {
        IPage<InspectionTaskConfDTO> inspectionTaskConfDTOIPage = inspectionTaskConfService.pageList(query);
        return new CommonApiResponse<>(inspectionTaskConfDTOIPage);
    }

    @PostMapping("/export")
    @ExportExcel(fileName = "巡检任务")
    public List<InspectionTaskConfDTO> export(@RequestBody IntlInspectionTaskQuery query) {
        IPage<InspectionTaskConfDTO> inspectionTaskConfDTOIPage = inspectionTaskConfService.pageList(query);
        return inspectionTaskConfDTOIPage.getRecords();
    }
}
