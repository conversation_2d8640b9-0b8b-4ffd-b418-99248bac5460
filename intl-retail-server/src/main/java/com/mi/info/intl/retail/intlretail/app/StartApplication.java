package com.mi.info.intl.retail.intlretail.app;

import com.xiaomi.mit.starter.initializer.MitProfile;
import com.xiaomi.mone.dubbo.docs.EnableDubboApiDocs;
import com.xiaomi.mone.http.docs.EnableHttpApiDocs;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * created by MIT
 *
 * <AUTHOR>
 * @date 2022/04/11
 */
@EnableDubboApiDocs
@EnableHttpApiDocs
@SpringBootApplication(scanBasePackages = {"com.mi.info.intl.retail", "com.xiaomi.mone.dubbo.server.registry", "com.xiaomi.com.i18n.area"})
@EnableDubbo(scanBasePackages = {"com.mi.info.intl.retail", "com.mi.info.intl.retail.intlretail.service", "com.mi.info.intl.retail.*.app"})
@EnableAsync
@EnableCaching
@MitProfile(enable = false)
@MapperScan(basePackages = {"com.mi.info.intl.retail.**.mapper", "com.mi.info.intl.**.mapper.**"})
public class StartApplication {

    private static final Class[] STARTUP_APP = {StartApplication.class
    };

    public static void main(String[] args) {
        SpringApplication.run(STARTUP_APP, args);
    }

}

