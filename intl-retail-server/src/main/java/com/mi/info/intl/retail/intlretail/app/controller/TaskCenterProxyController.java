package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.cooperation.task.app.mq.SRTaskActionEnum;
import com.mi.info.intl.retail.intlretail.app.event.RmsApiRequestEvent;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsAipRequestInfo;
import com.mi.info.intl.retail.intlretail.service.api.proxy.TaskCenterProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.ConfirmPopWindowsReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.HeadCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCalendarReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterCommonReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterDetailReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterTaskNumReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.cnzone.nr.common.utils.GsonUtil;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/taskcenter")
public class TaskCenterProxyController extends BaseController {

    @Autowired
    private TaskCenterProxyService taskCenterProxyService;
    //督导Job：500900002或者100000051或者100000024
    @Value("${promotion.notify:500900002,100000051,100000024}")
    private List<String> notifyJobTitles;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 列表视图(日/周/月)
     */
    @PostMapping("/getCalendar")
    @ResponseBody
    public CommonApiResponse<Object> getCalendarByType(@RequestBody TaskCenterCalendarReq req) {
        if (StringUtils.isNotBlank(req.getJobValue()) && notifyJobTitles.contains(req.getJobValue())) {
            long startTime = System.currentTimeMillis();
            Object calendarEventDetail = taskCenterProxyService.getEventCalendarByType(req);
            long endTime = System.currentTimeMillis();
            log.info("getEventCalendarByType req:{}, cost:{}", GsonUtil.toJson(req), endTime - startTime);
            return new CommonApiResponse<>(calendarEventDetail);
        }
        Object calendarDetail = taskCenterProxyService.getCalendarByType(req);
        return new CommonApiResponse<>(calendarDetail);
    }

    @PostMapping("/noNeedCompleteTask")
    @ResponseBody
    public CommonApiResponse<Void> noNeedCompleteTask(@RequestBody TaskCenterFinishTaskReq req) {
        taskCenterProxyService.noNeedCompleteTask(req);
        return new CommonApiResponse<>(null);
    }
    
    @PostMapping("/finishOuterTask")
    @ResponseBody
    public CommonApiResponse<Void> finishOuterTask(@RequestBody TaskCenterFinishTaskReq req) {
        try {
            if (notifyJobTitles.contains(req.getJobValue())) {
                log.info("督导员 销量上报(无销量), req: {}", req);
                String actionName = SRTaskActionEnum.SALES_UPLOAD_NO_SALES.getActionName();
                eventPublisher.publishEvent(new RmsApiRequestEvent(this,
                        new RmsAipRequestInfo(actionName, actionName, getAccount(), req.getStoreId())));
            } else {
                taskCenterProxyService.finishOuterTask(req);
            }
            return new CommonApiResponse<>(null);
        } catch (Exception e) {
            throw new RuntimeException("Error finish outer task: " + e.getMessage(), e);
        }
    }
    
    @PostMapping("/queryTaskNum")
    @ResponseBody
    public CommonApiResponse<Object> queryTaskNum(@RequestBody TaskCenterTaskNumReq req) {
        if (StringUtils.isNotBlank(req.getJobValue()) && notifyJobTitles.contains(req.getJobValue())) {
            long startTime = System.currentTimeMillis();
            Object eventTaskNums = taskCenterProxyService.queryEventTaskNum(req);
            long endTime = System.currentTimeMillis();
            log.info("queryEventTaskNum req:{}, cost:{}", RetailJsonUtil.toJson(req), endTime - startTime);
            return new CommonApiResponse<>(eventTaskNums);
        }
        Object taskNums = taskCenterProxyService.queryTaskNum(req);
        return new CommonApiResponse<>(taskNums);
    }

    @PostMapping("/getDetailTaskInfo")
    @ResponseBody
    public CommonApiResponse<Object> getDetailTaskInfo(@RequestBody TaskCenterDetailReq req) {
        Integer businessTypeId = req.getBusinessTypeId();
        // 如果是201或者202，调用大脑的getDetailTaskEventInfo接口，否则继续调原接口
        if (businessTypeId != null && (businessTypeId == 201 || businessTypeId == 202)) {
            Object detailTaskInfo = taskCenterProxyService.getDetailTaskEventInfo(req);
            return new CommonApiResponse<>(detailTaskInfo);
        } else {
            Object detailTaskInfo = taskCenterProxyService.getDetailTaskInfo(req);
            return new CommonApiResponse<>(detailTaskInfo);
        }
    }

    @PostMapping("/getPopWindowContent")
    @ResponseBody
    public CommonApiResponse<Object> getPopWindowContent(@RequestBody TaskCenterCommonReq req) {
        Object popWindowContent = taskCenterProxyService.getPopWindowContent(req);
        return new CommonApiResponse<>(popWindowContent);
    }

    @PostMapping("/confirmPopWindow")
    @ResponseBody
    public CommonApiResponse<Void> confirmPopWindow(@RequestBody ConfirmPopWindowsReq req) {
        taskCenterProxyService.confirmPopWindow(req);
        return new CommonApiResponse<>(null);
    }

    @PostMapping("/getHeadCalendar")
    @ResponseBody
    public CommonApiResponse<Object> getCalendarForHead(@RequestBody HeadCalendarReq req) {
        Object calendarForHead = taskCenterProxyService.getCalendarForHead(req);
        return new CommonApiResponse<>(calendarForHead);
    }
}
