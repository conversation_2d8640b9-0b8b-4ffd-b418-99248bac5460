package com.mi.info.intl.retail.intlretail.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 订单数据对象
 * @date 2022/06/22 16:00:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("xm_test_order")
public class OrderDO implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 订单ID
     */
    @TableField(value = "order_id")
    private String orderId;

    /**
     * 订单项
     */
    @TableField(value = "order_item")
    private String orderItem;

    /**
     * 订单状态
     */
    @TableField(value = "order_status")
    private String status;

    /**
     * 订单价格
     */
    @TableField(value = "total_price")
    private Double totalPrice;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;



}

