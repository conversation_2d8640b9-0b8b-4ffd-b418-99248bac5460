package com.mi.info.intl.retail.intlretail.infra.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.stream.JsonReader;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.io.Serializable;
import java.util.List;

@Component
public class XiaomiRetailConfig {
    public static final String FILE_NAME = "xiaomi-retail-config.json";

    @Autowired
    private ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        try (InputStream inputStream = JsonReader.class.getClassLoader().getResourceAsStream(FILE_NAME)) {
            if (inputStream != null) {
                config = objectMapper.readValue(inputStream, new TypeReference<List<Config>>() {
                });
            }
        } catch (Exception e) {
            throw new RuntimeException("Error reading xiaomi-retail-config.json: " + e.getMessage(), e);
        }
    }

    @Getter
    @Setter
    public List<Config> config;

    @Getter
    @Setter
    public static class Config implements Serializable {
        /**
         * 中文简称
         */
        @JsonProperty("short_name_cn")
        private String shortNameCn;

        /**
         * 英文简称
         */
        @JsonProperty("short_name_en")
        private String shortNameEn;

        /**
         * 英文全称
         */
        @JsonProperty("full_name_en")
        private String fullNameEn;

        /**
         * 地区ID
         */
        @JsonProperty("area_id")
        private String areaId;

        /**
         * 时区
         */
        @JsonProperty("timezone")
        private String timeZone;

        /**
         * 三字母ID
         */
        @JsonProperty("three_letter_id")
        private String threeLetterId;

        /**
         * 数字代码
         */
        @JsonProperty("digital_code")
        private int digitalCode;

        /**
         * WMS国家ID
         */
        @JsonProperty("wms_country_id")
        private int wmsCountryId;

        /**
         * 货币
         */
        @JsonProperty("currency")
        private String currency;

        /**
         * 货币代码
         */
        @JsonProperty("currency_code")
        private String currencyCode;

        /**
         * 货币小数位数
         */
        @JsonProperty("currency_digits")
        private int currencyDigits;

        /**
         * 货币千位符号
         */
        @JsonProperty("currency_thousand_symbol")
        private String currencyThousandSymbol;

        /**
         * 货币小数符号
         */
        @JsonProperty("currency_digits_symbol")
        private String currencyDigitsSymbol;

        /**
         * 地区代码
         */
        @JsonProperty("locale_code")
        private String localeCode;

        /**
         * 电话前缀
         */
        @JsonProperty("phone_prefix")
        private String phonePrefix;

        /**
         * IDC
         */
        @JsonProperty("idc")
        private String idc;

        /**
         * 区域
         */
        @JsonProperty("region")
        private String region;

        /**
         * 洲
         */
        @JsonProperty("continent")
        private String continent;

        /**
         * 状态
         */
        @JsonProperty("status")
        private int status;

        /**
         * 扩展信息
         */
        private Extend extend;

        public static class Extend implements Serializable {

            @JsonProperty("unicode_list")
            private String unicodeList;

            @JsonProperty("tax_rate")
            private String taxRate;
        }
    }


}
