package com.mi.info.intl.retail.intlretail.infra.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("xm_user_device")
public class UserDeviceDO implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 用户账号
     */
    @TableField("user_account")
    private String userAccount;

    /**
     * 设备号
     */
    @TableField("ga_id")
    private String gaId;

    /**
     * 设备类型: Android, IOS
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 是否绑定
     */
    @TableField("bind")
    private Integer bind;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Instant createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Instant updateTime;
}
