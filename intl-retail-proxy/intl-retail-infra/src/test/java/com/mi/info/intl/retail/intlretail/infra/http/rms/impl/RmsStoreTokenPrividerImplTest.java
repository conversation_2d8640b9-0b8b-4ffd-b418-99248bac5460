package com.mi.info.intl.retail.intlretail.infra.http.rms.impl;

import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsStoreTokenPrivider;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.infra.config.AppliUserTokenConfig;
import com.mi.info.intl.retail.intlretail.infra.http.rms.dto.RmsToken;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@SpringBootTest
class RmsStoreTokenPrividerImplTest {

    @Autowired
    private RmsStoreTokenPrivider rmsStoreTokenPrivider;

    @MockBean
    private AppliUserTokenConfig tokenConfig;

    @MockBean
    private RestTemplate restTemplate;

    @InjectMocks
    private RmsStoreTokenPrividerImpl rmsStoreTokenPrividerImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getToken_validToken() throws Exception {

        // 创建有效的RmsToken
        RmsToken validToken = new RmsToken();
        validToken.setAccessToken("valid_access_token");
        validToken.setExpiresOn(String.valueOf(System.currentTimeMillis() / 1000 + 3600)); // 1小时后过期

        // 使用反射初始化multiEnvTokenCacheMap
        List<RmsToken> tokenCache = new ArrayList<>();
        tokenCache.add(validToken);
        Map<String, List<RmsToken>> multiEnvTokenCacheMap = new ConcurrentHashMap<>();
        multiEnvTokenCacheMap.put("uat", tokenCache);
        setField(rmsStoreTokenPrividerImpl, "multiEnvTokenCacheMap", multiEnvTokenCacheMap);


        // 调用getToken方法
        String accessToken = rmsStoreTokenPrividerImpl.getToken("uat");

        // 验证第一次获取的为：当tokenCache中第1个token已过期时，更新后的新token
        assertNotNull(accessToken);
        assertEquals(tokenCache.get(0).getAccessToken(), accessToken);

    }

    @Test
    void getToken_ExpiredToken() throws Exception {

        // 创建已过期的RmsToken
        RmsToken expiredToken = new RmsToken();
        expiredToken.setAccessToken("expired_access_token");
        expiredToken.setExpiresOn(String.valueOf(System.currentTimeMillis() / 1000 - 60)); // 过期时间在1分钟前

        // 创建有效的RmsToken
        RmsToken validToken = new RmsToken();
        validToken.setAccessToken("valid_access_token");
        validToken.setExpiresOn(String.valueOf(System.currentTimeMillis() / 1000 + 3600)); // 1小时后过期

        // 使用反射初始化tokenCache
        List<RmsToken> tokenCache = new ArrayList<>();
        tokenCache.add(expiredToken);
        tokenCache.add(validToken);
        setField(rmsStoreTokenPrividerImpl, "tokenCache", tokenCache);

        // 创建ClientInfo列表
        AppliUserTokenConfig.ClientInfo clientInfo = new AppliUserTokenConfig.ClientInfo();
        clientInfo.setClientId("test_client_id");
        clientInfo.setClientSecret("test_client_secret");
        List<AppliUserTokenConfig.ClientInfo> clientInfos = new ArrayList<>();
        clientInfos.add(clientInfo);
        when(tokenConfig.getClients()).thenReturn(clientInfos);
        when(tokenConfig.getUrl()).thenReturn("https://login.microsoftonline.com/8b69898f-0512-4d15-9f34-be92f222d528/oauth2/token");
        Map<String, String> resources = new HashMap<>();
        resources.put("uat", "https://rmsuat.crm5.dynamics.com");
        when(tokenConfig.getResources()).thenReturn(resources);

        // 模拟doGetToken返回有效token
        RmsToken newValidToken = new RmsToken();
        newValidToken.setAccessToken("new_valid_access_token");
        newValidToken.setExpiresOn(String.valueOf(System.currentTimeMillis() / 1000 + 3600)); // 1小时后过期
        ResponseEntity<String> newResponse = ResponseEntity.ok(JsonUtil.bean2json(newValidToken));
        when(restTemplate.exchange(
                any(String.class),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(String.class)
        )).thenReturn(newResponse);

        // 调用getToken方法
        String accessToken = rmsStoreTokenPrividerImpl.getToken("uat");

        // 验证第一次获取的为：当tokenCache中第1个token已过期时，更新后的新token
        assertNotNull(accessToken);
        assertEquals(newValidToken.getAccessToken(), accessToken);
        assertEquals(tokenCache.get(0).getAccessToken(), accessToken);

        // 验证第二次获取的为tokenCache中第2个有效token
        String secondToken = rmsStoreTokenPrividerImpl.getToken("uat");
        assertEquals(validToken.getAccessToken(), secondToken);
    }

    // 辅助方法：使用反射设置私有字段的值
    private void setField(Object target, String fieldName, Object value) throws NoSuchFieldException, IllegalAccessException {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
} 