package com.mi.info.intl.retail.intlretail.service.app.store.impl.gateway;

import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.common.utils.RpcContextUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRmsRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RmsRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.IGateWayChannelInfoService;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto.GateWayChannelInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.NullArgumentException;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@DubboService(group = "${store.dubbo.group:}", interfaceClass = IGateWayChannelInfoService.class)
@Service
@Slf4j
public class GateWayChannelInfoServiceImpl implements IGateWayChannelInfoService {

    static final String QUERY_CHANNELINFO_PATH = "/api/data/v9.2/new_QueryChannelInfo";

    static final String QUERY_BUSINESSDATA_PATH = "/api/data/v9.2/new_QueryBusinessData";

    static final String QUERY_RETAILERLIST_PATH = "/api/data/v9.2/new_QueryBusinessData";

    private static final String QUERY_BUSINESSDATA_TYPE = "GetUserListByMid";

    private static final String QUERY_RETAILERLIST_TYPE = "QueryRetailsList";

    private static final String QUERY_BUSINESSDATA_SOURCE_TYPE = "MiRetail";
    @Autowired
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;

    /*
     * 调用方：渠道建店PC端、中央大脑后端
     */
    @Override
    public GateWayChannelInfoResponse queryChannelInfo(ChannelInfoRequest requestBody) {
        try {
            // universalProxyApi通过接口被前端通过网关，后端dubbo同时调用,
            // 获取areaId时按照UniversalProxyRequest > RpcContext的优先级取值
            String areaId = Optional.ofNullable(requestBody.getAreaId())
                    .orElseGet(() -> RpcContextUtil.getAreaId(RpcContext.getContext().getAttachment("heracontext")));
            if (areaId == null || areaId.isEmpty() || areaId.equals("null")) {
                throw new NullArgumentException("areaId is null or areaId is empty");
            }

            ChannelInfoRmsRequest channelInfoRmsRequest = new ChannelInfoRmsRequest();
            channelInfoRmsRequest.setType(requestBody.getType());
            channelInfoRmsRequest.setSearch(requestBody.getSearch());
            channelInfoRmsRequest.setCodes(requestBody.getCodes());
            channelInfoRmsRequest.setCountryCode(requestBody.getCountryCode());
            RmsRequest rmsRequest = new RmsRequest();
            rmsRequest.setInput(JsonUtil.bean2json(channelInfoRmsRequest));


            List<ChannelInfoResponse> channelInfoResponseList = rmsAppliUserOauthServiceProvider
                    .httpForList(areaId, QUERY_CHANNELINFO_PATH, rmsRequest, "POST", ChannelInfoResponse.class);

            // 根据code对List<ChannelInfoResponse>去重
            channelInfoResponseList = new ArrayList<>(channelInfoResponseList.stream()
                    .collect(Collectors.toMap(
                            ChannelInfoResponse::getBasic, // 使用 code 作为键
                            response -> response,          // 使用 ChannelInfoResponse 对象作为值
                            (existing, replacement) -> existing // 保留第一个出现的对象
                    )).values());


            GateWayChannelInfoResponse response = new GateWayChannelInfoResponse();
            response.setCode(0);
            response.setMessage("ok");
            response.setData(channelInfoResponseList);
            return response;
        } catch (Exception e) {
            throw new RuntimeException("GateWayChannelInfoServiceImpl.queryChannelInfo error", e);
        }
    }

    @Override
    public List<BusinessDataResponse> queryBusinessData(BusinessDataInputRequest requestBody) {
        try {
            BusinessDataRequest request = new BusinessDataRequest();
            request.setType(QUERY_BUSINESSDATA_TYPE);
            request.setSourceType(QUERY_BUSINESSDATA_SOURCE_TYPE);
            request.setInput(JsonUtil.bean2json(requestBody));
            List<BusinessDataResponse> result = rmsAppliUserOauthServiceProvider
                    .httpForList(requestBody.getRegion(), QUERY_BUSINESSDATA_PATH, request, "POST",
                            BusinessDataResponse.class);
            return result;
        } catch (Exception e) {
            throw new RuntimeException("queryBusinessData_error", e);
        }
    }

    @Override
    public List<RetailerListResponse> queryRetailerList(RetailerListRequest requestBody) {
        try {
            BusinessDataRequest request = new BusinessDataRequest();
            request.setType(QUERY_RETAILERLIST_TYPE);
            request.setSourceType(QUERY_BUSINESSDATA_SOURCE_TYPE);
            request.setInput(JsonUtil.bean2json(requestBody));
            List<RetailerListResponse> result = rmsAppliUserOauthServiceProvider
                    .httpForList(requestBody.getAreaId(), QUERY_RETAILERLIST_PATH, request, "POST",
                            RetailerListResponse.class);
            return result;
        } catch (Exception e) {
            throw new RuntimeException("queryRetailerList_error", e);
        }
    }
}