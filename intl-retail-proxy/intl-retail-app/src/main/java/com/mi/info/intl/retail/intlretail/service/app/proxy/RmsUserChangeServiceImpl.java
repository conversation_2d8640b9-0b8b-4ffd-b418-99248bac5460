package com.mi.info.intl.retail.intlretail.service.app.proxy;

import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.infra.mq.RocketMQProducer;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RmsUserChangeService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.ApiCommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.RmsPositionChangeReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@DubboService(interfaceClass = RmsUserChangeService.class, group = "${center.dubbo.group:}")
@Service
@Slf4j
public class RmsUserChangeServiceImpl implements RmsUserChangeService {

    @Value("${intl-retail.rocketmq.user-change.topic:brain-platform-task-personnel-change}")
    private String topic;

    @Value("${intl-retail.rocketmq.user-change.train-topic:training-learn-personnel-change}")
    private String trainTopic;

    @Value("${intl-retail.rocketmq.user-change.tag:USER_POSITION_UPDATE}")
    private String tag;

    @Autowired
    private RocketMQProducer rocketMQProducer;

    @Override
    public ApiCommonResponse<String> sendPositionInfoToBrain(RmsPositionChangeReq req) {
        final String json = JsonUtil.bean2json(req);
        String destination = "";
        if (req.getType() == 11) {
            destination = trainTopic + ":" + tag;
        } else {
            destination = topic + ":" + tag;
        }
        log.info("sendPositionInfoToBrain req:{}", json);
        String msgId = rocketMQProducer.sendMessageWithResult(destination, json);
        return new ApiCommonResponse<>(msgId);
    }
}
