package com.mi.info.intl.retail.intlretail.service.app.jobhandler;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlBigPromotionConf;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlInspectionMessageInstance;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.cooperation.task.inspection.BigPromotionConfigService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlInspectionMessageInstanceService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.domain.push.constant.CountryLanguageEnum;
import com.mi.info.intl.retail.intlretail.infra.rpc.TaskCenterServiceAdapter;
import com.mi.info.intl.retail.intlretail.service.api.job.InspectionTaskJobProvider;
import com.mi.info.intl.retail.intlretail.service.api.messagepush.MessagePushService;
import com.mi.info.intl.retail.intlretail.service.api.messagepush.dto.MessagePushRequest;
import com.mi.info.intl.retail.intlretail.service.api.messagepush.dto.MessagePushResponse;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import com.mi.xms.sdk.T;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.TaskInstanceReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.TaskInstanceByTypeResp;
import com.xiaomi.nr.job.core.context.JobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/13
 **/
@Slf4j
@DubboService(group = "${center.dubbo.group:}", interfaceClass = InspectionTaskJobProvider.class)
@Component
public class InspectionTaskJobProviderImpl implements InspectionTaskJobProvider {

    @Resource
    private IntlCountryTimezoneService intlCountryTimezoneService;

    @Resource
    private IntlInspectionMessageInstanceService intlInspectionMessageInstanceService;

    @Resource
    private BigPromotionConfigService bigPromotionConfService;

    @Resource
    private TaskCenterServiceAdapter taskCenterServiceAdapter;

    @Resource
    private MessagePushService messagePushService;

    @Resource
    private IntlRmsUserService rmsUserService;

    private static final String MESSAGE_TEMPLATE =
            "%s，%s-%s. During this period, if there are any sales or inventory upload tasks, " +
            "all models and any other individual tasks must be completed. Please check the [Task Stores]" +
            " located at the top right corner of the Task tab for details.";


    private static final String MESSAGE_TITLE = "New Product Tasks";

    private static final String MESSAGE_KEY = "message.templateContent3";

    private static final String DEFAULT_CONTENT = "Click to view details, click to jump to the Task Store page";


    /**
     * 用于判断是否是早上9点
     */
    private static final Integer NINE_HOURS = 9;

    @Value("${promotion.url:manpower/TaskStores}")
    private String notifyUrl;



    /**
     * 查询大促任务消息是否可以推送
     */
    @Override
    public void queryTaskInspectionPush(String jobParam) {

        JobHelper.log("queryTaskInspectionPush begin execute");

        List<IntlInspectionMessageInstance> intlInspectionMessageInstanceList = new ArrayList<>();

        //1.获取国家时间信息
        List<IntlRmsCountryTimezone> countryTimezoneList = intlCountryTimezoneService.list();


        countryTimezoneList.forEach(countryTimezone -> {
            LocalDateTime localDateTime = DateTimeUtil.getLocalDateTimeByCountryCode(countryTimezone.getCountryCode());
            String promotionStartTime = DateTimeUtil.formatLocalDateTimeWithCustomTime(localDateTime, "00:00:00");
            String promotionEndTime = DateTimeUtil.formatLocalDateTimeWithCustomTime(localDateTime, "23:59:59");

            //查询今天开始大促任务
            //查询今天开始且未结束、未停用的大促任务
        List<IntlBigPromotionConf> todayStartPromotions = bigPromotionConfService.list(Wrappers.<IntlBigPromotionConf>lambdaQuery()
                    .eq(IntlBigPromotionConf::getStartTime, promotionStartTime)
                    .eq(IntlBigPromotionConf::getIsDisabled, 0)  // 0表示未停用
                    .ge(IntlBigPromotionConf::getEndTime, promotionEndTime)
                .eq(IntlBigPromotionConf::getCountry, countryTimezone.getCountryCode()));  // 结束时间大于当前时间

            if (CollUtil.isEmpty(todayStartPromotions)) {
                log.info("没有今天开始的大促任务");
                return;
            }

            // 获取已生成消息实例的大促任务ID列表
            List<Integer> promotionIds = todayStartPromotions.stream()
                    .map(IntlBigPromotionConf::getId)
                    .collect(Collectors.toList());


            List<Integer> existingMessagePromotionIds = intlInspectionMessageInstanceService.list(
                            Wrappers.<IntlInspectionMessageInstance>lambdaQuery()
                                    .in(IntlInspectionMessageInstance::getPromotionConfId, promotionIds))
                    .stream()
                    .map(IntlInspectionMessageInstance::getPromotionConfId)
                    .distinct()
                    .collect(Collectors.toList());

            // 找出未生成消息实例的大促任务
            List<IntlBigPromotionConf> needGeneratePromotions = todayStartPromotions.stream()
                    .filter(promotion -> !existingMessagePromotionIds.contains(promotion.getId()))
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(needGeneratePromotions)) {
                log.info("所有大促任务都已生成消息实例");
                return;
            }

            log.info("需要生成消息实例的大促任务ID: {}", needGeneratePromotions.stream().map(IntlBigPromotionConf::getId).collect(Collectors.toList()));

            // 后续处理未生成消息实例的大促任务
            needGeneratePromotions.forEach(promotion -> {

                //从大脑根据国家和任务类型（巡检任务）获取有任务用户
                TaskInstanceReq req = new TaskInstanceReq();
                req.setAreaId(countryTimezone.getCountryCode());
                //105 测试数据，后续使用201
                req.setBusinessTypeId(201);
                TaskInstanceByTypeResp taskInstanceByTypeResp = taskCenterServiceAdapter.queryMiIdByType(req);

                if (Objects.nonNull(taskInstanceByTypeResp) &&
                        CollUtil.isNotEmpty(taskInstanceByTypeResp.getList())) {

                    List<Long> midList = taskInstanceByTypeResp.getList().stream()
                            .map(TaskInstanceByTypeResp.TaskInstanceByType::getMid).collect(Collectors.toList());

                    List<IntlRmsUser> rmsUserList = rmsUserService.list(Wrappers.<IntlRmsUser>lambdaQuery()
                            .in(IntlRmsUser::getMiId, midList));

                    Map<Long, IntlRmsUser> rmsUserMap = rmsUserList.stream()
                            .collect(Collectors.toMap(IntlRmsUser::getMiId,
                                    Function.identity(), (v1, v2) -> v1));


                    taskInstanceByTypeResp.getList().forEach(taskInstance -> {

                        // 优化用户语言获取逻辑，兜底处理
                        IntlRmsUser intlRmsUser = rmsUserMap.get(taskInstance.getMid());
                        String languageCode = (intlRmsUser == null || StringUtils.isBlank(intlRmsUser.getLanguageCode()))
                                ? "en-US" : intlRmsUser.getLanguageCode();

                        // 构建消息实例信息
                        IntlInspectionMessageInstance inspectionMessageInstance = new IntlInspectionMessageInstance();
                        inspectionMessageInstance.setCountry(promotion.getCountry());
                        inspectionMessageInstance.setLanguageCode(languageCode);
                        inspectionMessageInstance.setPromotionConfId(promotion.getId());
                        inspectionMessageInstance.setMid(taskInstance.getMid());
                        String startTime = DateTimeUtil.formatDateToYMD(promotion.getStartTime());
                        String endTime = DateTimeUtil.formatDateToYMD(promotion.getEndTime());
                        String message = String.format(MESSAGE_TEMPLATE, promotion.getName(), startTime, endTime);
                        inspectionMessageInstance.setTitle(MESSAGE_TITLE);
                        Long timestamp = DateTimeUtil.toTimestamp(LocalDateTime.now());
                        inspectionMessageInstance.setContent(message);
                        String params = String.format("promotionName:%s,startTime:%s,endTime:%s", promotion.getName(), startTime, endTime);
                        inspectionMessageInstance.setParams(params);
                        inspectionMessageInstance.setCreatedAt(Math.toIntExact(timestamp));
                        inspectionMessageInstance.setUpdatedAt(Math.toIntExact(timestamp));
                        intlInspectionMessageInstanceList.add(inspectionMessageInstance);

                    });

                }

            });

        });


        //7.写入大促任务消息推送表
        if (CollUtil.isNotEmpty(intlInspectionMessageInstanceList)) {
            intlInspectionMessageInstanceService.saveBatch(intlInspectionMessageInstanceList);
        }

    }


    /**
     * 推送消息
     */
    @Override
    public void pushMessage(String jobParam) {

        JobHelper.log("pushInspectionTask begin execute");
        Boolean isPush = StringUtils.isNotEmpty(jobParam) ? new JSONObject(jobParam).getBool("isPush") : Boolean.FALSE;
        // 获取所有国家时区信息
        List<IntlRmsCountryTimezone> countryTimezoneList = intlCountryTimezoneService.list();

        countryTimezoneList.forEach(countryTimezone -> {

            // 获取当前国家的本地时间
            LocalDateTime localDateTime = DateTimeUtil.getLocalDateTimeByCountryCode(countryTimezone.getCountryCode());

            if (null == localDateTime) {
                log.info("获取国家时间失败,国家:{}", countryTimezone.getCountryCode());
                return;
            }

            // 如果isPush为false或空，才判断是否为早上9点
            if (!isPush) {
                if (localDateTime.getHour() != NINE_HOURS) {
                    log.info("当前国家的时间不是早上9点,国家:{},时间:{}", countryTimezone.getCountryCode(), localDateTime);
                    return;
                }
            }

            // 查询该国家未推送的消息实例
            List<IntlInspectionMessageInstance> inspectionMessageInstanceList =
                    intlInspectionMessageInstanceService.list(Wrappers.<IntlInspectionMessageInstance>lambdaQuery()
                            .eq(IntlInspectionMessageInstance::getCountry, countryTimezone.getCountryCode())
                            .eq(IntlInspectionMessageInstance::getPush, Boolean.FALSE));

            if (CollUtil.isEmpty(inspectionMessageInstanceList)) {
                log.info("没有需要推送的大促用户，当前大促国家:{}", countryTimezone.getCountryCode());
                return;
            }

            // 收集推送消息失败的实例
            List<IntlInspectionMessageInstance> failedMessageInstanceList = new ArrayList<>();
            List<Long> midList = inspectionMessageInstanceList.stream().map(IntlInspectionMessageInstance::getMid).collect(Collectors.toList());
            List<IntlRmsUser> rmsUserList = rmsUserService.list(Wrappers.<IntlRmsUser>lambdaQuery()
                    .in(IntlRmsUser::getMiId, midList));

            Map<Long, IntlRmsUser> rmsUserMap = rmsUserList.stream()
                    .collect(Collectors.toMap(IntlRmsUser::getMiId,
                            Function.identity(), (v1, v2) -> v1));
            for (IntlInspectionMessageInstance messageInstance : inspectionMessageInstanceList) {
                IntlRmsUser rmsUser = rmsUserMap.getOrDefault(messageInstance.getMid(), null);
                String languageKey = null == rmsUser ? "en-US" : rmsUser.getLanguageCode();

                String content = parseContent(messageInstance, languageKey);

                    MessagePushRequest messagePushRequest = new MessagePushRequest();
                    messagePushRequest.setUserAccountList(Collections.singletonList(String.valueOf(messageInstance.getMid())));
                    messagePushRequest.setTitle(messageInstance.getTitle());
                    messagePushRequest.setContent(content);
                    messagePushRequest.setPageUrl(notifyUrl);
                    MessagePushResponse messagePushResponse = messagePushService.sendPushMessage(messagePushRequest);

                    // 推送失败
                    if (messagePushResponse.code == 500) {
                        failedMessageInstanceList.add(messageInstance);
                        log.error("推送失败,国家:{},mid:{}", countryTimezone.getCountryCode(), messageInstance.getMid());
                    }

            }

            // 获取失败实例的ID集合
            Set<Integer> failedIds = failedMessageInstanceList.stream()
                    .map(IntlInspectionMessageInstance::getId)
                    .collect(Collectors.toSet());

            // 过滤掉失败的实例，并设置推送状态
            List<IntlInspectionMessageInstance> successList = inspectionMessageInstanceList.stream()
                    .filter(instance -> !failedIds.contains(instance.getId()))
                    .peek(instance -> instance.setPush(Boolean.TRUE))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(successList)) {
                intlInspectionMessageInstanceService.updateBatchById(successList);
            }

            log.info("推送完成,国家:{},推送成功人数:{},失败人数:{}", countryTimezone.getCountryCode(), successList.size(), failedMessageInstanceList.size());
        });
    }



    @Override
    public void taskInstanceExpire(String jobParam) {
        JobHelper.log("taskInstanceExpire begin execute");

        // 获取所有国家时区信息
        List<IntlRmsCountryTimezone> countryTimezoneList = intlCountryTimezoneService.list();

        countryTimezoneList.forEach(countryTimezone -> {
            // 获取当前国家的本地时间
            LocalDateTime localDateTime = DateTimeUtil.getLocalDateTimeByCountryCode(countryTimezone.getCountryCode());

            if (null == localDateTime) {
                log.info("获取国家时间失败,国家:{}", countryTimezone.getCountryCode());
                return;
            }

            // 查询该国家已过期的大促任务
            String currentTime = DateTimeUtil.formatLocalDateTimeWithCustomTime(localDateTime, "23:59:59");
            // 查询该国家已过期的大促任务：已停用 或 未停用但已过期
            List<IntlBigPromotionConf> expiredPromotions = bigPromotionConfService.list(
                Wrappers.<IntlBigPromotionConf>lambdaQuery()
                    .eq(IntlBigPromotionConf::getCountry, countryTimezone.getCountryCode())
                    .and(wrapper ->
                        wrapper.eq(IntlBigPromotionConf::getIsDisabled, 1)
                               .or()
                               .eq(IntlBigPromotionConf::getIsDisabled, 0)
                               .lt(IntlBigPromotionConf::getEndTime, currentTime)
                    )
            );

            if (CollUtil.isEmpty(expiredPromotions)) {
                log.info("没有过期的大促任务，当前国家:{}", countryTimezone.getCountryCode());
                return;
            }

            // 获取过期大促任务的ID列表
            List<Integer> expiredPromotionIds = expiredPromotions.stream()
                    .map(IntlBigPromotionConf::getId)
                    .collect(Collectors.toList());

            log.info("过期的大促任务ID: {}, 国家: {}", expiredPromotionIds, countryTimezone.getCountryCode());

            // 查询相关的未读消息实例
            List<IntlInspectionMessageInstance> unreadMessageInstances = intlInspectionMessageInstanceService.list(
                    Wrappers.<IntlInspectionMessageInstance>lambdaQuery()
                            .in(IntlInspectionMessageInstance::getPromotionConfId, expiredPromotionIds)
                            .eq(IntlInspectionMessageInstance::getNotify, Boolean.FALSE)  // 未读消息
                            .eq(IntlInspectionMessageInstance::getIsDeleted, 0)  // 未删除
            );

            if (CollUtil.isEmpty(unreadMessageInstances)) {
                log.info("没有需要标记删除的未读消息，国家:{}", countryTimezone.getCountryCode());
                return;
            }

            // 批量更新消息实例，设置is_deleted为1
            List<IntlInspectionMessageInstance> updatedInstances = unreadMessageInstances.stream()
                    .peek(instance -> {
                        instance.setIsDeleted(1);
                        instance.setUpdatedAt(Math.toIntExact(DateTimeUtil.toTimestamp(LocalDateTime.now())));
                    })
                    .collect(Collectors.toList());

            // 批量更新
            boolean updateResult = intlInspectionMessageInstanceService.updateBatchById(updatedInstances);

            if (updateResult) {
                log.info("成功标记删除未读消息，国家:{}, 消息数量:{}", countryTimezone.getCountryCode(), updatedInstances.size());
            } else {
                log.error("标记删除未读消息失败，国家:{}", countryTimezone.getCountryCode());
            }
        });
    }

    /**
     * 解析参数字符串为Map，支持英文逗号和中文顿号分隔
     */
    private Map<String, String> parseParamString(String params) {
        if (params == null || params.trim().isEmpty()) {
            return Collections.emptyMap();
        }
        // 支持英文逗号和中文顿号分隔
        String[] paramPairs = params.split("[,、]");
        Map<String, String> resultMap = new HashMap<>(paramPairs.length);
        for (String pair : paramPairs) {
            if (pair == null || pair.trim().isEmpty()) continue;
            String[] keyValue = pair.split(":", 2);
            if (keyValue.length == 2) {
                String key = keyValue[0].trim();
                String value = keyValue[1].trim();
                if (!key.isEmpty()) {
                    resultMap.put(key, value);
                }
            }
        }
        return Collections.unmodifiableMap(resultMap);
    }

    /**
     * 根据消息实例和语言key生成推送内容
     */
    private String parseContent(IntlInspectionMessageInstance messageInstance, String languageKey) {
        Map<String, String> paramMap = parseParamString(messageInstance.getParams());
        String promotionName = paramMap.getOrDefault("promotionName", "");
        String startTime = paramMap.getOrDefault("startTime", "");
        String endTime = paramMap.getOrDefault("endTime", "");

        // 获取多语言内容，避免NPE
        String content1 = Optional.ofNullable(T.tr(MESSAGE_KEY, languageKey)).orElse("");

        StringBuilder sb = new StringBuilder();

        if (MESSAGE_KEY.equals(content1)) {
             content1 = DEFAULT_CONTENT;
        }

            // 内容加粗换行
            sb.append(promotionName)
                    .append(", ")
                    .append(startTime)
                    .append("-")
                    .append(endTime)
                    .append(",")
                    .append(content1);


        return sb.toString();
    }
}

