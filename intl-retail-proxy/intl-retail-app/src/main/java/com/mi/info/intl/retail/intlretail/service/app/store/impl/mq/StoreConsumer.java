package com.mi.info.intl.retail.intlretail.service.app.store.impl.mq;

import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RmsRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.StoreMessage;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.UniversalProxyRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.store.topic}", consumerGroup = "${intl-retail.rocketmq.store.group}")
public class StoreConsumer implements RocketMQListener<String> {

    static final String SAVE_STORE_PATH = "/api/data/v9.2/new_savestoreforchannelretail";

    @Autowired
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;

    @Override
    public void onMessage(String message) {
        log.info("StoreConsumer message:{}", message);
        try {
            // 将 message 转换为 StoreMessage 对象
            StoreMessage storeMessage = Optional.ofNullable(JsonUtil.json2bean(message, StoreMessage.class))
                    .orElseThrow(() -> new RuntimeException("storeMessage is null"));

            // 获取 areaId 并检查是否为 null 或空字符串
            String areaId = Optional.ofNullable(storeMessage.getAreaId())
                    .filter(id -> !id.isEmpty())
                    .orElseThrow(() -> new RuntimeException("areaId is null or areaId is empty"));

            RmsRequest rmsRequest = new RmsRequest();
            rmsRequest.setInput(message);
            rmsAppliUserOauthServiceProvider.httpForObject(areaId, SAVE_STORE_PATH, rmsRequest, "POST", String.class);
        } catch (Exception e) {
            throw new RuntimeException("StoreConsumer.onMessage error", e);
        }
    }
}