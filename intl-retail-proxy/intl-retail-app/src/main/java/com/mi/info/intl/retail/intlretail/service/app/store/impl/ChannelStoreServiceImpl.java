package com.mi.info.intl.retail.intlretail.service.app.store.impl;

import com.google.common.collect.Maps;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.common.utils.RpcContextUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsAppliUserOauthServiceProvider;
import com.mi.info.intl.retail.intlretail.domain.http.rms.dto.RmsResponseBody;
import com.mi.info.intl.retail.intlretail.service.api.store.IChannelStoreService;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RmsRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.UniversalProxyRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.UniversalProxyResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.NullArgumentException;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

@DubboService(group = "${store.dubbo.group:}", interfaceClass = IChannelStoreService.class)
@Slf4j
@Service
public class ChannelStoreServiceImpl implements IChannelStoreService {
    @Value("${config-url.rms}")
    private String rmsUrl;

    static final String RMSATTACHEDPATH = "/api/data/v9.2/";

    @Autowired
    private RmsAppliUserOauthServiceProvider rmsAppliUserOauthServiceProvider;

    /*
     * 调用方：渠道建店PC端、渠道建店后端、
     */
    @Override
    public UniversalProxyResponse universalProxyApi(UniversalProxyRequest requestBody) {
        // universalProxyApi通过接口被前端通过网关，后端dubbo同时调用,
        // 获取areaId时按照UniversalProxyRequest > RpcContext的优先级取值
        String areaId = Optional.ofNullable(requestBody.getAreaId())
                .orElseGet(() -> RpcContextUtil.getAreaId(RpcContext.getContext().getAttachment("heracontext")));
        if (areaId == null || areaId.isEmpty() || areaId.equals("null")) {
            throw new NullArgumentException("areaId is null or areaId is empty");
        }
        log.info("requestBody:{}", JsonUtil.bean2json(requestBody));
        RmsRequest rmsRequest = new RmsRequest(requestBody);
        UniversalProxyResponse proxyResponse;
        try {
            RmsResponseBody response = rmsAppliUserOauthServiceProvider
                    .httpForRmsResponseBody(areaId, RMSATTACHEDPATH + requestBody.path, rmsRequest, "POST");
            proxyResponse = new UniversalProxyResponse(response.getCode(), response.getMessage(), response.getResult());
            log.info("requestBody:{}", JsonUtil.bean2json(proxyResponse));
        } catch (Exception e) {
            throw new RuntimeException("ChannelStoreServiceImpl.universalProxyApi error", e);
        }
        return proxyResponse;
    }

    @Override
    public UniversalProxyResponse universalProxyApiByJwtToken(UniversalProxyRequest requestBody) {
        log.info("requestBody:{}", JsonUtil.bean2json(requestBody));
        String areaId = Optional.ofNullable(requestBody.getAreaId())
                .orElseGet(() -> RpcContextUtil.getAreaId(RpcContext.getContext().getAttachment("heracontext")));
        if (areaId == null || areaId.isEmpty() || areaId.equals("null")) {
            throw new NullArgumentException("areaId is null or areaId is empty");
        }
        RmsRequest rmsRequest = new RmsRequest(requestBody);
        UniversalProxyResponse proxyResponse;
        try {
            RmsResponseBody response = rmsAppliUserOauthServiceProvider
                    .httpForRmsResponseBodyByJwtToken(areaId, RMSATTACHEDPATH + requestBody.path, rmsRequest, "POST", requestBody.token);
            proxyResponse = new UniversalProxyResponse(response.getCode(), response.getMessage(), response.getResult());
            log.info("requestBody:{}", JsonUtil.bean2json(proxyResponse));
        } catch (Exception e) {
            throw new RuntimeException("ChannelStoreServiceImpl.universalProxyApi error", e);
        }
        return proxyResponse;
    }
}
