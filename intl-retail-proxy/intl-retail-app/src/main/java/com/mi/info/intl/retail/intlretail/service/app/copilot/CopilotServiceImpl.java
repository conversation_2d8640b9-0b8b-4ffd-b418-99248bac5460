package com.mi.info.intl.retail.intlretail.service.app.copilot;

import com.mi.info.intl.retail.intlretail.service.api.copilot.ICopilotService;
import com.xiaomi.nr.copilot.api.request.AIAssistantAnswerVoteRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantComparableProductRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantContrastProductParamsRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantFeedbackTagsRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantProductParamsRequest;
import com.xiaomi.nr.copilot.api.request.BreakAIAnswerRequest;
import com.xiaomi.nr.copilot.api.request.CopilotComparisonHistoryRequest;
import com.xiaomi.nr.copilot.api.request.CopilotConfigRequest;
import com.xiaomi.nr.copilot.api.request.CopilotTokenRequest;
import com.xiaomi.nr.copilot.api.request.CreateConversationRequest;
import com.xiaomi.nr.copilot.api.request.GetItemListRequest;
import com.xiaomi.nr.copilot.api.request.GetSpuInfoRequest;
import com.xiaomi.nr.copilot.api.response.AIAssistantAnswerVoteResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantComparableProductResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantContrastProductParamsResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantFeedbackTagsResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantProductComparisonHistoryResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantProductParamsResponse;
import com.xiaomi.nr.copilot.api.response.CopilotConfigResponse;
import com.xiaomi.nr.copilot.api.response.CreateConversationResponse;
import com.xiaomi.nr.copilot.api.response.GoodsItemResponseCate;
import com.xiaomi.nr.copilot.api.response.SpuInfoReponse;
import com.xiaomi.nr.copilot.api.service.BaseInfoService;
import com.xiaomi.nr.copilot.api.service.ChatAiAssistantService;
import com.xiaomi.nr.copilot.api.service.CopilotConfigService;
import com.xiaomi.nr.copilot.api.service.TokenProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-04-10 19:26
 */
@Service
@Slf4j
public class CopilotServiceImpl implements ICopilotService {

    @DubboReference(group = "${copilot.dubbo.group:}", interfaceClass = TokenProvider.class, version = "1.0", check = false)
    private TokenProvider tokenProvider;

    @DubboReference(group = "${copilot.dubbo.group:}", interfaceClass = CopilotConfigService.class, version = "1.0", check = false)
    private CopilotConfigService copilotConfigService;

    @DubboReference(group = "${copilot.dubbo.group:}", interfaceClass = ChatAiAssistantService.class, version = "1.0", check = false)
    private ChatAiAssistantService chatAiAssistantService;

    @DubboReference(group = "${copilot.dubbo.group:}", interfaceClass = BaseInfoService.class, version = "1.0", check = false)
    private BaseInfoService baseInfoService;

    @Override
    public CopilotConfigResponse getConfig(CopilotConfigRequest req) {
        Result<CopilotConfigResponse> result = copilotConfigService.getConfig(req);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get config error");
        }
        return result.getData();
    }

    @Override
    public String getToken(CopilotTokenRequest req) {
        Result<String> result = tokenProvider.getServiceToken(req);
        if (result == null || result.getCode() != 0 || StringUtils.isBlank(result.getData())) {
            throw new RuntimeException("get token error");
        }
        return result.getData();
    }

    @Override
    public List<GoodsItemResponseCate> getItemList(GetItemListRequest req) {
        Result<List<GoodsItemResponseCate>> result = baseInfoService.getItemList(req);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get items error");
        }
        return result.getData();
    }

    @Override
    public SpuInfoReponse getSpuInfo(GetSpuInfoRequest request) {
        Result<SpuInfoReponse> result = baseInfoService.getSpuInfo(request);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get spuInfo error");
        }
        return result.getData();
    }

    @Override
    public CreateConversationResponse createConversation(CreateConversationRequest req) throws BizError {
        Result<CreateConversationResponse> result = chatAiAssistantService.createConversation(req);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("create conversation error");
        }
        return result.getData();
    }

    @Override
    public Boolean breakAIAnswer(BreakAIAnswerRequest request) {
        Result<Boolean> result = chatAiAssistantService.breakAiGenerate(request);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("breakAIAnswer error");
        }
        return result.getData();
    }

    @Override
    public AIAssistantAnswerVoteResponse answerVote(AIAssistantAnswerVoteRequest req) throws BizError {
        Result<AIAssistantAnswerVoteResponse> result = chatAiAssistantService.answerVote(req);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("create conversation error");
        }
        return result.getData();
    }

    @Override
    public AIAssistantProductParamsResponse getProductParams(AIAssistantProductParamsRequest request) {
        Result<AIAssistantProductParamsResponse> result = null;
        try {
            result = chatAiAssistantService.getProductParams(request);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get productParams error");
        }
        return result.getData();
    }

    @Override
    public AIAssistantComparableProductResponse getComparableProducts(AIAssistantComparableProductRequest request) {
        Result<AIAssistantComparableProductResponse> result = null;
        try {
            result = chatAiAssistantService.getComparableProducts(request);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }

        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get productParams error");
        }
        return result.getData();
    }

    @Override
    public AIAssistantContrastProductParamsResponse contrastProductParams(AIAssistantContrastProductParamsRequest request) {
        Result<AIAssistantContrastProductParamsResponse> result = null;
        try {
            result = chatAiAssistantService.contrastProductParams(request);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get productParams error");
        }
        return result.getData();
    }

    @Override
    public AIAssistantProductComparisonHistoryResponse getUserComparisonHistory(CopilotComparisonHistoryRequest request) {
        Result<AIAssistantProductComparisonHistoryResponse> result = null;
        try {
            result = chatAiAssistantService.getUserComparisonHistory(request);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get productParams error");
        }
        return result.getData();
    }

    @Override
    public AIAssistantFeedbackTagsResponse getFeedbackTags(AIAssistantFeedbackTagsRequest request) {
        Result<AIAssistantFeedbackTagsResponse> result = null;
        try {
            result = chatAiAssistantService.getFeedbackTags(request);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("getFeedbackTags error");
        }
        return result.getData();
    }
}
