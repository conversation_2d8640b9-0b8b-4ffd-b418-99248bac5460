package com.mi.info.intl.retail.intlretail.domain.common.utils;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class RpcContextUtil {



    /**
     * 从RpcContext的heracontext中获取http请求Headers中的areaId
     *
     * @param heracontext
     * @return
     */
    public static String getAreaId(String heracontext) {
        return String.valueOf(getValueByKey(heracontext, "mone-retail-area-for-global"));
    }

    /**
     * 从RpcContext的heracontext中获取http请求Headers中的language
     *
     * @param heracontext
     * @return
     */
    public static String getLanguage(String heracontext) {
        return String.valueOf(getValueByKey(heracontext, "mone-retail-language-for-global"));
    }

    private static String getValueByKey(String heracontext, String key) {
        if (heracontext == null || heracontext.isEmpty()) {
            return null;
        }
        Map<String, String> map = Maps.newHashMap();
        final String[] split = heracontext.split(";");
        map = java.util.Arrays.stream(split)
                .map(part -> part.split(":"))
                .filter(keyValue -> keyValue.length >= 2)
                .collect(Collectors.toMap(keyValue -> keyValue[0], keyValue -> keyValue[1]));
        return map.get(key);
    }
}
