package com.mi.info.intl.retail.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 *
 * 分页请求对象
 *
 * <AUTHOR>
 * @date 2025/6/5
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BasePageRequest {

    /**
     * 每页条数 默认10条
     */
    @Min(10)
    @Max(1000)
    private Long pageSize = 10L;
    /**
     * 当前页码 默认1
     */
    @Min(1)
    private Long pageNum = 1L;


}
