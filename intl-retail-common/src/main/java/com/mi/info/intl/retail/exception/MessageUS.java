package com.mi.info.intl.retail.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 英文消息
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Getter
@AllArgsConstructor
public enum MessageUS {

    /**
     * US Message
     */
    SUCCESS(200, "success"),
    SYS_ERROR(1, "system error {%s}"),
    ERR_BIZ_ERROR(2, "business exception {%s}"),
    ERR_PARAM_IS_EMPTY(3, "param is null {%s}"),
    ERR_PARAMS_ERROR(4, "param error {%s}"),
    ERR_NOT_EXISTS(5, "not exists {%s}"),
    ERR_DATA_NOT_FOUND(6, "data not found {%s}"),
    ERR_SEND_MQ_FAILED(7, "send mq failed {%s}"),
    ERR_CONSUME_MQ_FAILED(8, "consumer mq failed {%s}"),
    POSITION_NOT_EXIST(9, "position not exist {%s}"),
    RETAILER_NOT_EXIST(10, "retailer not exist {%s}"),
    SO_RULE_NOT_EXIST(11, "so rules not exist{%s}"),
    COUNTRY_CODE_NOT_EXIST(12, "country code not exist {%s}"),
    SO_RULE_LOG_NOT_EXIST(13, "The so rule does not exist in the approval record{%s}"),
    CALL_FDS_ERROR(14, "call fds error {%s}"),
    RPC_CALL_TIMEOUT(15, "interface {%s} timeout"),
    RPC_CALL_ERROR(16, "interface {%s} error"),
    CAN_NOT_OBTAIN_USERS(17, "Job id:{%s} cannot obtain user information"),
    ;

    private int code;
    private String desc;

    public static MessageUS get(int code) {
        MessageUS[] elements = MessageUS.values();
        for (MessageUS element : elements) {
            if (element.getCode() == code) {
                return element;
            }
        }
        return null;
    }

}
