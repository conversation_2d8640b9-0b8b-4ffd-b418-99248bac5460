package com.mi.info.intl.retail.exception;

import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * BIZ异常
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Setter
@Getter
@Slf4j
public class BizException extends RuntimeException {
    private final transient ErrorCode errorCode;
    private final transient Object[] args;


    public BizException(ErrorCode errorCode, Object... args) {
        this.errorCode = errorCode;
        this.args = args;
    }

    public Integer getCode() {
        return this.errorCode.getCode();
    }

    @Override
    public String getMessage() {
        try {
            return MessageUtil.getMessageForLanguage(errorCode.getCode(), args);
        } catch (Exception e) {
            log.error("BizException getMessage error:{}", e.getMessage(), e);
            return getCode() + "";
        }
    }
}

