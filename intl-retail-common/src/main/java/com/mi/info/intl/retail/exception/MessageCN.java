package com.mi.info.intl.retail.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息CN
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Getter
@AllArgsConstructor
public enum MessageCN {

    /**
     * 中文Message
     */
    SUCCESS(200, "成功"),
    SYS_ERROR(1, "系统异常{%s}"),
    ERR_BIZ_ERROR(2, "业务异常{%s}"),
    ERR_PARAM_IS_EMPTY(3, "参数为空{%s}"),
    ERR_PARAMS_ERROR(4, "参数异常{%s}"),
    ERR_NOT_EXISTS(5, "不存在{%s}"),
    ERR_DATA_NOT_FOUND(6, "未找到{%s}"),
    ERR_SEND_MQ_FAILED(7, "发送MQ失败{%s}"),
    ERR_CONSUME_MQ_FAILED(8, "消费MQ失败{%s}"),
    POSITION_NOT_EXIST(9, "阵地不存在{%s}"),
    RETAILER_NOT_EXIST(10, "零售商不存在{%s}"),
    SO_RULE_NOT_EXIST(11, "SO规则不存在{%s}"),
    COUNTRY_CODE_NOT_EXIST(12, "国家不存在{%s}"),
    SO_RULE_LOG_NOT_EXIST(13, "so规则不存在审批中记录{%s}"),
    CALL_FDS_ERROR(14, "调用FDS异常{%s}"),
    RPC_CALL_TIMEOUT(15, "服务:{%s}调用超时"),
    RPC_CALL_ERROR(16, "服务:{%s}调用异常"),
    CAN_NOT_OBTAIN_USERS(17, "岗位id:{%s}无法获取用户信息"),
    ;

    private int code;
    private String desc;

    public static MessageCN get(int code) {
        MessageCN[] elements = MessageCN.values();
        for (MessageCN element : elements) {
            if (element.getCode() == code) {
                return element;
            }
        }
        return null;
    }

}
