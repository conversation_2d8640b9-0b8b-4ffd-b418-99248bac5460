package com.mi.info.intl.retail.utils.redis;

import lombok.Getter;

@Getter
public enum RedisKeyEnum {

    LOCK_SYNC_DATA_IMEI("LOCK_SYNC_DATA_IMEI", -1, "同步RMS的IMEI数据锁"),
    LOCK_SYNC_DATA_QTY("LOCK_SYNC_DATA_QTY", -1, "同步RMS的QRY数据锁"),

    ;

    private String key;
    private int expireTime;
    private String desc;

    RedisKeyEnum(String key, int expireTime, String desc) {
        this.key = key;
        this.expireTime = expireTime;
        this.desc = desc;
    }


    private void setKey(String key) {
        this.key = key;
    }


    private void setExpireTime(int expireTime) {
        this.expireTime = expireTime;
    }

    private void setDesc(String desc) {
        this.desc = desc;
    }

    public RedisKey get(Object... args) {
        return new RedisKey(this.key, this.desc, this.expireTime, args);
    }

    @Override
    public String toString() {
        return "RedisKeyEnum{" +
                "key='" + key + '\'' +
                ", expireTime=" + expireTime +
                ", desc='" + desc + '\'' +
                '}';
    }
}
