package com.mi.info.intl.retail.intlretail.service.api.masterdata;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.BatchDisableRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextUserRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.AddPlainTextImeiUserRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;

/**
 * 主数据服务接口
 *
 * <AUTHOR>
 * @date 2025/7/25
 **/
public interface MasterDataService {

    /**
     * 分页查询黑名单列表
     *
     * @param request 查询参数
     */
    CommonApiResponse<IPage<SnBlackDTO>> querySnBlackList(SnBlackRequest request);

    /**
     * 导入黑名单
     * @param fileUrl 文件URL
     * @return 异常数据Excel文件URL，如果没有异常数据则返回空字符串
     */
    CommonApiResponse<String> importSnBlackList(String fileUrl, String locale);

    /**
     * 导出黑名单
     *
     * @param request 查询参数
     * @return
     */
    CommonApiResponse<String> exportSnBlackList(SnBlackRequest request);

    /**
     * 批量禁用
     *
     * @param request 批量禁用请求
     */
    CommonApiResponse<String> batchDisable(BatchDisableRequest request);


    /**
     * 分页查询明文IMEI用户列表
     *
     * @return
     */
    CommonApiResponse<IPage<PlainTextImeiUserDTO>> pageQueryPlainTextUser(PlainTextUserRequest request);

    /**
     * 添加明文IMEI用户
     *
     * @param request
     * @return
     */
    CommonApiResponse<String> addPlainTextUser(AddPlainTextImeiUserRequest request);

    /**
     * 删除明文IMEI用户
     *
     * @param id 明文IMEI用户ID
     *
     */
    CommonApiResponse<String> deletePlainTextUser(String id);
}
