package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

/**
 * 任务状态枚举值
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum implements I18nDesc {

    /**
     * 未完成
     */
    NOT_COMPLETED(3, () -> T.tr("inspection.not_completed")),

    /**
     * 已完成
     */
    COMPLETED(1, () -> T.tr("inspection.completed")),

    /**
     * 无需完成
     */
    NO_NEED_TO_DO(2, () -> T.tr("inspection.no_need_to_do")),

    /**
     * 默认状态 表示无任务状态 当巡检状态为未下发，任务状态应该是空
     */
    DEFAULT(0, () -> "");

    /**
     * 编码
     */
    @EnumValue
    private final Integer code;

    private final Supplier<String> i18nDesc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TaskStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaskStatusEnum value : TaskStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}