package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class PositionInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("PositionCode")
    private String positionCode;
    @JsonProperty("PositionType")
    private String positionType;
    @JsonProperty("PositionName")
    private String positionName;
    @JsonProperty("TitleCode")
    private Integer titleCode;
    @JsonProperty("TitleName")
    private String titleName;
    @JsonProperty("ChannelTypeId")
    private String channelTypeId;
    @JsonProperty("ChannelTypeName")
    private String channelTypeName;
        /**
     * 扩展字段(JSON)
     * 示例：{"storeId":"12345","timezoneCode":92,"languageCode":"zh-CN","frequency":"1月2次"}
     */
    @JsonProperty("ExtInfo")
    private String extInfo;
    /**
     * 定义extInfo DTO , 方便后续转换
     */
    @Data
    public static class PositionExtInfoDTO {
        private String storeId;
        private String storeCode;
        private String storeGrade;
        private Integer userTitle;
        private Integer timezoneCode;
        private String languageCode;
        private String frequency; // 例如 "1月2次"
        private Integer inspectionTime; // 巡检时长
        private Integer taskConfId; // 巡检任务配置id
    }
}
