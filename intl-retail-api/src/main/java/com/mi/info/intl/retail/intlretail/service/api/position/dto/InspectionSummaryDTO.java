package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 巡检记录汇总数据DTO
 */
@Data
public class InspectionSummaryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 巡检阵地总数
     */
    private Integer totalInspectionCount;
    
    /**
     * 完成率（百分比）
     */
    private BigDecimal completionRate;
    
    /**
     * 核检率（百分比）
     */
    private BigDecimal verificationRate;
    
    /**
     * 检核通过率（百分比）
     */
    private BigDecimal passRate;
    
    /**
     * 未下发数量
     */
    private Integer notIssuedCount;
    
    /**
     * 未完成巡检的阵地数量
     */
    private Integer undoCount;
    
    /**
     * 未完成原因：下发未完成的数量
     */
    private Integer undoIncompleteCount;
    
    /**
     * 未完成原因：核验未通过的数量
     */
    private Integer undoRefuseCount;
    
    /**
     * 已完成巡检的阵地数量
     */
    private Integer completedCount;
    
    /**
     * 已完成但待核验的数量
     */
    private Integer completedVerifyingCount;
    
    /**
     * 已完成且核验通过的数量
     */
    private Integer completedPassedCount;
    
    /**
     * 无需完成巡检的阵地数量
     */
    private Integer noNeedToDoCount;
    
    /**
     * 无需完成原因：待核验的数量
     */
    private Integer noNeedToDoVerifyingCount;
    
    /**
     * 无需完成原因：核验通过的数量
     */
    private Integer noNeedToDoPassedCount;
}