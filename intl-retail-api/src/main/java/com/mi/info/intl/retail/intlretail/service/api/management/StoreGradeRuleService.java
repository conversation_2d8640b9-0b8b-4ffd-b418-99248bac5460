package com.mi.info.intl.retail.intlretail.service.api.management;

import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleModifyVersionResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeApprovaResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleRecallReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRulePageQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRulePageResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleDetailResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.*;
import com.mi.info.intl.retail.model.CommonApiResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 门店等级规则服务接口
 */
public interface StoreGradeRuleService {

    /**
     * 保存门店等级规则
     *
     * @param storeGradeRuleReq 门店等级规则请求对象
     * @return 保存结果，成功返回true，失败返回false
     */
    CommonApiResponse<Boolean> saveStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq);

    /**
     * 更新门店等级规则
     *
     * @param storeGradeRuleReq 门店等级规则请求对象
     * @return 更新结果，成功返回true，失败返回false
     */
    CommonApiResponse<Boolean> updateStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq);

    /**
     * 查询门店等级规则提交记录
     *
     * @param request
     * @return
     */
    CommonApiResponse<StoreGradeRulePageResp> query(RuleQueryReq request);

    /**
     * 查询门店等级规则详情
     *
     * @param request
     * @return
     */
    CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> queryDetail(RuleQueryReq request);

    /**
     * 查询门店等级规则审批列表
     *
     * @param request
     * @return
     */
    CommonApiResponse<List<StoreGradeRuleDetailResp.RuleModificationLog>> queryApprovalList(RuleQueryReq request);

    /**
     * 查询门店等级规则修改版本列表
     *
     * @param request
     * @return
     */
    CommonApiResponse<List<RuleModifyVersionResp>> queryVersionList(RuleQueryReq request);

    /**
     * 分页查询门店等级规则
     *
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    CommonApiResponse<StoreGradeRulePageResp> pageQuery(StoreGradeRulePageQueryReq request);

    /**
     * 根据ID删除门店等级规则
     *
     * @param id 规则ID
     * @return 删除结果，成功返回true，失败返回false
     */
    CommonApiResponse<Boolean> deleteStoreGradeRule(Integer id);

    /**
     * 流程提交
     */
    SubmitResp submit(StoreGradeRuleReq  req);

    /**
     * 流程撤回
     */
    Boolean recall(StoreGradeRuleRecallReq req);

    /**
     * 查看审批记录
     */
    CommonApiResponse<List<StoreGradeApprovaResp>> listProcessLog(Long id);
    
    /**
     * 根据retailer和kapa查询Grade信息
     */
    CommonApiResponse<StoreGradeResp> queryStoreGrade(StoreGradeReq req);
    
    /**
     * Store Grade 的直接批量修改和审批
     */
    Boolean batchUpdateStoreGrade(MultipartFile file, StoreGradeRuleReq req);
    
    /**
     * 获取上传文件模板
     */
    CommonApiResponse<String> getFileTemplate();
    
    /**
     *  根据国家编码和渠道查询所有零售商信息
     */
    CommonApiResponse<List<RetailerQueryResp>> queryRetailerList(RetailerQueryReq request);
    
    /**
     * 获取手动上传规则详情
     *
     */
    CommonApiResponse<UploadManuallyRuleDetailsResp> getUploadManuallyRuleDetails(Long ruleId);
}