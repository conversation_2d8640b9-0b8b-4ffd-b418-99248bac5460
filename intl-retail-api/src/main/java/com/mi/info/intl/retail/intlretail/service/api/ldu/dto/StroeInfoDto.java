package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

@Data
public class StroeInfoDto implements Serializable {

    private static final long serialVersionUID = -765432109871143210L;

    /**
     * 门店编码
     */
    @ApiDocClassDefine(value = "门店唯一标识")
    private String storeId;

    /**
     * 渠道类型
     */
    @ApiDocClassDefine(value = "渠道类型")
    private String channelTypeDesc;

    /**
     * 渠道类型
     */
    @ApiDocClassDefine(value = "渠道")
    private Integer channelType;

    /**
     * 门店名称
     */
    @ApiDocClassDefine(value = "门店名称")
    private String storeName;

}
