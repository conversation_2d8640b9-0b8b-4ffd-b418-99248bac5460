package com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SupervisorTaskHttpReq extends TaskCenterCommonReq {


    private String startDay;
    private String endDay;
    private List<String> regionIds;
    private List<String> areaIds;
    private List<String> userTitles;
    private List<String> storeLevel;
    private String storeLevelId;
    private Integer viewType;
    private Integer pageNo;
    private Integer pageSize;
    private String staffId;
    private String orderKey;
    private Integer orderType;
    private int achType;

}

