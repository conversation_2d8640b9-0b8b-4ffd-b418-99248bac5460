package com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotEmpty;
/**
 * 新品目标新增时的入参
 *
 * @author: chuang
 * @since: 2025/8/4
 */
@Data
public class NewProductTargetAddReq implements Serializable {
    /**
    *品类
    */
    @NotBlank(message = "categoryCode is required")
    String categoryCode;
    @NotBlank(message = "categoryName is required")
    String categoryName;
    /**
    * 系列
    */
    @NotBlank(message = "series is required")
    String series;
    /**
    *项目
    */
    @NotEmpty(message = "projects is required")
    List<String>   projects;
    /**
    *国家
    */
    @NotEmpty(message = "countryCode is required")
    List<String> countryCode;
    /**
     * 起始时间
     */
    @NotNull(message = "startTime is required")
    private Long startTime;

    /**
     * 截止时间
     */
    @NotNull(message = "endTime is required")
    private Long endTime;
}