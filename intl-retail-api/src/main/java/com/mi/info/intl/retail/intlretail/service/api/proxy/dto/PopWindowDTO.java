package com.mi.info.intl.retail.intlretail.service.api.proxy.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 **/
@Data
public class PopWindowDTO {
    /**
     * 用户米ID
     */
    private Long miId;
    /**
     * 组织ID，阵地ID
     */
    private String orgId;


    /**
     * 弹窗详情
     */
    private List<PopWindowDetailDTO> list;

    /**
     * 自定义弹窗详情
     */
    private List<PopWindowDetailDTO> customList;

    /**
     * 弹窗详情
     */
    @Data
    public static class PopWindowDetailDTO {
        private String taskInstanceId;
        private String title;
        private String content;
        private Boolean redFlag = false;
        /**
         * 普通、警告
         */
        private String taskType;
    }

}
