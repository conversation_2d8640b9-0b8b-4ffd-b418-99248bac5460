package com.mi.info.intl.retail.intlretail.service.api.masterdata.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SN黑名单DTO
 *
 * <AUTHOR>
 * @date 2025/7/28
 **/
@Data
@ExcelIgnoreUnannotated
public class SnBlackDTO  implements Serializable {
    
    private static final long serialVersionUID = 4654197179926097128L;

    /**
     * 黑名单ID
     */
    @ExcelProperty("ID")
    private Long id;

    /**
     * 国家短码
     */
    @ExcelProperty("Country")
    private String countryCode;

    /**
     * 类型
     */
    @ExcelProperty("Type")
    private String type;

    /**
     *  SN/IMEI
     */
    @ExcelProperty("IMEI/SN")
    private String snImei;

    /**
     * 状态
     */
    @ExcelProperty("Status")
    private boolean status;

    /**
     * 创建时间
     */
    @ExcelProperty("Created On")
    private String createdOn;

    /**
     * 创建人id
     */
    private String createdBy;

    /**
     * 创建人
     */
    @ExcelProperty("Created By")
    private String createdByName;

    /**
     * 修改时间
     */
    @ExcelProperty("Modification Time")
    private String modifiedOn;

    /**
     * 修改人id
     */
    private String modifiedBy;

    /**
     * 修改人
     */
    @ExcelProperty("Modifier")
    private String modifiedByName;

}
