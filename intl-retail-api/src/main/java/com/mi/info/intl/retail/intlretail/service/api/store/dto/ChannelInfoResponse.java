package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 返回渠道零售建店所需关联的Supplier/Retailer/Distributor信息
 */
@Setter
@Getter
public class ChannelInfoResponse implements Serializable {
    private static final long serialVersionUID = 138130563364571070L;
    private BasicDTO basic;
    private ExtraDTO extra;

    @Setter
    @Getter
    public static class BasicDTO implements Serializable {
        private static final long serialVersionUID = 1654787391405819100L;
        private String code;
        private String name;

        // Getters and Setters

    }

    @Setter
    @Getter
    public static class ExtraDTO implements Serializable {
        private static final long serialVersionUID = -4237656782165202224L;
        private String grade;
        private String channelType;
        private Integer monthlySales;
        private String topBrand;
        private Integer monthlySalesOfMi;
        private Integer miShare;

        // Get<PERSON> and Setters

    }
}
