package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 巡检任务配置-编辑传参
 */
@Data
public class InspectionTaskConfigurationDTO implements Serializable {

    @ApiDocClassDefine(value = "id")
    private Long id;
    @ApiDocClassDefine(value = "规则编码")
    private String ruleCode;
    @ApiDocClassDefine(value = "规则名称")
    @NotBlank(message = "Rule Name is required")
    private String ruleName;
    @ApiDocClassDefine(value = "规则状态")
    private Integer ruleStatus;
    @ApiDocClassDefine(value = "规则状态名称")
    private String ruleStatusDesc;
    @ApiDocClassDefine(value = "任务场景")
    @NotNull(message = "Task Type is required")
    private Integer taskType;
    @ApiDocClassDefine(value = "任务场景名称")
    private String taskTypeDesc;
    @ApiDocClassDefine(value = "任务名称")
    @NotBlank(message = "Task Name is required")
    private String taskName;
    @ApiDocClassDefine(value = "目标类型 1 首销期 2 生命周期 编码")
    @NotBlank(message = "Whether First-sales Period is required")
    private String targetType;
    @ApiDocClassDefine(value = "目标类型 1 首销期 2 生命周期 名称")
    private String targetTypeDesc;
    @ApiDocClassDefine(value = "循环逻辑")
    @NotNull(message = "Task Cycle is required")
    private Integer cycleType;
    @ApiDocClassDefine(value = "循环逻辑名称")
    private String cycleTypeDesc;
    @ApiDocClassDefine(value = "自定义循环天数")
    private Integer customCycleDays;
    @ApiDocClassDefine(value = "待办提醒周期任务循环类型")
//    @NotNull(message = "Remind Cycle is required")
    private Integer reminderCycleType;
    @ApiDocClassDefine(value = "待办提醒周期任务循环类型名称")
    private String reminderCycleTypeDesc;
    @ApiDocClassDefine(value = "待办提醒周期任务循环天数")
    private Integer reminderDays;
    @ApiDocClassDefine(value = "开始时间")
    private Long startTime;
    @ApiDocClassDefine(value = "结束时间")
    private Long endTime;
    @ApiDocClassDefine(value = "建议执行时间段开始时间戳（毫秒）")
    @NotNull(message = "Suggest Time is required")
    private Long suggestedTimeRangeStart;
    @NotNull(message = "Suggest Time is required")
    @ApiDocClassDefine(value = "建议执行时间段结束时间戳（毫秒）")
    private Long suggestedTimeRangeEnd;
    @ApiDocClassDefine(value = "是否允许从相册选择图片")
    @NotNull(message = "Whether Take Photo is required")
    private Integer allowPhotoFromGallery;
    @ApiDocClassDefine(value = "是否需要检查")
    private Integer needInspection;
    @ApiDocClassDefine(value = "POSM物料")
    private List<String> posmMaterials;
    @ApiDocClassDefine(value = "任务指引")
    private String taskGuidance;
    @ApiDocClassDefine(value = "品类")
    @NotBlank(message = "Category is required")
    private String category;
    @ApiDocClassDefine(value = "品类名称")
    private String categoryDesc;
    @ApiDocClassDefine(value = "系列")
    @NotBlank(message = "Series is required")
    private String series;
    @ApiDocClassDefine(value = "项目")
    @NotBlank(message = "Program is required")
    private String project;
    @ApiDocClassDefine(value = "区域")
    @NotBlank(message = "Region is required")
    private String region;
    @ApiDocClassDefine(value = "区域名称")
    private String regionDesc;
    @ApiDocClassDefine(value = "国家")
    @NotBlank(message = "Country is required")
    private String country;
    @ApiDocClassDefine(value = "国家名称")
    private String countryDesc;
    @ApiDocClassDefine(value = "门店类型")
    private String storeType;
    @ApiDocClassDefine(value = "自定义上传零售商编码")
    private List<String> assignedStore;
    @ApiDocClassDefine(value = "文件路径")
    private String filePath;
    @ApiDocClassDefine(value = "新品目标ID")
    @NotNull(message = "新品目标ID")
    private Long newProductTargetId;
}
