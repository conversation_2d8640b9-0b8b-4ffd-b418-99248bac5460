package com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 新品目标更新的入参
 *
 * @author: chuang
 * @since: 2025/8/4
 */
@Data
public class NewProductTargetUpdateReq implements Serializable {
    @NotEmpty(message = "items is required")
    List<NewProductTargetUpdateItemReq> items;
    @Data
    public static class NewProductTargetUpdateItemReq {
        /**
         * 主键ID
         */
        @NotNull(message = "id is required")
        private Long id;
        /**
         * LDU计划数
         */
        @NotNull(message = "lduPlanCount is required")
        private Integer lduPlanCount;

        /**
         * LDU覆盖门店数
         */
        @NotNull(message = "lduStoreCoverage is required")
        private Integer lduStoreCoverage;

        /**
         * 价签覆盖门店目标数
         */
        @NotNull(message = "priceTagCoverageTarget is required")
        private Integer priceTagCoverageTarget;

        /**
         * Dummy覆盖门店数
         */
        @NotNull(message = "dummyStoreCoverage is required")
        private Integer dummyStoreCoverage;

        /**
         * POSM覆盖门店数
         */
        @NotNull(message = "posmStoreCoverage is required")
        private Integer posmStoreCoverage;

        /**
         * 销售激活门店数
         */
        @NotNull(message = "salesActivation is required")
        private Integer salesActivation;

        /**
         * 零售可做功目标
         */
        @NotNull(message = "retailEffortTarget is required")
        private Integer retailEffortTarget;
        /**
         *状态:0 已下发 ;1 国家已修改;
         */
        Integer status = 2;
        /**
         * 起始时间
         */
        private Long startTime;

        /**
         * 截止时间
         */
        private Long endTime;

    }


}