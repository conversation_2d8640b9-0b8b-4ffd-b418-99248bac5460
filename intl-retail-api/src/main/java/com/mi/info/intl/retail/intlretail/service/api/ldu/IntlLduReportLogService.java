package com.mi.info.intl.retail.intlretail.service.api.ldu;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.BarcodeScanReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportLogDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportSubmitReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.RmsPositionReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ScanCodeReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.StroeInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;

public interface IntlLduReportLogService {

    CommonResponse<Page<IntlLduReportLogDto>> pageList(IntlLduReportReq query);

    CommonResponse<IntlLduReportLogDto> detail(IntlLduReportReq query);

    CommonResponse<String> update(IntlLduReportReq query);

    CommonResponse<String> excelExport(IntlLduReportReq query);

    CommonResponse<SnImeiGoodsInfoDto> scan(ScanCodeReq scanCodeReq);

    CommonResponse<Integer> submit(IntlLduReportSubmitReq intlLduReportSubmitReq, RmsUserBaseDataResponse userBaseInfo);

    CommonResponse<SnImeiGoodsInfoDto> scanBarcode(BarcodeScanReq request);

    CommonResponse<StroeInfoDto> getStoreInfo(RmsPositionReq request);

    CommonApiResponse<Page<IntlLduReportLogDto>> historyList(IntlLduReportReq intlLduReportReq);


}
