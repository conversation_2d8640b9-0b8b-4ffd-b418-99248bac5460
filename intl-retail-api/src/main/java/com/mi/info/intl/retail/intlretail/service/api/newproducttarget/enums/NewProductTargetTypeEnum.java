package com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

/**
 * 任务状态枚举值
 */
@Getter
@AllArgsConstructor
public enum NewProductTargetTypeEnum  {


    /**
     * 首销期
     */
    FIRST("1", "first"),

    /**
     * 生命周期
     */
    LIFE_CIRCLE("2", "life_circle");


    /**
     * 编码
     */
    @EnumValue
    private final String code;

    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static NewProductTargetTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (NewProductTargetTypeEnum value : NewProductTargetTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

//    @Override
//    public Supplier<String> getI18nDesc() {
//
//        return null;
//    }
}