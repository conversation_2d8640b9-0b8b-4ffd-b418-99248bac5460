package com.mi.info.intl.retail.intlretail.service.api.copilot;

import com.xiaomi.nr.copilot.api.request.AIAssistantAnswerVoteRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantComparableProductRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantContrastProductParamsRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantFeedbackTagsRequest;
import com.xiaomi.nr.copilot.api.request.AIAssistantProductParamsRequest;
import com.xiaomi.nr.copilot.api.request.BreakAIAnswerRequest;
import com.xiaomi.nr.copilot.api.request.CopilotComparisonHistoryRequest;
import com.xiaomi.nr.copilot.api.request.CopilotConfigRequest;
import com.xiaomi.nr.copilot.api.request.CopilotTokenRequest;
import com.xiaomi.nr.copilot.api.request.CreateConversationRequest;
import com.xiaomi.nr.copilot.api.request.GetItemListRequest;
import com.xiaomi.nr.copilot.api.request.GetSpuInfoRequest;
import com.xiaomi.nr.copilot.api.response.AIAssistantAnswerVoteResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantComparableProductResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantContrastProductParamsResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantFeedbackTagsResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantProductComparisonHistoryResponse;
import com.xiaomi.nr.copilot.api.response.AIAssistantProductParamsResponse;
import com.xiaomi.nr.copilot.api.response.CopilotConfigResponse;
import com.xiaomi.nr.copilot.api.response.CreateConversationResponse;
import com.xiaomi.nr.copilot.api.response.GoodsItemResponseCate;
import com.xiaomi.nr.copilot.api.response.SpuInfoReponse;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

public interface ICopilotService {

    /**
     * 获取copilot白名单配置
     * @param req
     * @return
     */
    CopilotConfigResponse getConfig(CopilotConfigRequest req);

    /**
     * 获取AI对话token
     * @param req
     * @return
     */
    String getToken(CopilotTokenRequest req);

    /**
     * 获取品类+商品列表
     * @param req
     * @return
     */
    List<GoodsItemResponseCate> getItemList(GetItemListRequest req);

    SpuInfoReponse getSpuInfo(GetSpuInfoRequest request);

    /**
     * 创建一个新的对话
     * @param req
     * @return
     */
    CreateConversationResponse createConversation(CreateConversationRequest req) throws BizError;

    /**
     * 终止AI问答
     * @param request
     * @return
     */
    Boolean breakAIAnswer(BreakAIAnswerRequest request);

    /**
     * 对Ai回答点赞/踩
     * @param req
     * @return
     */
    AIAssistantAnswerVoteResponse answerVote(AIAssistantAnswerVoteRequest req) throws BizError;

    /**
     * 获取产品参数
     * @param request
     * @return
     */
    AIAssistantProductParamsResponse getProductParams(AIAssistantProductParamsRequest request);

    /**
     * 获取同类产品
     * @param request
     * @return
     */
    AIAssistantComparableProductResponse getComparableProducts(AIAssistantComparableProductRequest request);

    /**
     * 对比产品参数
     * @param request
     * @return
     */
    AIAssistantContrastProductParamsResponse contrastProductParams(AIAssistantContrastProductParamsRequest request);

    /**
     * 获取用户比较历史记录
     * @param request
     * @return
     */
    AIAssistantProductComparisonHistoryResponse getUserComparisonHistory(CopilotComparisonHistoryRequest request);

    /**
     * 获取AI助手反馈标签
     *
     * @param request
     * @return
     */
    AIAssistantFeedbackTagsResponse getFeedbackTags(AIAssistantFeedbackTagsRequest request);
}
