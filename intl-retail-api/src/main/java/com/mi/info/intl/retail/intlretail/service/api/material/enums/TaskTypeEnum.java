package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum TaskTypeEnum {
    DUMMY(401, "Dummy", "新品DUMMY巡检", Lists.newArrayList(1134L)),
    POSM(402, "POSM", "新品POSM巡检", Lists.newArrayList(1135L)),
    PRICE_TAG(403, "Price Tag", "新品价签巡检", Lists.newArrayList(1136L)),
    LDU(404, "LDU", "新品LDU巡检", Lists.newArrayList(1137L));
    private final Integer code;
    private final String name;
    private final String desc;

    private final List<Long> events;

    TaskTypeEnum(Integer code, String name, String desc, List<Long> events) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.events = events;
    }

    public static List<Map<String, Object>> getNode() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskTypeEnum value : TaskTypeEnum.values()) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("code", value.getCode());
            map.put("name", value.getName());
            list.add(map);
        }
        return list;
    }

    public static String getNameByCode(Integer code) {
        for (TaskTypeEnum value : TaskTypeEnum.values()) {
            if (value.getCode().compareTo(code) == 0) {
                return value.getName();
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        for (TaskTypeEnum value : TaskTypeEnum.values()) {
            if (value.getCode().compareTo(code) == 0) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static List<Long> getEventsByCode(Integer code) {
        for (TaskTypeEnum value : TaskTypeEnum.values()) {
            if (value.getCode().compareTo(code) == 0) {
                return value.getEvents();
            }
        }
        return Collections.emptyList();
    }


}
