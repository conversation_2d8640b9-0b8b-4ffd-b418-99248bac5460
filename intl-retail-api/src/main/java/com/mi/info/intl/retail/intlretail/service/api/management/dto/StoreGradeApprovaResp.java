package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;

/**
 * 门店等级规则请求实体
 * 用于接收前端传递的门店等级配置数据
 */
@Data
public class StoreGradeApprovaResp {
    
    /**
     * 任务处理人
     */
    private Assignee assignee;
    
    /**
     * 处理任务的客户端,可用值:
     * API - 第三方服务,
     * APP - 小米人（已废弃）,
     * MINI_APP - 小米审批移动端,
     * MINI_APP_PC - 小米审批桌面端,
     * SYSTEM - BPM,
     * WEB - 浏览器
     */
    private String client;
    
    /**
     * 评论（驳回时，此字段为驳回原因）
     */
    private String comment;
    
    /**
     * S级最小数量
     */
    private String createTime;
    
    /**
     * 任务过期时间
     */
    private String dueDate;
    
    /**
     * 任务结束时间
     * （未完成任务的结束时间为空，可作为待办和已办的判断依据）
     */
    private String endTime;
    
    /**
     * 任务操作,可用值:
     * AGREE - 同意,
     * CC - 抄送,
     * DELEGATE - 委派,
     * END - 归档（历史遗留状态目前已不再使用，保留仅为兼容历史流程）,
     * PIN - 收藏,
     * REJECT - 驳回,
     * RESOLVE - 委派解决,
     * ROLLBACK - 回退,
     * SIGN - 加签,
     * SUBMIT - 审批任务加签后重新提交,
     * TERMINATE - 终止,
     * TRANSFER - 转审,
     * UNPIN - 取消收藏
     */
    private String operation;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     *审批方式,可用值:
     * PARALLEL_ALL - 多人会签(并签),
     * PARALLEL_ONE - 多人非会签(或签),
     * SEQUENTIAL - 多人顺签,
     * SINGLE - 单人审批
     */
    
    private String signType;
    
    /**
     * 任务定义KEY
     */
    private String taskDefinitionKey;
    
    /**
     * 任务id
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 处理人信息
     */
    @Data
    public static class Assignee {
        /**
         * 显示名
         */
        private String displayName;
        
        /**
         * 所属组织
         */
        private Org org;
        
        /**
         * 工号
         */
        private String personId;
        
        /**
         * 人员id
         */
        private String uid;
        
        /**
         * 邮箱前缀
         */
        private  String userName;
    }
    
    /**
     * 处理人组织信息
     */
    @Data
    public static class Org {
        /**
         * 详细组织信息
         */
        private String fullOrgDesc;
        
        /**
         * 简单组织信息
         */
        private String orgDesc;
        
        /**
         * 组织ID
         */
        private String orgId;
    }
}
