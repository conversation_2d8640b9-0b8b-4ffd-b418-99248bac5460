package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class RetailerInfoResponse implements Serializable {

    private static final long serialVersionUID = 973434739743971L;

    @JsonProperty("code")
    private String code;

    @JsonProperty("name")
    private String name;

}
