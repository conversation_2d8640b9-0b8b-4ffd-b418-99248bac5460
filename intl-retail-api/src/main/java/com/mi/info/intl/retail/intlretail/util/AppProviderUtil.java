package com.mi.info.intl.retail.intlretail.util;

import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.mit.common.function.Function0;
import com.xiaomi.mit.common.function.Function1;
import com.xiaomi.mit.common.function.Function2;
import com.xiaomi.mit.common.json.Jacksons;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.slf4j.Logger;

import java.util.Optional;

/**
 * provider统一返回值工具
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/11 19:12
 */
@SuppressWarnings("all")
public class AppProviderUtil {

    public static final String UNKNOWN_ERROR = "unknown error :";
    public static final String SYS_ERROR = "system error";

    public static <T, R> CommonApiResponse<R> wrap(Logger log, String desc, T req, Function1<T, R> func,
                                                   boolean printResLog) {
        return wrap(log, desc, req, func, Optional.empty(), printResLog);
    }

    public static <T1, T2, R> CommonApiResponse<R> wrap(Logger log, String desc, T1 req1, T2 req2,
                                                        Function2<T1, T2, R> func) {
        return wrap(log, desc, req1, req2, func, Optional.empty());
    }

    /**
     * 封装一个调用，将结果用 Result<T> 封装返回。
     *
     * @param log
     * @param desc
     * @param req
     * @param func
     * @param onFail
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> CommonApiResponse<R> wrap(Logger log, String desc, T req, Function1<T, R> func,
                                                   Optional<R> onFail,
                                        boolean printResLog) {
        log.info("{} request={}", desc, JSONUtil.toJsonStr(req));
        long beginTime = System.currentTimeMillis();
        CommonApiResponse<R> res;
        try {
            res = CommonApiResponse.success(func.apply(req));
        } catch (BizException e) {
            log.error(desc + " error :" + e.getMessage(), e);
            res = onFail.map(CommonApiResponse::success)
                    .orElseGet(() -> CommonApiResponse.failure(e.getErrorCode().getCode(), e.getMessage()));
        } catch (Exception e) {
            String errorMessage = desc + "error" + e.getMessage();
            log.error(errorMessage, e);
            res = onFail.map(CommonApiResponse::success)
                    .orElseGet(() -> CommonApiResponse.failure(GeneralCodes.InternalError.getCode(), SYS_ERROR));
        }
        log.info(desc + " cost:[" + (System.currentTimeMillis() - beginTime) + "]");
        if (printResLog) {
            log.info(desc + " res=" + Jacksons.BASE.lazyToJson(res));
        }
        return res;
    }

    /**
     * 封装一个调用，将结果用 Result<T> 封装返回。
     *
     * @param log
     * @param desc
     * @param req1
     * @param req2
     * @param func
     * @param onFail
     * @param <T1>
     * @param <T2>
     * @param <R>
     * @return
     */
    public static <T1, T2, R> CommonApiResponse<R> wrap(Logger log, String desc, T1 req1, T2 req2,
                                                        Function2<T1, T2, R> func,
                                             Optional<R> onFail) {
        log.info("{} request1={} request2={}", desc, JSONUtil.toJsonStr(req1), JSONUtil.toJsonStr(req2));
        CommonApiResponse<R> res;
        try {
            res = CommonApiResponse.success(func.apply(req1, req2));
        } catch (BizException e) {
            log.error(desc + " error :" + e.getMessage(), e);
            res = onFail.map(CommonApiResponse::success)
                    .orElseGet(() -> CommonApiResponse.failure(e.getErrorCode().getCode(), e.getMessage()));
        } catch (Exception e) {
            String errorMessage = desc + UNKNOWN_ERROR + e.getMessage();
            log.error(errorMessage, e);
            res = onFail.map(CommonApiResponse::success)
                    .orElseGet(() -> CommonApiResponse.failure(GeneralCodes.InternalError.getCode(), SYS_ERROR));
        }
        //log.info(desc + " res={}", Jacksons.BASE.lazyToJson(res));
        return res;
    }

    public static <R> CommonApiResponse<R> wrap(Logger log, String desc, Function0<R> func, boolean printResLog) {
        return wrap(log, desc, func, printResLog, Optional.empty());
    }

    public static <R> CommonApiResponse<R> wrap(Logger log, String desc, Function0<R> func, boolean printResLog,
                                     Optional<R> onFail) {
        log.info("{} req param is null", desc);
        CommonApiResponse<R> res;
        try {
            res = CommonApiResponse.success(func.apply());
        } catch (BizException e) {
            log.error(desc + " error :" + e.getMessage(), e);
            res = onFail.map(CommonApiResponse::success)
                    .orElseGet(() -> CommonApiResponse.failure(e.getErrorCode(), e.getMessage()));
        } catch (Exception e) {
            String errorMessage = desc + UNKNOWN_ERROR + e.getMessage();
            log.error(errorMessage, e);
            res = onFail.map(CommonApiResponse::success)
                    .orElseGet(() -> CommonApiResponse.failure(GeneralCodes.InternalError, SYS_ERROR));
        }
        if (printResLog) {
            log.info(desc + " res=" + Jacksons.BASE.lazyToJson(res));
        }
        return res;
    }
}
