package com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto;

import com.mi.info.intl.retail.model.BasePageRequest;
import lombok.Data;

import java.util.List;

/**
 * 新品目标查询的入参
 *
 * @author: chuang
 * @since: 2025/8/1
 */
@Data
public class NewProductTargetReq  extends BasePageRequest {
    Long id;
    /**
    *品类
    */
    List<String> categoryCode;
    /**
    *系列
    */
    List<String> seriess;
    /**
     * 项目
     */
    List<String> projects;
    /**
    *国家
    */
    List<String> regions;
    /**
    *国家 列表形式
    */
    List<String> countryCode;
    /**
    *状态:0 已下发 ;1 国家已修改;
    */
    String status;
    /**
    *目标类型 1 首销期 2 生命周期
    */
    String targetType;
}