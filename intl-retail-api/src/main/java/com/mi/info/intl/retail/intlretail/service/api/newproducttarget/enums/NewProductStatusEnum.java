package com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

public enum NewProductStatusEnum {
    DONE(0),
    MOIDFY(1);
    @EnumValue
    private final int code;

    NewProductStatusEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static NewProductStatusEnum fromCode(int code) {
        for (NewProductStatusEnum action : NewProductStatusEnum.values()) {
            if (action.getCode() == code) {
                return action;
            }
        }
        throw new IllegalArgumentException("Invalid VerifyAction code: " + code);
    }
}
