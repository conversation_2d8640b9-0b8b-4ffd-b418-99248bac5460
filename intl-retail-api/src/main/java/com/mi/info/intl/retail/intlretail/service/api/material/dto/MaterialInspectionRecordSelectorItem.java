package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 物料巡检记录选择器数据结构
 */
@Data
public class MaterialInspectionRecordSelectorItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 品类
     */
    private List<OptionalItem<String>> category;

    /**
     * 渠道
     */
    private List<OptionalItem<String>> channel;

    /**
     * 门店等级
     */
    private List<OptionalItem<String>> storeGrade;

    /**
     * 阵地类型
     */
    private List<OptionalItem<String>> positionType;

    /**
     * 任务状态
     */
    private List<OptionalItem<Integer>> taskStatus;

    /**
     * 任务类型
     */
    private List<OptionalItem<Integer>> taskType;

    /**
     * 巡检状态
     */
    private List<OptionalItem<Integer>> inspectionStatus;
}