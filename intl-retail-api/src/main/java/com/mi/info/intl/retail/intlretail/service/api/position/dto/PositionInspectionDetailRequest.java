package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description 查询巡店详情入参
 * <AUTHOR>
 * @Date 2025/7/11 16:55
 */
@Data
public class PositionInspectionDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 阵地编码
     */
    @NotNull(message = "positionInspectionId is required")
    private Long positionInspectionId;

}
