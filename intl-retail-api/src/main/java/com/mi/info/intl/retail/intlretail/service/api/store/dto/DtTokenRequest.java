package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import lombok.Data;

@Data
public class DtTokenRequest {
    private String sk;
    private Long ts;
    private String version;
    private String userName;
    private String sysId;
    private String pageId;


    /**
     * 表示看板的类型：exam 考试看板 examDetail 考试详情
     * 这个字段用于存储当前请求中涉及的板子的类型信息。
     * 板子类型可以是硬件板子的型号、软件模拟的板子类型等。
     *
     * @see DtTokenRequest
     */
    private String boardType;

}