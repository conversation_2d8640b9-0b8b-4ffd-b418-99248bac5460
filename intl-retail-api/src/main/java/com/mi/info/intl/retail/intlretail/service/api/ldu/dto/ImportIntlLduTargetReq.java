package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class ImportIntlLduTargetReq implements Serializable {

    private static final long serialVersionUID = 852257008547568237L;

    /**
     * excel文件下载地址
     */
    @JsonProperty("url")
    @ApiDocClassDefine(value = "excel文件下载地址")
    private String url;


}
