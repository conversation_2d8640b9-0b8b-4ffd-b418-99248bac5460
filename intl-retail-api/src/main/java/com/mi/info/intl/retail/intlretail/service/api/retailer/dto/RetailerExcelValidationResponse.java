package com.mi.info.intl.retail.intlretail.service.api.retailer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class RetailerExcelValidationResponse implements Serializable {

    private static final long serialVersionUID = 101839384037498739L;

    private List<RetailerExcelValidationContent> content;

    @JsonProperty("error_url")
    private String errorUrl;


}
