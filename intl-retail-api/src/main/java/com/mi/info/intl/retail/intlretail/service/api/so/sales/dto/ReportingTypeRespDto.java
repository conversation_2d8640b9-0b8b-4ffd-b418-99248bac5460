package com.mi.info.intl.retail.intlretail.service.api.so.sales.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述：Reporting Type 返回dto
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Data
public class ReportingTypeRespDto implements Serializable {
    
    private static final long serialVersionUID = 7398873161447095762L;
    
    @ApiDocClassDefine("APP Count")
    private Long appCount;

    @ApiDocClassDefine("PC Count")
    private Long pcCount;


}
