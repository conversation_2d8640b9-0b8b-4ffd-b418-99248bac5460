package com.mi.info.intl.retail.intlretail.service.api.so.sales.provider;

import com.alibaba.cola.dto.MultiResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SearchReferenceRespDto;

/**
 * 功能描述：So Sales common dubbo提供者
 *
 * <AUTHOR>
 * @date 2025/8/5
 */
public interface SoSalesCommonDubboProvider {
    
    /**
     * 获取search reference列表数据
     *
     * @param table
     * @param keyWord
     * @param parentKey
     * @return
     */
    MultiResponse<SearchReferenceRespDto> getSearchReferencesData(String table, String keyWord, String parentKey);
    
    /**
     * 根据业务关联ID和所属模块名称获取图片列表数据
     *
     * @param relatedId  业务关联ID
     * @param moduleName 所属模块名称（如ldu_upload）
     * @return
     */
    MultiResponse<String> getPicturesByRelatedIdAndModuleName(Long relatedId, String moduleName);
    
    
}
