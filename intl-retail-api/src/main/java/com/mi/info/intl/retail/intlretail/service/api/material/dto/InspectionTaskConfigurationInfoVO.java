package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

/**
 * 巡检任务配置-详情
 */
@Data
public class InspectionTaskConfigurationInfoVO extends InspectionTaskConfigurationDTO {

    @ApiDocClassDefine(value = "创建人")
    private String creator;
    @ApiDocClassDefine(value = "创建时间")
    private Long creationTime;
    @ApiDocClassDefine(value = "修改人")
    private String modifier;
    @ApiDocClassDefine(value = "修改时间")
    private Long modificationTime;


}
