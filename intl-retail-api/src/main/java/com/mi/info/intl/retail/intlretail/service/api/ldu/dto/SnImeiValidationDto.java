package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * sn/imei校验结果dto
 *
 * <AUTHOR>
 * @date 2025/7/8 11:08
 */
@Data
public class SnImeiValidationDto implements Serializable {

    private static final long serialVersionUID = 973434739743971L;

    @JsonProperty("snImei")
    private String snImei;

    @JsonProperty("snType")
    private String snType;

    @JsonProperty("isValid")
    private Boolean isValid;

    @JsonProperty("message")
    private String message;

}
