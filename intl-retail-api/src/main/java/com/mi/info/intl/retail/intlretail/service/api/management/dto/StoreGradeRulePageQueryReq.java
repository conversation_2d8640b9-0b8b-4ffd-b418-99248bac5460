package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;

import java.util.List;

/**
 * 门店等级规则分页查询请求
 */
@Data
public class StoreGradeRulePageQueryReq {

    /**
     * 规则状态（如：In Effect, Not In Effect）
     */
    private List<String> ruleStatus;

    /**
     * 审批状态（如：In Approval, Rejected, Approved）
     */
    private List<String> approvalStatus;

    /**
     * 渠道类型（如：IR, OPR, DKA, NKA, CES, Xiaomi Store）
     */
    private List<String> channelType;

    /**
     * 零售商名称或编码（模糊搜索）
     */
    private String retailerNameCode;

    /**
     * 申请日期区间
     */
    private DateRange applicationDate;

    /**
     * 审批日期区间
     */
    private DateRange approvedDate;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    @Data
    public static class DateRange {
        private String start;
        private String end;
    }
}
