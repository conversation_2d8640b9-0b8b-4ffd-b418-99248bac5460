package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import lombok.Data;

import java.io.Serializable;
import javax.validation.constraints.NotNull;

@Data
public class MaterialInspectionVerifyRequest implements Serializable {

    /**
     * 巡检记录ID
     */
    @NotNull(message = "id can not be null")
    private Long id;

    /**
     * 审核状态
     */
    @NotNull(message = "verifyStatus can not be null")
    private Integer verifyStatus;

    /**
     * 拒绝原因(枚举code)
     */
    private Integer disapproveReason;

    /**
     * 备注
     */
    private String remark;
}
