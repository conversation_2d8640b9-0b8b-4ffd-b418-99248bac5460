package com.mi.info.intl.retail.intlretail.service.api.so.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * IMEI数据提交请求参数
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Data
public class SubmitImeiReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 匹配规则 ID
     */
    @JsonProperty("ruleId")
    private Long ruleId;

    /**
     * IMEI激活规则id（规则查询接口获取）
     */
    @JsonProperty("imeiRuleId")
    private Integer imeiRuleId;

    /**
     * 国家code，如 CN (中国)、US (美国)
     */
    @JsonProperty("countryCode")
    private String countryCode;

    /**
     * 阵地code
     */
    @JsonProperty("positionCode")
    private String positionCode;

    /**
     * 门店code
     */
    @JsonProperty("storeCode")
    private String storeCode;

    /**
     * 当前用户GUID
     */
    @JsonProperty("userId")
    private String userId;

    /**
     * 当前用户虚拟miId
     */
    @JsonProperty("miId")
    private Long miId;

    /**
     * 职位code
     */
    @JsonProperty("userTitle")
    private Long userTitle;

    /**
     * IMEI明细数组
     */
    @JsonProperty("detailList")
    private List<ImeiDetailDto> detailList;

    /**
     * 图片数组
     */
    @JsonProperty("photoList")
    private List<PhotoDto> photoList;
}
