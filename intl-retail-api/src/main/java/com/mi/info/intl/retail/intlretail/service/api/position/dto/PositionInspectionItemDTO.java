package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PositionInspectionItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String storeName;

    private String positionName;

    private Integer inspectionStatus;

    private String inspectionStatusDesc;

    private String region;

    private String country;

    private String positionCode;

    private String positionType;

    private Integer positionConstructionType;

    private Integer canVerfiy;

    private String positionCategory;

    private Integer storeLimitedRange;

    private String storeLimitedRangeDesc;

    private String positonLocation;

    private String displayStandardization;

    private Long creationTime;

    private String owner;

    private Integer taskStatus;

    private String taskStatusDesc;

    private Integer verificationStatus;

    private String verificationStatusDesc;

    private String positionConstructionTypeDesc;

    private String verifier;

    private Long taskCompletionTime;

    private Long verificationTime;

    /**
     * 拍照是否允许相册选择：1-是，0-否
     */
    private Integer allowPhotoFromGallery;

    private String positionTypeDesc;
}
