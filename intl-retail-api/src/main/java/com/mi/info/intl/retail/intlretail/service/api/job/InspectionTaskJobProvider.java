package com.mi.info.intl.retail.intlretail.service.api.job;

/**
 * <AUTHOR>
 * @date 2025/6/25
 **/
public interface InspectionTaskJobProvider {

    /**
     * 查询大促任务消息是否可以推送
     */
    void queryTaskInspectionPush(String jobParam);

    /**
     * 推送消息
     */
    void pushMessage(String jobParam);

    /**
     * 任务实例过期
     *
     * @param jobParam
     */
    void taskInstanceExpire(String jobParam);
}
