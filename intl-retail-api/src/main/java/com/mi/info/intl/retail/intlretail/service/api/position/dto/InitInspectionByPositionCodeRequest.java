package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 阵地巡检初始化请求参数
 */
@Data
public class InitInspectionByPositionCodeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 国家ID
     */
    @ExcelProperty(index = 0)
    private String countryId;

    /**
     * 阵地编码
     */
    @ExcelProperty(index = 1)
    private String positionCode;

    /**
     * 动作类型
     */
    @ExcelProperty(index = 2)
    private Integer actionType;

    /**
     * 动作时间
     */
    @ExcelProperty(index = 3)
    private Long actionTime;

    /**
     * 建设编码
     */
    private String actionCode;
}