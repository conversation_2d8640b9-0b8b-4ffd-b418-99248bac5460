package com.mi.info.intl.retail.intlretail.service.api.management;

import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerChange;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeChange;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.model.ChannelTypeStatistics;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import com.xiaomi.cnzone.maindataapi.model.req.store.EditPositionInfoRequest;

import java.util.List;

public interface StoreGradeService {
    /**
     * 门店变化触发
     * @param storeGradeChange
     */
    void storeChangeTrigger(StoreGradeChange storeGradeChange);
    void storeChangeTrigger(String storeCode);

    /**
     * 商变化触发
     * @param retailerChange
     */
    void retailerChangeTrigger(RetailerChange retailerChange);

    /**
     * 规则变化触发
     * @param storeGradeRuleReq
     */
    void ruleChangeTrigger(StoreGradeRuleReq storeGradeRuleReq);

    /**
     * 编辑主数据
     * @param editPositionInfoRequest
     */
    void editPositionInfo(EditPositionInfoRequest editPositionInfoRequest);

    /**
     * 获取每个渠道类型的统计信息
     * @return 渠道类型统计列表
     */
    List<ChannelTypeStatistics> getChannelTypeStatistics();

    /**
     * 获取门店等级统计信息
     * @param ruleId 门店等级规则ID
     * @return 门店等级统计列表
     */
    List<StoreGradeCompleteStatistics> getStoreGradeCompleteStatistics(Integer ruleId);

}
