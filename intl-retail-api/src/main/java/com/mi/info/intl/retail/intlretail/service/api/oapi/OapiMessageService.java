package com.mi.info.intl.retail.intlretail.service.api.oapi;

import com.lark.oapi.service.im.v1.model.ListChatResp;
import com.mi.info.intl.retail.intlretail.service.api.oapi.dto.MissingRuleAlertDTO;

import java.util.Map;

public interface OapiMessageService {

    boolean sendInteractiveMessage(String receiveId, String template, Map<String, Object> map);

    ListChatResp searchChat(String userIdType, String pageToke);

    boolean sendMissingRuleAlertMessage(String receiveId, MissingRuleAlertDTO missingRuleAlertDTO);
}
