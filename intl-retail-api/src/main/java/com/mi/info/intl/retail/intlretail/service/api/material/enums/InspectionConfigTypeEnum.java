package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import lombok.Getter;

@Getter
public enum InspectionConfigTypeEnum {
    /**
     * 巡检配置类型枚举
     */
    TASK_TYPE("1", "任务类型"),
    CYCLE_TYPE("2", "循环类型"),
    POSM_MATERIAL("3", "POSM材料类型"),
    TARGET_TYPE("4", "首销期-生命周期");

    private final String code;
    private final String description;

    InspectionConfigTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
