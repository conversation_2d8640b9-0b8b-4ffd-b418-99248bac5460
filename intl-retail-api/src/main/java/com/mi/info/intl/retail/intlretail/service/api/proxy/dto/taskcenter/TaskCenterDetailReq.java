package com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TaskCenterDetailReq extends TaskCenterCommonReq {

    // 实例id
    private String actionTaskCenterTaskInstanceId;

    // 批次id
    private String taskBatchId;

    // 任务模板id
    private String taskDefinitionId;

    // 国家或者地区信息，必传
    private String locale;

    // 任务二级分类id
    private Integer businessTypeId;

    // 事件id
    private Integer actionTaskCenterEventInstanceId;

    // 未来日期时间
    private String effectiveDay;

    private String languageKey;

}

