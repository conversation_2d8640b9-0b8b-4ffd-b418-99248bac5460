package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class CommonResponse<T> implements Serializable {
    private static final long serialVersionUID = 6465906308962043704L;
    private int code;
    private String message;
    private transient T data;

    public CommonResponse(T body) {
        this.code = 0;
        this.message = "ok";
        this.data = body;
    }

    public CommonResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 通用失败响应方法（支持泛型）
     */
    public static <T> CommonResponse<T> failure(int code, String message) {
        return new CommonResponse<>(code, message, null);
    }
}

