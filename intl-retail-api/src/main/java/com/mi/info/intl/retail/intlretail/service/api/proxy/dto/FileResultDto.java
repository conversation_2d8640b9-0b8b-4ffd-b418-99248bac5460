package com.mi.info.intl.retail.intlretail.service.api.proxy.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class FileResultDto {

    private String fileName;

    private Integer height;

    private Long id;

    private String tmpUri;

    private String uri;

    private String cdnUri;

    private String uuid;

    private Integer width;

    public FileResultDto() {
        this.fileName = "";
        this.height = null;
        this.id = 0L;
        this.tmpUri = "";
        this.uri = "";
        this.uuid = "";
        this.width = null;
    }

    public FileResultDto(String fileName, String uri) {
        this.fileName = fileName;
        this.height = null;
        this.id = 0L;
        this.tmpUri = "";
        this.uri = uri;
        this.uuid = "";
        this.width = null;
    }
}
