package com.mi.info.intl.retail.intlretail.service.api.masterdata.dto;

import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025/7/31
 **/
@Data
public class PlainTextImeiUserDTO {

    /**
     *  id
     */
    private Long id;

    /**
     *  国家名称
     */
    private String countryName;

    /**
     *  用户名称
     */
    private String userName;

    /**
     *  用户账号
     */
    private String userAccount;

    /**
     *  用户职位
     */
    private String userTitle;

    /**
     *  图片链接
     */
    private String photoUrl;

    /**
     *  创建时间
     */
    private String createdOn;

    /**
     *  创建人账号
     */
    private Long createdBy;

    /**
     *  创建人
     */
    private String createByName;
}
