package com.mi.info.intl.retail.intlretail.service.api.bpm;

import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;

import java.util.Map;

public interface BpmService {

    void create(Map<String, Object> map, BpmApproveBusinessCodeEnum businessCode, String emailPrefix,
                String businessKey);

    void terminate(String businessKey, String emailPrefix, String comment);

}
