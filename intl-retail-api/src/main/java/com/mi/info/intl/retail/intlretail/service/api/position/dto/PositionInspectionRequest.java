package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import com.mi.info.intl.retail.model.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 阵地巡检请求DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PositionInspectionRequest extends BasePageRequest {
    
    /**
     * 查询阵地名称 手机专属
     */
    private String query;
    
    /**
     * 阵地名称（支持模糊查询）
     */
    private String positionName;
    
    /**
     * 区域
     */
    private List<String> region;
    
    /**
     * 国家
     */
    private List<String> country;

    /**
     * positionLocation
     */
    private List<String> positionLocation;
    
    /**
     * 阵地类型
     */
    private List<String> positionType;
    
    /**
     * 阵地编码
     */
    private String positionCode;
    
    /**
     * displayStandardization
     */
    private List<String> displayStandardization;
    
    /**
     * 阵地类别
     */
    private List<String> positionCategory;
    
    /**
     * 阵地建设类型
     */
    private List<Integer> positionConstructionType;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 创建时间开始
     */
    private Long createTimeStart;
    
    /**
     * 创建时间结束
     */
    private Long createTimeEnd;
    
    /**
     * 任务状态
     */
    private List<Integer> taskStatus;
    
    /**
     * 巡检状态
     */
    private List<Integer> inspenctionStatus;
    
    /**
     * 巡检负责人
     */
    private String owner;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 商店限制范围
     */
    private Integer storeLimitedRange;
}