package com.mi.info.intl.retail.intlretail.service.api.so.sales.request;

import com.alibaba.cola.dto.PageQuery;
import com.mi.info.intl.retail.core.es.EsQuery;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 功能描述：Sales Imei 请求dto
 *
 * <AUTHOR>
 * @date 2025/7/31
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class SalesImeiReqDto extends PageQuery {

    private static final long serialVersionUID = 3740893506603531448L;

    @ApiDocClassDefine("id")
    @EsQuery(field = "id", queryType = EsQuery.QueryType.TERM)
    private Long id;
    
    @ApiDocClassDefine("销售人员关联的 mid")
    @EsQuery(field = "salesmanMid", queryType = EsQuery.QueryType.TERM)
    private Long salesmanMid;
    
    @ApiDocClassDefine("销售人员的职位")
    @EsQuery(field = "salesmanJobTitle", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> salesmanJobtitleList;

    @ApiDocClassDefine("imei")
    private String imei;

    @ApiDocClassDefine("sn")
    private String sn;
    
    @ApiDocClassDefine("store_code")
    @EsQuery(field = "storeCode", queryType = EsQuery.QueryType.TERM)
    private String storeRmsCode;
    
    @ApiDocClassDefine("门店等级")
    @EsQuery(field = "storeGrade", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> storeGradeList;
    
    @ApiDocClassDefine("门店类型")
    @EsQuery(field = "storeType", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> storeTypeList;
    
    @ApiDocClassDefine("门店渠道类型（线上/线下等，需结合业务明确）")
    @EsQuery(field = "storeChannelType", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> storeChannelTypeList;
    
    @ApiDocClassDefine("position code")
    @EsQuery(field = "positionCode", queryType = EsQuery.QueryType.TERM)
    private String positionRmsCode;
    
    @ApiDocClassDefine("位置类型（如 仓库、展厅 等）")
    @EsQuery(field = "positionType", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> positionTypeList;
    
    @ApiDocClassDefine("关联零售商ID")
    @EsQuery(field = "retailerCode", queryType = EsQuery.QueryType.TERM)
    private String retailerCode;
    
    @ApiDocClassDefine("国家缩写")
    @EsQuery(field = "countryShortcode", queryType = EsQuery.QueryType.TERM)
    private String countryShortcode;
    
    @ApiDocClassDefine("城市编码")
    @EsQuery(field = "cityCode", queryType = EsQuery.QueryType.TERM)
    private String cityCode;
    
    @ApiDocClassDefine("产品 code")
    @EsQuery(field = "productCode", queryType = EsQuery.QueryType.TERM)
    private Long productCode;

    @ApiDocClassDefine("激活开始时间")
    @EsQuery(field = "activationTime", queryType = EsQuery.QueryType.RANGE)
    private Long activationStartTime;

    @ApiDocClassDefine("激活结束时间")
    @EsQuery(field = "activationTime", queryType = EsQuery.QueryType.RANGE)
    private Long activationEndTime;

    @ApiDocClassDefine("销售开始时间")
    @EsQuery(field = "salesTime", queryType = EsQuery.QueryType.RANGE)
    private Long salesStartTime;
    
    @ApiDocClassDefine("销售结束时间")
    @EsQuery(field = "salesTime", queryType = EsQuery.QueryType.RANGE)
    private Long salesEndTime;

    @ApiDocClassDefine("验证结果")
    @EsQuery(field = "activationResult", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> verificationResultList;
    
    @ApiDocClassDefine("SPU英文")
    @EsQuery(field = "spuNameEn", queryType = EsQuery.QueryType.MATCH)
    private String spuNameEn;
    
    @ApiDocClassDefine("产品线EN")
    @EsQuery(field = "productLineEn", queryType = EsQuery.QueryType.TERMS)
    private List<String> productLineEnList;
    
    @ApiDocClassDefine("上报类型")
    @EsQuery(field = "reportingType", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> reportingTypeList;
    
    @ApiDocClassDefine("创建人mid")
    @EsQuery(field = "createdBy", queryType = EsQuery.QueryType.TERM)
    private Long createdBy;
    
    @ApiDocClassDefine("创建开始时间")
    @EsQuery(field = "createdOn", queryType = EsQuery.QueryType.RANGE)
    private Long createStartTime;
    
    @ApiDocClassDefine("创建结束时间")
    @EsQuery(field = "createdOn", queryType = EsQuery.QueryType.RANGE)
    private Long createEndTime;
    
    @ApiDocClassDefine("门店是否含 SR（0：否；1：是）")
    @EsQuery(field = "storeHasSr", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> hasSrList;
    
    @ApiDocClassDefine("门店是否含 PC（0：否；1：是）")
    @EsQuery(field = "storeHasPc", queryType = EsQuery.QueryType.TERMS)
    private List<Integer> hasPcList;

    // 浅翻页，供导出使用 上一页的最后一条数据的唯一key，如id,或者createdOn+id
    private transient List<Object> searchAfter;
    
}
