package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

@Getter
@AllArgsConstructor
public enum DisapproveReasonEnum implements I18nDesc {

    PHOTO_BLURRY(1, () -> T.tr("inspection.photo_blurry")),
    POSITION_TYPE_INCORRECT(2, () -> T.tr("inspection.position_type_incorrect")),
    INCONSISTENT_WITH_ACCEPTANCE_PHOTO(3, () -> T.tr("inspection.inconsistent_with_acceptance_photo")),
    POSITION_CATEGORY_MISMATCH(4, () -> T.tr("inspection.position_category_mismatch")),
    DISPLAY_STANDARDIZATION_UNQUALIFIED(5, () -> T.tr("inspection.display_standardization_unqualified")),
    OTHER(6, () -> T.tr("inspection.other"));

    @EnumValue
    private final Integer code;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static DisapproveReasonEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DisapproveReasonEnum value : DisapproveReasonEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    private final Supplier<String> i18nDesc;
}
