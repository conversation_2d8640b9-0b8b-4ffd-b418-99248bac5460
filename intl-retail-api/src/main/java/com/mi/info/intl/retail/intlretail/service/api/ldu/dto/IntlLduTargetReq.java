package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mi.info.intl.retail.model.BasePageRequest;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class IntlLduTargetReq extends BasePageRequest implements Serializable {

    private static final long serialVersionUID = 852257008547568237L;

    /**
     * 区域
     */
    @ApiDocClassDefine(value = "区域")
    @JsonProperty("countryCode")
    private List<String> countryCode;

    /**
     * 零售商编码
     */
    @JsonProperty("retailerCode")
    @ApiDocClassDefine(value = "零售商编码")
    private String retailerCode;

    /**
     * 产品线
     */
    @JsonProperty("productLine")
    @ApiDocClassDefine(value = "产品线")
    private String productLine;

    /**
     * 项目编码
     */
    @JsonProperty("projectCode")
    @ApiDocClassDefine(value = "项目编码")
    private String projectCode;

    /**
     * 商品ID
     */
    @JsonProperty("goodsId")
    @ApiDocClassDefine(value = "商品ID")
    private String goodsId;

    /**
     * 商品名称
     */
    @JsonProperty("goodsName")
    @ApiDocClassDefine(value = "商品名称")
    private String goodsName;


    /**
     * 产品ID
     */
    @JsonProperty("productId")
    @ApiDocClassDefine(value = "产品ID")
    private String productId;

    /**
     * 产品名称
     */
    @JsonProperty("productName")
    @ApiDocClassDefine(value = "产品名称")
    private String productName;
}
