package com.mi.info.intl.retail.intlretail.service.api.so;

import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetFilterListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SubmitQtyReq;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyStatisticsRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyStatisticsResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetQtyBoardDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetQtyBoardDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetStoreListRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetStoreListResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportTemplateRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.GetImportTemplateResponse;

/**
 * QTY服务接口
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
public interface QtyService {

    /**
     * 可售SKU查询
     *
     * @param request 查询请求
     * @return 可售SKU列表
     */
    CommonResponse<GetSkuListResponse> getSkuList(GetSkuListRequest request);

    /**
     * QTY提交
     *
     * @param request 提交请求
     * @return 提交结果
     */
    CommonResponse<Object> submitQty(SubmitQtyReq request);

    /**
     * 获取筛选列表
     *
     * @return 筛选列表
     */
    CommonResponse<GetFilterListResponse> getFilterList();

    /**
     * 查询QTY列表
     *
     * @param request 查询请求
     * @return QTY列表
     */
    CommonResponse<QueryQtyListResponse> queryQtyList(QueryQtyListRequest request);

    /**
     * QTY统计查询
     *
     * @param request 查询请求
     * @return 统计结果
     */
    CommonResponse<QueryQtyStatisticsResponse> queryQtyStatistics(QueryQtyStatisticsRequest request);

    /**
     * QTY看板数据查询
     *
     * @param request 查询请求
     * @return 看板数据
     */
    CommonResponse<GetQtyBoardDataResponse> getQtyBoardData(GetQtyBoardDataRequest request);

    /**
     * QTY明细页查询
     *
     * @param request 查询请求
     * @return QTY明细
     */
    CommonResponse<QueryQtyDetailResponse> queryQtyDetail(QueryQtyDetailRequest request);

    /**
     * 获取门店列表
     *
     * @param request 查询请求
     * @return 门店列表
     */
    CommonResponse<GetStoreListResponse> getStoreList(GetStoreListRequest request);

    /**
     * 导入模板下载
     *
     * @param request 下载请求
     * @return 模板文件链接
     */
    CommonResponse<GetImportTemplateResponse> getImportTemplate(GetImportTemplateRequest request);
} 