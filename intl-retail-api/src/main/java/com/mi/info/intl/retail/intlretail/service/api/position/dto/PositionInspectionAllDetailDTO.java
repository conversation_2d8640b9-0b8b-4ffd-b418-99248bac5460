package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/15 11:32
 */
@Data
public class PositionInspectionAllDetailDTO {
    /**
     * 阵地基础信息
     */
    private PositionBasicInfo positionBasicInfo;
    /**
     * 巡检信息
     */
    private InspectionInfo inspectionInfo;
    /**
     * 图片库
     */
    private PhotoGalleries photoGalleries;

    @Data
    public static class PositionBasicInfo {
        private String country;
        private String positionCode;
        private String positionName;
        private String positionType;
        private String positionCategory;
        private Long creationTime;
        private String positionConstructionType;
        private String displayStandardization;
        private String positionLocation;
        private Integer furnitureTotal = 0;
    }

    @Data
    public static class InspectionInfo {
        private String inspectionOwner;
        private String taskStatus;
        private Long taskCompletionTime;
        private String verifier;
        private String verificationStatus;
        private Long verificationTime;
        private Integer submitCount;
        private Long taskCreationTime;
    }

    @Data
    public static class PhotoGalleries {
        private UploadData positionCreation;
        private UploadData inspectionUpload;
    }


}


