package com.mi.info.intl.retail.intlretail.service.api.ldu;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiGoodsInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SnImeiValidationDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @description sn/imei查询校验服务
 * @date 2025/7/8 10:58
 */
public interface SnImeiService {

    /**
     * 根据SnImeiQueryDto参数查询sn/imei信息，返回CommonResponse<List<SnImeiInfoDto>>
     *
     * @param snImeiQueryDto 请求参数，批量
     * @return CommonResponse<List < SnImeiInfoDto>>
     */
    CommonResponse<List<SnImeiInfoDto>> querySnImeiInfo(SnImeiQueryDto snImeiQueryDto);

    /**
     * 根据SnImeiQueryDto参数查询sn/imei信息，并校验sn/imei是否有效，返回CommonResponse<List<SnImeiValidationDto>>
     *
     * @param snImeiQueryDto 请求参数，批量
     * @return CommonResponse<List < SnImeiValidationDto>>
     */
    CommonResponse<List<SnImeiValidationDto>> validateSnImeiInfo(SnImeiQueryDto snImeiQueryDto);

    /**
     * 根据SN/IMEI查询商品信息，返回CommonResponse<List<SnImeiGoodsInfoDto>>
     *
     * @param snImeiQueryDto 请求参数，批量
     * @return 商品信息
     */
    CommonResponse<List<SnImeiGoodsInfoDto>> queryGoodsInfoBySnImei(SnImeiQueryDto snImeiQueryDto);
}

