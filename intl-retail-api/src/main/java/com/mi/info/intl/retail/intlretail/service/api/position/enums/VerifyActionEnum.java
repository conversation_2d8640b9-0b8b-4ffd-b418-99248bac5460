package com.mi.info.intl.retail.intlretail.service.api.position.enums;

public enum VerifyActionEnum {
    APPROVE(1),
    DISAPPROVE(2);

    private final int code;

    VerifyActionEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static VerifyActionEnum fromCode(int code) {
        for (VerifyActionEnum action : VerifyActionEnum.values()) {
            if (action.getCode() == code) {
                return action;
            }
        }
        throw new IllegalArgumentException("Invalid VerifyAction code: " + code);
    }
}
