package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class ChannelInfoRequest implements Serializable {

    private static final long serialVersionUID = 13434343234343L;
    // 10=Supplier/20=Retailer/30=Distributor
    private int type;
    // 基于名称/code的模糊搜索
    public String search;
    //基于code集合的精确查找，限制200个
    private List<String> codes;
    //国家2位短编码，非必填
    private String areaId;

    private List<CodeWithArea> codeWithArea;

    private String countryCode;


    @Setter
    @Getter
    public static class CodeWithArea implements Serializable {
        private static final long serialVersionUID = 3539116450862287082L;
        public String code;
        public String areaId;
    }
}
