package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.mi.info.intl.retail.model.BasePageRequest;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 新物料巡检列表请求参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialInspectionReq extends BasePageRequest {
    
    /**
     * 巡检负责人
     */
    private String owner;
    
    /**
     * 任务状态 0： 未完成 1：已完成 2：无需完成
     */
    @ApiDocClassDefine(value = "taskStatus 任务状态 0： 未完成 1：已完成 2：无需完成")
    private Integer taskStatus = 0;
    
    /**
     * 阵地名称（支持模糊查询）
     */
    @ApiDocClassDefine(value = "position name 阵地名称（支持模糊查询）")
    private String positionName;
    
    
    /**
     * 巡检状态
     */
    private List<Integer> inspectionStatus;
    
    
}
