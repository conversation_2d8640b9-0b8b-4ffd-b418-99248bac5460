package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;
import java.io.Serializable;

@Data
public class ProductSimpleInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商品id
     */
    @ApiDocClassDefine(value = "商品id")
    @JsonProperty("goodsId")
    private String goodsId;

    /**
     * 商品名称
     */
    @ApiDocClassDefine(value = "商品名称")
    @JsonProperty("goodsName")
    private String goodsName;

}
