package com.mi.info.intl.retail.intlretail.service.api.retailer;

import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerAreaResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerExcelValidationContent;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.DtTokenRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.DtTokenResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ExcelValidationRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface DtService {

    DtTokenResponse getToken(DtTokenRequest request);

}
