package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;

import java.util.List;

/**
 * 规则审批提交参数
 *
 */
@Data
public class StoreGradeRuleApprovalReq {
    
    /**
     * 渠道类型
     */
    private String channelType;
    
    /**
     * 零售商列表
     */
    private List<Retailer> retailers;
    
    /**
     * S级最小数量
     */
    private Integer sMinCount;
    
    /**
     * A级最小数量
     */
    private Integer aMinCount;
    
    /**
     * B级最小数量
     */
    private Integer bMinCount;
    
    /**
     * C级最小数量
     */
    private Integer cMinCount;
    
    /**
     * D级最小数量
     */
    private Integer dMinCount;
    
    /**
     * 零售商内部类
     */
    @Data
    public static class Retailer {
        /**
         * 零售商名称
         */
        private String name;
        
        /**
         * 零售商代码
         */
        private String code;
    }
}
