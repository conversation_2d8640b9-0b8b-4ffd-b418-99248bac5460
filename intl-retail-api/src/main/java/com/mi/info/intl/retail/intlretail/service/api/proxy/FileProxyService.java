package com.mi.info.intl.retail.intlretail.service.api.proxy;

import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.FileResultDto;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.UUID;

public interface FileProxyService {
    FileResultDto upload(String fileName, MultipartFile file, Long timestamp);

    FileResultDto uploadFile(String fileName, File file, Long timestamp, UUID uuid);

    FileResultDto upload(String url, String userToken);

    ResponseEntity<ByteArrayResource> httpForFile(String url, String token, HttpMethod method);
}
