package com.mi.info.intl.retail.intlretail.service.api.so.sales.provider;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesImeiRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.VerificationResultRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;

/**
 * 功能描述：So Sales IMEI dubbo提供者
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
public interface SoSalesImeiDubboProvider {
    
    /**
     * Imei数据分页查询
     *
     * @param salesImeiReqDto
     * @return
     */
    PageResponse<SalesImeiRespDto> getSalesImeiForPage(SalesImeiReqDto salesImeiReqDto);
    
    /**
     * Imei获取验证结果数据
     *
     * @param salesImeiReqDto
     * @return
     */
    SingleResponse<VerificationResultRespDto> getImeiVerificationResultData(SalesImeiReqDto salesImeiReqDto);
    
    /**
     * Imei获取上报角色数据
     *
     * @param salesImeiReqDto
     * @return
     */
    SingleResponse<ReportingRoleRespDto> getImeiReportingRoleData(SalesImeiReqDto salesImeiReqDto);
    
    /**
     * 是否明文IMEI用户（true：是，false：否）
     *
     * @return
     */
    SingleResponse<Boolean> isPlaintextImeiUser();
    
    /**
     * IMEI数据下载
     *
     * @param salesImeiReqDto
     * @return
     */
    SingleResponse<String> export(SalesImeiReqDto salesImeiReqDto);
    
    
}
