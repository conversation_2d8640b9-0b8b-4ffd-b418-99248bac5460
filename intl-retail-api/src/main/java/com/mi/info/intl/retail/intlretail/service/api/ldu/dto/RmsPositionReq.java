package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class RmsPositionReq implements Serializable {

    private static final long serialVersionUID = 853257008547568237L;

    /**
     * 阵地编号
     */
    private String positionCode;


    /**
     *
     * 门店编码
     */
    private String storeCode;
}
