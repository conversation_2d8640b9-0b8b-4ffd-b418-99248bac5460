package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
public class IntlLduSnDto implements Serializable {

    private static final long serialVersionUID = -765432109871143210L;

    private Integer id;

    /**
     * 区域
     */
    private String region;

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家/地区编码
     */
    private String countryCode;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 零售商编码
     */
    private String retailerCode;

    /**
     * 零售商名称
     */
    private String retailerName;

    /**
     * LDU类型:大货（Mass Production Version）、专样（Customized Version）
     */
    private String lduType;

    /**
     * 产品线
     */
    private String productLine;


    /**
     * 产品ID
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 商品ID
     */
    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 项目代码
     */
    private String projectCode;

    /**
     * RAM容量
     */
    private String ramCapacity;

    /**
     * 计划创建日期
     */
    private long planCreateDate;

    /**
     * 计划停用日期
     */
    private long planStopDate;


    /**
     * IMEI 1
     */
    private String imei1;

    /**
     * ROM容量
     */
    private String romCapacity;

    /**
     * 序列号SN
     */
    private String sn;

    /**
     * IMEI 2
     */
    private String imei2;


    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 停用人ID
     */
    private String stopUserId;

    /**
     * 停用人ID
     */
    private String stopUserName;


    /**
     * 0未上报  1已上报
     */
    private int isReport;

    /**
     * 状态:1有效，0是无效
     */
    private int status;

}
