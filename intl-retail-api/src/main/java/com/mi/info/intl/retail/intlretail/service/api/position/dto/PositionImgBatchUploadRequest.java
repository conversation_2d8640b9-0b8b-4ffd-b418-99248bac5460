package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 批量上传弹窗请求
 */
@Data
public class PositionImgBatchUploadRequest implements Serializable {
    /**
     * 店铺门相关图片资源链接
     */
    private ImageFds storeGate;
    /**
     * 职位着陆照片相关图片资源链接
     */
    private ImageFds positionLandingPhoto;
    /**
     * 职位展示相关图片资源链接
     */
    private ImageFds positionDisplay;
    /**
     * 家具图片相关图片资源链接
     */
    private ImageFds furniturePicture;

    /**
     * 提交人(miId)
     */
    private Long account;
    /**
     * 提交人邮箱
     */
    private String email;
} 