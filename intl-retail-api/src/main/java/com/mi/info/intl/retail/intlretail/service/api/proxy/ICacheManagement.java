package com.mi.info.intl.retail.intlretail.service.api.proxy;

import com.mi.info.intl.retail.intlretail.service.api.messagepush.dto.MessagePushResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.DeleteRedisHashCacheDto;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.DeleteUserBaseDataDto;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.PutRedisHashCacheDto;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.UpdateMenuStatusCacheDto;

public interface ICacheManagement {
    void hashDelete(DeleteRedisHashCacheDto deleteRedisHashCacheDto);

    void hashDelete(DeleteUserBaseDataDto deleteUserBaseDataDto);

    void hashPutAll(PutRedisHashCacheDto putRedisHashCacheDto);

    MessagePushResponse hashUpdate(UpdateMenuStatusCacheDto updateMenuStatusCacheDto);
}