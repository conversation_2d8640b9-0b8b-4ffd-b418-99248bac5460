package com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto;

import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class GateWayChannelInfoResponse implements Serializable {
    private int code;
    private String message;
    private List<ChannelInfoResponse> data;
}
