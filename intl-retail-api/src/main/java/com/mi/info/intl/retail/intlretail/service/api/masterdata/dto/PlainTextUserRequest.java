package com.mi.info.intl.retail.intlretail.service.api.masterdata.dto;

import com.mi.info.intl.retail.model.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/31
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PlainTextUserRequest extends BasePageRequest implements Serializable {
    
    private static final long serialVersionUID = 3517120758306970951L;
    
    /**
     *  id
     */
    private Long id;
    /**
     *  国家代码
     */
    private String countryCode;
    /**
     *  用户米ID
     */
    private String userName;

    /**
     * 用户头衔
     */
    private Integer userTitle;

    /**
     * 创建时间
     */
    private Long createStartOn;

    /**
     * 创建时间
     */
    private Long createEndOn;

    /**
     * 创建人
     */
    private String createBy;

    private Long  offset;
    
    /**
     * 地区标
     */
    private String locale;

}
