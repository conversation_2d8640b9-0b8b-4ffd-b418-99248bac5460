package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/7 15:21
 */
@Data
public class ProductLineDto implements Serializable {

    private static final long serialVersionUID = 973434739743971L;

    /**
     * 产品线id
     */
    @ApiDocClassDefine(value = "产品线id")
    @JsonProperty("productLine")
    private String productLine;

    /**
     * 产品线中文名
     */
    @ApiDocClassDefine(value = "产品线中文名")
    @JsonProperty("enName")
    private String enName;

    /**
     * 产品线英文名
     */
    @ApiDocClassDefine(value = "产品线英文名")
    @JsonProperty("cnName")
    private String cnName;
}
