package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;
import java.util.List;

/**
 * 门店等级规则详情响应
 */
@Data
public class StoreGradeRuleDetailResp {
    
    /**
     * 规则详情
     */
    private RuleDetails ruleDetails;
    
    /**
     * 规则修改日志
     */
    private List<RuleModificationLog> ruleModificationLogs;
    
    /**
     * 规则详情
     */
    @Data
    public static class RuleDetails {
        /**
         * 规则ID（修改时才有）
         */
        private Integer id;
        
        /**
         * 渠道类型
         */
        private String channelType;

        /**
         * 零售商名称
         */
        private String retailerName;

        /**
         * 零售商代码
         */
        private String retailerCode;
        
        /**
         * 修改方式
         */
        private String modificationMethod;
        
        /**
         * 门店等级
         */
        private String storeGrade;
        
        /**
         * S级最小数量
         */
        private Integer sMinCount;
        
        /**
         * A级最小数量
         */
        private Integer aMinCount;
        
        /**
         * B级最小数量
         */
        private Integer bMinCount;
        
        /**
         * C级最小数量
         */
        private Integer cMinCount;
        
        /**
         * D级最小数量
         */
        private Integer dMinCount;
    }
    
    /**
     * 零售商信息
     */
    @Data
    public static class Retailer {
        /**
         * 零售商名称
         */
        private String name;
        
        /**
         * 零售商代码
         */
        private String code;
    }
    
    /**
     * 规则修改日志
     */
    @Data
    public static class RuleModificationLog {
        /**
         * 修改人
         */
        private String changedBy;
        
        /**
         * 修改时间
         */
        private Long changedTime;
        
        /**
         * 旧值
         */
        private RuleValues oldValues;
        
        /**
         * 新值
         */
        private RuleValues newValues;
    }
    
    /**
     * 规则值
     */
    @Data
    public static class RuleValues {
        /**
         * S级最小数量
         */
        private Integer sMinCount;
        
        /**
         * A级最小数量
         */
        private Integer aMinCount;
        
        /**
         * B级最小数量
         */
        private Integer bMinCount;
        
        /**
         * C级最小数量
         */
        private Integer cMinCount;
        
        /**
         * D级最小数量
         */
        private Integer dMinCount;
    }
} 