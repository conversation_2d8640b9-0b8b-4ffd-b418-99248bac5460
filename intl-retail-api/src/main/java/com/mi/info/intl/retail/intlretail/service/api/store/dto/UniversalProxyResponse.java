package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class UniversalProxyResponse implements Serializable {
    public Integer code;
    public String message;
    public String data;

    public UniversalProxyResponse(Integer code, String message, String data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
}
