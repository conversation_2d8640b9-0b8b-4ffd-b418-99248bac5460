package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class TaskCenterNoNeedCompleteReq implements Serializable {
    private static final long serialVersionUID = 5813200837116019198L;

    // Mid
    private Long mid;
    // 阵地ID
    private String orgId;
    // 批次ID
    private Long taskBatchId;
    // 1=无需完成
    private Integer type;
    private String retailAppSign = "CHANNEL_RETAIL";
    private String retailTenantId = "2";
    private String languageKey = "en-US";
}
