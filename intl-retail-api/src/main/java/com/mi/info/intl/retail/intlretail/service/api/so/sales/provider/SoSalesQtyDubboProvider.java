package com.mi.info.intl.retail.intlretail.service.api.so.sales.provider;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingTypeRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesQtyRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;

/**
 * 功能描述：So Sales QTY dubbo提供者
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
public interface SoSalesQtyDubboProvider {
    
    /**
     * Qty数据分页查询
     *
     * @param salesQtyReqDto
     * @return
     */
    PageResponse<SalesQtyRespDto> getSalesQtyForPage(SalesQtyReqDto salesQtyReqDto);
    
    /**
     * Qty获取上报角色数据
     *
     * @param salesQtyReqDto
     * @return
     */
    SingleResponse<ReportingRoleRespDto> getQtyReportingRoleData(SalesQtyReqDto salesQtyReqDto);
    
    /**
     * Qty获取上报类型数据
     *
     * @param salesQtyReqDto
     * @return
     */
    SingleResponse<ReportingTypeRespDto> getQtyReportingTypeData(SalesQtyReqDto salesQtyReqDto);
    
    /**
     * QTY数据下载
     *
     * @param salesQtyReqDto
     * @return
     */
    SingleResponse<String> export(SalesQtyReqDto salesQtyReqDto);
    
    
}
