package com.mi.info.intl.retail.intlretail.service.api.position.enums.seria;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;

import java.io.IOException;
import java.util.Arrays;

public class IntToI18nDescDeserializer<T extends Enum<T> & I18nDesc> extends JsonDeserializer<I18nDesc> {
    @Override
    public T deserialize(JsonParser jsonParser, DeserializationContext ctx)
            throws IOException {
        int value = jsonParser.getIntValue();
        JavaType contextualType = ctx.getContextualType();
        if (contextualType != null && contextualType.hasGenericTypes()) {
            Class<T> realType = (Class<T>) contextualType.getBindings().getTypeParameters().get(0).getRawClass();
            return Arrays.stream(realType.getEnumConstants())
                    .filter(item -> item.getCode() == value)
                    .findFirst().orElse(null);
        }
        return null;
    }
}
