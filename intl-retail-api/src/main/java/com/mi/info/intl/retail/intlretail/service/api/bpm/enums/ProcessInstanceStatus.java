

package com.mi.info.intl.retail.intlretail.service.api.bpm.enums;

import lombok.Getter;

@Getter
public enum ProcessInstanceStatus {
    RUNNING("RUNNING", "审批中", "RUNNING"),
    TERMINATED("TERMINATED", "已终止", "TERMINATED"),
    COMPLETED("COMPLETED", "已通过", "COMPLETED"),
    REJECTED("REJECTED", "已驳回", "REJECTED");

    private String code;
    private String descCn;
    private String descEn;

    ProcessInstanceStatus(String code, String descCn, String descEn) {
        this.code = code;
        this.descCn = descCn;
        this.descEn = descEn;
    }
}
