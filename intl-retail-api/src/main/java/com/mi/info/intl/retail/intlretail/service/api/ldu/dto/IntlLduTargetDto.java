package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class IntlLduTargetDto implements Serializable {

    private static final long serialVersionUID = -723132109871143210L;

    private Long id;

    /**
     * 区域
     */
    private String region;

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 零售商编码
     */
    private String retailerCode;

    /**
     * 零售商名称
     */
    private String retailerName;


    /**
     * 产品线
     */
    private String productLine;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;


    /**
     * 产品ID
     */
    private String goodsId;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 项目代码
     */
    private String projectCode;

    /**
     * RAM容量
     */
    private String ramCapacity;

    /**
     * 目标创建日期
     */
    private long targetCreateDate;

    /**
     * 目标修改日期
     */
    private long targetUpdateDate;

    /**
     * ROM容量
     */
    private String romCapacity;


    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人ID
     */
    private String updateUserId;

    /**
     * 修改人ID
     */
    private String updateUserName;

    /**
     * 目标覆盖门店数
     */
    private int targetCoveredStores;

    /**
     * 实际覆盖门店数
     */
    private int actualCoveredStores;

    /**
     * 目标销出样品数
     */
    private int targetSampleOut;

    /**
     * 实际销出样品数
     */
    private int actualSampleOut;
}
