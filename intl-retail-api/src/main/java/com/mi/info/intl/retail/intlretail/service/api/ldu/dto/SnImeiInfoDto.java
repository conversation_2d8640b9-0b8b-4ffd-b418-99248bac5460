package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * IMEI信息DTO
 *
 * <AUTHOR>
 * @date 2021/10/12 10:50
 */
@Data
public class SnImeiInfoDto implements Serializable {

    private static final long serialVersionUID = 973434739743971L;


    /**
     * 串号
     */
    @JsonProperty("sn")
    private String sn;

    /**
     * IMEI号
     */
    @JsonProperty("imei")
    private String imei;

    /**
     * 第二IMEI号
     */
    @JsonProperty("imei2")
    private String imei2;

    /**
     * MEID号
     */
    @JsonProperty("meid")
    private String meid;

    /**
     * MAC地址
     */
    @JsonProperty("mac")
    private String mac;

    /**
     * 旧串号
     */
    @JsonProperty("snOld")
    private String snOld;

    /**
     * 外箱号
     */
    @JsonProperty("outerBox")
    private String outerBox;

    /**
     * 物流中箱号
     */
    @JsonProperty("box")
    private String box;

    /**
     * CMII号
     */
    @JsonProperty("cmii")
    private String cmii;

    /**
     * 销售渠道
     */
    @JsonProperty("saleChannel")
    private Integer saleChannel;

    /**
     * 入库类型
     */
    @JsonProperty("inType")
    private Integer inType;

    /**
     * 创建时间（写入IMEI服务数据库的时间）
     */
    @JsonProperty("addTime")
    private Date addTime;

    /**
     * 首次入库类型
     */
    @JsonProperty("firstInType")
    private Integer firstInType;

    /**
     * 首次入库时间
     */
    @JsonProperty("firstInTime")
    private Date firstInTime;

    /**
     * 出库类型
     */
    @JsonProperty("lastOutType")
    private Integer lastOutType;

    /**
     * 出库时间
     */
    @JsonProperty("lastOutTime")
    private Date lastOutTime;

    /**
     * SKU
     */
    @JsonProperty("sku")
    private String sku;

    /**
     * 商品ID
     */
    @JsonProperty("goodsId")
    private String goodsId;

    /**
     * 状态
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * B2B 客户代码
     */
    @JsonProperty("b2bCustomerCode")
    private Integer b2bCustomerCode;

    /**
     * B2B 销售国家
     */
    @JsonProperty("b2bCountry")
    private String b2bCountry;

    /**
     * MiHome关联ID
     */
    @JsonProperty("mihome")
    private Integer mihome;

    /**
     * 主板 FSN
     */
    @JsonProperty("sno")
    private String sno;

    /**
     * 是否可保修，具体定义由售后提供（仅供售后使用）
     */
    @JsonProperty("canRepair")
    private Integer canRepair;

    /**
     * 是否可回购
     */
    @JsonProperty("canRepurchase")
    private Integer canRepurchase;

    /**
     * 单据号，可能是 ASN，也可能是订单号
     */
    @JsonProperty("businessNo")
    private String businessNo;

    /**
     * 是否已回收
     */
    @JsonProperty("recover")
    private Boolean recover;

    /**
     * 出厂时间
     */
    @JsonProperty("exFactoryDate")
    private Date exFactoryDate;

    /**
     * 商品类型
     */
    @JsonProperty("goodsType")
    private Integer goodsType;

    /**
     * 订单号
     */
    @JsonProperty("po")
    private String po;

    /**
     * 串号数据更新时间
     */
    @JsonProperty("lastUpdate")
    private Date lastUpdate;

    /**
     * 第三方串号
     */
    @JsonProperty("thirdSn")
    private String thirdSn;
}
