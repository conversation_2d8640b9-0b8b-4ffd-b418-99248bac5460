package com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class QueryCentralBrainListRequest implements Serializable {


    private static final long serialVersionUID = -6386160831811925699L;

    private Integer pageNo = 1;
    private Integer pageSize = 20;
    private String orderKey;
    private Integer orderType;
    private String taskBatchId;
    private String startTime;
    private String endTime;


}
