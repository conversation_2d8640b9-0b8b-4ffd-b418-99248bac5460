package com.mi.info.intl.retail.intlretail.service.api.result;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class PersonAreaResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<CountryInfoDTO> countryList; // 国家信息列表
    private List<AreaInfoDTO> areaList; // 区域信息列表

    @Data
    public static class AreaInfoDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String area; // 区域
        private String areaCode; // 区域短代码
    }

    @Data
    public static class CountryInfoDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String areaId; // 对应country_code
        private String countryName; // 对应country_name
        private String areaCode; // 区域短代码
    }
}
