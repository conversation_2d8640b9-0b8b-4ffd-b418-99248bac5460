package com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TaskCenterCalendarReq extends TaskCenterCommonReq {

    @JsonProperty("startTimeStamp")
    private Long startTimeStamp;

    @JsonProperty("endTimeStamp")
    private Long endTimeStamp;

    // 0 -时间线 1 -任务类型 非必传，默认为1
    @JsonProperty("orderType")
    private Integer orderType = 1;

    // 任务状态筛选 3:全部 2:无需完成 1:完成 0:未完成 不传返回全部
    @JsonProperty("status")
    private Integer status;

    @JsonProperty("type")
    private String type;

    @JsonProperty("countryShortCode")
    private String countryShortCode;

    @JsonProperty("manageCheckout")
    private Integer manageCheckout;

    private String languageKey;
}
