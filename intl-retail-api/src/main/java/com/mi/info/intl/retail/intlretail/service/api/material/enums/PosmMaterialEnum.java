package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum PosmMaterialEnum {
    POSTER("poster", "poster"),
    STICKER("sticker", "sticker");
    private final String code;
    private final String name;

    PosmMaterialEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<Map<String, Object>> getNode() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (PosmMaterialEnum value : PosmMaterialEnum.values()) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("code", value.getCode());
            map.put("name", value.getName());
            list.add(map);
        }
        return list;
    }
}
