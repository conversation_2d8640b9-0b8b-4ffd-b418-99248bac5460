package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;
import javax.validation.constraints.NotNull;

@Data
public class PositionInspectionHistoryRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 阵地巡检ID
     */
    @NotNull(message = "positionInspectionId is required")
    private Long positionInspectionId;
} 