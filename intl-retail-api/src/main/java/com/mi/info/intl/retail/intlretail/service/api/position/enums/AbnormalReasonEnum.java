package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

@Getter
@AllArgsConstructor
public enum AbnormalReasonEnum implements I18nDesc {

    RETURNED_TO_WAREHOUSE(1, () -> T.tr("inspection.returned_to_warehouse")),
    TRANSFERRED_TO_ANOTHER_STORE(2, () -> T.tr("inspection.transferred_to_another_store")),
    SCRAPPED(3, () -> T.tr("inspection.scrapped")),
    LOST(4, () -> T.tr("inspection.lost")),
    OTHER(5, () -> T.tr("inspection.other"));

    private final Integer code;
    private final Supplier<String> i18nDesc;
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static AbnormalReasonEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AbnormalReasonEnum value : AbnormalReasonEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
