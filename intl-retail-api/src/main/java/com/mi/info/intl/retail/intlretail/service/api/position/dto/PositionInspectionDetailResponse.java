package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import lombok.Data;
import java.util.List;

/**
 * 阵地巡检详情响应DTO
 */
@Data
public class PositionInspectionDetailResponse {
    
    /**
     * 门店信息
     */
    private StoreInfo storeInfo;
    
    /**
     * 各部分内容
     */
    private UploadData sections;
    
    /**
     * 门店信息
     */
    @Data
    public static class StoreInfo {
        /**
         * 门店名称
         */
        private String storeName;
        
        /**
         * 阵地名称
         */
        private String frontName;
        
        /**
         * 阵地建设类型
         */
        private String positionConstructionType;

        
        /**
         * 创建时间
         */
        private Long creationTime;
        
        /**
         * 巡检单状态
         */
        private String status;
        
        /**
         * 备注
         */
        private String remark;
    }
} 