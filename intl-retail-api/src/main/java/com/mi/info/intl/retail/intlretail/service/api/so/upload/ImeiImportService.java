package com.mi.info.intl.retail.intlretail.service.api.so.upload;

import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;

/**
 * IMEI数据导入服务接口
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
public interface ImeiImportService {

    /**
     * IMEI数据导入接口
     *
     * @param request 导入请求参数
     * @return 导入结果
     */
    CommonApiResponse<ImeiImportResponse> importImeiData(ImeiImportRequest request);
}
