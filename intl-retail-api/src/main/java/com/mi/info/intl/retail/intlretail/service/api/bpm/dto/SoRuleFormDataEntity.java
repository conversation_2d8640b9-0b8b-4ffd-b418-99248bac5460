package com.mi.info.intl.retail.intlretail.service.api.bpm.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
@Data
public class SoRuleFormDataEntity extends FormDataEntity {
    // Task Name
    @JsonProperty("input_3ecb4517a2e7")
    private String taskName;
    // Applicant
    @JsonProperty("input_c43d86870158")
    private String applicant;
    // Application Time
    @JsonProperty("input_51d9990264ff")
    private String applicationTime;
    // Rule List
    @JsonProperty("formTable_a42365363fd0")
    private List<ImeiActivationRule> imeiActivationRuleList;
    @Data
    public static class ImeiActivationRule {
        // User Title
        @JsonProperty("input_1753181982452")
        private String userTitle;
        // Activation Category
        @JsonProperty("input_1753182024450")
        private String category;
        // Activation Time From
        @JsonProperty("input_1753182026969")
        private String timeFrom;
        // Activation Time To
        @JsonProperty("input_1753182029903")
        private String timeTo;
    }

    // 表格明细
    @JsonProperty("formTable_37c0e7924e7a")
    private List<RequiringPhotoRule> requiringPhotoRuleList;
    @Data
    public static class RequiringPhotoRule {
        // User Title
        @JsonProperty("input_1754383685915")
        private String userTitle;
        // IMEI Require Photo
        @JsonProperty("input_1754383692313")
        private String imeiRequirePhoto;
        // QTY Require Photo
        @JsonProperty("input_1754383694884")
        private String qtyRequirePhoto;
    }

    // 超链接
    @JsonProperty("link_b1a024be225b")
    private String link;

}