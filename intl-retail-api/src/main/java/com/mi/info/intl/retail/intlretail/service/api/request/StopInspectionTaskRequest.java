package com.mi.info.intl.retail.intlretail.service.api.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 停用巡检任务请求实体
 *
 * <AUTHOR>
 * @date 2025/6/24
 **/
@Data
public class StopInspectionTaskRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 米Id
     */
    private Integer miId;

    /**
     * 状态
     */
    private Boolean status;

}
