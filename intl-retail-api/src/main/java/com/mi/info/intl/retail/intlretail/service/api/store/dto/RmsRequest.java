package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RmsRequest implements Serializable {
    @JsonProperty("Input")
    public String input;

    @JsonProperty("Type")
    public String type;

    public RmsRequest(UniversalProxyRequest universalProxyRequest) {
        this.input = universalProxyRequest.getInput();
        this.type = universalProxyRequest.getType();
    }

    public RmsRequest() {
    }
}
