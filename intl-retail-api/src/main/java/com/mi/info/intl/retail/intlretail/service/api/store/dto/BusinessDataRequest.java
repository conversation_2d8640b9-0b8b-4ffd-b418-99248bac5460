package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class BusinessDataRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("SourceType")
    private String sourceType;
    @JsonProperty("Type")
    private String type;
    @JsonProperty("Input")
    private String input;

}
