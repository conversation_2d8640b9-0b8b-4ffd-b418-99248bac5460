package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 阵地巡检导出响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PositionInspectionExportResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 下载的url
     */
    private String url;
} 