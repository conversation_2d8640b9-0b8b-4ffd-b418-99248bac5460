package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * 根据商品id或者商品名称查询商品信息
 *
 * <AUTHOR>
 * @date 2025/7/17 15:08
 */
@Data
public class SearchProductInfoDto implements Serializable {

    private static final long serialVersionUID = 973434739743971L;

    /**
     * 商品id或名称关键字
     */
    @ApiDocClassDefine(value = "商品id或名称关键字", required = true)
    @JsonProperty("queryKey")
    private String queryKey;

}