package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PositionUploadDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private ImageGroup storeGate;
    private ImageGroup positionDisplay;
    private List<ImageGroup> furniturePictures;
    private ImageGroup positionLandingPhoto;
    @Data
    public class ImageGroup implements Serializable {

        private static final long serialVersionUID = 1L;
        private String name;
        private List<String> images;
    }
}


