package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/8 11:12
 */
@Data
public class SnImeiQueryDto implements Serializable {

    private static final long serialVersionUID = 973434739743971L;

    /**
     * sn串号
     */
    @ApiDocClassDefine(value = "sn串号列表")
    @JsonProperty("snList")
    private List<String> snList;

    /**
     * imei串号
     */
    @ApiDocClassDefine(value = "imei串号列表")
    @JsonProperty("imeiList")
    private List<String> imeiList;

    /**
     * 69码
     */
    @ApiDocClassDefine(value = "69码列表")
    @JsonProperty("sixNineCodeList")
    private List<String> sixNineCodeList;

}
