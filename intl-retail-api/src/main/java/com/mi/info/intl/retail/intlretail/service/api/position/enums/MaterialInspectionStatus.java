package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

@AllArgsConstructor
@Getter
public enum MaterialInspectionStatus implements I18nDesc {

    /**
     * 核验通过
     */
    PASSED(1, () -> "Passed"),

    /**
     * 核验未通过
     */
    REJECTED(2, () -> "Rejected"),

    /**
     * 待核验
     */
    VERIFYING(3, () -> "Verifying"),

    /**
     * 未完成
     */
    INCOMPLETED(4, () -> "InCompleted"),

    /**
     * 已完成
     */
    COMPLETED(5, () -> "Completed");

    @EnumValue
    private final Integer code;

    private final Supplier<String> i18nDesc;
}
