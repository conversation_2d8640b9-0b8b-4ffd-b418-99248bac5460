package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 新品物料巡检详情
 *
 * <AUTHOR>
 * @date 2025年08月01日
 */
@Data
public class MaterialInspectionDetailResponse implements Serializable {
    
    private static final long serialVersionUID = 4861242400170722867L;
    
    /**
     * 门店信息
     */
    private StoreInfo storeInfo;
    
    /**
     * 各部分内容
     */
    private List<UploadMaterialData> sections;
    /**
     * 门店信息
     */
    @Data
    public static class StoreInfo {
        /**
         * 门店名称
         */
        private String storeName;
        
        /**
         * 阵地名称
         */
        private String frontName;
        
        /**
         * 阵地建设类型
         */
        private String positionConstructionType;
        
        
        /**
         * 创建时间
         */
        private Long creationTime;
        
        /**
         * 巡检单状态
         */
        private String status;
        
        /**
         * 备注
         */
        private String remark;
    }
    
    
}
