package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@TableName(value = "intl_file_upload", autoResultMap = true)
@Data
public class IntlFileUploadDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件全局唯一标识
     */
    @TableField("guid")
    private String guid;

    /**
     * 业务关联ID
     */
    @TableField("related_id")
    private Long relatedId;

    /**
     * 标记是否为离线上传：0-在线，1-离线
     */
    @TableField("is_offline_upload")
    private Integer isOfflineUpload;

    /**
     * 标记是否已上传到Blob：0-未上传，1-已上传
     */
    @TableField("is_uploaded_to_blob")
    private Integer isUploadedToBlob;

    /**
     * 所属模块名称（如ldu_upload）
     */
    @TableField("module_name")
    private String moduleName;

    /**
     * 上传者用户名或工号
     */
    @TableField("uploader_name")
    private String uploaderName;

    /**
     * 文件上传时间（UTC时间）
     */
    @TableField("uploader_time")
    private Long uploaderTime;

    /**
     * 文件存储URL（FDS文件服务地址）
     */
    @TableField("fds_url")
    private String fdsUrl;

    /**
     * 文件水印标记：0-需要水印，1-无需水印
     */
    @TableField("is_no_watermark")
    private Integer isNoWatermark;

    /**
     * 记录创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 记录最后更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 文件后缀
     */
    @TableField("suffix")
    private String suffix;
}
