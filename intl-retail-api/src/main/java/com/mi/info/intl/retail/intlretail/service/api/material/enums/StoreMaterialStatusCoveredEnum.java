package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum StoreMaterialStatusCoveredEnum {
    NO_COVERED(0, "未覆盖(未收到物料)"),
    HAD_COVERED(1, "已覆盖(已收到物料)");
    private final Integer code;
    private final String name;

    StoreMaterialStatusCoveredEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (TargetTypeEnum value : TargetTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

}
