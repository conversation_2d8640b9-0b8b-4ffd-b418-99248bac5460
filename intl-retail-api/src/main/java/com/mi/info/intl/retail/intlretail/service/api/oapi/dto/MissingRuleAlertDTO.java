package com.mi.info.intl.retail.intlretail.service.api.oapi.dto;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class MissingRuleAlertDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    //规则预警参数
    public static final String MISSING_RULE_ALERT_PARAM_TITLE = "title";
    public static final String MISSING_RULE_ALERT_PARAM_CONTENT = "content";

    // 国家
    @ApiDocClassDefine("国家")
    private String country;
    // 规则明细
    @ApiDocClassDefine("规则明细")
    private List<MissingRuleAlertLineDTO> lines;

    public Map<String, Object> getContent() {
        Map<String, Object> map = new HashMap<>();
        map.put(MISSING_RULE_ALERT_PARAM_TITLE, formatTitle());
        map.put(MISSING_RULE_ALERT_PARAM_CONTENT, formatLine());
        return map;
    }

    private String formatTitle() {
        return StrUtil.format("{}新增任务无规则配置", country);
    }

    private String formatLine() {
        if (CollectionUtil.isEmpty(lines)) {
            return "";
        }
        List<String> formattedLines = new ArrayList<>();
        for (MissingRuleAlertLineDTO line : this.lines) {
            formattedLines.add(line.formatLine());
        }
        return StrUtil.join("\\n", formattedLines);
    }

    @Data
    public static class MissingRuleAlertLineDTO implements Serializable {
        //任务事件
        @ApiDocClassDefine("任务事件")
        private String taskEvent;
        //任务模板ID
        @ApiDocClassDefine("任务模板ID")
        private String taskTemplateId;
        //规则1
        @ApiDocClassDefine("功能规则")
        private String rule1;
        //规则2
        @ApiDocClassDefine("菜单规则")
        private String rule2;

        //{任务事件_EN}，{任务模板id}，未配置功能规则：{rule name}/未配置菜单规则：{rule name}
        public String formatLine() {
            StringBuilder stringBuffer = new StringBuilder();
            stringBuffer.append(StrUtil.format("{}，{}，", taskEvent, taskTemplateId, rule1, rule2));
            boolean flag = false;
            if (StrUtil.isNotBlank(rule1)) {
                stringBuffer.append(StrUtil.format("未配置功能规则：{}", rule1));
                flag = true;
            }
            if (StrUtil.isNotBlank(rule2)) {
                if (flag) {
                    stringBuffer.append("/");
                }
                stringBuffer.append(StrUtil.format("未配置菜单规则：{}", rule2));
            }
            return stringBuffer.toString();
        }
    }

}
