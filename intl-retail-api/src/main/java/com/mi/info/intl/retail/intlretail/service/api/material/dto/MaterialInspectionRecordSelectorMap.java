package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import lombok.Data;

import java.util.Map;

/**
 * 物料巡检记录选择器数据结构
 */
@Data
public class MaterialInspectionRecordSelectorMap {
    /**
     * 品类
     */
    private Map<String, String> category;

    /**
     * 渠道
     */
    private Map<String, String> channel;

    /**
     * 门店等级
     */
    private Map<String, String> storeGrade;

    /**
     * 阵地类型
     */
    private Map<String, String> positionType;

    /**
     * 任务状态
     */
    private Map<String, String> taskStatus;

    /**
     * 任务类型
     */
    private Map<String, String> taskType;

    /**
     * 巡检状态
     */
    private Map<String, String> inspectionStatus;
}