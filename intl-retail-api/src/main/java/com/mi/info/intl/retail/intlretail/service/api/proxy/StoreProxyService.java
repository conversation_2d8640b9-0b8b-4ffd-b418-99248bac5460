package com.mi.info.intl.retail.intlretail.service.api.proxy;

import com.github.pagehelper.PageInfo;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.UpdateDefaultStoreDto;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.AppPageRequest;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.RmsCallBackReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeQueryReq;
import com.xiaomi.cnzone.storeapi.model.v2.storenode.req.NodeSubmitReq;
import com.xiaomi.cnzone.storems.api.model.req.store.GetPositionInfoRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetStoreLogListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetStorePositionLogListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.PositionListReq;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelMergeListRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelStoreDetailRequest;

import java.util.List;

public interface StoreProxyService {

    void updateDefaultStore(String key, String userAccount, UpdateDefaultStoreDto dto, String userToken);

    /**
     * 获取门店列表
     *
     * @param
     * @return
     */
    Object listStore(GetInternationalChannelMergeListRequest request, String userId, String userAccount, String userToken);

    Object listAllStore(GetInternationalChannelMergeListRequest request, String userId, String userAccount, String userToken);


    Object getStore(GetInternationalChannelStoreDetailRequest request);

    /**
     * 获取门店列表
     *
     * @param
     * @return
     */
    Object listPosition(PositionListReq var1, String name, String userAccount, String userToken);


    Object getPosition(GetPositionInfoRequest var1);
    /**
     * 获取已提交门店列表
     *
     * @param params
     * @return
     */
    PageInfo listSubmitStore(AppPageRequest var1);

    Object query(NodeQueryReq request);

    Object audit(RmsCallBackReq var1);

    Object getSelectorAll();

    Object submit(NodeSubmitReq request, String userToken, RmsUserBaseDataResponse userBaseInfo);

    Object recall(NodeSubmitReq request, String userToken, String userId);

    Object listStoreLogs(GetStoreLogListRequest var1);

    Object listPositionLogs(GetStorePositionLogListRequest var1);

    List<String> listRMSIds(String userToken, String userId, String type);
}
