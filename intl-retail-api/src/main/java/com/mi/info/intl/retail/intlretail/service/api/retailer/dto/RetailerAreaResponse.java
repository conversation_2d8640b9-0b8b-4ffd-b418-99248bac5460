package com.mi.info.intl.retail.intlretail.service.api.retailer.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class RetailerAreaResponse implements Serializable {

    private static final long serialVersionUID = 109839384037498739L;

    private List<ChannelDto> channelList;

    private List<PositionDto> positionCodeList;

    private List<PositionDto> positionTypeList;

    private List<PositionDto> userTitleList;

}
