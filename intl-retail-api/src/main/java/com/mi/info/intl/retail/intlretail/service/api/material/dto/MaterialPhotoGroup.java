package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPhotoGroup implements Serializable {
    private static final long serialVersionUID = 1L;
    private String name;
    private String guid;
    private List<String> images;
}
