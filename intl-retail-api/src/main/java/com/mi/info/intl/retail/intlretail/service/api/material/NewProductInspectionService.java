package com.mi.info.intl.retail.intlretail.service.api.material;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationInfoVO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.SubmitMaterialInspectionRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;

import java.util.List;
import java.util.Map;

public interface NewProductInspectionService {

    /**
     * 查询新品物料巡检列表
     *
     * @param req
     * @return
     */
    CommonApiResponse<PageResponse<MaterialInspectionItem>> getMaterialInspectionList(MaterialInspectionReq req);

    CommonApiResponse<Page<? extends InspectionTaskConfigurationPageReq>> findInspectionTaskPage(
            InspectionTaskConfigurationPageRequest dto);

    CommonApiResponse<InspectionTaskConfigurationInfoVO> getInspectionTaskInfo(Long id);

    CommonApiResponse<List<Map<String, Object>>> getEnumByType(String type);

    CommonApiResponse saveInspectionTask(InspectionTaskConfigurationDTO dto);

    CommonApiResponse submitInspectionTask(InspectionTaskConfigurationDTO dto);

    CommonApiResponse inactiveInspectionTask(InspectionTaskConfigurationDTO dto);

    CommonApiResponse exportInspectionTask(InspectionTaskConfigurationPageRequest dto);

    CommonApiResponse uploadAssignedStore(InspectionTaskConfigurationPageRequest dto);

    /**
     * 获取新品物料列表详情
     *
     * @param req
     * @return
     */
    CommonApiResponse<MaterialInspectionDetailResponse> getMaterialInspectionDetail(
            MaterialInspectionDetailRequest req);

    /**
     * 提交新品物料列表
     *
     * @param req
     * @return
     */
    CommonApiResponse<String> submitMaterialInspection(SubmitMaterialInspectionRequest req);

    /**
     * 获取新品物料列表操作历史
     *
     * @param req
     * @return
     */
    CommonApiResponse<List<MaterialInspectionOperationHistoryResponse>> getMaterialInspectionOperationHistory(
            MaterialInspectionOperationHistoryRequest req);

    /**
     * 获取新品物料巡检示例图
     *
     * @return
     */
    List<String> getMaterialSample(MaterialSampleRequest req);

    /**
     * PC新品物料巡检任务筛选框
     *
     * @return
     */
    CommonApiResponse<MaterialInspectionRecordSelectorItem> getMaterialInspectionRecordSelectorItem();

    /**
     * PC新品物料巡检任务列表
     *
     * @param inspectionRecordPageRequest 查询条件
     * @return 结果
     */
    CommonApiResponse<PageResponse<InspectionRecordPageItem>> getMaterialInspectionRecordPageItem(
            InspectionRecordPageRequest inspectionRecordPageRequest);

    /**
     * PC新品物料巡检审核
     *
     * @param request 审核状态请求
     * @return 结果
     */
    CommonApiResponse<Void> verifyMaterialInspection(MaterialInspectionVerifyRequest request);

    /**
     * PC新品物料巡检任务列表导出
     *
     * @param inspectionRecordPageRequest 查询条件
     * @return 结果
     */
    CommonApiResponse<String> exportMaterialInspectionRecordPageItem(
            InspectionRecordPageRequest inspectionRecordPageRequest);
}
