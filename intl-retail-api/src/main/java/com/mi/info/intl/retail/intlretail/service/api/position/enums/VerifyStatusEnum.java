package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

@Getter
@AllArgsConstructor
public enum VerifyStatusEnum implements I18nDesc {

    NO_NEED(-1, () -> "No Need To Verify"),

    VERIFYING(0, () -> "Verifying"),

    PASSED(1, () -> "Verification Passed"),

    FAILED(2, () -> "Verification Failed");

    @EnumValue
    private final Integer code;

    private final Supplier<String> i18nDesc;
}
