package com.mi.info.intl.retail.intlretail.service.api.ldu;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.BathConReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ImportIntlLduTargetReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IntlLduTargetService {

    /**
     * 分页查询国际LDU目标
     */
    IPage<IntlLduTargetDto> pageListTarget(IntlLduTargetReq query);

    CommonApiResponse<IPage<IntlLduTargetDto>> pageList(IntlLduTargetReq query);

    CommonApiResponse<String> create(IntlLduTargetDto confList);

    CommonApiResponse<String> modify(IntlLduTargetDto confList);

    CommonResponse<String> exportTargetMaintenance(IntlLduTargetReq query);

    CommonApiResponse<List<String>> importTargetMaintenance(ImportIntlLduTargetReq query);

    CommonApiResponse<String> downLoadLduTemp(BathConReq query);

}
