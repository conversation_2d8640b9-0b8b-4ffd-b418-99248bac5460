package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;

/**
 * 门店等级规则请求实体
 * 用于接收前端传递的门店等级配置数据
 */
@Data
public class StoreGradeRuleReq {
    
    /**
     * 主键ID，修改时才传入
     */
    private Integer id;

    /**
     * 类型 提交1  保存2
     */
    private Integer submitType;

    /**
     * 规则日志ID
     */
    private Integer ruleLogId;

    private String method;
    
    /**
     * 渠道类型
     */
    private String channelType;
    
    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 零售商
     */
    private Retailer retailer;

    /**
     * 零售商代码
     */
    private String retailerCode;

    /**
     * 零售商名称
     */
    private String retailerName;
    
    /**
     * S级最小数量
     */
    private Integer sMinCount;
    
    /**
     * A级最小数量
     */
    private Integer aMinCount;
    
    /**
     * B级最小数量
     */
    private Integer bMinCount;
    
    /**
     * C级最小数量
     */
    private Integer cMinCount;

    /**
     * D级最小数量
     */
    private Integer dMinCount;

    /**
     * 跳转申请的页面链接
     */
    private String applicationURL;

    /**
     * 文件链接
     */
    private String fileLink;
    
    /**
     * 文件数据(手动上传时文件信息数据)
     */
    private String fileData;

    /**
     * 零售商内部类
     */
    @Data
    public static class Retailer {
        /**
         * 零售商名称
         */
        private String name;
        
        /**
         * 零售商代码
         */
        private String code;
    }
}
