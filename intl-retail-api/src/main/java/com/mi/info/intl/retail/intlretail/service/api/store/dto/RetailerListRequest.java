package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class RetailerListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("search")
    private String search; // 模糊查找字段 支持名称和code

    @JsonProperty("codes")
    private List<String> codes;

    @JsonProperty("areaId")
    private String areaId;

    @JsonProperty("channelType")
    private String channelType;

    @JsonProperty("pageIndex")
    private Integer pageIndex;

    @JsonProperty("pageCount")
    private Integer pageCount;
}
