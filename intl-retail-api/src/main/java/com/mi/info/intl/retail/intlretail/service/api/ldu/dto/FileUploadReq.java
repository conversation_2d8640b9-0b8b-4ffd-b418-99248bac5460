package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class FileUploadReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 是否离线上传
     */
    private Boolean isOfflineUpload;

    /**
     * 是否已上传到Blob
     */
    private Boolean isUploadedToBlob;

    /**
     * 模块名称
     */
    private String moduleName;


    /**
     * 上传文件详情列表
     */
    private List<UPDetail> upDetails;

    /**
     * 上传者名称
     */
    private String uploaderName;

    /**
     * 上传时间
     */
    private Date uploaderTime;

    /**
     * 上传文件详情
     */
    @Data
    public static class UPDetail  implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 文件存储URL
         */
        private String fdsUrl;

        /**
         * 文件全局唯一标识
         */
        private String guid;

        /**
         * 是否不需要水印
         */
        private Boolean isNoNeedWatermark;
    }
}
