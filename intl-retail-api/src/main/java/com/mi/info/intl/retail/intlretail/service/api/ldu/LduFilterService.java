package com.mi.info.intl.retail.intlretail.service.api.ldu;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductSimpleInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.RetailerQueryDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchProductInfoDto;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;

import java.util.List;

/**
 * ldu列表查询过滤条件数据服务接口
 *
 * <AUTHOR>
 * @date 2025/7/8 17:44
 */
public interface LduFilterService {

    /**
     * 查询产品线列表，返回List<ProductLineDto>，无参数
     */
    CommonResponse<List<ProductLineDto>> queryProductLines();

    /**
     * 查询零售商列表，返回List<RetailerInfoResponse>，参数为RetailerQueryDto
     *
     * @param retailerQueryDto 查询条件
     * @return List<RetailerInfoResponse>
     */
    CommonResponse<List<RetailerInfoResponse>> queryRetailers(RetailerQueryDto retailerQueryDto);

    /**
     * 根据商品id或名称搜索商品信息
     *
     * @param searchProductInfoDto 搜索条件
     * @return <List<ProductSimpleInfoDto>>
     */
    CommonResponse<List<ProductSimpleInfoDto>> searchProductInfoByIdOrName(SearchProductInfoDto searchProductInfoDto);
}
