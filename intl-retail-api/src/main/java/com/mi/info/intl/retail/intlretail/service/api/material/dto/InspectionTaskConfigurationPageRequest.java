package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.mi.info.intl.retail.model.BasePageRequest;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 巡检任务配置-分页查询请求参数
 */
@Data
public class InspectionTaskConfigurationPageRequest extends BasePageRequest implements Serializable {

    @ApiDocClassDefine(value = "规则编码")
    private String ruleCode;

    @ApiDocClassDefine(value = "规则名称")
    private String ruleName;

    @ApiDocClassDefine(value = "创建人")
    private String creator;

    @ApiDocClassDefine(value = "修改人")
    private String modifier;

    @ApiDocClassDefine(value = "规则状态")
    private List<Integer> ruleStatusList;

    @ApiDocClassDefine(value = "区域集合")
    private List<String> regionList;

    @ApiDocClassDefine(value = "国家集合")
    private List<String> countryList;

    @ApiDocClassDefine(value = "场景集合")
    private List<String> taskTypeList;

    @ApiDocClassDefine(value = "场景名称")
    private String taskName;

    @ApiDocClassDefine(value = "待办提醒周期集合")
    private List<Integer> reminderCycleTypeList;

    @ApiDocClassDefine(value = "循环逻辑周期集合")
    private List<Integer> cycleTypeList;

    @ApiDocClassDefine(value = "是否允许从相册选择图片")
    private Integer allowPhotoFromGallery;

    //开始时间、截止时间、创建时间、修改时间

    @ApiDocClassDefine(value = "开始时间_开始")
    private Long startTimeBegin;
    @ApiDocClassDefine(value = "开始时间_结束")
    private Long startTimeEnd;
    @ApiDocClassDefine(value = "结束时间_开始")
    private Long endTimeBegin;
    @ApiDocClassDefine(value = "结束时间_结束")
    private Long endTimeEnd;


    @ApiDocClassDefine(value = "创建时间_开始")
    private Long creationTimeBegin;
    @ApiDocClassDefine(value = "创建时间_结束")
    private Long creationTimeEnd;
    @ApiDocClassDefine(value = "修改时间_开始")
    private Long modificationTimeBegin;
    @ApiDocClassDefine(value = "修改时间_结束")
    private Long modificationTimeEnd;

}
