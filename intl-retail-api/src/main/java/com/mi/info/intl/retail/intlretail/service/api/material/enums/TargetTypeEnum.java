package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum TargetTypeEnum {

    FIRST_SALES_PERIOD("1", "首销期"),
    LIFECYCLE("2", "生命周期");
    private final String code;
    private final String name;

    TargetTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (TargetTypeEnum value : TargetTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static List<Map<String, Object>> getNode() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TargetTypeEnum value : TargetTypeEnum.values()) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("code", value.getCode());
            map.put("name", value.getName());
            list.add(map);
        }
        return list;
    }
}
