package com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新品物料级联下拉
 *
 * @author: chuang
 * @since: 2025/8/4
 */
@Data
public class NewProdcutTargetMetaReq  implements Serializable {
    /**
     * 品类
     */
    private String category;

    /**
     * 系列
     */
    private String series;

    /**
     * 项目
     */
    private String project;

    /**
     * 区域
     */
    private String region;

    /**
     * 国家
     */
    private String country;
    /**
    * // 0表示获得 品类下拉; 1表示获得 系列下拉; 2表示获得 项目下拉;3:表示获得 区域下拉;4表示获得 国家的下拉
    */
    @NotNull(message = "level is required")
    Integer level;
}