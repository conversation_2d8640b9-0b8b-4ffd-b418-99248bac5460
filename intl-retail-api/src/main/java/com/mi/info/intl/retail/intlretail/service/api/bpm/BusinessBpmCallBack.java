package com.mi.info.intl.retail.intlretail.service.api.bpm;

import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;

/**
 * 回调的接口类
 * <AUTHOR>
 */
public interface BusinessBpmCallBack {

    /**
     * 审批流程类型
     * 一般情况 一个审批流程对应一个service
     * 如果多个审批流程类型公用service可以在实现类调用相同的方法即可
     * @return bpmApproveTypeEnum
     */
    BpmApproveBusinessCodeEnum getBpmApproveTypeEnum();

    /**
     * 回调方法
     * @param response CrmApproveCallBackParamDto crm统一的回调参数
     */
    void doCallback(BpmCallBackParamDto response);

}
