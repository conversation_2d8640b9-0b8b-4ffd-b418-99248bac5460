package com.mi.info.intl.retail.intlretail.service.api.proxy.dto;

import lombok.Getter;

import java.io.Serializable;

@Getter
public class ApiCommonResponse<T> implements Serializable {

    private int code;
    private String message;
    private T data;

    public ApiCommonResponse(T body) {
        this.code = 0;
        this.message = "ok";
        this.data = body;
    }

    public ApiCommonResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

}
