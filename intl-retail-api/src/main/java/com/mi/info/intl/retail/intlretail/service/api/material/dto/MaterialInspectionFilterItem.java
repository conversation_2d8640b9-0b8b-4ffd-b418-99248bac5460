package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * 新品物料巡检筛选参数
 */
@Slf4j
@Data
@ExcelIgnoreUnannotated
@JsonIgnoreProperties(ignoreUnknown = true)
public class MaterialInspectionFilterItem implements Serializable {
    
    private static final long serialVersionUID = -1083449467621193633L;
    /**
     * 品类列表
     */
    @ExcelProperty("categoryList")
    private List<Item>  categoryList;
    /**
     * 系列列表
     */
    @ExcelProperty("seriesList")
    private List<Item>  seriesList;
    /**
     * 项目列表
     */
    @ExcelProperty("projectList")
    private List<Item>  projectList;
    /**
     * 任务类型列表
     */
    @ExcelProperty("taskTypeList")
    private List<TaskTypeItem>  taskTypeList;
    
    
    @Data
    public static class Item implements Serializable {
        private static final long serialVersionUID = -1083449467621193633L;
        @ExcelProperty("id")
        private String  name;
        
        @ExcelProperty("value")
        private String  value;
    }
    
    @Data
    public static class TaskTypeItem implements Serializable {
        private static final long serialVersionUID = -1083449467621193633L;
        @ExcelProperty("id")
        private Integer  id;
        
        @ExcelProperty("value")
        private String  name;
    }
}
