package com.mi.info.intl.retail.intlretail.service.api.bpm.enums;

import lombok.Getter;

@Getter
public enum BpmApproveBusinessCodeEnum {

    /**
     * SO规则审批
     */
    SO_RULE("bpmn_1154058707884433408", "SO Update Approve", "wangxuankun");

    BpmApproveBusinessCodeEnum(String modelCode, String instanceName, String emailPrefix) {
        this.modelCode = modelCode;
        this.instanceName = instanceName;
        this.emailPrefix = emailPrefix;
    }

    private final String modelCode;
    private final String instanceName;
    private final String emailPrefix;

}
