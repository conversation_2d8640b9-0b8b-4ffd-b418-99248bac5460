package com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ConfirmPopWindowsReq extends TaskCenterCommonReq {

    private List<ConfirmPopWindowDetail> list;

    private String type;


    @Getter
    @Setter
    public static class ConfirmPopWindowDetail {

        private String taskInstanceId;

        private Boolean redFlag;

        private String taskType;
    }

}
