package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

@Getter
@AllArgsConstructor
public enum BusinessTypeEnum implements I18nDesc {
    //401 New product inspection tour-Dummy  新品POSM巡检
    NEW_PRODUCT_INSPECTION_TOUR_DUMMY(401, () -> T.tr("new.product.inspection.tour-dummy")),
    //402 New product inspection tour-POSM 新品POSM巡检
    NEW_PRODUCT_INSPECTION_TOUR_POSM(402, () -> T.tr("new.product.inspection.tour-posm")),
    //403 New product inspection tour-Price Tag  新品价签巡检
    NEW_PRODUCT_INSPECTION_TOUR_PRICE_TAG(403, () -> T.tr("new.product.inspection.tour-price.tag")),
    //404 New product inspection tour-LDU  新品LDU巡检
    NEW_PRODUCT_INSPECTION_TOUR_LDU(404, () -> T.tr("new.product.inspection.tour-price.ldu")),
    ;
    
    @EnumValue
    private final Integer code;
    
    private final Supplier<String> i18nDesc;
    
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static BusinessTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BusinessTypeEnum value : BusinessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
