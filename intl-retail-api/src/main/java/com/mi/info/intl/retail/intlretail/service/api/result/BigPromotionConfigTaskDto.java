package com.mi.info.intl.retail.intlretail.service.api.result;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ExcelIgnoreUnannotated
public class BigPromotionConfigTaskDto implements Serializable {
    private static final long serialVersionUID = -3691820748332317517L;
    /**
     * 新品变更id
     */
    @ExcelProperty("New Product Change ID")
    private Integer id;
    
    /**
     * 新品名称
     */
    @ExcelProperty("New Product Name")
    private String name;
    
    /**
     * 区域名称
     */
    @ExcelProperty("Region Name")
    private String regionName;
    
    /**
     * 国家/地区名称
     */
    @ExcelProperty("Country/Region Name")
    private String countryName;
    
    /**
     * 变更开始时间
     */
    @ExcelProperty("Change Start Time")
    private String startTime;
    
    /**
     * 变更结束时间
     */
    @ExcelProperty("Change End Time")
    private String endTime;
    
    /**
     * 禁用状态
     */
    private Integer disabledStatus = 0;
    
    /**
     * 禁用状态描述
     */
    @ExcelProperty("Disabled Status")
    private String disabledDesc;
    
    /**
     * 生效状态
     */
    @ExcelProperty("Effective Status")
    private String effectiveDesc;
}
