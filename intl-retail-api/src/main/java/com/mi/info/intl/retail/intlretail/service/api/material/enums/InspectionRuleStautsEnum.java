package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import lombok.Getter;

@Getter
public enum InspectionRuleStautsEnum {
    DRAFT(0, "草稿"),
    ACTIVE(1, "启用"),
    INACTIVE(2, "停用");
    
    private final Integer code;
    
    private final String name;
    
    InspectionRuleStautsEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public static String getNameByCode(Integer code) {
        for (InspectionRuleStautsEnum value : InspectionRuleStautsEnum.values()) {
            if (value.getCode().compareTo(code) == 0) {
                return value.getName();
            }
        }
        return null;
    }
}
