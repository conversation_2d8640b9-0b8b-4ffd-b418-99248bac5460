package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class ChannelInfoRmsRequest implements Serializable {

    private static final long serialVersionUID = 1253588362800523067L;

    // 10=Supplier/20=Retailer/30=Distributor
    public int type;
    // 基于名称/code的模糊搜索
    public String search;
    //基于code集合的精确查找，限制200个
    private List<String> codes;

    private String countryCode;

}
