package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;
import java.util.List;

/**
 * 门店等级规则分页查询响应
 */
@Data
public class StoreGradeRulePageResp {
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Integer totalPages;
    
    /**
     * 数据列表
     */
    private List<StoreGradeRuleItem> list;
    
    /**
     * 门店等级规则项
     */
    @Data
    public static class StoreGradeRuleItem {
        /**
         * 规则ID
         */
        private Integer id;
        
        /**
         * 渠道类型
         */
        private String channelType;
        
        /**
         * 销售方式
         */
        private String method;
        
        /**
         * 零售商名称
         */
        private String retailerName;
        
        /**
         * 零售商代码
         */
        private String retailerCode;
        
        /**
         * S级最小数量
         */
        private Integer sMinCount;
        
        /**
         * A级最小数量
         */
        private Integer aMinCount;
        
        /**
         * B级最小数量
         */
        private Integer bMinCount;
        
        /**
         * C级最小数量
         */
        private Integer cMinCount;
        
        /**
         * D级最小数量
         */
        private Integer dMinCount;
        
        /**
         * 规则状态
         */
        private Integer ruleStatus;
        
        /**
         * 审批状态
         */
        private Integer approvalStatus;
        
        /**
         * 规则状态描述
         */
        private String ruleStatusDesc;
        
        /**
         * 审批状态描述
         */
        private String approvalStatusDesc;
        
        /**
         * 申请时间
         */
        private String applicationTime;
        
        /**
         * 审批时间
         */
        private String approvedTime;
    }
}
