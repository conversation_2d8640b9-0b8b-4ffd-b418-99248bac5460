package com.mi.info.intl.retail.intlretail.service.api.so.upload.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * IMEI数据导入请求参数
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Data
public class ImeiImportRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入记录Id
     */
    private Long importLogId;

    /**
     * 用户mid
     */
    private Long miId;

    /**
     * 源文件链接
     */
    private String sourceFileUrl;
}
