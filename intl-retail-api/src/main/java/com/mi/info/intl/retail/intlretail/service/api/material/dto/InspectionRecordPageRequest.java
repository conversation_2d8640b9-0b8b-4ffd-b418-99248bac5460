package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.mi.info.intl.retail.model.BasePageRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 检查任务查询条件对象
 */
@Data
public class InspectionRecordPageRequest extends BasePageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 品类 - 来源: intl_inspection_rule */
    private String category;

    /** 系列 - 来源: intl_inspection_rule */
    private String series;

    /** 项目 - 来源: intl_inspection_rule.project */
    private String program;

    /** 任务类型 - 来源: intl_inspection_rule */
    private Integer taskType;

    /** 任务状态 - 来源: inspection_record */
    private List<Integer> taskStatus;

    /** 区域 - 来源: intl_inspection_rule */
    private String region;

    /** 国家 - 来源: intl_inspection_rule */
    private List<String> country;

    /** 渠道 - 来源: intl_rms_position.channel_type(channel_type_name) */
    private Integer channel;

    /** 门店等级 - 来源: intl_rms_position.level */
    private Integer storeGrade;

    /** 门店编码 - 来源: intl_rms_position.code */
    private String storeCode;

    /** 门店名称 - 来源: intl_rms_position.store_name */
    private String storeName;

    /** 阵地类型 - 来源: intl_rms_position.type */
    private Integer positionType;

    /** 审核人 - 来源: inspection_record */
    private String verifier;

    /** 是否覆盖 - 来源: intl_inspection_rule */
    private Boolean whetherCover;

    /** 门店检查状态 - 来源: inspection_record */
    private List<Integer> inspectionStatus;

    /** 创建时间起始 - 来源: inspection_record.businessCreationTime */
    private Long createTimeStart;

    /** 创建时间结束 - 来源: inspection_record.businessCreationTime */
    private Long createTimeEnd;

    /** 是否在门店范围内 - 来源: inspection_record */
    private Boolean storeLimitedRange;

    /** 是否有照片 - 来源: inspection_record.uploadData == null */
    private Boolean whetherTakePhoto;

    /** 排序字段集合 */
    private List<OrderItem> orderBy;

    /** 是否导出 */
    private boolean export;

    /**
     * 排序项定义
     */
    @Data
    public static class OrderItem {
        /** 排序字段名 */
        private String field;

        /** 排序方式 (0-升序, 1-降序) */
        private Integer desc;

    }
}