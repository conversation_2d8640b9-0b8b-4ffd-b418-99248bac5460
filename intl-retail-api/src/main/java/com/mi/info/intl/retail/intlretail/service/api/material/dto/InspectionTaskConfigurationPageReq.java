package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.seria.TimestampToAreaTimeConverter;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
/**
 * 巡检任务配置-分页查询列表
 */
@Data
@ExcelIgnoreUnannotated
public class InspectionTaskConfigurationPageReq implements Serializable {
    @ApiDocClassDefine(value = "ID")
    private Long id;

    @ApiDocClassDefine(value = "规则编码")
    @ExcelProperty(value = "规则编码")
    private String ruleCode;

    @ApiDocClassDefine(value = "规则名称")
    @ExcelProperty(value = "规则名称")
    private String ruleName;

    @ApiDocClassDefine(value = "状态")
    private Integer ruleStatus;

    @ApiDocClassDefine(value = "状态描述")
    @ExcelProperty(value = "状态")
    private String ruleStatusDesc;

    @ApiDocClassDefine(value = "区域编码")
    private String region;

    @ApiDocClassDefine(value = "区域名称")
    @ExcelProperty(value = "区域")
    private String regionDesc;

    @ApiDocClassDefine(value = "国家编码")
    private String country;

    @ApiDocClassDefine(value = "国家名称")
    @ExcelProperty(value = "国家")
    private String countryDesc;

    @ApiDocClassDefine(value = "任务场景")
    private Integer taskType;

    @ApiDocClassDefine(value = "任务场景描述")
    @ExcelProperty(value = "任务场景")
    private String taskTypeDesc;

    @ApiDocClassDefine(value = "任务名称")
    @ExcelProperty(value = "任务名称")
    private String taskName;

    @ApiDocClassDefine(value = "循环逻辑")
    private Integer cycleType;

    @ApiDocClassDefine(value = "循环逻辑描述")
    @ExcelProperty(value = "循环逻辑")
    private String cycleTypeDesc;

    @ApiDocClassDefine(value = "创建人")
    @ExcelProperty(value = "创建人")
    private String creator;

    @ApiDocClassDefine(value = "创建时间")
    @ExcelProperty(value = "创建时间", converter = TimestampToAreaTimeConverter.class)
    private Long creationTime;

    @ApiDocClassDefine(value = "修改人")
    @ExcelProperty(value = "修改人")
    private String modifier;

    @ApiDocClassDefine(value = "修改时间")
    @ExcelProperty(value = "修改时间", converter = TimestampToAreaTimeConverter.class)
    private Long modificationTime;



}
