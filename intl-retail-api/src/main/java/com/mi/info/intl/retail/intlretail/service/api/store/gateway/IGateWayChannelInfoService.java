package com.mi.info.intl.retail.intlretail.service.api.store.gateway;

import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ChannelInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.gateway.dto.GateWayChannelInfoResponse;

import java.util.List;

public interface IGateWayChannelInfoService {

    GateWayChannelInfoResponse queryChannelInfo(ChannelInfoRequest requestBody);

    List<BusinessDataResponse> queryBusinessData(BusinessDataInputRequest requestBody);

    List<RetailerListResponse> queryRetailerList(RetailerListRequest requestBody);
}