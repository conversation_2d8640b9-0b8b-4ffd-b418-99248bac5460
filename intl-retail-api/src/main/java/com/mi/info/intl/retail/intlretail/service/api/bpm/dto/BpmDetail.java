package com.mi.info.intl.retail.intlretail.service.api.bpm.dto;

import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.ProcessInstanceStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BpmDetail implements Serializable {

    private static final long serialVersionUID = 1948802524855835441L;
    protected String modelCode;
    protected String processDefinitionId;
    protected String processDefinitionName;
    protected Integer processDefinitionVersion;
    protected String businessKey;
    protected String processInstanceId;
    protected String processInstanceName;
    protected ProcessInstanceStatus processInstanceStatus;
    protected BpmUser startUser;
}
