package com.mi.info.intl.retail.intlretail.service.api.proxy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceBindingRequest implements Serializable {
    private String userAccount;
    private String gaId;
    private String deviceType;
    private Boolean bind;
}
