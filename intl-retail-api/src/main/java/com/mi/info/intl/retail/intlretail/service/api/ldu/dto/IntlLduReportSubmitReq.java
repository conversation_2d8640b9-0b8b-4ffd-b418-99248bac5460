package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class IntlLduReportSubmitReq implements Serializable {


    /**
     * 提交类型
     */
    @ApiDocClassDefine(value = "提交类型")
    private Integer submitType;

    /**
     * 国家
     */
    @ApiDocClassDefine(value = "国家编码")
    private String countryCode;
    /**
     * 商品列表
     */
    @ApiDocClassDefine(value = "商品列表")
    private List<SnImeiGoodsInfoReq> goodsList;

    /**
     * 上报距离差
     */
    @ApiDocClassDefine(value = "上报距离差")
    private BigDecimal reportDistance;

    /**
     * 阵地编号
     */
    @ApiDocClassDefine(value = "阵地编号")
    private String positionCode;

}
