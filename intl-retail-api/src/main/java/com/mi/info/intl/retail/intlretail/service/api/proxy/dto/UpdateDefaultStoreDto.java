package com.mi.info.intl.retail.intlretail.service.api.proxy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UpdateDefaultStoreDto implements Serializable {

    @JsonProperty("store_id")
    private String storeId;

    @JsonProperty("user_account")
    private String userAccount;

    @JsonProperty("longitude")
    private String longitude;

    @JsonProperty("latitude")
    private String latitude;


}
