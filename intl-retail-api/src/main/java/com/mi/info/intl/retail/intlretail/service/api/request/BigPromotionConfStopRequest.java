package com.mi.info.intl.retail.intlretail.service.api.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class BigPromotionConfStopRequest implements Serializable {
    private static final long serialVersionUID = -1351813487471017867L;
        /**
         * id
         */
        private Integer id;
        /**
         * 创建人
         */
        private Long updatedBy;
        /**
         * 创建时间
         */
        private Long miID;
        
}
