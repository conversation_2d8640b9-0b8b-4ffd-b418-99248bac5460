package com.mi.info.intl.retail.intlretail.service.api.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class BigPromotionConfCreateRequest implements Serializable {
    
    private static final long serialVersionUID = -1351813487471017867L;
    
    /**
     * 国家/地区
     */
    private String country;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 名称
     */
    private String name;
    
    private Long createdBy;
    
    private Long miID;
}
