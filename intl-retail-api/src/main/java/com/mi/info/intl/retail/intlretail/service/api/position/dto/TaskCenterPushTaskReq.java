package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Data
public class TaskCenterPushTaskReq implements Serializable {
    private static final long serialVersionUID = 1L;

    //为空则为全量推送
    private List<OrgAndMid> list;

    private Long taskBatchId;


    @Data
    public static class OrgAndMid implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String orgId;
        private Long mid;
    }
}
