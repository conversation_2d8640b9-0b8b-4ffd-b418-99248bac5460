package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

/**
 * @Description 阵地建设状态
 * <AUTHOR>
 * @Date 2025/7/10 17:54
 */
@Getter
@AllArgsConstructor
public enum ConstructionType implements I18nDesc {
    /**
     * 创建
     */
    CREATE(0, "Create Inspection", () -> T.tr("inspection.create")),
    /**
     * 更新
     */
    UPDATE(1, "Upgrade Inspection", () -> T.tr("inspection.upgrade"));

    @EnumValue
    private final Integer code;

    private final String status;

    private final Supplier<String> i18nDesc;

}
