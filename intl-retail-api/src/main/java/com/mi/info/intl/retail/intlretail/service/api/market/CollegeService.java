package com.mi.info.intl.retail.intlretail.service.api.market;

import com.xiaomi.nrme.market.api.vo.article.PageResp;
import com.xiaomi.nrme.market.api.vo.article.intl.pojo.IntlArticleVO;
import com.xiaomi.nrme.market.api.vo.article.intl.req.ArticleSearchEsPageableReq;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlAppArticlePageableVO;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlArticleViewProgressReqVO;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlCollegeArticleReq;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlRetailCollegeLikeRequest;
import com.xiaomi.nrme.market.api.vo.article.intl.resp.IntlCollegeArticleResp;
import com.xiaomi.nrme.market.api.vo.article.intl.resp.IntlPageResp;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryAppFirstPageRespVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryAppSecondVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryPageableReqVO;

import java.io.IOException;
import java.util.List;

/**
 * - <AUTHOR>
 * - @date 2025/6/24
 * - @description:
 * -
 **/

public interface CollegeService {

    /**
     * APP - 一级分类列表
     *
     * @param pageableReqVO
     * @return
     */
    List<IntlCategoryAppFirstPageRespVO> appFirstCategoryList(IntlCategoryPageableReqVO pageableReqVO);

    /**
     * 国际版知识库APP - 二级分类列表
     */
    List<IntlCategoryAppSecondVO> intlAppSecondCategoryList(IntlCategoryPageableReqVO pageableReqVO);

    /**
     * 分页查询
     */
    IntlPageResp<IntlArticleVO> pageList(IntlAppArticlePageableVO articlePageableVO);

    /**
     * 内容 点赞和取消点赞
     *
     * @param retailCollegeLikeRequest 实体
     * @return 实体
     */
    Long toggleLike(IntlRetailCollegeLikeRequest retailCollegeLikeRequest);

    /**
     * 用户浏览文章进度上报
     *
     * @param progressReqVO
     * @return
     */
    Void syncArticleProgress(IntlArticleViewProgressReqVO progressReqVO);

    /**
     * 国际知识库根据spu名称和内容标全局搜索该国的内容列表
     *
     * @param articleSearchEsPageableReq
     * @return
     * @throws IOException
     */
    PageResp<IntlArticleVO> fullTextExplore(ArticleSearchEsPageableReq articleSearchEsPageableReq);

    /**
     * 国际知识库详情
     *
     * @param intlCollegeArticleReq
     * @return
     */
    IntlCollegeArticleResp detail(IntlCollegeArticleReq intlCollegeArticleReq);

}
