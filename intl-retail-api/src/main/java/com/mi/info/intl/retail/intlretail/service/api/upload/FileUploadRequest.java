package com.mi.info.intl.retail.intlretail.service.api.upload;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FileUploadRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("UserId")
    private String userId;

    @JsonProperty("Photos")
    private List<Photo> photos;

    @Data
    public static class Photo implements Serializable {
        private static final long serialVersionUID = 1L;

        @JsonProperty("ModuleName")
        private String moduleName;

        @JsonProperty("GUID")
        private String guid;

        @JsonProperty("fdsUrl")
        private String fdsUrl;

        @JsonProperty("IsOfflineUpload")
        private boolean offlineUpload;

        @JsonProperty("IsUploadedToBlob")
        private boolean uploadedToBlob;

        @JsonProperty("StoreCode")
        private String storeCode;

        @JsonProperty("StoreName")
        private String storeName;

        @JsonProperty("UploaderName")
        private String uploaderName;

        @JsonProperty("IsNoNeedWatermark")
        private boolean noNeedWatermark;
    }
}