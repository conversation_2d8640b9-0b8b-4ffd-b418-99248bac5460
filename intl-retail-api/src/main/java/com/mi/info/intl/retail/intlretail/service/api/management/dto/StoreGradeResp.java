package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class StoreGradeResp {
    private Integer id;
    private String channelType;
    private String method;
    private String retailerName;
    private String retailerCode;
    private Long sMinCount;
    private Long aMinCount;
    private Long bMinCount;
    private Long cMinCount;
    private Long dMinCount;
    private String rulesStatus;
    private LocalDateTime applicationTime;
    private LocalDateTime approvedTime;
    private LocalDateTime lastCalculationTime;
    private String approveStatus;
    private String countryCode;
    private String storeGrade;
} 