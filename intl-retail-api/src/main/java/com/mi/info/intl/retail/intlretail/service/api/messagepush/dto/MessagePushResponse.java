package com.mi.info.intl.retail.intlretail.service.api.messagepush.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class MessagePushResponse implements Serializable {
    public int code;
    public String message;
    public String data;


    public MessagePushResponse() {
        this.code = 200;
        this.message = "success";
        this.data = "";
    }
    public MessagePushResponse(String data) {
        this.code = 200;
        this.message = "success";
        this.data = data;
    }

    public MessagePushResponse(int code, String message, String data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
}
