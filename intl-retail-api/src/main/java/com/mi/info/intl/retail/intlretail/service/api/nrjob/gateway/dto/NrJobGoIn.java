package com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NrJobGoIn implements Serializable {
    private static final long serialVersionUID = -8496454287120974536L;
    private String taskParam;
    private String taskDesc;
    private String taskName;
    private String jobKey;
    private String owner;
    private String traceId;
}
