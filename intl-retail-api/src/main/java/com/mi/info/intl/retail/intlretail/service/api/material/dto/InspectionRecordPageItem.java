package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.seria.TimestampToAreaTimeConverter;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
public class InspectionRecordPageItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     * 数据库字段：intl_inspection_record.id
     */
    private Long id;

    /**
     * 巡检状态
     * 数据库字段：intl_inspection_record.inspection_status
     */
    private Integer inspectionStatus;

    /**
     * 创建时间（时间戳，毫秒）
     * 数据库字段：intl_inspection_record.created_time (BIGINT)
     */
    @ExcelProperty(value = "Create Time", order = 1, converter = TimestampToAreaTimeConverter.class)
    private Long createTime;

    /**
     * 阵地上传的数据（JSON数组）
     * 数据库字段：intl_inspection_record.upload_data (JSON)
     * 示例格式：["url1.jpg", "url2.jpg"]
     */
    private List<String> pictures;

    /**
     * 阵地上传原始数据
     */
    private transient String uploadData;

    private transient List<UploadMaterialData> sections;

    /**
     * 核验状态（门店状态）
     * 数据库字段：intl_inspection_record.verify_status
     * 取值说明：0-未核验, 1-已核验
     */
    @ExcelProperty(value = "Store Status", index = 9)
    private Integer storeStatus;

    /**
     * 任务状态
     * 数据库字段：intl_inspection_record.task_status
     * 取值说明：0-未完成, 1-已完成, 2-无需完成
     */
    private Integer taskStatus;

    /**
     * 核验人
     * 数据库字段：intl_inspection_record.verifier
     */
    @ExcelProperty(value = "Verifier", order = 18)
    private String verifier;

    /**
     * 阵地新建/升级时间（时间戳，毫秒）
     * 数据库字段：intl_inspection_record.business_creation_time
     */
    private Long businessCreationTime;

    /**
     * 是否符合限制范围
     * 数据库字段：intl_inspection_record.store_limited_range
     * 取值说明：0-不符合, 1-符合
     */
    private Integer storeLimitedRange;

    /**
     * 国家
     * 数据库字段：intl_inspection_rule.country
     */
    private String countryCode;

    @ExcelProperty(value = "Country", order = 4)
    private String country;

    /**
     * 系列
     * 数据库字段：intl_inspection_rule.series
     */
    @ExcelProperty(value = "Series", index = 5)
    private String series;

    /**
     * 区域
     * 数据库字段：intl_inspection_rule.region
     */
    @ExcelProperty(value = "Region", index = 12)
    private String region;

    /**
     * 区域
     * 数据库字段：intl_inspection_rule.region
     */
    private String regionCode;

    /**
     * 项目
     * 数据库字段：intl_inspection_rule.project
     */
    @ExcelProperty(value = "Program", index = 6)
    private String program;

    /**
     * 品类
     * 数据库字段：intl_inspection_rule.category
     */
    private String category;

    /**
     * 大脑任务业务场景类型
     * 数据库字段：intl_inspection_rule.task_type
     */
    private Integer taskType;

    /**
     * 阵地名称
     * 数据库字段：intl_rms_position.name
     */
    @ExcelProperty(value = "Position Name", order = 7)
    private String positionName;

    /**
     * 渠道类型
     * 数据库字段：intl_rms_position.channel_type
     * 取值说明：具体值需参考渠道类型字典
     */
    private Integer channel;

    /**
     * 门店等级标签
     * 数据库字段：intl_rms_position.level_name
     */
    private String storeGrade;

    /**
     * 门店名称
     * 数据库字段：intl_rms_position.store_name
     */
    @ExcelProperty(value = "Store Name", order = 17)
    private String storeName;

    /**
     * 阵地代码（唯一编码）
     * 数据库字段：intl_rms_position.code
     */
    @ExcelProperty(value = "Store Code", order = 16)
    private String storeCode;

    /**
     * 阵地类型
     * 数据库字段：intl_rms_position.type
     */
    private Integer positionType;


    @ExcelProperty(value = "Category", order = 10)
    private transient String categoryDesc;

    @ExcelProperty(value = "Channel", order = 13)
    private transient String channelDesc;

    @ExcelProperty(value = "Store Grade", order = 14)
    private transient String storeGradeDesc;

    @ExcelProperty(value = "Position Type", order = 15)
    private transient String positionTypeDesc;

    @ExcelProperty(value = "Task Status", order = 11)
    private transient String taskStatusDesc;

    @ExcelProperty(value = "Task Type", order = 3)
    private transient String taskTypeDesc;

    @ExcelProperty(value = "Inspection Status", order = 2)
    private transient String inspectionStatusDesc;

    @ExcelProperty(value = "Pictures", order = 8)
    private transient String picturesDesc;

    /**
     * 巡检上报拍照时的用户经纬度和门店经纬度之间的差值，单位（米）
     */
    private Double reportDistance;
}
