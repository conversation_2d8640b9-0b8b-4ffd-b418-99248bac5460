package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class BusinessDataResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("Mid")
    private String mid;
    @JsonProperty("Name")
    private String name;
    @JsonProperty("AreaId")
    private String areaId;
    @JsonProperty("PositionList")
    private List<PositionInfoDto> positionList;

}
