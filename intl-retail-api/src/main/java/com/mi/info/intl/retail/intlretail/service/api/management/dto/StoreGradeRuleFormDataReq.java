package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.FormDataEntity;
import lombok.Data;

import java.util.List;

@Data
public class StoreGradeRuleFormDataReq extends FormDataEntity {
// National Retail  Manager
@JsonProperty("input_a3d41e97b696")
private String manager;
@JsonProperty("input_011a24ce79c1")
private String storeGradeRule;
// Country
@JsonProperty("input_b101b26d6aab")
private String country;
// Channel  Type
@JsonProperty("input_c78c77c69429")
private String channelType;
// Retailer  Name
@JsonProperty("input_cc199b072070")
private String retailerName;
// Retailer  Code
@JsonProperty("input_e23a6630391b")
private String retailerCode;
// S  Store   CAPA
@JsonProperty("input_1356ea7eadad")
private String capaS;
// A  Store   CAPA
@JsonProperty("input_569b1ec293ba")
private String capaA;
// B  Store   CAPA
@JsonProperty("input_4838e284d9f8")
private String capaB;
// C  Store   CAPA
@JsonProperty("input_23630d89c936")
private String capaC;
// D  Store   CAPA
@JsonProperty("input_7bd576cae138")
private String capaD;
// Store  Amount
@JsonProperty("input_2921e231a591")
private String amount;
// Application  URL
@JsonProperty("link_71354c966202")
private String applicationURL;
// File  Link
@JsonProperty("link_9be35bc84ea4")
private String fileLink;
    
@JsonProperty("input_dc22134619ef")
private String flag;
    // 手动上传时，文件信息
@JsonProperty("upload_f848ddaf993a")
private List<UploadObject> file;
    
    @Data
    public static class UploadObject {
        // 文件名
        private String fileName;
        // 原始文件名
        private String originFileName;
        // 文件资源链接
        private String uri;
    }
}