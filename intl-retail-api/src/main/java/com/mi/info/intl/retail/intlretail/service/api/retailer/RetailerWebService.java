package com.mi.info.intl.retail.intlretail.service.api.retailer;

import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.RetailerExcelValidationResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.DtTokenRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.DtTokenResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.ExcelValidationRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerInfoResponse;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.RetailerListResponse;

import java.util.List;


public interface RetailerWebService {

    CommonResponse<RetailerExcelValidationResponse> uploadFds(ExcelValidationRequest request);

   // CommonResponse<RetailerExcelValidationResponse> uploadExcel(MultipartFile file, String region);

    CommonResponse<List<RetailerInfoResponse>> getRetailerInfo(RetailerInfoRequest request);

    CommonResponse<List<RetailerListResponse>> getRetailerList(RetailerListRequest request);

    CommonResponse<DtTokenResponse> getToken(DtTokenRequest request);

}
