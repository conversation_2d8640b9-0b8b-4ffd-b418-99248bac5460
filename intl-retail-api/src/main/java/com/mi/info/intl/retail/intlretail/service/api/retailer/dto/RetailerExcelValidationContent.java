package com.mi.info.intl.retail.intlretail.service.api.retailer.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class RetailerExcelValidationContent implements Serializable {

    private static final long serialVersionUID = 109839384037498239L;

    private String code;

    private String name;

    private Integer valid;

    private String msg;

    private Integer line;

    public RetailerExcelValidationContent() {
        this.code = "";
        this.valid = 1;
        this.msg = "";
        this.line = 0;
    }


}
