package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MaterialInspectionOperationHistoryResponse implements Serializable {
    
    private static final long serialVersionUID = 4023065530346130460L;
    
    private Long operationTime;
    
    private String operator;
    
    private String operationType;
    private String operationTypeDesc;
    
    private String disapproveReasonDesc;
    
    
    private List<UploadMaterialData> sections;
}
