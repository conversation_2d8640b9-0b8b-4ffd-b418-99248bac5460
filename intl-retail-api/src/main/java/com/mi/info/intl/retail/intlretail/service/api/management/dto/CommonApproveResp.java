package com.mi.info.intl.retail.intlretail.service.api.management.dto;

import lombok.Data;

import java.util.Date;

@Data
public class CommonApproveResp {

    private Integer id;

    private String businessKey;

    private String businessId;

    private String flowInstId;

    private String flowStatus;

    private String approveUserId;

    private String originBody;

    private String targetBody;

    private Long createdAt;

    private Long createdBy;

    private Long updatedAt;

    private String updatedBy;

    private Date applicationTime;

    private Date approvedTime;
}
