package com.mi.info.intl.retail.intlretail.service.api.newproducttarget;

import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.*;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: chuang
 * @since: 2025/8/1
 */
 public interface NewProductTargetService {
       /**
         * 查询新品物料巡检列表
         *
         * @param req
         * @return
         */
    public PageResponse<NewProductTargetItem> listNewProductTarget(NewProductTargetReq req);
    public  CommonApiResponse<PageResponse<NewProductTargetItem>> listNewProductTargetGateWay(NewProductTargetReq req);

    CommonResponse<String> listNewProductTargetExport(NewProductTargetReq query);
    /**
    *新增新品目标物料列表
    */
    public CommonApiResponse<List<String>>  addNewProductTarget(@Valid NewProductTargetAddReq req);
    /**
    *更新新品目标物料列表
    */
    public CommonApiResponse<String> updateNewProductTarget(@Valid NewProductTargetUpdateReq req);

    public CommonApiResponse<List<NewProdcutTargetMetaResp>> listMeta(@Valid NewProdcutTargetMetaReq req);
}
