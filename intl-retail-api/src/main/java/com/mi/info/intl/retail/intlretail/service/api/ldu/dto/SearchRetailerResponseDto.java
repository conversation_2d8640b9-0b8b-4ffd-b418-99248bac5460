package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
@Data
public class SearchRetailerResponseDto implements Serializable {
    private static final long serialVersionUID = 973434739743971L;

    /**
     * 零售商编码
     */
    @ApiDocClassDefine(value = "零售商编号", required = true)
    @JsonProperty("code")
    private String code;

    /**
     * 零售商名称
     */
    @ApiDocClassDefine(value = "零售商名称", required = true)
    @JsonProperty("name")
    private String name;

    /**
     * 国家编码
     */
    @ApiDocClassDefine(value = "国家编码", required = true)
    @JsonProperty("countryCode")
    private String countryCode;
}