package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.ConstructionType;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.StoreLimitedRangeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.seria.I18nDescToIntSerializer;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.seria.TimestampToAreaTimeConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 阵地巡检项DTO
 */
@Slf4j
@Data
@ExcelIgnoreUnannotated
@JsonIgnoreProperties(ignoreUnknown = true)
public class PositionInspectionItem implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 巡检记录ID
     */
    @ExcelProperty("Inspection Record ID")
    private Long id;
    
    /**
     * 门店名称
     */
    @ExcelProperty("Store Name")
    private String storeName;
    
    /**
     * 阵地名称
     */
    @ExcelProperty("Position Name")
    private String positionName;
    
    /**
     * 状态码
     */
    @JsonSerialize(using = I18nDescToIntSerializer.class)
    private InspectionStatusEnum inspectionStatus;

    /**
     * 状态描述
     */
    @ExcelProperty("Inspection Status")
    private String inspectionStatusDesc;

    /**
     * 区域
     */
    @ExcelProperty("Region")
    private String region;
    
    /**
     * 国家
     */
    @ExcelProperty("Country")
    private String country;
    
    /**
     * 阵地代码
     */
    @ExcelProperty("Position Code")
    private String positionCode;
    
    /**
     * 阵地类型
     */
    private String positionType;

    /**
     * 阵地类型描述
     */
    @ExcelProperty("Position Type")
    private String positionTypeDesc;
    
    /**
     * 阵地建设类型
     */
    @JsonSerialize(using = I18nDescToIntSerializer.class)
    private ConstructionType positionConstructionType;


    /**
     * 可以审核 1为显示 0隐藏
     */
    private Integer canVerfiy;
    
    /**
     * 阵地品类
     */
    @ExcelProperty("Position Category")
    private String positionCategory;
    
    /**
     * 是否阵地范围内
     */
    @JsonSerialize(using = I18nDescToIntSerializer.class)
    private StoreLimitedRangeEnum storeLimitedRange;

    /**
     * 阵地范围内描述
     */
    @ExcelProperty("Within Position Range")
    private String storeLimitedRangeDesc;
    
    /**
     * 阵地落位
     */
    @ExcelProperty("Position Location")
    private String positonLocation;
    
    /**
     * 阵地展陈标准化打标信息
     */
    @ExcelProperty("Position Display Standardization Info")
    private String displayStandardization;
    
    /**
     * 阵地新建/升级时间
     */
    @ExcelProperty(value = "Position Creation/Upgrade Time", converter = TimestampToAreaTimeConverter.class)
    private Long creationTime;
    
    /**
     * 巡检负责人
     */
    @ExcelProperty("Inspection Owner")
    private String owner;
    
    /**
     * 任务状态
     */
    @JsonSerialize(using = I18nDescToIntSerializer.class)
    private TaskStatusEnum taskStatus;

    /**
     * 任务状态描述
     */
    @ExcelProperty("Task Status")
    private String taskStatusDesc;
    
    /**
     * 巡检状态
     */
    private Integer verificationStatus;
    
    /**
     * 核验状态描述
     */
    @ExcelProperty("Verification Status")
    private String verificationStatusDesc;
    
    /**
     * 阵地建设类型描述
     */
    @ExcelProperty("Position Construction Type")
    private String positionConstructionTypeDesc;
    
    /**
     * 核验人
     */
    @ExcelProperty("Verifier")
    private String verifier;
    
    /**
     * 任务完成时间
     */
    @ExcelProperty(value = "Task Completion Time", converter = TimestampToAreaTimeConverter.class)
    private Long taskCompletionTime;
    
    /**
     * 核验时间
     */
    @ExcelProperty(value = "Verification Time", converter = TimestampToAreaTimeConverter.class)
    private Long verificationTime;
    
    /**
     * 拍照是否允许相册选择：1-是，0-否
     */
    private Integer allowPhotoFromGallery;
    
    /**
     * 阵地经度
     */
    private Double positionLongitude;

    /**
     * 阵地纬度
     */
    private Double positionLatitude;
    
    /**
     * 距离（单位：米，仅用于排序，不参与序列化）
     */
    private transient Double distance;

    public Double getDistance() {
        return distance;
    }
    public void setDistance(Double distance) {
        this.distance = distance;
    }
    
    /**
     * 获取是否可以审核
     * 当巡检状态为待核验(TO_BE_VERIFIED, 值为3)时返回1，否则返回0
     * @return 1表示可以审核，0表示不可以审核
     */
    public Integer getCanVerfiy() {
        if (this.inspectionStatus != null && this.inspectionStatus.equals(InspectionStatusEnum.TO_BE_VERIFIED)) {
            return 1;
        }
        return 0;
    }
    
    /**
     * 获取阵地范围内描述
     * 当storeLimitedRange为1时返回"Yes"，为0时返回"No"，为null时返回"-"
     * @return 阵地范围内描述
     */
    @JsonProperty("storeLimitedRangeDesc")
    public String getStoreLimitedRangeDesc() {
        return storeLimitedRange == null ? "-" : storeLimitedRange.getDesc();
    }

    /**
     * 获取任务状态描述
     * 根据taskStatus获取对应的TaskStatusEnum的status值
     * @return 任务状态描述
     */
    @JsonProperty("taskStatusDesc")
    public String getTaskStatusDesc() {
        return taskStatus == null ? "-" : taskStatus.getDesc();
    }
    
    /**
     * 获取核验状态描述
     * 根据verificationStatus获取对应的InspectionStatusEnum的status值
     * @return 核验状态描述
     */
    @JsonProperty("verificationStatusDesc")
    public String getVerificationStatusDesc() {
        InspectionStatusEnum inspectionStatusEnum = InspectionStatusEnum.getByCode(this.verificationStatus);
        return inspectionStatusEnum == null ? "-" : inspectionStatusEnum.getDesc();
    }
    
    /**
     * 获取阵地建设类型描述
     * 根据positionConstructionType获取对应的ConstructionType的description值
     * @return 阵地建设类型描述
     */
    @JsonProperty("positionConstructionTypeDesc")
    public String getPositionConstructionTypeDesc() {
        return positionConstructionType == null ? "-" : positionConstructionType.getDesc();
    }

    public String getInspectionStatusDesc() {
        return inspectionStatus == null ? "-" : inspectionStatus.getDesc();
    }
}