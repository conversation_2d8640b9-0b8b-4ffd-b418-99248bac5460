package com.mi.info.intl.retail.intlretail.service.api.so.upload.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * IMEI数据导入响应参数
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Data
public class ImeiImportResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入记录Id
     */
    private Long importLogId;

    /**
     * 导入状态：0-处理中、1-成功、2-失败
     */
    private Integer status;

    /**
     * 结果文件Id
     */
    private Long resultFileId;

    /**
     * 结果文件链接
     */
    private String resultFileUrl;

    /**
     * 失败原因
     */
    private String errorMsg;
}
