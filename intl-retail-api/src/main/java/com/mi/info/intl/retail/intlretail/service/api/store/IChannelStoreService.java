package com.mi.info.intl.retail.intlretail.service.api.store;

import com.mi.info.intl.retail.intlretail.service.api.store.dto.UniversalProxyRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.UniversalProxyResponse;

public interface IChannelStoreService {
    UniversalProxyResponse universalProxyApi(UniversalProxyRequest requestBody);

    UniversalProxyResponse universalProxyApiByJwtToken(UniversalProxyRequest requestBody);
}
