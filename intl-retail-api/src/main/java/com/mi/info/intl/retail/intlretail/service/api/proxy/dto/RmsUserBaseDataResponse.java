package com.mi.info.intl.retail.intlretail.service.api.proxy.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RmsUserBaseDataResponse implements Serializable {
    @JsonProperty("UserId")
    private String userId;
    @JsonProperty("JobValue")
    private Long jobValue;
    @JsonProperty("JobTitle")
    private String jobTitle;
    @JsonProperty("EmployeeCode")
    private String employeeCode;
    @JsonProperty("EnglishName")
    private String englishName;
    @JsonProperty("UserAccount")
    private String userAccount;
    @JsonProperty("IsAgreedPrivacy")
    private Boolean isAgreedPrivacy;
    @JsonProperty("IsAgreedNotification")
    private Boolean isAgreedNotification;
    @JsonProperty("UserCountryId")
    private String userCountryId;
    @JsonProperty("UserCountryCode")
    private String userCountryCode;
    @JsonProperty("UserCountryShortCode")
    private String userCountryShortCode;
    @JsonProperty("CurrencyCode")
    private String currencyCode;
    @JsonProperty("PrivacyId")
    private String privacyId;
    @JsonProperty("EnvironmentFlag")
    private String environmentFlag;
    @JsonProperty("IsOfflineIMEI")
    private Boolean isOfflineIMEI;
    @JsonProperty("IsOffline")
    private Boolean isOffline;
    @JsonProperty("UserLanguageId")
    private String userLanguageId;
    @JsonProperty("UserLanguageCode")
    private String userLanguageCode;
    @JsonProperty("StoreRetailerId")
    private String storeRetailerId;
    @JsonProperty("DailyHour")
    private Integer dailyHour;
    @JsonProperty("StoreId")
    private String storeId;
    @JsonProperty("StoreName")
    private String storeName;
    @JsonProperty("StoreCode")
    private String storeCode;
    @JsonProperty("Longitude")
    private String longitude;
    @JsonProperty("Latitude")
    private String latitude;
    @JsonProperty("StoreCount")
    private Integer storeCount;
    @JsonProperty("StoreAddress")
    private String storeAddress;
    @JsonProperty("StoreGrade")
    private Long storeGrade;
    @JsonProperty("StoreType")
    private Integer storeType;
    @JsonProperty("ChannelType")
    private Long channelType;
    @JsonProperty("IsPromotionStore")
    private Boolean isPromotionStore;
    @JsonProperty("StoreAccountId")
    private String storeAccountId;
    @JsonProperty("StoreCountryId")
    private String storeCountryId;
    @JsonProperty("IsBrand")
    private Boolean isBrand;
    @JsonProperty("SignRuleId")
    private String signRuleId;
    @JsonProperty("IsNormal")
    private Boolean isNormal;
    @JsonProperty("NormalPhotoRequired")
    private Boolean normalPhotoRequired;
    @JsonProperty("NormalNoteRequired")
    private Boolean normalNoteRequired;
    @JsonProperty("Effectiveduration")
    private Double effectiveDuration;
    @JsonProperty("Effectiverange")
    private Double effectiveRange;
    @JsonProperty("IsAbnormalPhoto")
    private Boolean isAbnormalPhoto;
    @JsonProperty("AbNormalPhotoRequired")
    private Boolean abNormalPhotoRequired;
    @JsonProperty("IsAbnormalNote")
    private Boolean isAbnormalNote;
    @JsonProperty("AbNormalNoteRequired")
    private Boolean abNormalNoteRequired;
    @JsonProperty("SignRecordId")
    private String signRecordId;
    @JsonProperty("SigninTime")
    private String signinTime;
    @JsonProperty("SignoutTime")
    private String signoutTime;
    @JsonProperty("MiIDVirtual")
    private String miIdVirtual;
    @JsonProperty("MiTalk")
    private String miTalk;
    @JsonProperty("ParentStoreCode")
    private String parentStoreCode;
    @JsonProperty("ParentStoreCrssCode")
    private String parentStoreCrssCode;

    @JsonProperty("Menu")
    private String menuJson;

    private List<MenuInfo> menu;

    @Data
    public static class MenuInfo implements Serializable {
        @JsonProperty("MenuStoreJobVisible")
        private Boolean menuStoreJobVisible;
        @JsonProperty("MenuStoreJobFinish")
        private Boolean menuStoreJobFinish;
        @JsonProperty("ItemRequired")
        private Boolean itemRequired;
        @JsonProperty("ScreenName")
        private String screenName;
        @JsonProperty("Label")
        private String label;
        @JsonProperty("traslateLabel")
        private String traslateLabel;
        @JsonProperty("statusCount")
        private int statusCount;
        @JsonProperty("Type")
        private long type;
        @JsonProperty("Executor")
        private long executor;
        @JsonProperty("InStoreJob")
        private Boolean inStoreJob;
    }
}
