package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UniversalProxyRequest implements Serializable {

    public String areaId;

    public String path;

    public String type;

    @JsonProperty("Input")
    public String input;

    public String token;
}
