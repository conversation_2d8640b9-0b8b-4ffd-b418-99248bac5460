package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
public enum CycleTypeEnum {
    NONE(0, "无"),
    DAILY(1, "日"),
    WEEKLY(2, "周"),
    MONTHLY(3, "月"),
    QUARTERLY(4, "季"),
    CUSTOM(5, "自定义天数");
    private final Integer code;
    private final String name;

    CycleTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<Map<String, Object>> getNode() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (CycleTypeEnum value : CycleTypeEnum.values()) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("code", value.getCode());
            map.put("name", value.getName());
            list.add(map);
        }
        return list;
    }

    public static String getNameByCode(Integer code) {
        CycleTypeEnum cycleTypeEnum = valueOfCode(code);
        if (Objects.isNull(cycleTypeEnum)) {
            return null;
        }
        return cycleTypeEnum.name;
    }

    public static CycleTypeEnum valueOfCode(Integer code) {
        for (CycleTypeEnum value : CycleTypeEnum.values()) {
            if (value.getCode().compareTo(code) == 0) {
                return value;
            }
        }
        return null;
    }
}
