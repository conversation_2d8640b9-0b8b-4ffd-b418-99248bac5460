package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.BusinessTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.seria.I18nDescToIntSerializer;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.seria.TimestampToAreaTimeConverter;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 新品物料巡检
 *
 * <AUTHOR>
 * @date 2025年07月31日
 */
@Slf4j
@Data
@ExcelIgnoreUnannotated
@JsonIgnoreProperties(ignoreUnknown = true)
public class MaterialInspectionItem implements Serializable {
    
    private static final long serialVersionUID = 474827031816523628L;
    
    /**
     * 巡检记录ID
     */
    @ApiDocClassDefine(value = "Inspection Record ID")
    private Long materialInspectionId;
    
    /**
     * 门店名称
     */
    @ApiDocClassDefine(value = "Store Name")
    private String storeName;
    
    /**
     * 阵地名称
     */
    @ApiDocClassDefine(value = "Position Name")
    private String positionName;
    
    /**
     * 状态码
     */
    @JsonSerialize(using = I18nDescToIntSerializer.class)
    private InspectionStatusEnum inspectionStatus;
    
    /**
     * 状态描述
     */
    @ApiDocClassDefine(value = "Inspection Status")
    private String inspectionStatusDesc;
    
    /**
     * 阵地代码
     */
    @ApiDocClassDefine(value = "Position Code")
    private String positionCode;
    
    /**
     * 拍照是否允许相册选择：1-是，0-否
     */
    @ApiDocClassDefine(value = "allow Photo From Gallery")
    private Integer allowPhotoFromGallery;
    
    /**
     * 任务截止时间
     */
    @ExcelProperty(value = "Task dead line", converter = TimestampToAreaTimeConverter.class)
    private Long deadline;
    
    private String taskStatusDesc;
    
    /**
     *
     */
    @ApiDocClassDefine(value = " 301： front_inspection，401：Dummy，501：POSM，601：Price Tag，701：LDU")
    private TaskTypeEnum taskType;
    
    /**
     * 301： front_inspection，401：Dummy，501：POSM，601：Price Tag，701：LDU
     */
    @ApiDocClassDefine(value = "TaskTypeDesc")
    private String taskTypeDesc;
    
    /**
     * 任务名称
     */
     private  String taskName;
    /**
     * 业务类型
     */
    private BusinessTypeEnum businessType;
    
    /**
     * 任务状态
     */
    @JsonSerialize(using = I18nDescToIntSerializer.class)
    private TaskStatusEnum taskStatus;
    
    @JsonProperty("taskStatusDesc")
    public String getTaskStatusDesc() {
        return taskStatus == null ? "-" : taskStatus.getDesc();
    }
    
    @JsonProperty("inspectionStatusDesc")
    public String getInspectionStatusDesc() {
        return inspectionStatus == null ? null : inspectionStatus.getDesc();
    }
    
    
    @JsonProperty("taskTypeDesc")
    public String getTaskTypeDesc() {
        return taskType == null ? null : taskType.getDesc();
    }
    @JsonProperty("taskName")
    public String getTaskName() {
        return businessType == null ? null : businessType.getDesc();
    }
    
    //区域、国家、系列（机型）
    @ApiDocClassDefine(value = "region")
    private String region;
    
    @ApiDocClassDefine(value = "Country")
    private String country;
    
    @ApiDocClassDefine(value = "Series")
    private String series;
    //covered 0 未覆盖(未收到物料) 1 已覆盖(已收到物料)'
    @ApiDocClassDefine(value = "covered")
    private Integer covered = 0;
}

