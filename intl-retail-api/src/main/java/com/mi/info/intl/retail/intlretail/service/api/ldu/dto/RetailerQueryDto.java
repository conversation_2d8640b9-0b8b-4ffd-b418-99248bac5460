package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 零售商列表查询参数
 *
 * <AUTHOR>
 * @date 2025/7/8 17:48
 */
@Data
public class RetailerQueryDto implements Serializable {

    private static final long serialVersionUID = 973434739743971L;

    /**
     * 区域/国家编码
     */
    @ApiDocClassDefine(value = "区域/国家编码", required = true)
    @JsonProperty("region")
    private String region;

    /**
     * 零售商编码列表
     */
    @ApiDocClassDefine(value = "零售商编码列表")
    @JsonProperty("codes")
    private List<String> codes;

    /**
     * 按名称或编码模糊搜索
     */
    @ApiDocClassDefine(value = "按名称或编码模糊搜索")
    @JsonProperty("keyword")
    private String keyword;

}
