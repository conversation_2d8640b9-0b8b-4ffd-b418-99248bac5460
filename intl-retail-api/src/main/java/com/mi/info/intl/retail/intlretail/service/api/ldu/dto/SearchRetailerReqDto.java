package com.mi.info.intl.retail.intlretail.service.api.ldu.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SearchRetailerReqDto implements Serializable {
    private static final long serialVersionUID = 973434739743971L;

    /**
     * 零售商名称或编号，模糊搜索
     */
    @ApiDocClassDefine(value = "零售商名称或编号", required = true)
    @JsonProperty("keyword")
    private String keyword;

    /**
     * 国家编码，非必填
     */
    @ApiDocClassDefine(value = "国家编码")
    @JsonProperty("countryCode")
    private List<String> countryCode;

    /**
     * 零售商id，非必填
     */
    @ApiDocClassDefine(value = "零售商id")
    @JsonProperty("id")
    private String id;
}
