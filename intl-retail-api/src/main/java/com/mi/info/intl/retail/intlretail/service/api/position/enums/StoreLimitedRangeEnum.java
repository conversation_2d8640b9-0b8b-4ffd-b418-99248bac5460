package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

@Getter
@AllArgsConstructor
public enum StoreLimitedRangeEnum implements I18nDesc {

    NO(0, () -> T.tr("inspection.no")),
    YES(1, () -> T.tr("inspection.yes"));

    @EnumValue
    private final Integer code;

    private final Supplier<String> i18nDesc;
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static StoreLimitedRangeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StoreLimitedRangeEnum value : StoreLimitedRangeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
