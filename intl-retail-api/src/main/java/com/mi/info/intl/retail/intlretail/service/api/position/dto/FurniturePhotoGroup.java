package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 家具照片组，继承自PhotoGroup，添加了reason和remark字段
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FurniturePhotoGroup extends PhotoGroup implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 异常原因
     */
    private Integer reason;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 无参构造函数
     */
    public FurniturePhotoGroup() {
        super();
    }
    
    /**
     * 带参构造函数
     * 
     * @param name 名称
     * @param guid 唯一标识
     * @param images 图片列表
     * @param reason 驳回原因
     * @param remark 备注
     */
    public FurniturePhotoGroup(String name, String guid, List<String> images, Integer reason, String remark) {
        super(name, guid, images);
        this.reason = reason;
        this.remark = remark;
    }
    
    /**
     * 带参构造函数（不包含reason和remark）
     * 
     * @param name 名称
     * @param guid 唯一标识
     * @param images 图片列表
     */
    public FurniturePhotoGroup(String name, String guid, List<String> images) {
        super(name, guid, images);
    }
} 