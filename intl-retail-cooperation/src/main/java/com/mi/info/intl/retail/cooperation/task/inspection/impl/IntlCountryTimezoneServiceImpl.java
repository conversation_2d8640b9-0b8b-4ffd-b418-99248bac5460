package com.mi.info.intl.retail.cooperation.task.inspection.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.CountryTimezoneReadMapper;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlCountryTimezoneService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/16
 **/
@Service
public class IntlCountryTimezoneServiceImpl extends
        ServiceImpl<CountryTimezoneReadMapper, IntlRmsCountryTimezone>
        implements IntlCountryTimezoneService {
}
