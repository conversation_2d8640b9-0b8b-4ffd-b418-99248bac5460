<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.cooperation.task.infra.mapper.read.RmsUserReadMapper">
    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsUser">
        <id property="id" column="id"/>
        <result property="rmsUserid" column="rms_userid"/>
        <result property="code" column="code"/>
        <result property="domainName" column="domain_name"/>
        <result property="englishName" column="english_name"/>
        <result property="countryId" column="country_id"/>
        <result property="countryName" column="country_name"/>
        <result property="jobId" column="job_id"/>
        <result property="jobName" column="job_name"/>
        <result property="email" column="email"/>
        <result property="mobile" column="mobile"/>
        <result property="miId" column="mi_id"/>
        <result property="managerId" column="manager_id"/>
        <result property="managerName" column="manager_name"/>
        <result property="virtualMiId" column="virtual_mi_id"/>
        <result property="languageId" column="language_id"/>
        <result property="languageName" column="language_name"/>
        <result property="isDisabled" column="is_disabled"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,rms_userid,code,domain_name,english_name,country_id,country_name,job_id,job_name,email,mobile,
          mi_id,manager_id,manager_name,virtual_mi_id,language_id,language_name,
          is_disabled,created_at,updated_at
    </sql>
    <select id="selectByDomainName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM intl_rms_user
        WHERE domain_name = #{domainName}
    </select>
</mapper>