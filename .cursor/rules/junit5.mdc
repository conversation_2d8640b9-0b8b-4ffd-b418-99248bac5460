---
description: 
globs: 
alwaysApply: false
---
# JUnit 5测试规则 (junit5rules)

## 1. 规则定义
- **规则名称**: JUnit 5测试代码生成规则
- **版本**: 4.0
- **创建日期**: 2025-03-14
- **适用语言**: Java

## 2. 目标与范围
- **主要目标**: 生成高质量、全面且可直接运行的Java单元测试代码，并确保测试环境配置正确
- **适用场景**: 
  - Java服务类方法的单元测试
  - Spring Boot 2.x/3.x组件测试
  - 依赖注入场景下的功能测试
  - 接口和实现类的模拟测试

## 3. 环境检测流程
- **自动检测**: 在生成单元测试前，自动检测项目环境配置
  - 检查POM文件中的依赖配置
  - 验证JUnit 5、Mockito和SpringBoot版本兼容性
  - 提供环境修复建议

- **依赖版本兼容性表**:
  | SpringBoot版本 | 推荐JUnit版本 | 推荐Mockito版本 | 说明 |
  |--------------|-------------|---------------|-----|
  | 3.x          | 5.10.x      | 5.7.x         | SpringBoot 3.x默认集成JUnit 5 |
  | 2.7.x+       | 5.9.x       | 5.3.x         | SpringBoot 2.7+推荐JUnit 5 |
  | 2.4.x-2.6.x  | 5.8.x       | 4.5.x         | SpringBoot 2.4+支持JUnit 5 |
  | 无           | 5.10.x      | 5.7.x         | 普通Java项目推荐配置 |

- **环境检测关键点**:
  - JUnit 5与Mockito版本兼容性
  - JUnit Platform支持
  - 是否存在版本冲突
  - 是否缺少必要依赖

## 4. 代码规范与约束
- **测试框架要求**:
  - 使用JUnit 5和Mockito进行测试
  - 测试类使用`@ExtendWith(MockitoExtension.class)`注解
  - 使用`@InjectMocks`和`@Mock`自动注入需要模拟的对象
  
- **命名约定**:
  - 测试方法名应遵循`testedMethod_Scenario_ExpectedBehavior`格式
  - 测试类名应为`被测类名Test`
  
- **测试方法结构**:
  - 每个测试方法分为准备数据(Arrange)、执行测试(Act)、验证结果(Assert)三部分
  - 对于复杂方法应提供多种情况的测试用例
  - 使用`@Test`注解，方法可以是`void`或返回类型
  
- **禁止项**:
  - 禁止对static、private、protected修饰的方法进行mock
  - 禁止对static类进行mock
  - 避免使用`eq()`进行参数匹配，应直接判断参数值

## 5. 静态类mock
静态类mock可以使用Mockito.mockStatic方法（需要Mockito 3.4.0及以上版本）
```java
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.DisplayName;

class StaticClass {
    public static String staticMethod() {
        return "real result";
    }
}

@ExtendWith(MockitoExtension.class)
class StaticMethodVerificationTest {
    @Test
    void testStaticMethodVerification() {
        // 创建一个 MockedStatic 实例
        // 注意: MockedStatic 有严格的线程和作用域限制，一个类在同一时间只能有一个活动的mock
        try (MockedStatic<StaticClass> mockedStatic = mockStatic(StaticClass.class)) {
            // 模拟静态方法的返回值
            mockedStatic.when(StaticClass::staticMethod).thenReturn("mocked result");

            // 调用静态方法
            String result = StaticClass.staticMethod();

            // 验证静态方法的调用
            mockedStatic.verify(StaticClass::staticMethod);

            // 验证返回值是否为模拟值
            assertEquals("mocked result", result);
        }
    }
}
```

## 6. 代码模板与示例
### 6.1 基础测试模板
```java
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.DisplayName;

@ExtendWith(MockitoExtension.class)
class ServiceClassTest {

    @InjectMocks
    private ServiceClass serviceClass;

    @Mock
    private DependencyClass dependencyClass;
    
    @Test
    @DisplayName("保存阵地信息 - 成功")
    void save_Success() {
        // 准备数据
        when(positionMapper.insert(any(IntlRmsPosition.class))).thenReturn(1);

        // 执行测试
        boolean result = positionRepository.save(testPositionDomain);

        // 验证结果
        assertTrue(result);
        verify(positionMapper).insert(any(IntlRmsPosition.class));
    }
}
```

### 6.2 异常测试示例
```java
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.DisplayName;

@ExtendWith(MockitoExtension.class)
class ControllerTest {

    @InjectMocks
    private RuDashboardController ruDashboardController;

    @Mock
    private RuDashboardService ruDashboardService;
    
    @Test
    void listForWeb_ThrowsException() throws Exception {
        //准备数据
        RuDashboardExportReq req = new RuDashboardExportReq();
        String errorMessage = "Export error";
        
        //mock
        when(ruDashboardService.getRuDashboardExport(argThat(request -> request != null)))
                .thenThrow(new RuntimeException(errorMessage));
        
        //执行测试并验证异常
        SmcRuntimeException exception = assertThrows(SmcRuntimeException.class,
                () -> ruDashboardController.export(req));
        
        //验证异常细节
        assertEquals(errorMessage, exception.getMessage());
    }
}
```

### 6.3 生命周期钩子示例
```java
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ServiceTest {

    @InjectMocks
    private ServiceClass serviceClass;

    @Mock
    private DependencyClass dependencyClass;
    
    @BeforeEach
    void setUp() {
        // 初始化测试数据
        // 设置通用的mock行为
    }
    
    @AfterEach
    void tearDown() {
        // 清理测试资源
        // 重置mock状态
    }
    
    @Test
    void testMethod() {
        // 测试逻辑
    }
}
```

### 6.4 POM依赖示例
```xml
<!-- JUnit 5 Platform -->
<dependency>
    <groupId>org.junit.platform</groupId>
    <artifactId>junit-platform-engine</artifactId>
    <version>1.10.1</version>
    <scope>test</scope>
</dependency>

<!-- JUnit 5 Jupiter -->
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <version>5.10.1</version>
    <scope>test</scope>
</dependency>

<!-- Mockito for JUnit 5 -->
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-junit-jupiter</artifactId>
    <version>5.7.0</version>
    <scope>test</scope>
</dependency>

<!-- Spring Boot Test (可选) -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

## 7. 最佳实践
- 测试应覆盖方法的所有可能执行路径
- 每个测试方法专注于测试一个特定场景
- 使用`@BeforeEach`初始化公共测试数据
- 使用`@AfterEach`清理测试资源
- 使用有意义的测试数据而非随机值
- 为边界情况和异常路径编写专门的测试方法
- 测试应该是可重复执行且独立的
- 使用`@DisplayName`为测试提供可读性更好的描述
- 使用`@Nested`组织相关的测试方法

## 8. 环境修复指南
### 8.1 常见问题与解决方案
- **No tests found matching Method...**
    - 检查测试方法是否使用了正确的`@Test`注解（来自`org.junit.jupiter.api.Test`）
    - 确认测试方法是`public`或`package-private`
  
- **UnnecessaryStubbingException**
    - 使用`@ExtendWith(MockitoExtension.class)`
    - 或在`@BeforeEach`中添加`Mockito.validateMockitoUsage()`

- **JUnit 5兼容性问题**
    - 确保使用`mockito-junit-jupiter`而不是`mockito-core`
    - 检查Spring Boot版本是否支持JUnit 5

### 8.2 POM修复自动化
根据检测到的项目环境，系统将提供POM修复建议，包括：
- JUnit 5 Platform和Jupiter依赖配置
- 兼容的Mockito版本
- 必要的支持库（Hamcrest、ByteBuddy）
- Spring Boot Test配置

## 9. 规则使用指南
要在Cursor中使用这个规则生成JUnit 5测试，请按照以下步骤操作：

1. **引用规则并环境检测**:
   ```
   @junit5rules.mdc 请检测项目环境并为[类名]中的[方法名]生成单元测试
   ```

2. **提供上下文**:
   - 被测试类的完整代码
   - 相关依赖类的接口或关键方法
   - 项目pom.xml文件的路径
   - 特定的测试需求或关注点

3. **指定具体要求**:
   - 可以指定特定的测试场景
   - 可以要求关注特定的边界条件或异常情况
   - 可以指定是否自动修复环境问题

## 10. 环境检测与修复流程
1. **检测阶段**:
   - 分析pom.xml文件中的依赖
   - 检查JUnit 5版本
   - 检查Mockito版本
   - 检查Spring Boot版本(如适用)
   - 验证版本兼容性

2. **诊断阶段**:
   - 识别缺失的必要依赖
   - 检测版本冲突
   - 验证测试运行所需的插件配置
   - 生成环境问题报告

3. **修复阶段**:
   - 提供自动修复建议
   - 生成补充依赖XML片段
   - 提供版本升级/降级建议
   - 生成完整的环境修复方案

4. **测试代码生成阶段**:
   - 输出测试的业务场景
   - 基于修复后的环境生成兼容的测试代码
   - 确保生成的代码与项目环境匹配
   - 提供额外的环境特定配置

## 11. JUnit 5新特性支持
- **参数化测试**: 使用`@ParameterizedTest`和`@ValueSource`
- **嵌套测试**: 使用`@Nested`组织测试结构
- **测试标签**: 使用`@Tag`标记测试
- **条件测试**: 使用`@EnabledOnOs`、`@DisabledIf`等
- **重复测试**: 使用`@RepeatedTest`
- **超时测试**: 使用`@Timeout`
- **测试顺序**: 使用`@TestMethodOrder` 