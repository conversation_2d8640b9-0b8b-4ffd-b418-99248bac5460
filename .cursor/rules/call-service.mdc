---
description: 
globs: 
alwaysApply: false
---
# 项目结构
intl-retail
│
├── pom.xml (父POM文件)
│
├── intl-retail-api (对外API定义模块，不同领域对应的是不同的包名)
│   ├── src/main/java/com/mi/info/intl/retail/intlretail/service/api 文件路径：intl-retail-api/src/main/java/com/mi/info/intl/retail/intlretail/service/api
│   │   ├── retailer    (零售商相关API)
│   │   ├── inspection  (检查相关API)
│   │   ├── result      (结果相关API)
│   │   ├── request     (请求相关API)
│   │   ├── proxy       (代理相关API)
│   │   ├── mq          (消息队列相关API)
│   │   ├── job         (任务相关API)
│   │   ├── market      (市场相关API)
│   │   ├── messagepush (消息推送相关API)
│   │   ├── copilot     (AI助手相关API)
│   │   ├── fds         (文件存储相关API)
│   │   ├── store       (商店相关API)
│   │   └── demo        (示例API)
│
├── intl-retail-common (通用工具类模块)
│   ├── src/main/java/com/mi/info/intl/retail
│   │   ├── advice      (通知/增强相关)
│   │   │   └── excel   (Excel处理相关)
│   │   ├── constant    (常量定义)
│   │   ├── enums       (枚举定义)
│   │   ├── model       (模型定义)
│   │   └── utils       (工具类)
│
├── intl-retail-core (核心配置模块)
│   ├── src/main/java/com/mi/info/intl/retail/core
│   │   ├── config      (配置相关)
│   │   ├── exception   (异常处理)
│   │   └── utils       (核心工具类)
│
├── intl-retail-cooperation (协同模块)
│   ├── src/main/java/com/mi/info/intl/retail/cooperation
│   │   └── task        (任务相关)
│   │       ├── app     (应用层)
│   │       ├── component (组件)
│   │       ├── config    (配置)
│   │       ├── domain    (领域层)
│   │       ├── dto       (数据传输对象)
│   │       ├── infra     (基础设施层)
│   │       └── inspection (检查相关)
│
├── intl-retail-fieldforce (人力领域模块 领域子模块)
│   ├── src/main/java/com/mi/info/intl/retail
│   │   ├── attendance  (考勤相关)
│   │   └── user        (用户相关)
│   │       ├── app     (应用层)
│   │       ├── constant (常量)
│   │       ├── domain   (领域层)
│   │       └── infra    (基础设施层)
│
├── intl-retail-front (阵地领域模块 领域子模块)
│   ├── src/main/java/com/mi/info/intl/retail/org
│   │   ├── app         (应用层 主要负责服务编排 是api的实现)
│   │   ├── domain      (领域层 主要包含domain层和domainService层 包含最主要的业务逻辑 repository的接口)
│   │   └── infra       (基础设施层 repository的实现)
                entity 对应的数据库的表
                mapper 对应的mapper接口 实现xml在resources/mapper中
                repositoy repository的实现类
│
├── intl-retail-sales (销售/库存领域模块)
│
├── intl-retail-proxy (代理模块)
│   ├── intl-retail-app (应用层模块)
│   │   ├── src/main/java/com/mi/info/intl/retail/intlretail
│   │   │   ├── domain  (领域相关)
│   │   │   ├── service (服务相关)
│   │   │   │   └── app (应用层服务)
│   │   │   │       ├── copilot    (AI助手)
│   │   │   │       ├── demo       (示例)
│   │   │   │       ├── fds        (文件存储)
│   │   │   │       ├── inspection (检查)
│   │   │   │       ├── jobhandler (任务处理)
│   │   │   │       ├── market     (市场)
│   │   │   │       ├── messagepush (消息推送)
│   │   │   │       ├── proxy      (代理)
│   │   │   │       ├── retailer   (零售商)
│   │   │   │       ├── rpc        (远程调用)
│   │   │   │       └── store      (商店)
│   │   │   └── utils  (工具类)
│   │
│   ├── intl-retail-domain (领域层模块)
│   │   ├── src/main/java/com/mi/info/intl/retail/intlretail/domain
│   │   │   ├── common   (通用领域)
│   │   │   ├── http     (HTTP相关)
│   │   │   ├── order    (订单领域)
│   │   │   ├── product  (产品领域)
│   │   │   ├── push     (推送领域)
│   │   │   └── userdevice (用户设备领域)
│   │
│   └── intl-retail-infra (基础设施层模块)
│       ├── src/main/java/com/mi/info/intl/retail/intlretail/infra
│           ├── advice    (通知/增强)
│           ├── cache     (缓存)
│           ├── config    (配置)
│           ├── database  (数据库)
│           ├── http      (HTTP)
│           ├── mq        (消息队列)
│           ├── push      (推送)
│           ├── repository (仓储)
│           ├── rpc       (远程调用)
│           └── utils     (工具类)
│
└── intl-retail-server (服务启动模块)
    ├── src/main/java/com/mi/info/intl/retail/intlretail/app
        ├── aspect     (切面)
        ├── config     (配置)
        ├── consumer   (消费者)
        ├── controller (控制器 app的对外服务在这里面写controller)
        ├── convert    (转换器)
        ├── dto        (数据传输对象)
        ├── enums      (枚举)
        ├── event      (事件)
        ├── exception  (异常)
        ├── interceptor (拦截器)
        └── scheduler  (调度器)

# 领域数据表
阵地信息
intl_rms_position 表
code 阵地code编号 可以跟其他表做关联
阵地巡检
inspection_record 表
businesss_code = intl_rms_position.code
# 涉及到多领域的规则
1. 如果是联表查 可以在指定的领域中mapper完成SQL拼接以及mapper.xml构建,同时在repository类返回对应的结果  domainService调用repository接口完成领域的集成

# 主数据枚举
InspectionStatusEnum 巡检状态枚举
TaskStatusEnum 任务状态枚举

# 领域的基础设施层
阵地巡检记录 InspectionRecordRepository
阵地信息 PositionRepository
阵地巡检规则配置 RuleConfigRepository
阵地巡检历史记录 InspectionHistoryRepository

# 领域Domain
阵地巡检记录 InspectionRecordDomain

# 更新rule指南
如果发现新增代码是domain和repository 则需要将对应的领域含义和代码更新到rule文件中