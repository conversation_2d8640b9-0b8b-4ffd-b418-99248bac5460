---
description: 
globs: 
alwaysApply: false
---
# 项目结构
intl-retail
│
├── pom.xml (父POM文件)
│
├── intl-retail-api (对外API定义模块，不同领域对应的是不同的包名)
│   ├── src/main/java/com/mi/info/intl/retail/intlretail/service/api 文件路径：intl-retail-api/src/main/java/com/mi/info/intl/retail/intlretail/service/api
│   │   ├── retailer    (零售商相关API)
│   │   ├── inspection  (检查相关API)
│   │   ├── result      (结果相关API)
│   │   ├── request     (请求相关API)
│   │   ├── proxy       (代理相关API)
│   │   ├── mq          (消息队列相关API)
│   │   ├── job         (任务相关API)
│   │   ├── market      (市场相关API)
│   │   ├── messagepush (消息推送相关API)
│   │   ├── copilot     (AI助手相关API)
│   │   ├── fds         (文件存储相关API)
│   │   ├── store       (商店相关API)
│   │   └── demo        (示例API)
│
├── intl-retail-common (通用工具类模块)
│   ├── src/main/java/com/mi/info/intl/retail
│   │   ├── advice      (通知/增强相关)
│   │   │   └── excel   (Excel处理相关)
│   │   ├── constant    (常量定义)
│   │   ├── enums       (枚举定义)
│   │   ├── model       (模型定义)
│   │   └── utils       (工具类)
│
├── intl-retail-core (核心配置模块)
│   ├── src/main/java/com/mi/info/intl/retail/core
│   │   ├── config      (配置相关)
│   │   ├── exception   (异常处理)
│   │   └── utils       (核心工具类)
│
├── intl-retail-cooperation (协同模块)
│   ├── src/main/java/com/mi/info/intl/retail/cooperation
│   │   └── task        (任务相关)
│   │       ├── app     (应用层)
│   │       ├── component (组件)
│   │       ├── config    (配置)
│   │       ├── domain    (领域层)
│   │       ├── dto       (数据传输对象)
│   │       ├── infra     (基础设施层)
│   │       └── inspection (检查相关)
│
├── intl-retail-fieldforce (人力领域模块 领域子模块)
│   ├── src/main/java/com/mi/info/intl/retail
│   │   ├── attendance  (考勤相关)
│   │   └── user        (用户相关)
│   │       ├── app     (应用层)
│   │       ├── constant (常量)
│   │       ├── domain   (领域层)
│   │       └── infra    (基础设施层)
│
├── intl-retail-front (阵地领域模块 领域子模块)
│   ├── src/main/java/com/mi/info/intl/retail/org
│   │   ├── app         (应用层 主要负责服务编排 是api的实现)
│   │   ├── domain      (领域层 主要包含domain层和domainService层 包含最主要的业务逻辑 repository的接口)
│   │   └── infra       (基础设施层 repository的实现)
│
├── intl-retail-sales (销售/库存领域模块)
│
├── intl-retail-proxy (代理模块)
│   ├── intl-retail-app (应用层模块)
│   │   ├── src/main/java/com/mi/info/intl/retail/intlretail
│   │   │   ├── domain  (领域相关)
│   │   │   ├── service (服务相关)
│   │   │   │   └── app (应用层服务)
│   │   │   │       ├── copilot    (AI助手)
│   │   │   │       ├── demo       (示例)
│   │   │   │       ├── fds        (文件存储)
│   │   │   │       ├── inspection (检查)
│   │   │   │       ├── jobhandler (任务处理)
│   │   │   │       ├── market     (市场)
│   │   │   │       ├── messagepush (消息推送)
│   │   │   │       ├── proxy      (代理)
│   │   │   │       ├── retailer   (零售商)
│   │   │   │       ├── rpc        (远程调用)
│   │   │   │       └── store      (商店)
│   │   │   └── utils  (工具类)
│   │
│   ├── intl-retail-domain (领域层模块)
│   │   ├── src/main/java/com/mi/info/intl/retail/intlretail/domain
│   │   │   ├── common   (通用领域)
│   │   │   ├── http     (HTTP相关)
│   │   │   ├── order    (订单领域)
│   │   │   ├── product  (产品领域)
│   │   │   ├── push     (推送领域)
│   │   │   └── userdevice (用户设备领域)
│   │
│   └── intl-retail-infra (基础设施层模块)
│       ├── src/main/java/com/mi/info/intl/retail/intlretail/infra
│           ├── advice    (通知/增强)
│           ├── cache     (缓存)
│           ├── config    (配置)
│           ├── database  (数据库)
│           ├── http      (HTTP)
│           ├── mq        (消息队列)
│           ├── push      (推送)
│           ├── repository (仓储)
│           ├── rpc       (远程调用)
│           └── utils     (工具类)
│
└── intl-retail-server (服务启动模块)
    ├── src/main/java/com/mi/info/intl/retail/intlretail/app
        ├── aspect     (切面)
        ├── config     (配置)
        ├── consumer   (消费者)
        ├── controller (控制器 app的对外服务在这里面写controller)
        ├── convert    (转换器)
        ├── dto        (数据传输对象)
        ├── enums      (枚举)
        ├── event      (事件)
        ├── exception  (异常)
        ├── interceptor (拦截器)
        └── scheduler  (调度器)

# API通用
返回的DTO需要使用 CommonApiResponse<T>.class 其中T是泛型 指的是data的类型
com.mi.info.intl.retail.model.CommonApiResponse
如果请求体存在分页要求，需要继承 BasePageRequest (com.mi.info.intl.retail.cooperation.task.infra.entity.core.BasePageRequest)
PageResponse 为APP的分页的DTO

# 生成对应的接口内容
intl-retail-api 在对应的模块生成的对应的接口用于定义对外服务
具体实现要在子模块中实现
1.对外服务的实现在 子模块/app/service包中
2.对外服务的实现可以调用领域服务对象获取领域对象
注意领域对象不依赖任何外部服务，只是充血模型
领域服务对象的接口在 intl-retail-api/领域/domain
领域服务对象的实现、领域对象、外部依赖接口在 子模块/domain包中
其中领域服务对象类名格式为XXXDomianService
领域对象为XXXDomain
其中领域对象服务可以调用XXXDomain
领域服务对象可以调用外部依赖接口
外部依赖实现要在 子模块/infra包中 比如RuleConfigRepository 数据持久化接口层
外部依赖实现包括所有的外部服务，比如数据库等 可以调用mapper
# 好的代码示例 APP端
controller intl-retail-server中 controller寻找对应的领域
app需要获取账号id 可以通过 调用父类的getAccount()获取
```java
@Slf4j
@RestController
@RequestMapping({"/api/position/inspection", "/*/api/position/inspection"})
@ApiModule(value = "阵地巡检 Controller", apiInterface = PositionInspectionController.class)
public class PositionInspectionController extends BaseController {

    @Resource
    private PositionInspectionService positionInspectionService;

    /**
     * 查询阵地巡检列表
     *
     * @param request 请求参数
     * @return 阵地巡检列表响应
     */
    @PostMapping("/list")
    @ResponseBody
    @ApiDoc(value = "/api/position/inspection/list", description = "查询阵地巡检列表")
    public CommonApiResponse<PageResponse<PositionInspectionItem>> listPositionInspection(@RequestBody PositionInspectionRequest request) {
        log.info("查询阵地巡检列表，请求参数：{}", request);
        PageResponse<PositionInspectionItem> response = positionInspectionService.listPositionInspection(request);
        return new CommonApiResponse<>(response);
    }
}
```
DTO
```java
@Data
public class PositionInspectionItem implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 门店名称
     */
    private String storeName;
    
    /**
     * 阵地名称
     */
    private String positionName;
    
    /**
     * 状态码
     */
    private String status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
}
```
应用层接口
```java
package com.mi.info.intl.retail.intlretail.service.api.position;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;
import com.mi.info.intl.retail.model.PageResponse;

/**
 * 阵地巡检的服务入口
 * 支持APP+PC
 */
public interface PositionInspectionService {

    /**
     * 查询阵地巡检列表
     *
     * @param request 请求参数
     * @return 阵地巡检列表响应
     */
    PageResponse<PositionInspectionItem> listPositionInspection(PositionInspectionRequest request);
}

```
应用层实现
```java
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 阵地巡检服务实现类
 */
@Slf4j
@Service
public class PositionInspectionServiceImpl implements PositionInspectionService {

    @Resource
    private PositionInspectionDomainService positionInspectionDomainService;

    @Override
    public PageResponse<PositionInspectionItem> listPositionInspection(PositionInspectionRequest request) {

        
        List<PositionInspectionItem> positionInspectionItems =  positionInspectionDomainService.listPositionInspection(request);

        return new PageResponse<>(0L, 0L, 0L, positionInspectionItems);
    }

}
```
可以调用domainSerive处理伙计或者获取对应的domain类，调用domain类的接口
domainService 接口
```java
package com.mi.info.intl.retail.intlretail.service.api.position.domain;

import java.util.List;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;

public interface PositionInspectionDomainService {

    List<PositionInspectionItem> listPositionInspection(PositionInspectionRequest request);

}

```
domainService 实现
```java
@Slf4j
@Service
public class PositionInspectionDomainServiceImpl implements PositionInspectionDomainService {

    @Override
    public List<PositionInspectionItem> listPositionInspection(PositionInspectionRequest request) {
        //实际业务逻辑
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'listPositionInspection'");
    }

    

}
```
这里面可以调用对应的数据库和外部依赖

## controller规范
需要使用@ApiModule 标注类
 @ApiDoc 标注对应的类方法
 请求参数和响应体的成员变量标注  @ApiDocClassDefine
 import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
miapi-doc-annos包中字段说明（接口信息标注说明）
1. @ApiDoc
  - value -- 接口路径
  - description -- 接口描述
    - name --接口名称
3. @ApiModule
  - value -- 中文名称
  - apiInterface -- Class<?>


